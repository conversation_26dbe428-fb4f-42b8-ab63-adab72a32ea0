from typing import List, Optional, Union, Dict, Any, Generator
from app.models import Export<PERSON>istory, AISuggestion, UserORM, APIKeyORM, OperationLogORM, Base
from app.models.project import ProjectORM
from app.models.drug import DrugORM
from app.models.excipient import ExcipientORM
from app.models.environment import EnvironmentORM
from app.models.stability import StabilityDataORM
from .schemas import ProjectCreate, ProjectOut, LoginRequest, TokenResponse
from datetime import datetime, timedelta
import jwt as pyjwt
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import os
import uuid
from sqlalchemy.orm import Session

SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 初始化数据库表
Base.metadata.create_all(bind=engine)

def get_db():
    """
    获取数据库Session。
    用于FastAPI依赖注入。
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def generate_ai_suggestions(project_id: Union[str, int], lang: str = 'zh', input_data: Optional[Dict[str, Any]] = None) -> List[AISuggestion]:
    project = next((p for p in _projects_db if p.id == project_id), None)
    drug = (input_data or {}).get('drugName') or getattr(project, 'name', '药物')
    batch = (input_data or {}).get('batch') or getattr(project, 'batch', '202406') if hasattr(project, 'batch') else '202406'
    temp = (input_data or {}).get('temperature') or '40°C'
    humidity = (input_data or {}).get('humidity') or '75%'
    package = (input_data or {}).get('package') or '铝箔'
    excipients = (input_data or {}).get('excipients') or []
    smiles = (input_data or {}).get('smiles') or ''
    
    # 获取药物结构信息
    drug_structure_info: Dict[str, Any] = {}
    if smiles:
        try:
            from rdkit import Chem  # type: ignore
            from rdkit.Chem import Descriptors, MolSurf, Lipinski  # type: ignore
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                drug_structure_info = {
                    "logP": round(Descriptors.MolLogP(mol), 2),  # type: ignore
                    "tpsa": round(MolSurf.TPSA(mol), 2),  # type: ignore
                    "hba": Lipinski.NumHAcceptors(mol),  # type: ignore
                    "hbd": Lipinski.NumHDonors(mol),  # type: ignore
                    "mw": round(Descriptors.MolWt(mol), 2),  # type: ignore
                    "rotatable_bonds": Descriptors.NumRotatableBonds(mol),  # type: ignore
                    "aromatic_rings": Chem.Lipinski.NumAromaticRings(mol),  # type: ignore
                    "has_ester": mol.HasSubstructMatch(Chem.MolFromSmarts("[C,c](=[O,o])[O,o][C,c]")),
                    "has_amide": mol.HasSubstructMatch(Chem.MolFromSmarts("[C,c](=[O,o])[N,n]")),
                    "has_phenol": mol.HasSubstructMatch(Chem.MolFromSmarts("c[O;H]")),
                    "has_aldehyde": mol.HasSubstructMatch(Chem.MolFromSmarts("[C,c;H1](=[O,o])")),
                    "has_ketone": mol.HasSubstructMatch(Chem.MolFromSmarts("[C,c](=[O,o])[C,c]")),
                    "has_carboxylic_acid": mol.HasSubstructMatch(Chem.MolFromSmarts("[C,c](=[O,o])[O;H]")),
                    "has_amine": mol.HasSubstructMatch(Chem.MolFromSmarts("[N;!$(NC=O)]")),
                }
        except Exception as e:
            print(f"RDKit结构分析错误: {e}")
    
    # 专家级根提示词（中英文）
    zh_root = f"""
你是一名药物制剂、药物化学和有机化学及药物稳定性领域的顶级专家，参考《药物稳定性研究技术指导原则》《药物制剂工艺学》《有机化学》《药物化学》《ICH Q1A (R2)》《Stability of Drugs and Dosage Forms》等权威书籍文献。请基于如下参数为药物制剂稳定性研究项目生成3条结构化AI建议：
- 药物名：{drug}
- 批次：{batch}
- 温度：{temp}
- 湿度：{humidity}
- 包装：{package}
- 辅料：{', '.join(excipients) if excipients else '未提供'}
- 药物结构信息：{drug_structure_info}

每条建议需包含：
1. title：一句话总结建议
2. desc：简要描述建议内容
3. risk：高/中/低，表明风险等级
4. detail：详细分析建议的科学依据、实验数据、相关反应机理
5. type：建议类型（如配比、包装、储存、工艺等）
6. mechanism：涉及的化学/物理/药理机理
7. reference：权威文献或指南出处

请基于药物结构特征（如存在的官能团、LogP值等）和环境条件，分析可能的降解途径和稳定性风险，并给出针对性的改进建议。
输出JSON数组。
"""
    en_root = f"""
You are a top expert in pharmaceutical formulation, medicinal chemistry, organic chemistry, and drug stability, referencing authoritative books and guidelines such as 'Stability of Drugs and Dosage Forms', 'ICH Q1A (R2)', 'Pharmaceutical Process Engineering', 'Organic Chemistry', and 'Guideline for Drug Stability Study'. Based on the following parameters, generate 3 structured AI suggestions for a drug stability study project:
- Drug: {drug}
- Batch: {batch}
- Temperature: {temp}
- Humidity: {humidity}
- Package: {package}
- Excipients: {', '.join(excipients) if excipients else 'Not provided'}
- Drug structure information: {drug_structure_info}

Each suggestion must include:
1. title: one-sentence summary
2. desc: brief description
3. risk: High/Medium/Low
4. detail: scientific rationale, experimental data, or mechanism
5. type: suggestion type (e.g., ratio, packaging, storage, process)
6. mechanism: involved chemical/physical/pharmacological mechanism
7. reference: authoritative literature or guideline

Please analyze potential degradation pathways and stability risks based on the drug's structural features (such as functional groups, LogP value, etc.) and environmental conditions, and provide targeted improvement suggestions.
Output as a JSON array.
"""

    # 基于药物结构信息的规则引擎建议
    rule_based_suggestions = []
    
    # 如果有药物结构信息，基于结构特征生成建议
    if drug_structure_info:
        # 酯基水解风险
        if drug_structure_info.get("has_ester", False):
            rule_based_suggestions.append({
                "title": "防止酯基水解" if lang == 'zh' else "Prevent ester hydrolysis",
                "desc": f"降低{drug}中酯基水解风险" if lang == 'zh' else f"Reduce ester hydrolysis risk in {drug}",
                "risk": "高" if lang == 'zh' else "High",
                "detail": f"药物分子中含有酯基，在{humidity}湿度条件下易发生水解反应。建议使用干燥剂并确保{package}包装完整性。" 
                          if lang == 'zh' else 
                          f"The drug molecule contains ester groups that are susceptible to hydrolysis at {humidity} humidity. Recommend using desiccants and ensuring integrity of {package} packaging.",
                "type": "包装" if lang == 'zh' else "Packaging",
                "mechanism": "酯基水解反应" if lang == 'zh' else "Ester hydrolysis",
                "reference": "ICH Q1A (R2), Carstensen JT. Drug Stability: Principles and Practices"
            })
        
        # 酰胺水解风险
        if drug_structure_info.get("has_amide", False):
            rule_based_suggestions.append({
                "title": "控制酰胺水解" if lang == 'zh' else "Control amide hydrolysis",
                "desc": f"减缓{drug}中酰胺键水解" if lang == 'zh' else f"Slow down amide bond hydrolysis in {drug}",
                "risk": "中" if lang == 'zh' else "Medium",
                "detail": f"药物分子含有酰胺键，在{temp}温度下可能加速水解。建议调整pH至4-5之间，并考虑添加适当缓冲剂。" 
                          if lang == 'zh' else 
                          f"The drug contains amide bonds that may undergo accelerated hydrolysis at {temp}. Recommend adjusting pH to 4-5 and considering appropriate buffer agents.",
                "type": "配方" if lang == 'zh' else "Formulation",
                "mechanism": "酰胺水解反应" if lang == 'zh' else "Amide hydrolysis",
                "reference": "Florence AT, Attwood D. Physicochemical Principles of Pharmacy"
            })
        
        # 氧化风险
        if drug_structure_info.get("has_phenol", False) or drug_structure_info.get("aromatic_rings", 0) > 1:
            rule_based_suggestions.append({
                "title": "防止氧化降解" if lang == 'zh' else "Prevent oxidative degradation",
                "desc": f"添加抗氧化剂保护{drug}" if lang == 'zh' else f"Add antioxidants to protect {drug}",
                "risk": "高" if lang == 'zh' else "High",
                "detail": f"药物含有酚羟基或多个芳香环，易发生氧化反应。建议添加0.1-0.2%抗坏血酸或丁基羟基甲苯(BHT)作为抗氧化剂，并使用氮气充填。" 
                          if lang == 'zh' else 
                          f"The drug contains phenolic hydroxyl groups or multiple aromatic rings susceptible to oxidation. Recommend adding 0.1-0.2% ascorbic acid or butylated hydroxytoluene (BHT) as antioxidants, and using nitrogen purging.",
                "type": "配方" if lang == 'zh' else "Formulation",
                "mechanism": "自由基氧化" if lang == 'zh' else "Free radical oxidation",
                "reference": "Yoshioka S, Stella VJ. Stability of Drugs and Dosage Forms"
            })
        
        # 光降解风险
        if drug_structure_info.get("aromatic_rings", 0) > 0:
            rule_based_suggestions.append({
                "title": "防止光降解" if lang == 'zh' else "Prevent photodegradation",
                "desc": f"保护{drug}免受光照影响" if lang == 'zh' else f"Protect {drug} from light exposure",
                "risk": "中" if lang == 'zh' else "Medium",
                "detail": f"药物含有芳香环结构，可能对光敏感。建议使用棕色玻璃容器或遮光包装，并在说明书中注明'避光保存'。" 
                          if lang == 'zh' else 
                          f"The drug contains aromatic structures that may be photosensitive. Recommend using amber glass containers or light-protective packaging, and including 'store protected from light' in the instructions.",
                "type": "包装" if lang == 'zh' else "Packaging",
                "mechanism": "光敏化反应" if lang == 'zh' else "Photosensitization reaction",
                "reference": "ICH Q1B, Tonnesen HH. Photostability of Drugs and Drug Formulations"
            })
        
        # 高LogP值药物的溶解性问题
        if drug_structure_info.get("logP", 0) > 4:
            rule_based_suggestions.append({
                "title": "提高溶解性" if lang == 'zh' else "Improve solubility",
                "desc": f"增强高脂溶性{drug}的溶解度" if lang == 'zh' else f"Enhance dissolution of lipophilic {drug}",
                "risk": "中" if lang == 'zh' else "Medium",
                "detail": f"药物LogP值为{drug_structure_info.get('logP')}，属高脂溶性，可能面临溶解度和生物利用度挑战。建议考虑使用表面活性剂、固体分散体或环糊精包合物技术提高溶解性。" 
                          if lang == 'zh' else 
                          f"The drug has a LogP of {drug_structure_info.get('logP')}, indicating high lipophilicity with potential solubility and bioavailability challenges. Consider using surfactants, solid dispersions, or cyclodextrin complexation to improve solubility.",
                "type": "配方" if lang == 'zh' else "Formulation",
                "mechanism": "增溶技术" if lang == 'zh' else "Solubilization technology",
                "reference": "Stegemann S, et al. Eur J Pharm Sci. 2007;31(5):249-261"
            })
    
    # 基于包装类型的建议
    if "PVC" in package:
        rule_based_suggestions.append({
            "title": "升级包装材料" if lang == 'zh' else "Upgrade packaging material",
            "desc": f"从{package}升级为铝塑包装" if lang == 'zh' else f"Upgrade from {package} to Alu-alu blister",
            "risk": "高" if lang == 'zh' else "High",
            "detail": f"PVC包装材料对水汽屏障性能较差，在{humidity}湿度条件下可能导致药物吸湿并加速降解。建议升级为铝塑复合包装以提供更好的水汽和氧气屏障。" 
                      if lang == 'zh' else 
                      f"PVC packaging has poor moisture barrier properties, which may lead to drug hygroscopicity and accelerated degradation at {humidity} humidity. Recommend upgrading to aluminum-aluminum blister for better moisture and oxygen barrier.",
            "type": "包装" if lang == 'zh' else "Packaging",
            "mechanism": "水汽渗透" if lang == 'zh' else "Moisture permeation",
            "reference": "Pilchik R. Pharm Tech. 2000;24(11):68-78"
        })
    
    # 基于温度的建议
    if "40" in temp:
        rule_based_suggestions.append({
            "title": "优化储存条件" if lang == 'zh' else "Optimize storage conditions",
            "desc": f"调整{drug}的储存温度要求" if lang == 'zh' else f"Adjust storage temperature requirements for {drug}",
            "risk": "高" if lang == 'zh' else "High",
            "detail": f"在{temp}高温条件下，药物降解速率可能增加5-10倍（根据阿伦尼乌斯方程）。建议在说明书中明确标注'储存温度不超过25°C'，并考虑在特殊气候区域使用冷链运输。" 
                      if lang == 'zh' else 
                      f"At {temp}, drug degradation rate may increase 5-10 fold (according to Arrhenius equation). Recommend clearly stating 'store below 25°C' in the product information and considering cold chain transportation in special climatic zones.",
            "type": "储存" if lang == 'zh' else "Storage",
            "mechanism": "热降解动力学" if lang == 'zh' else "Thermal degradation kinetics",
            "reference": "ICH Q1A (R2), WHO Technical Report Series, No. 953, 2009"
        })
    
    # 多模型支持
    ai_services = []
    if os.getenv('OPENAI_API_KEY'):
        ai_services.append('openai')
    if os.getenv('AZURE_OPENAI_KEY'):
        ai_services.append('azure')
    if os.getenv('QWEN_API_KEY'):
        ai_services.append('qwen')
    if os.getenv('ERNIE_API_KEY'):
        ai_services.append('ernie')
    if os.getenv('GLM_API_KEY'):
        ai_services.append('glm')
    if os.getenv('MOONSHOT_API_KEY'):
        ai_services.append('moonshot')
    if os.getenv('GEMINI_API_KEY'):
        ai_services.append('gemini')
    if os.getenv('GROK_API_KEY'):
        ai_services.append('grok')
    if os.getenv('DEEPSEEK_API_KEY'):
        ai_services.append('deepseek')
    
    # 依次尝试可用大模型
    ai_suggestions = []
    for service in ai_services:
        try:
            if service == 'openai':
                import openai  # type: ignore
                openai.api_key = os.getenv('OPENAI_API_KEY')
                prompt = zh_root if lang == 'zh' else en_root
                response = openai.ChatCompletion.create(  # type: ignore
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.2,
                    max_tokens=1024
                )
                import json
                content = response.choices[0].message.content  # type: ignore
                suggestions = json.loads(content)
                ai_suggestions = [AISuggestion(**s) for s in suggestions]
                break
            # 其它主流大模型API调用（伪代码/可扩展）
            if service == 'azure':
                # import openai
                # openai.api_type = 'azure'
                # openai.api_key = os.getenv('AZURE_OPENAI_KEY')
                # ...
                pass
            if service == 'qwen':
                # requests.post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', ...)
                pass
            if service == 'ernie':
                # requests.post('https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions', ...)
                pass
            if service == 'glm':
                # requests.post('https://open.bigmodel.cn/api/paas/v4/chat/completions', ...)
                pass
            if service == 'moonshot':
                # requests.post('https://api.moonshot.cn/v1/chat/completions', ...)
                pass
            if service == 'gemini':
                # Google Gemini API 健壮性增强
                import requests
                prompt = zh_root if lang == 'zh' else en_root
                headers = {"Authorization": f"Bearer {os.getenv('GEMINI_API_KEY')}", "Content-Type": "application/json"}
                data = {"model": "gemini-pro", "messages": [{"role": "user", "content": prompt}], "max_tokens": 1024}
                resp = requests.post("https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", headers=headers, json=data)
                if resp.ok:
                    import json
                    try:
                        candidates = resp.json().get("candidates")
                        if candidates and "content" in candidates[0] and "parts" in candidates[0]["content"] and candidates[0]["content"]["parts"]:
                            content = candidates[0]["content"]["parts"][0].get("text")
                            suggestions = json.loads(content)
                            ai_suggestions = [AISuggestion(**s) for s in suggestions]
                            break
                    except Exception as e:
                        print('Gemini内容解析失败', e)
            if service == 'grok':
                # Grok API 健壮性增强
                import requests
                prompt = zh_root if lang == 'zh' else en_root
                headers = {"Authorization": f"Bearer {os.getenv('GROK_API_KEY')}", "Content-Type": "application/json"}
                data = {"messages": [{"role": "user", "content": prompt}], "max_tokens": 1024}
                resp = requests.post("https://api.grok.x.ai/v1/chat/completions", headers=headers, json=data)
                if resp.ok:
                    import json
                    try:
                        choices = resp.json().get("choices")
                        if choices and "message" in choices[0] and "content" in choices[0]["message"]:
                            content = choices[0]["message"]["content"]
                            suggestions = json.loads(content)
                            ai_suggestions = [AISuggestion(**s) for s in suggestions]
                            break
                    except Exception as e:
                        print('Grok内容解析失败', e)
            if service == 'deepseek':
                # Deepseek API 健壮性增强
                import requests
                prompt = zh_root if lang == 'zh' else en_root
                headers = {"Authorization": f"Bearer {os.getenv('DEEPSEEK_API_KEY')}", "Content-Type": "application/json"}
                data = {"model": "deepseek-chat", "messages": [{"role": "user", "content": prompt}], "max_tokens": 1024}
                resp = requests.post("https://api.deepseek.com/v1/chat/completions", headers=headers, json=data)
                if resp.ok:
                    import json
                    try:
                        choices = resp.json().get("choices")
                        if choices and "message" in choices[0] and "content" in choices[0]["message"]:
                            content = choices[0]["message"]["content"]
                            suggestions = json.loads(content)
                            ai_suggestions = [AISuggestion(**s) for s in suggestions]
                            break
                    except Exception as e:
                        print('Deepseek内容解析失败', e)
        except Exception as e:
            print(f'{service} AI服务调用失败，尝试下一个', e)
    
    # 如果AI服务都失败了，使用规则引擎生成的建议
    if not ai_suggestions and rule_based_suggestions:
        # 从规则引擎中选择最多3条建议
        selected_suggestions = rule_based_suggestions[:3]
        # 转换为AISuggestion对象
        for i, s in enumerate(selected_suggestions):
            ai_suggestions.append(AISuggestion(title=s["title"],
                desc=s["desc"],
                risk=s["risk"],
                detail=s["detail"],
                type=s["type"]
            ))
    
    # 如果AI服务和规则引擎都失败，使用模板建议
    if not ai_suggestions:
        if lang == 'en':
            suggestions = [
                AISuggestion(title="Optimize excipient ratio", desc=f"Adjust excipient A ratio for better stability of {drug}.", risk="High", detail=f"Excipient A degrades at high temperature ({temp}), recommend reducing from 20% to 10%.", type="Ratio"),
                AISuggestion(title="Change packaging material", desc=f"Use {package} packaging to reduce humidity impact.", risk="Medium", detail=f"{package} packaging isolates moisture, improving long-term stability.", type="Packaging"),
                AISuggestion(title="Optimize storage conditions", desc="Lower storage temperature to extend shelf life.", risk="High", detail=f"High temperature ({temp}) accelerates degradation, recommend ≤25°C.", type="Storage"),
            ]
        else:
            suggestions = [
                AISuggestion(title="优化辅料配比", desc=f"建议调整{drug}的辅料A比例以提升稳定性。", risk="高", detail=f"辅料A在高温（{temp}）下易降解，建议比例从20%降至10%。", type="配比"),
                AISuggestion(title="更换包装材料", desc=f"推荐使用{package}包装以降低湿度（{humidity}）影响。", risk="中", detail=f"{package}包装可有效隔绝水汽，提升长期稳定性。", type="包装"),
                AISuggestion(title="优化储存条件", desc="建议降低储存温度以延长药物有效期。", risk="高", detail=f"高温（{temp}）加速药物降解，建议储存温度不高于25℃。", type="储存"),
            ]
        ai_suggestions = suggestions
    
    # 按风险等级排序
    ai_suggestions.sort(key=lambda s: {'高': 0, 'High': 0, '中': 1, 'Medium': 1, '低': 2, 'Low': 2}.get(s.risk, 3))
    return ai_suggestions

def add_export_history(history_list: List[ExportHistory], record: ExportHistory) -> None:
    history_list.append(record)
    # 可扩展持久化 

def list_projects() -> List[ProjectOut]:
    return _projects_db

def create_project(proj: ProjectCreate) -> ProjectOut:
    new_id = max([p.id for p in _projects_db], default=0) + 1
    new_proj = ProjectOut(id=new_id, name=proj.name, status=proj.status, created_at=datetime.now().strftime("%Y-%m-%d"))
    _projects_db.append(new_proj)
    return new_proj

def delete_project(project_id: int) -> bool:
    global _projects_db
    before = len(_projects_db)
    _projects_db = [p for p in _projects_db if p.id != project_id]
    return len(_projects_db) < before

def update_project(project_id: int, proj: ProjectCreate) -> ProjectOut:
    for p in _projects_db:
        if p.id == project_id:
            p.name = proj.name
            p.status = proj.status
            return p
    raise ValueError('项目未找到')

# def authenticate_user(req: LoginRequest) -> TokenResponse:
#     ... # 已废弃，使用app/services/auth.py中的新版本

def list_api_keys() -> List[Dict[str, Any]]:
    return [{**k, "key": k["key"][:8] + '****'} for k in _api_key_store]

def create_api_key() -> Dict[str, Any]:
    import secrets
    new_key = {"key": 'api-' + secrets.token_hex(12), "enabled": True, "created_at": datetime.now().strftime('%Y-%m-%d %H:%M')}
    _api_key_store.append(new_key)
    return new_key

def disable_api_key(key: str) -> bool:
    for k in _api_key_store:
        if k["key"] == key:
            k["enabled"] = False
            return True
    return False

def list_operation_logs(skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
    return _operation_logs[skip:skip+limit]

# 示例：API Key数据库操作雏形
def db_list_api_keys(db: Session) -> List[APIKeyORM]:
    """
    查询所有API Key。
    - db: Session
    - return: List[APIKeyORM]
    """
    return db.query(APIKeyORM)  # type: ignore.all()

def db_create_api_key(db: Session, user_id: int) -> APIKeyORM:
    """
    创建新API Key。
    - db: Session
    - user_id: 用户ID
    - return: APIKeyORM
    """
    import secrets
    new_key = APIKeyORM(key='api-' + secrets.token_hex(12), enabled=True, user_id=user_id)
    db.add(new_key)
    db.commit()
    db.refresh(new_key)
    return new_key

# 示例：操作日志数据库操作雏形
def db_list_operation_logs(db: Session, skip: int = 0, limit: int = 20) -> List[OperationLogORM]:
    """
    查询操作日志，支持分页。
    - db: Session
    - skip: 跳过条数
    - limit: 返回条数
    - return: List[OperationLogORM]
    """
    return db.query(OperationLogORM)  # type: ignore.offset(skip).limit(limit).all()

# ================== 单元测试 =====================
def test_db_create_api_key():
    db = next(get_db())
    user_id = 1
    key = db_create_api_key(db, user_id)
    assert key.key.startswith('api-')
    assert key.enabled
    assert key.user_id == user_id

def test_db_list_api_keys():
    db = next(get_db())
    keys = db_list_api_keys(db)
    assert isinstance(keys, list)

def test_db_list_operation_logs():
    db = next(get_db())
    logs = db_list_operation_logs(db, 0, 10)
    assert isinstance(logs, list)

# 导出历史相关服务函数

_export_history = []

def add_export_history_service(history_item: dict):
    """添加导出历史记录"""
    _export_history.append(history_item)
    return True

def list_export_history_service():
    """获取所有导出历史记录"""
    return _export_history 