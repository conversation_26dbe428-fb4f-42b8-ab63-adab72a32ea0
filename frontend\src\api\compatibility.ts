import axios from 'axios';
import { message } from 'antd';
import { API_BASE_URL } from '../config';

/**
 * 专业辅料相容性分析API
 * 基于药物化学和物理化学原理的深度相容性评估
 */

// 辅料详细信息接口
export interface ExcipientDetail {
  name: string;
  cas?: string;
  structure?: string;          // SMILES结构
  batch?: string;              // 批号
  supplier?: string;           // 供应商
  purity?: number;             // 纯度(%)
  moisture?: number;           // 水分含量(%)
  ph?: number;                 // pH值
  particle_size?: string;      // 粒径分布
  specific_surface?: number;   // 比表面积(m²/g)
  concentration?: number;      // 使用浓度(%)
}

// 相容性风险类型
export enum RiskType {
  CHEMICAL = 'chemical',       // 化学不相容
  PHYSICAL = 'physical',       // 物理不相容
  STABILITY = 'stability',     // 稳定性影响
  FUNCTIONAL = 'functional'    // 功能性影响
}

// 相互作用机制
export interface InteractionMechanism {
  type: string;                // 作用类型
  mechanism: string;           // 机理描述
  severity: 'high' | 'medium' | 'low';  // 严重程度
  evidence: string[];          // 证据/文献
  kinetics?: string;           // 动力学描述
  prevention: string[];        // 预防措施
}

// 相容性分析结果
export interface CompatibilityResult {
  excipient: string;
  risk_level: 'high' | 'medium' | 'low';
  risk_score: number;          // 0-10分
  risk_types: RiskType[];
  mechanisms: InteractionMechanism[];
  degradation_products?: string[];  // 可能的降解产物
  incompatible_groups?: string[];   // 不相容的官能团
  ph_sensitivity?: {
    optimal_range: [number, number];
    risk_zones: Array<{ range: [number, number]; risk: string }>;
  };
  temperature_sensitivity?: {
    critical_temp: number;
    activation_energy?: number;
  };
  moisture_sensitivity?: {
    critical_rh: number;
    hygroscopicity: string;
  };
  recommendations: {
    formulation: string[];     // 配方建议
    processing: string[];      // 工艺建议
    packaging: string[];       // 包装建议
    storage: string[];         // 储存建议
  };
  alternatives?: Array<{       // 替代辅料建议
    name: string;
    reason: string;
    advantages: string[];
  }>;
  references?: string[];       // 参考文献
}

// 相容性分析请求
export interface CompatibilityAnalysisRequest {
  drug: {
    name: string;
    structure: string;         // SMILES
    cas?: string;             // CAS号
    formula?: string;         // 分子式
    category?: string;        // 药物类别
    properties?: {
      mw?: number;             // 分子量
      logp?: number;           // 脂水分配系数
      pka?: number[];          // 解离常数
      solubility?: number;     // 溶解度
      melting_point?: number;  // 熔点
      stability_profile?: string;  // 稳定性特征
    };
  };
  excipients: ExcipientDetail[];
  conditions?: {
    temperature?: number;      // 储存温度
    humidity?: number;         // 相对湿度
    ph?: number;              // 体系pH
    light_exposure?: boolean;  // 光照暴露
    oxygen_exposure?: boolean; // 氧气暴露
    process_stress?: string[]; // 工艺应力（如：压片、制粒）
  };
  analysis_depth?: 'basic' | 'detailed' | 'comprehensive';
  project_id?: string;       // 项目ID，用于保存分析结果
}

// 相容性分析响应
export interface CompatibilityAnalysisResponse {
  drug_name: string;
  analysis_date: string;
  overall_risk: 'high' | 'medium' | 'low';
  critical_excipients: string[];  // 关键风险辅料
  results: CompatibilityResult[];
  interaction_matrix?: {           // 辅料间相互作用矩阵
    [key: string]: {
      [key: string]: {
        compatible: boolean;
        interaction?: string;
      }
    }
  };
  formulation_suggestions: {
    preferred_excipients: string[];
    avoid_combinations: Array<string[]>;
    processing_order?: string[];   // 建议的加工顺序
    special_requirements?: string[];
  };
  stability_impact: {
    estimated_shelf_life_reduction?: number;  // 预计货架期缩短(%)
    critical_quality_attributes?: string[];   // 关键质量属性
    monitoring_requirements?: string[];       // 监测要求
  };
}

// API实例
const compatibilityAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
compatibilityAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
compatibilityAPI.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const errorMessage = error.response?.data?.detail || error.message || '相容性分析失败';
    console.error('相容性分析API错误:', error);
    message.error(errorMessage);
    return Promise.reject(error);
  }
);

/**
 * 执行辅料相容性分析
 * @param request 相容性分析请求
 * @returns 分析结果
 */
export async function analyzeCompatibility(
  request: CompatibilityAnalysisRequest
): Promise<CompatibilityAnalysisResponse> {
  try {
    console.log('发送相容性分析请求:', request);
    const response = await compatibilityAPI.post<CompatibilityAnalysisResponse>(
      '/compatibility/assess',
      request
    );
    console.log('相容性分析响应:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('相容性分析失败:', error);
    throw error;
  }
}

/**
 * 获取辅料数据库信息
 */
export async function getExcipientDatabase(): Promise<{
  categories: { [key: string]: string[] };
  properties: { [key: string]: any };
}> {
  try {
    const response = await compatibilityAPI.get<{
      categories: { [key: string]: string[] };
      properties: { [key: string]: any };
    }>('/excipients-database');
    return response.data;
  } catch (error: any) {
    console.warn('获取辅料数据库失败，使用默认数据:', error.response?.status || error.message);
    // 返回默认数据，不显示错误消息给用户
    return {
      categories: {
        '填充剂': ['微晶纤维素', '乳糖', '甘露醇', '淀粉'],
        '崩解剂': ['羧甲基纤维素钠', '交联聚维酮', '羟丙纤维素'],
        '润滑剂': ['硬脂酸镁', '硬脂酸', '滑石粉'],
        '黏合剂': ['聚维酮K30', '羟丙甲纤维素', '聚乙二醇'],
        '表面活性剂': ['十二烷基硫酸钠', '聚山梨酯80', '泊洛沙姆'],
        '防腐剂': ['苯甲酸钠', '尼泊金甲酯', '山梨酸钾'],
        '抗氧化剂': ['维生素E', 'BHT', '抗坏血酸']
      },
      properties: {
        excipient_compatibility_matrix: []
      }
    };
  }
}

/**
 * 预测降解产物
 */
export async function predictDegradationProducts(
  drugStructure: string,
  conditions: any
): Promise<string[]> {
  try {
    const response = await compatibilityAPI.post<{ products: string[] }>('/predict/degradation', {
      structure: drugStructure,
      conditions: conditions
    });
    return response.data.products;
  } catch (error) {
    console.error('降解产物预测失败:', error);
    return ['水解产物', '氧化产物', '光解产物'];
  }
}

/**
 * 获取相容性案例库
 */
export async function getCompatibilityCases(
  drugType?: string,
  excipientType?: string
): Promise<Array<{
  drug: string;
  excipient: string;
  issue: string;
  solution: string;
  reference: string;
}>> {
  try {
    const response = await compatibilityAPI.get<{
      cases: Array<{
        drug: string;
        excipient: string;
        issue: string;
        solution: string;
        reference: string;
      }>
    }>('/compatibility/cases', {
      params: { drug_type: drugType, excipient_type: excipientType }
    });
    return response.data.cases || [];
  } catch (error: any) {
    console.warn('获取案例库失败，返回空数组:', error.response?.status || error.message);
    return [];
  }
}