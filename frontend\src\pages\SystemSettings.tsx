import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Select, Button, Switch, Alert, Typography, Row, Col, Tabs, InputNumber, Divider, message, Space, Tag } from 'antd';
import { SaveOutlined, ReloadOutlined, DatabaseOutlined, ApiOutlined, ExperimentOutlined, SecurityScanOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface SystemConfig {
  api: {
    pubchem_enabled: boolean;
    chemspider_enabled: boolean;
    chemspider_api_key?: string;
    timeout: number;
    retry_count: number;
    chembl_enabled: boolean;
    drugbank_enabled: boolean;
    zinc_enabled: boolean;
    cas_enabled: boolean;
  };
  ai: {
    enabled: boolean;
    provider: string;
    openai_api_key?: string;
    deepseek_api_key?: string;
    grok_api_key?: string;
    default_model?: string;
    temperature?: number;
    max_tokens?: number;
  };
  database: {
    auto_backup: boolean;
    backup_interval: number;
    max_backups: number;
  };
  prediction: {
    default_confidence_level: number;
    default_prediction_months: number;
    enable_ml_models: boolean;
    cache_predictions: boolean;
  };
  security: {
    session_timeout: number;
    password_complexity: boolean;
    two_factor_auth: boolean;
  };
  notifications: {
    email_enabled: boolean;
    email_server?: string;
    email_port?: number;
    stability_alerts: boolean;
  };
}

const SystemSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [activeTab, setActiveTab] = useState('api');

  useEffect(() => {
    loadSystemConfig();
  }, []);

  const loadSystemConfig = async () => {
    setLoading(true);
    try {
      const response = await axios.get<SystemConfig>('/api/system/config');
      setConfig(response.data);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('加载系统配置失败:', error);
      // 使用默认配置
      const defaultConfig: SystemConfig = {
        api: {
          pubchem_enabled: true,
          chemspider_enabled: false,
          timeout: 30,
          retry_count: 3,
          chembl_enabled: false,
          drugbank_enabled: false,
          zinc_enabled: false,
          cas_enabled: false
        },
        ai: {
          enabled: true,
          provider: 'deepseek',
          default_model: 'deepseek-chat',
          temperature: 0.7,
          max_tokens: 4096
        },
        database: {
          auto_backup: true,
          backup_interval: 24,
          max_backups: 7
        },
        prediction: {
          default_confidence_level: 95,
          default_prediction_months: 24,
          enable_ml_models: true,
          cache_predictions: true
        },
        security: {
          session_timeout: 60,
          password_complexity: true,
          two_factor_auth: false
        },
        notifications: {
          email_enabled: false,
          stability_alerts: true
        }
      };
      setConfig(defaultConfig);
      form.setFieldsValue(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    setSaving(true);
    try {
      // 确保所有必需的字段都有值
      const configData = {
        api: {
          pubchem_enabled: values.api?.pubchem_enabled ?? true,
          chemspider_enabled: values.api?.chemspider_enabled ?? false,
          chemspider_api_key: values.api?.chemspider_api_key || null,
          timeout: values.api?.timeout || 30,
          retry_count: values.api?.retry_count || 3,
          chembl_enabled: values.api?.chembl_enabled ?? false,
          drugbank_enabled: values.api?.drugbank_enabled ?? false,
          zinc_enabled: values.api?.zinc_enabled ?? false,
          cas_enabled: values.api?.cas_enabled ?? false
        },
        ai: {
          enabled: values.ai?.enabled ?? true,
          provider: values.ai?.provider || 'deepseek',
          openai_api_key: values.ai?.openai_api_key || null,
          deepseek_api_key: values.ai?.deepseek_api_key || null,
          grok_api_key: values.ai?.grok_api_key || null,
          default_model: values.ai?.default_model || 'deepseek-chat',
          temperature: values.ai?.temperature ?? 0.7,
          max_tokens: values.ai?.max_tokens || 4096
        },
        database: {
          auto_backup: values.database?.auto_backup ?? true,
          backup_interval: values.database?.backup_interval || 24,
          max_backups: values.database?.max_backups || 7
        },
        prediction: {
          default_confidence_level: values.prediction?.default_confidence_level || 95,
          default_prediction_months: values.prediction?.default_prediction_months || 24,
          enable_ml_models: values.prediction?.enable_ml_models ?? true,
          cache_predictions: values.prediction?.cache_predictions ?? true
        },
        security: {
          session_timeout: values.security?.session_timeout || 60,
          password_complexity: values.security?.password_complexity ?? true,
          two_factor_auth: values.security?.two_factor_auth ?? false
        },
        notifications: {
          email_enabled: values.notifications?.email_enabled ?? false,
          email_server: values.notifications?.email_server || null,
          email_port: values.notifications?.email_port || null,
          stability_alerts: values.notifications?.stability_alerts ?? true
        }
      };
      
      console.log('发送配置数据:', configData);
      await axios.post('/api/system/config', configData);
      message.success('系统配置已保存');
      setConfig(configData);
    } catch (error: any) {
      console.error('保存配置失败:', error);
      console.error('错误详情:', error.response?.data);
      
      if (error.response?.status === 422) {
        message.error(`数据验证失败: ${error.response.data.detail?.[0]?.msg || '请检查输入数据'}`);
      } else {
        message.error('保存配置失败，请重试');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (config) {
      form.setFieldsValue(config);
    }
  };

  return (
    <div>
      <Title level={2}>系统设置</Title>
      
      <Card loading={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={config || {}}
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane 
              tab={
                <span>
                  <ApiOutlined />
                  API配置
                </span>
              } 
              key="api"
            >
              <Alert
                message="外部API配置"
                description="配置外部化学数据库API，用于获取药物结构和性质信息"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'pubchem_enabled']}
                    label="启用PubChem API"
                    valuePropName="checked"
                    extra="免费开放API，无需密钥"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'chemspider_enabled']}
                    label="启用ChemSpider API"
                    valuePropName="checked"
                    extra={
                      <Space direction="vertical" size="small">
                        <Text type="secondary">需要API密钥（每月1000次免费调用）</Text>
                        <Space>
                          <a href="https://developer.rsc.org/" target="_blank" rel="noopener noreferrer">
                            注册获取密钥
                          </a>
                          <Text type="secondary">|</Text>
                          <a href="https://developer.rsc.org/terms" target="_blank" rel="noopener noreferrer">
                            使用条款
                          </a>
                        </Space>
                      </Space>
                    }
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'chembl_enabled']}
                    label="启用ChEMBL API"
                    valuePropName="checked"
                    extra="欧洲生物信息研究所的开放数据库，免费使用"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'drugbank_enabled']}
                    label="启用DrugBank API"
                    valuePropName="checked"
                    extra="综合药物数据库，部分功能需要学术许可"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => 
                  prevValues?.api?.chemspider_enabled !== currentValues?.api?.chemspider_enabled
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue(['api', 'chemspider_enabled']) ? (
                    <Form.Item
                      name={['api', 'chemspider_api_key']}
                      label="ChemSpider API密钥"
                      rules={[{ required: true, message: '请输入API密钥' }]}
                      extra={
                        <Alert
                          message="如何获取ChemSpider API密钥"
                          description={
                            <ol style={{ marginBottom: 0, paddingLeft: 20 }}>
                              <li>访问 <a href="https://developer.rsc.org/" target="_blank" rel="noopener noreferrer">RSC开发者平台</a></li>
                              <li>注册一个免费账户</li>
                              <li>登录后点击"Add a new key"</li>
                              <li>复制生成的API密钥</li>
                              <li>粘贴到上方输入框中</li>
                            </ol>
                          }
                          type="info"
                          showIcon
                          style={{ marginTop: 8 }}
                        />
                      }
                    >
                      <Input.Password placeholder="输入ChemSpider API密钥" />
                    </Form.Item>
                  ) : null
                }
              </Form.Item>
              
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => 
                  prevValues?.api?.drugbank_enabled !== currentValues?.api?.drugbank_enabled
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue(['api', 'drugbank_enabled']) ? (
                    <Form.Item
                      name={['api', 'drugbank_api_key']}
                      label="DrugBank API密钥"
                      extra={
                        <Text type="secondary">
                          访问 <a href="https://www.drugbank.ca/api" target="_blank" rel="noopener noreferrer">DrugBank API</a> 申请密钥
                        </Text>
                      }
                    >
                      <Input.Password placeholder="输入DrugBank API密钥（可选）" />
                    </Form.Item>
                  ) : null
                }
              </Form.Item>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'timeout']}
                    label="API超时时间（秒）"
                    rules={[{ required: true, message: '请输入超时时间' }]}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['api', 'retry_count']}
                    label="重试次数"
                    rules={[{ required: true, message: '请输入重试次数' }]}
                  >
                    <InputNumber min={0} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <ApiOutlined />
                  AI服务配置
                </span>
              } 
              key="ai"
            >
              <Alert
                message="AI服务配置"
                description={
                  <div>
                    <p>配置AI大模型服务，用于增强药物稳定性分析和预测</p>
                    <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                      <li><strong>OpenAI：</strong>ChatGPT系列模型，功能强大，需要付费API密钥</li>
                      <li><strong>DeepSeek：</strong>专注于深度思考的AI模型，性价比高</li>
                      <li><strong>Grok：</strong>X.AI提供的AI模型，具有实时信息获取能力</li>
                    </ul>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Form.Item
                name={['ai', 'enabled']}
                label="启用AI服务"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => 
                  prevValues?.ai?.enabled !== currentValues?.ai?.enabled
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue(['ai', 'enabled']) ? (
                    <>
                      <Form.Item
                        name={['ai', 'provider']}
                        label="AI服务提供商"
                        rules={[{ required: true, message: '请选择AI服务提供商' }]}
                      >
                        <Select>
                          <Option value="openai">OpenAI</Option>
                          <Option value="deepseek">DeepSeek</Option>
                          <Option value="grok">Grok</Option>
                        </Select>
                      </Form.Item>
                      
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) => 
                          prevValues?.ai?.provider !== currentValues?.ai?.provider
                        }
                      >
                        {({ getFieldValue }) => {
                          const provider = getFieldValue(['ai', 'provider']);
                          return (
                            <>
                              {provider === 'openai' && (
                                <Form.Item
                                  name={['ai', 'openai_api_key']}
                                  label="OpenAI API密钥"
                                  rules={[{ required: true, message: '请输入OpenAI API密钥' }]}
                                >
                                  <Input.Password placeholder="sk-..." />
                                </Form.Item>
                              )}
                              {provider === 'deepseek' && (
                                <Form.Item
                                  name={['ai', 'deepseek_api_key']}
                                  label="DeepSeek API密钥"
                                  rules={[{ required: true, message: '请输入DeepSeek API密钥' }]}
                                >
                                  <Input.Password placeholder="sk-..." />
                                </Form.Item>
                              )}
                              {provider === 'grok' && (
                                <Form.Item
                                  name={['ai', 'grok_api_key']}
                                  label="Grok API密钥"
                                  rules={[{ required: true, message: '请输入Grok API密钥' }]}
                                >
                                  <Input.Password placeholder="xai-..." />
                                </Form.Item>
                              )}
                            </>
                          );
                        }}
                      </Form.Item>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name={['ai', 'temperature']}
                            label="温度参数"
                            tooltip="控制AI输出的随机性，0-1之间，越高越随机"
                          >
                            <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name={['ai', 'max_tokens']}
                            label="最大Token数"
                            tooltip="控制AI输出的最大长度"
                          >
                            <InputNumber min={100} max={8192} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  ) : null
                }
              </Form.Item>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <DatabaseOutlined />
                  数据库配置
                </span>
              } 
              key="database"
            >
              <Alert
                message="数据库备份设置"
                description="配置自动备份策略，保护您的数据安全"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Form.Item
                name={['database', 'auto_backup']}
                label="启用自动备份"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => 
                  prevValues?.database?.auto_backup !== currentValues?.database?.auto_backup
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue(['database', 'auto_backup']) ? (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name={['database', 'backup_interval']}
                            label="备份间隔（小时）"
                            rules={[{ required: true, message: '请输入备份间隔' }]}
                          >
                            <InputNumber min={1} max={168} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name={['database', 'max_backups']}
                            label="最大备份数量"
                            rules={[{ required: true, message: '请输入最大备份数量' }]}
                          >
                            <InputNumber min={1} max={30} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  ) : null
                }
              </Form.Item>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <ExperimentOutlined />
                  预测配置
                </span>
              } 
              key="prediction"
            >
              <Alert
                message="稳定性预测默认设置"
                description="配置稳定性预测的默认参数"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['prediction', 'default_confidence_level']}
                    label="默认置信区间（%）"
                    rules={[{ required: true, message: '请输入置信区间' }]}
                  >
                    <InputNumber min={50} max={99} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['prediction', 'default_prediction_months']}
                    label="默认预测时长（月）"
                    rules={[{ required: true, message: '请输入预测时长' }]}
                  >
                    <InputNumber min={1} max={60} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['prediction', 'enable_ml_models']}
                    label="启用机器学习模型"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['prediction', 'cache_predictions']}
                    label="缓存预测结果"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <SecurityScanOutlined />
                  安全设置
                </span>
              } 
              key="security"
            >
              <Alert
                message="系统安全配置"
                description="配置系统安全相关的参数"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['security', 'session_timeout']}
                    label="会话超时时间（分钟）"
                    rules={[{ required: true, message: '请输入超时时间' }]}
                  >
                    <InputNumber min={5} max={480} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['security', 'password_complexity']}
                    label="强制密码复杂度"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name={['security', 'two_factor_auth']}
                label="启用双因素认证"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </TabPane>
          </Tabs>
          
          <Divider />
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={saving} icon={<SaveOutlined />}>
                保存设置
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      
      <Card title="系统信息" style={{ marginTop: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>系统版本：</Text>
            <Tag color="blue">v1.0.0</Tag>
          </Col>
          <Col span={8}>
            <Text strong>数据库状态：</Text>
            <Tag color="green">正常</Tag>
          </Col>
          <Col span={8}>
            <Text strong>API服务状态：</Text>
            <Tag color="green">在线</Tag>
          </Col>
        </Row>
        <Divider />
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>最后备份时间：</Text>
            <Text>2024-01-15 10:30</Text>
          </Col>
          <Col span={8}>
            <Text strong>数据库大小：</Text>
            <Text>125 MB</Text>
          </Col>
          <Col span={8}>
            <Text strong>缓存使用率：</Text>
            <Text>45%</Text>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SystemSettings; 