from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, JSON, Text
import datetime
# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base

class EnvironmentORM(Base):
    __tablename__ = 'environments'
    id = Column(String, primary_key=True, index=True)
    temperature = Column(Float, nullable=True)  # 温度(°C)
    humidity = Column(Float, nullable=True)  # 湿度(%)
    packaging = Column(String, nullable=True)  # 包装材料
    batch = Column(String, nullable=True)  # 批次
    sample_code = Column(String, nullable=True)  # 样品编号
    
    # 环境条件详情
    light_condition = Column(String, nullable=True)  # 光照条件
    light_intensity = Column(Float, nullable=True)  # 光照强度(lux)
    uv_exposure = Column(Boolean, nullable=True)  # 是否暴露于UV光
    pressure = Column(Float, nullable=True)  # 压力(kPa)
    oxygen_level = Column(Float, nullable=True)  # 氧气浓度(%)
    carbon_dioxide_level = Column(Float, nullable=True)  # 二氧化碳浓度(%)
    
    # 气候区域
    climatic_zone = Column(String, nullable=True)  # 气候区，如"I", "II", "III", "IVa", "IVb"
    ich_condition = Column(String, nullable=True)  # ICH条件，如"长期", "中间", "加速"
    
    # 包装材料详情
    packaging_type = Column(String, nullable=True)  # 包装类型，如"泡罩", "瓶装"
    packaging_material = Column(String, nullable=True)  # 包装材料，如"PVC", "铝塑"
    packaging_details = Column(JSON, nullable=True)  # 包装详情
    
    # 屏障性能
    moisture_barrier = Column(Float, nullable=True)  # 水汽屏障性能(0-1)
    oxygen_barrier = Column(Float, nullable=True)  # 氧气屏障性能(0-1)
    light_barrier = Column(Float, nullable=True)  # 光屏障性能(0-1)
    
    # 密封系统
    closure_type = Column(String, nullable=True)  # 密封类型
    desiccant = Column(Boolean, nullable=True)  # 是否使用干燥剂
    desiccant_type = Column(String, nullable=True)  # 干燥剂类型
    
    # 测试条件
    test_duration = Column(Integer, nullable=True)  # 测试持续时间(天)
    cycling = Column(Boolean, nullable=True)  # 是否进行循环测试
    cycling_details = Column(JSON, nullable=True)  # 循环测试详情
    
    # 稳定性研究相关
    study_type = Column(String, nullable=True)  # 研究类型，如"长期"、"加速"、"中间条件"、"光照"、"应力"
    time_point = Column(Float, nullable=True)  # 时间点(月)
    storage_orientation = Column(String, nullable=True)  # 储存方向，如"直立"、"倒置"、"侧放"
    
    # 应力测试条件
    stress_condition = Column(String, nullable=True)  # 应力条件类型，如"酸性水解"、"碱性水解"、"氧化"
    stress_level = Column(Float, nullable=True)  # 应力水平，如酸碱浓度
    stress_duration = Column(Integer, nullable=True)  # 应力持续时间(小时)
    
    # 其他信息
    notes = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class PackagingMaterialORM(Base):
    """包装材料表"""
    __tablename__ = 'packaging_materials'
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)  # 名称
    type = Column(String, nullable=True)  # 类型，如"泡罩", "瓶装"
    material = Column(String, nullable=True)  # 材料，如"PVC", "HDPE"
    
    # 屏障性能
    moisture_vapor_transmission_rate = Column(Float, nullable=True)  # 水汽透过率(g/m²/day)
    oxygen_transmission_rate = Column(Float, nullable=True)  # 氧气透过率(cc/m²/day)
    light_transmission = Column(JSON, nullable=True)  # 光透过率(按波长)
    
    # 物理性能
    thickness = Column(Float, nullable=True)  # 厚度(mm)
    tensile_strength = Column(Float, nullable=True)  # 抗张强度(MPa)
    elongation = Column(Float, nullable=True)  # 延伸率(%)
    
    # 化学性能
    chemical_resistance = Column(JSON, nullable=True)  # 化学抵抗性
    extractables_profile = Column(JSON, nullable=True)  # 可提取物谱
    leachables_risk = Column(String, nullable=True)  # 可浸出物风险
    
    # 相容性
    known_incompatibilities = Column(JSON, nullable=True)  # 已知不相容性
    
    # 供应商信息
    supplier = Column(String, nullable=True)  # 供应商
    grade = Column(String, nullable=True)  # 等级
    specifications = Column(JSON, nullable=True)  # 规格
    
    # 监管信息
    regulatory_status = Column(JSON, nullable=True)  # 监管状态
    
    # 其他信息
    notes = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class StabilityConditionORM(Base):
    """稳定性条件表"""
    __tablename__ = 'stability_conditions'
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)  # 名称，如"长期", "加速"
    temperature = Column(Float, nullable=False)  # 温度(°C)
    humidity = Column(Float, nullable=False)  # 湿度(%)
    duration = Column(Integer, nullable=False)  # 持续时间(月)
    
    # ICH分类
    ich_type = Column(String, nullable=True)  # ICH类型
    climatic_zone = Column(String, nullable=True)  # 气候区
    
    # 测试时间点
    time_points = Column(JSON, nullable=True)  # 测试时间点(月)
    
    # 其他信息
    description = Column(Text, nullable=True)  # 描述
    reference = Column(String, nullable=True)  # 参考标准
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class StabilityProtocolORM(Base):
    """稳定性方案表"""
    __tablename__ = 'stability_protocols'
    id = Column(String, primary_key=True, index=True)
    project_id = Column(String, nullable=False)
    name = Column(String, nullable=False)  # 方案名称
    version = Column(String, nullable=True)  # 版本号
    
    # 方案内容
    conditions = Column(JSON, nullable=True)  # 测试条件列表
    time_points = Column(JSON, nullable=True)  # 测试时间点列表
    test_items = Column(JSON, nullable=True)  # 测试项目列表
    
    # 方案状态
    status = Column(String, nullable=True)  # 状态，如"草稿"、"已批准"、"执行中"
    approved_by = Column(String, nullable=True)  # 批准人
    approved_date = Column(DateTime, nullable=True)  # 批准日期
    
    # 其他信息
    description = Column(Text, nullable=True)  # 描述
    notes = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow) 