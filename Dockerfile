# 基础镜像
FROM python:3.10-slim as backend
WORKDIR /app
COPY backend/ ./backend/
RUN pip install --no-cache-dir -r backend/requirements.txt

FROM node:18 as frontend
WORKDIR /frontend
COPY frontend/ ./frontend/
RUN cd frontend && npm ci && npm run build

FROM python:3.10-slim
WORKDIR /app
COPY --from=backend /app/backend /app/backend
COPY --from=frontend /frontend/frontend/build /app/backend/static
EXPOSE 8000
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"] 