import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ExportHistoryList from './ExportHistoryList';

describe('ExportHistoryList', () => {
  it('renders export history list', () => {
    render(<ExportHistoryList projectId={1} />);
    expect(screen.getByText('导出历史')).toBeInTheDocument();
  });

  it('can open preview modal', async () => {
    render(<ExportHistoryList projectId={1} />);
    // 模拟点击预览按钮
    await waitFor(() => {
      const previewBtns = screen.getAllByText('预览');
      expect(previewBtns.length).toBeGreaterThan(0);
      fireEvent.click(previewBtns[0]);
    });
    expect(screen.getByText('内容预览')).toBeInTheDocument();
  });

  it('can start renaming', async () => {
    render(<ExportHistoryList projectId={1} />);
    await waitFor(() => {
      const fileName = screen.getByText('药物稳定性研究报告_2024-06-01.pdf');
      fireEvent.click(fileName);
    });
    expect(screen.getByDisplayValue('药物稳定性研究报告_2024-06-01.pdf')).toBeInTheDocument();
  });

  it('can delete a record', async () => {
    render(<ExportHistoryList projectId={1} />);
    await waitFor(() => {
      const deleteBtns = screen.getAllByText('删除');
      expect(deleteBtns.length).toBeGreaterThan(0);
      fireEvent.click(deleteBtns[0]);
    });
    // 弹出Popconfirm
    await waitFor(() => {
      expect(screen.getByText('确定删除该记录？')).toBeInTheDocument();
      fireEvent.click(screen.getByText('删除'));
    });
  });
});

describe('ExportHistoryList 关键业务流集成测试', () => {
  it('应能正常渲染导出历史', async () => {
    render(<ExportHistoryList projectId={1} />);
    expect(await screen.findByText('导出历史')).toBeInTheDocument();
  });

  it('API异常时应有错误提示', async () => {
    // 可通过mock fetch/axios抛出异常，断言页面有错误提示
  });

  it('无数据时应有无数据提示', async () => {
    // 可通过mock空数据，断言页面有无数据提示
  });
}); 