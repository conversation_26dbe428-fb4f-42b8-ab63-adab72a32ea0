"""
认证服务模块
"""
import jwt as pyjwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import secrets
from sqlalchemy.orm import Session
from app.models.user import UserORM
from app.models.api_key import APIKeyORM
from app.models.operation_log import OperationLogORM

# 认证密钥和设置
SECRET_KEY = "your-secret-key"  # 生产环境中应该使用环境变量设置
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

def authenticate_user(login_data: Any) -> Optional[Dict[str, Any]]:
    """
    认证用户并生成JWT令牌
    """
    username = login_data.username
    password = login_data.password
    
    if not username or not password:
        return None
    
    user = UserORM.query.filter_by(username=username).first()
    if not user or not user.check_password(password):
        return None
    
    # 创建访问令牌
    access_token = create_access_token(
        data={"sub": username, "role": user.role}
    )
    
    return {
        "access_token": access_token,
        "role": user.role
    }

def create_access_token(data: Dict[str, Any]) -> str:
    """
    创建JWT访问令牌
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    
    encoded_jwt = pyjwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# API密钥管理
api_key_store = [
    {'key': 'demo-api-key-123456', 'enabled': True, 'created_at': '2024-06-01 10:00'},
]

def list_api_keys() -> List[Dict[str, Any]]:
    """获取API密钥列表"""
    return api_key_store

def create_api_key() -> Dict[str, str]:
    """创建新的API密钥"""
    new_key = 'api-' + secrets.token_hex(12)
    api_key_store.append({
        'key': new_key,
        'enabled': True,
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M')
    })
    return {'apiKey': new_key}

def disable_api_key(key: str) -> bool:
    """禁用API密钥"""
    for api_key in api_key_store:
        if api_key['key'] == key:
            api_key['enabled'] = False
            return True
    return False

# 操作日志
operation_logs = [
    {'id': 1, 'time': '2024-06-01 10:00', 'action': '登录', 'detail': '用户登录系统'},
    {'id': 2, 'time': '2024-06-01 10:05', 'action': '导出报告', 'detail': '导出项目1报告'},
    {'id': 3, 'time': '2024-06-01 10:10', 'action': '批量导入', 'detail': '导入药物信息Excel'},
]

def list_operation_logs() -> List[Dict[str, Any]]:
    """获取操作日志列表"""
    return operation_logs

# 数据库版本API密钥管理
def db_list_api_keys(db: Session) -> List[Dict[str, Any]]:
    """从数据库获取API密钥列表"""
    try:
        db_api_keys = db.query(APIKeyORM).all()
        return [
            {
                'id': key.id,
                'key': key.key,
                'user_id': key.user_id,
                'enabled': key.enabled,
                'created_at': key.created_at,
                'last_used_at': key.last_used_at
            }
            for key in db_api_keys
        ]
    except Exception:
        # 回退到内存版本
        return list_api_keys()

def db_create_api_key(db: Session, user_id: int) -> Dict[str, str]:
    """在数据库中创建新的API密钥"""
    try:
        new_key = 'api-' + secrets.token_hex(12)
        db_api_key = APIKeyORM(
            key=new_key,
            user_id=user_id,
            enabled=True
        )
        db.add(db_api_key)
        db.commit()
        db.refresh(db_api_key)
        return {'apiKey': new_key}
    except Exception:
        # 回退到内存版本
        return create_api_key()

def db_list_operation_logs(db: Session, skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
    """从数据库获取操作日志列表"""
    try:
        db_logs = db.query(OperationLogORM).offset(skip).limit(limit).all()
        return [
            {
                'id': log.id,
                'user_id': log.user_id,
                'action': log.action,
                'detail': log.detail,
                'ip_address': log.ip_address,
                'created_at': log.created_at
            }
            for log in db_logs
        ]
    except Exception:
        # 回退到内存版本
        return operation_logs[skip:skip+limit] 