import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Row, 
  Col, 
  Typography, 
  Alert, 
  Spin, 
  Tabs, 
  Select, 
  Space, 
  Tag, 
  Descriptions, 
  Timeline, 
  List, 
  Progress, 
  Statistic, 
  Divider,
  Result,
  Modal,
  message,
  Badge,
  Collapse,
  Table,
  Upload,
  InputNumber,
  Switch,
  TreeSelect
} from 'antd';
import { 
  ExperimentOutlined, 
  FileTextOutlined, 
  BulbOutlined, 
  AlertOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CloudUploadOutlined,
  DownloadOutlined,
  SyncOutlined,
  ApiOutlined,
  SafetyCertificateOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { ProjectContext } from '../App';
import { analyzeFormulation } from '../api/formulation';
import axios from 'axios';
import ReportGenerator from '../components/Reports/ReportGenerator';
import ProjectSelector from '../components/ProjectSelector';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TreeNode } = TreeSelect;

// 定义类型接口
interface ProjectCompleteData {
  project: {
    id: string;
    name: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  drug?: {
    name: string;
    smiles?: string;
    formula?: string;
    molecular_weight?: number;
    properties?: any;
  };
  excipients?: Array<{
    id: string;
    name: string;
    amount?: number;
    unit?: string;
    category?: string;
    properties?: any;
  }>;
  formulation?: {
    dosage_form?: string;
    route?: string;
    strength?: string;
    excipients?: any[];
    manufacturing_process?: string;
  };
  stability?: {
    conditions?: any[];
    predictions?: any[];
    test_results?: any[];
  };
  packaging?: {
    primary?: string;
    secondary?: string;
    materials?: string[];
  };
  compatibility_results?: any;
}

interface FormulationRecommendation {
  category: string;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  evidence: string[];
  impact: string;
  implementation: string[];
}

interface FormulationScore {
  overall: number;
  stability: number;
  manufacturability: number;
  patient_compliance: number;
  cost_effectiveness: number;
}

const FormulationAnalysis: React.FC = () => {
  const [form] = Form.useForm();
  const { currentProject } = useContext(ProjectContext);
  const [loading, setLoading] = useState(false);
  const [projectData, setProjectData] = useState<ProjectCompleteData | null>(null);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('input');
  const [autoLoadData, setAutoLoadData] = useState(true);
  const [dataLoading, setDataLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedDataSources, setSelectedDataSources] = useState<string[]>(['all']);

  // 自动加载项目数据
  useEffect(() => {
    if (currentProject && autoLoadData) {
      loadProjectCompleteData();
    }
  }, [currentProject, autoLoadData]);

  // 加载完整的项目数据
  const loadProjectCompleteData = async () => {
    if (!currentProject) {
      message.warning('请先选择或创建一个项目');
      return;
    }

    setDataLoading(true);
    try {
      // 获取完整的项目数据
      const response = await axios.get(`/api/projects/${currentProject.id}/complete-data`);
      const data = response.data as any;
      if (data.success && data.data) {
        setProjectData(data.data as ProjectCompleteData);
        
        // 自动填充表单
        populateFormWithProjectData(data.data as ProjectCompleteData);
        
        message.success('项目数据已自动加载');
      } else {
        message.warning('项目数据不完整，请手动输入缺失信息');
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      message.error('加载项目数据失败，请手动输入');
      setAutoLoadData(false);
    } finally {
      setDataLoading(false);
    }
  };

  // 使用项目数据填充表单
  const populateFormWithProjectData = (data: ProjectCompleteData) => {
    const formData: any = {
      drug_name: data.drug?.name || '',
      drug_structure: data.drug?.smiles || '',
      molecular_weight: data.drug?.molecular_weight || undefined,
      dosage_form: data.formulation?.dosage_form || 'tablet',
      route: data.formulation?.route || 'oral',
      target_strength: data.formulation?.strength || '',
      manufacturing_process: data.formulation?.manufacturing_process || 'direct_compression'
    };

    // 处理辅料数据
    if (data.excipients && data.excipients.length > 0) {
      formData.excipients = data.excipients.map(exc => ({
        name: exc.name,
        amount: exc.amount,
        function: exc.category || '填充剂'
      }));
    }

    // 处理包装材料
    if (data.packaging) {
      formData.primary_packaging = data.packaging.primary || '';
      formData.secondary_packaging = data.packaging.secondary || '';
    }

    // 处理稳定性数据
    if (data.stability && data.stability.conditions) {
      formData.storage_conditions = data.stability.conditions[0] || {};
    }

    form.setFieldsValue(formData);
  };

  // 执行配方分析
  const handleAnalyze = async (values: any) => {
    if (!currentProject) {
      message.error('请先选择或创建一个项目');
      return;
    }

    setLoading(true);
    try {
      // 构建分析请求，严格按照FormulationAnalysisRequest类型
      const request = {
        drug_name: values.drug_name,
        drug_structure: values.drug_structure,
        excipients: (values.excipients || []).map((exc: any) => ({
          name: exc.name,
          structure: exc.structure,
          concentration: exc.concentration,
          ph: exc.ph
        })),
        temperature: values.temperature ?? 25,
        humidity: values.humidity ?? 60,
        ph: values.ph,
        packaging: values.packaging,
        history_data: values.history_data,
        prediction_timepoints: values.prediction_timepoints || [0, 3, 6, 12, 24],
        model_selection: values.model_selection || 'auto',
        confidence_level: values.confidence_level || 0.95
      };

      console.log('发送配方分析请求:', request);
      const result = await analyzeFormulation(request);
      
      if (result) {
      setAnalysisResult(result);
        setActiveTab('results');
        message.success('配方分析完成');
      }
    } catch (error: any) {
      console.error('配方分析失败:', error);
      const errorMsg = error.response?.data?.detail || error.message || '配方分析失败';
      message.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // 渲染相容性分析汇总
  const renderCompatibilityAnalysisSummary = () => {
    // 模拟相容性分析数据
    const compatibilityData = {
      api_excipient_interactions: [
        { api: projectData?.drug?.name || '药物', excipient: '微晶纤维素', risk: 'Low risk', type: 'API-辅料' },
        { api: projectData?.drug?.name || '药物', excipient: '硬脂酸镁', risk: 'Medium risk', type: 'API-辅料' },
        { api: projectData?.drug?.name || '药物', excipient: '交联聚维酮', risk: 'Low risk', type: 'API-辅料' }
      ],
      excipient_excipient_interactions: [
        { excipient1: '微晶纤维素', excipient2: '硬脂酸镁', risk: 'Low risk', type: '辅料-辅料' },
        { excipient1: '交联聚维酮', excipient2: '硬脂酸镁', risk: 'Medium risk', type: '辅料-辅料' }
      ],
      packaging_interactions: [
        { component: projectData?.drug?.name || '药物', packaging: 'PVC/PVDC泡罩', risk: 'Low risk', type: '原辅料-包材' },
        { component: '硬脂酸镁', packaging: 'PVC/PVDC泡罩', risk: 'Low risk', type: '原辅料-包材' }
      ]
    };

    const allInteractions = [
      ...compatibilityData.api_excipient_interactions,
      ...compatibilityData.excipient_excipient_interactions,
      ...compatibilityData.packaging_interactions
    ];

    const riskCounts = {
      'High risk': allInteractions.filter(i => i.risk === 'High risk').length,
      'Medium risk': allInteractions.filter(i => i.risk === 'Medium risk').length,
      'Low risk': allInteractions.filter(i => i.risk === 'Low risk').length
    };

    const columns = [
      {
        title: '相互作用类型',
        dataIndex: 'type',
        key: 'type',
        render: (type: string) => <Tag color="blue">{type}</Tag>
      },
      {
        title: '组分1',
        dataIndex: 'component1',
        key: 'component1',
        render: (_: any, record: any) => {
          if (record.api) return record.api;
          if (record.excipient1) return record.excipient1;
          return record.component;
        }
      },
      {
        title: '组分2',
        dataIndex: 'component2',
        key: 'component2',
        render: (_: any, record: any) => {
          if (record.excipient) return record.excipient;
          if (record.excipient2) return record.excipient2;
          return record.packaging;
        }
      },
      {
        title: '风险等级',
        dataIndex: 'risk',
        key: 'risk',
        render: (risk: string) => {
          const color = risk === 'High risk' ? 'red' : risk === 'Medium risk' ? 'orange' : 'green';
          return <Tag color={color}>{risk}</Tag>;
        }
      },
      {
        title: '操作',
        key: 'action',
        render: () => (
          <Button type="link" size="small">查看详情</Button>
        )
      }
    ];

    return (
      <div>
        {/* 风险统计 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic
              title="总相互作用数"
              value={allInteractions.length}
              prefix={<ApiOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="高风险"
              value={riskCounts['High risk']}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<AlertOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="中风险"
              value={riskCounts['Medium risk']}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="低风险"
              value={riskCounts['Low risk']}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
        </Row>

        {/* 相互作用详情表 */}
        <Table
          columns={columns}
          dataSource={allInteractions}
          pagination={false}
          size="small"
          rowKey={(record, index) => `${record.type}-${index}`}
        />
      </div>
    );
  };

  // 渲染详细相容性分析
  const renderDetailedCompatibilityAnalysis = () => {
    return (
      <Tabs defaultActiveKey="api-excipient">
        <TabPane tab="API-辅料相互作用" key="api-excipient">
          <Alert
            message="API与辅料相容性分析"
            description="分析主药与各辅料之间的化学相容性，识别潜在的相互作用风险。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <List
            dataSource={[
              { name: '微晶纤维素', risk: 'Low risk', mechanism: '无明显相互作用' },
              { name: '硬脂酸镁', risk: 'Medium risk', mechanism: '可能存在金属离子络合' },
              { name: '交联聚维酮', risk: 'Low risk', mechanism: '物理吸附，无化学反应' }
            ]}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  title={
                    <Space>
                      <Text>{projectData?.drug?.name || '药物'} + {item.name}</Text>
                      <Tag color={item.risk === 'High risk' ? 'red' : item.risk === 'Medium risk' ? 'orange' : 'green'}>
                        {item.risk}
                      </Tag>
                    </Space>
                  }
                  description={item.mechanism}
                />
              </List.Item>
            )}
          />
        </TabPane>

        <TabPane tab="辅料-辅料相互作用" key="excipient-excipient">
          <Alert
            message="辅料间相容性分析"
            description="分析不同辅料之间的相互作用，确保配方稳定性。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <List
            dataSource={[
              { pair: '微晶纤维素 + 硬脂酸镁', risk: 'Low risk', mechanism: '常用组合，相容性良好' },
              { pair: '交联聚维酮 + 硬脂酸镁', risk: 'Medium risk', mechanism: '可能影响崩解性能' }
            ]}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  title={
                    <Space>
                      <Text>{item.pair}</Text>
                      <Tag color={item.risk === 'High risk' ? 'red' : item.risk === 'Medium risk' ? 'orange' : 'green'}>
                        {item.risk}
                      </Tag>
                    </Space>
                  }
                  description={item.mechanism}
                />
              </List.Item>
            )}
          />
        </TabPane>

        <TabPane tab="原辅料-包材相互作用" key="packaging-interaction">
          <Alert
            message="包装材料相容性分析"
            description="评估原辅料与包装材料的相容性，确保产品在储存期间的稳定性。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <List
            dataSource={[
              { component: projectData?.drug?.name || '药物', packaging: 'PVC/PVDC泡罩', risk: 'Low risk', mechanism: '阻隔性能良好，适合药物储存' },
              { component: '硬脂酸镁', packaging: 'PVC/PVDC泡罩', risk: 'Low risk', mechanism: '无明显相互作用' }
            ]}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  title={
                    <Space>
                      <Text>{item.component} + {item.packaging}</Text>
                      <Tag color={item.risk === 'High risk' ? 'red' : item.risk === 'Medium risk' ? 'orange' : 'green'}>
                        {item.risk}
                      </Tag>
                    </Space>
                  }
                  description={item.mechanism}
                />
              </List.Item>
            )}
          />
        </TabPane>
      </Tabs>
    );
  };

  // 渲染分析结果
  const renderAnalysisResults = () => {
    if (!analysisResult) return null;

    return (
      <div>
        {/* 总体评分 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={4}>
              <Statistic
                title="综合评分"
                value={analysisResult.summary?.formulation_score?.overall || 85}
                suffix="/ 100"
                prefix={<SafetyCertificateOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="稳定性评分"
                value={analysisResult.summary?.formulation_score?.stability || 88}
                suffix="/ 100"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="可制造性"
                value={analysisResult.summary?.formulation_score?.manufacturability || 82}
                suffix="/ 100"
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="患者依从性"
                value={analysisResult.summary?.formulation_score?.patient_compliance || 90}
                suffix="/ 100"
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="关键问题"
                value={analysisResult.summary?.critical_issues || 2}
                prefix={<AlertOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="优化建议"
                value={analysisResult.summary?.total_recommendations || 5}
                prefix={<BulbOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* 关键建议 */}
        <Card title="配方优化建议" extra={<RocketOutlined />} style={{ marginBottom: 16 }}>
          <Timeline>
            {analysisResult.recommendations?.map((rec: FormulationRecommendation, index: number) => (
              <Timeline.Item
                key={index}
                color={
                  rec.priority === 'high' ? 'red' : 
                  rec.priority === 'medium' ? 'orange' : 'green'
                }
                dot={
                  rec.priority === 'high' ? <AlertOutlined /> : 
                  rec.priority === 'medium' ? <WarningOutlined /> : 
                  <CheckCircleOutlined />
                }
              >
                <Card size="small" style={{ marginBottom: 8 }}>
                  <Row>
                    <Col span={20}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Space>
                          <Text strong>{rec.title}</Text>
                          <Tag color={
                            rec.priority === 'high' ? 'error' : 
                            rec.priority === 'medium' ? 'warning' : 'success'
                          }>
                            {rec.priority === 'high' ? '高优先级' : 
                             rec.priority === 'medium' ? '中优先级' : '低优先级'}
                          </Tag>
                          <Tag>{rec.category}</Tag>
                        </Space>
                        <Paragraph>{rec.description}</Paragraph>
                        <Text type="secondary">预期影响: {rec.impact}</Text>
                        <Collapse ghost>
                          <Panel header="实施方案" key="1">
                            <List
                              size="small"
                              dataSource={rec.implementation}
                              renderItem={item => <List.Item>{item}</List.Item>}
                            />
                          </Panel>
                          <Panel header="科学依据" key="2">
                            <List
                              size="small"
                              dataSource={rec.evidence}
                              renderItem={item => (
                                <List.Item>
                                  <Text type="secondary">{item}</Text>
                                </List.Item>
                              )}
                            />
                          </Panel>
                        </Collapse>
                      </Space>
                    </Col>
                    <Col span={4} style={{ textAlign: 'right' }}>
                      <Button type="primary" ghost size="small">
                        采纳建议
                      </Button>
                    </Col>
                  </Row>
                </Card>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>

        {/* 风险评估 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Card title="风险评估" extra={<ExperimentOutlined />}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="整体风险">
                  <Tag color={
                    analysisResult.risk_assessment?.overall_risk === 'high' ? 'error' :
                    analysisResult.risk_assessment?.overall_risk === 'medium' ? 'warning' : 'success'
                  }>
                    {analysisResult.risk_assessment?.overall_risk === 'high' ? '高' :
                     analysisResult.risk_assessment?.overall_risk === 'medium' ? '中' : '低'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="稳定性风险">
                  <Progress 
                    percent={
                      analysisResult.risk_assessment?.stability_risk === 'high' ? 80 :
                      analysisResult.risk_assessment?.stability_risk === 'medium' ? 50 : 20
                    }
                    strokeColor={
                      analysisResult.risk_assessment?.stability_risk === 'high' ? '#ff4d4f' :
                      analysisResult.risk_assessment?.stability_risk === 'medium' ? '#faad14' : '#52c41a'
                    }
                    size="small"
                  />
                </Descriptions.Item>
                <Descriptions.Item label="生产风险">
                  <Progress 
                    percent={
                      analysisResult.risk_assessment?.manufacturing_risk === 'high' ? 80 :
                      analysisResult.risk_assessment?.manufacturing_risk === 'medium' ? 50 : 20
                    }
                    strokeColor={
                      analysisResult.risk_assessment?.manufacturing_risk === 'high' ? '#ff4d4f' :
                      analysisResult.risk_assessment?.manufacturing_risk === 'medium' ? '#faad14' : '#52c41a'
                    }
                    size="small"
                  />
                </Descriptions.Item>
                <Descriptions.Item label="法规风险">
                  <Progress 
                    percent={
                      analysisResult.risk_assessment?.regulatory_risk === 'high' ? 80 :
                      analysisResult.risk_assessment?.regulatory_risk === 'medium' ? 50 : 20
                    }
                    strokeColor={
                      analysisResult.risk_assessment?.regulatory_risk === 'high' ? '#ff4d4f' :
                      analysisResult.risk_assessment?.regulatory_risk === 'medium' ? '#faad14' : '#52c41a'
                    }
                    size="small"
                  />
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          
          <Col span={12}>
            <Card title="预测货架期" extra={<InfoCircleOutlined />}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="加速试验">
                  <Text strong>{analysisResult.predicted_shelf_life?.accelerated}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="长期试验">
                  <Text strong style={{ color: '#52c41a' }}>
                    {analysisResult.predicted_shelf_life?.long_term}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="置信度">
                  <Badge 
                    count={analysisResult.predicted_shelf_life?.confidence} 
                    style={{ backgroundColor: '#52c41a' }}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="建议复测期">
                  <Text>12个月</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {/* 相容性分析汇总 */}
        <Card title="原辅料相容性分析汇总" style={{ marginBottom: 16 }}>
          {renderCompatibilityAnalysisSummary()}
        </Card>

        {/* 优化建议详情 */}
        <Card title="具体优化方案" style={{ marginBottom: 16 }}>
          <Tabs defaultActiveKey="formulation">
            <TabPane tab="配方优化" key="formulation">
              <List
                dataSource={analysisResult.optimization_suggestions?.formulation}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      title={typeof item === 'string' ? item : JSON.stringify(item)}
                    />
                  </List.Item>
                )}
              />
            </TabPane>
            <TabPane tab="工艺优化" key="process">
              <List
                dataSource={analysisResult.optimization_suggestions?.process}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
                      title={typeof item === 'string' ? item : JSON.stringify(item)}
                    />
                  </List.Item>
                )}
              />
            </TabPane>
            <TabPane tab="包装优化" key="packaging">
              <List
                dataSource={analysisResult.optimization_suggestions?.packaging}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<CheckCircleOutlined style={{ color: '#722ed1' }} />}
                      title={typeof item === 'string' ? item : JSON.stringify(item)}
                    />
                  </List.Item>
                )}
              />
            </TabPane>
            <TabPane tab="相容性分析" key="compatibility">
              {renderDetailedCompatibilityAnalysis()}
            </TabPane>
          </Tabs>
        </Card>

        {/* 成本分析 */}
        {analysisResult.cost_analysis && (
          <Card title="成本分析" extra={<Text type="secondary">仅供参考</Text>}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="原料药成本"
                  value={analysisResult.cost_analysis.api_cost}
                  prefix="$"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="辅料成本"
                  value={analysisResult.cost_analysis.excipient_cost}
                  prefix="$"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="单位成本"
                  value={analysisResult.cost_analysis.total_cost_per_unit}
                  prefix="$"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="成本优化潜力"
                  value={analysisResult.cost_analysis.cost_reduction_potential}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
          </Card>
        )}
      </div>
    );
  };

        return (
    <div>
      {/* 项目选择器 */}
      <div style={{ marginBottom: '16px' }}>
        <ProjectSelector showCreateButton={true} />
      </div>

      <Title level={2}>
        <ExperimentOutlined /> 配方综合分析
      </Title>
      
      <Alert
        message="智能分析说明"
        description={
          <Space direction="vertical">
            <Text>本功能会自动整合项目中的所有数据，包括：</Text>
            <ul style={{ marginBottom: 0 }}>
              <li>药物基本信息和理化性质</li>
              <li>辅料相容性分析结果</li>
              <li>稳定性预测数据</li>
              <li>包装材料信息</li>
            </ul>
            <Text>基于这些数据，AI将提供专业的配方优化建议</Text>
          </Space>
        }
        type="info"
        showIcon
        icon={<ApiOutlined />}
        style={{ marginBottom: 16 }}
      />
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="数据输入" key="input">
          <Card 
            title="数据源选择" 
            style={{ marginBottom: 16 }}
            extra={
              <Space>
                <Switch
                  checked={autoLoadData}
                  onChange={setAutoLoadData}
                  checkedChildren="自动加载"
                  unCheckedChildren="手动输入"
                />
                {currentProject && (
                  <Button
                    icon={<SyncOutlined spin={dataLoading} />}
                    onClick={loadProjectCompleteData}
                    loading={dataLoading}
                  >
                    刷新数据
                  </Button>
                )}
              </Space>
            }
          >
            {projectData ? (
              <Descriptions size="small" column={2}>
                <Descriptions.Item label="项目名称">
                  {projectData.project.name}
                </Descriptions.Item>
                <Descriptions.Item label="药物">
                  {projectData.drug?.name || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="辅料数量">
                  {projectData.excipients?.length || 0}
                </Descriptions.Item>
                <Descriptions.Item label="剂型">
                  {projectData.formulation?.dosage_form || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="稳定性数据">
                  {projectData.stability ? '已有' : '无'}
                </Descriptions.Item>
                <Descriptions.Item label="相容性分析">
                  {projectData.compatibility_results ? '已完成' : '未完成'}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <Result
                status="info"
                title="暂无项目数据"
                subTitle="请先在其他模块中输入数据，或手动填写下方表单"
              />
            )}
          </Card>
          
          <Form
            form={form}
            layout="vertical"
            onFinish={handleAnalyze}
            initialValues={{
              dosage_form: 'tablet',
              route: 'oral',
              manufacturing_process: 'direct_compression',
              include_cost_analysis: true,
              include_regulatory_assessment: true,
              target_market: 'global'
            }}
          >
            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
            <Form.Item
              name="drug_name"
              label="药物名称"
              rules={[{ required: true, message: '请输入药物名称' }]}
            >
                    <Input placeholder="例如：阿司匹林" />
            </Form.Item>
                </Col>
                <Col span={12}>
            <Form.Item
              name="drug_structure"
                    label="药物结构 (SMILES)"
            >
                    <Input placeholder="例如：CC(=O)Oc1ccccc1C(=O)O" />
                          </Form.Item>
                        </Col>
                    </Row>
              
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                    name="dosage_form"
                    label="剂型"
                    rules={[{ required: true }]}
                  >
                    <Select>
                      <Option value="tablet">片剂</Option>
                      <Option value="capsule">胶囊</Option>
                      <Option value="injection">注射剂</Option>
                      <Option value="suspension">混悬剂</Option>
                      <Option value="solution">溶液剂</Option>
                      <Option value="cream">乳膏剂</Option>
                      <Option value="ointment">软膏剂</Option>
                    </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                    name="route"
                    label="给药途径"
                    rules={[{ required: true }]}
                  >
                    <Select>
                      <Option value="oral">口服</Option>
                      <Option value="injection">注射</Option>
                      <Option value="topical">外用</Option>
                      <Option value="inhalation">吸入</Option>
                      <Option value="transdermal">透皮</Option>
                    </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                    name="target_strength"
                    label="目标规格"
                >
                    <Input placeholder="例如：100mg" />
                </Form.Item>
              </Col>
            </Row>
            </Card>
            
            <Card title="制造工艺" style={{ marginBottom: 16 }}>
            <Form.Item
                name="manufacturing_process"
                label="生产工艺"
            >
                <Select>
                  <Option value="direct_compression">直接压片</Option>
                  <Option value="wet_granulation">湿法制粒</Option>
                  <Option value="dry_granulation">干法制粒</Option>
                  <Option value="fluid_bed">流化床制粒</Option>
                  <Option value="hot_melt">热熔挤出</Option>
                  <Option value="spray_drying">喷雾干燥</Option>
              </Select>
            </Form.Item>
          </Card>
            
            <Card title="分析选项" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                    name="include_cost_analysis"
                    valuePropName="checked"
                >
                    <Switch checkedChildren="包含成本分析" unCheckedChildren="不含成本分析" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                    name="include_regulatory_assessment"
                    valuePropName="checked"
                >
                    <Switch checkedChildren="包含法规评估" unCheckedChildren="不含法规评估" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                    name="target_market"
                    label="目标市场"
                  >
                    <Select>
                      <Option value="global">全球</Option>
                      <Option value="us">美国</Option>
                      <Option value="eu">欧盟</Option>
                      <Option value="china">中国</Option>
                      <Option value="japan">日本</Option>
                    </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
            
            <Form.Item>
                      <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<ExperimentOutlined />}
                  size="large"
                >
                  开始综合分析
                </Button>
                <Button size="large" onClick={() => form.resetFields()}>
                  重置
                </Button>
                      </Space>
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab="分析结果" key="results" disabled={!analysisResult}>
          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" tip="正在进行综合分析，请稍候..." />
                  </div>
          ) : (
            renderAnalysisResults()
              )}
          </TabPane>
          
        <TabPane tab="导出报告" key="export" disabled={!analysisResult}>
          <Card>
            <Title level={4}>生成专业分析报告</Title>
            <Paragraph type="secondary">
              基于配方分析结果生成专业的PDF报告，包含执行摘要、详细分析、图表和建议。
            </Paragraph>
            
            {/* 集成报告生成组件 */}
            <ReportGenerator 
              analysisData={{
                drug_name: form.getFieldValue('drug_name'),
                drug_smiles: form.getFieldValue('drug_structure'),
                analysis_type: 'formulation',
                ...analysisResult,
                // 转换格式以适配报告生成器
                prediction: analysisResult?.predicted_shelf_life ? {
                  long_term: {
                    t90: parseInt(analysisResult.predicted_shelf_life.long_term?.match(/\d+/)?.[0] || '24'),
                    t95: parseInt(analysisResult.predicted_shelf_life.long_term?.match(/\d+/)?.[0] || '24') * 0.8,
                    ci: [20, 28]
                  },
                  risk_factors: analysisResult.recommendations?.filter((r: any) => r.priority === 'high').map((r: any) => r.title) || []
                } : undefined,
                compatibility_assessment: {
                  overall_risk: analysisResult?.risk_assessment?.overall_risk || 'medium',
                  compatibility_results: []
                },
                structure_analysis: {
                  properties: {
                    molecular_weight: form.getFieldValue('molecular_weight'),
                    dosage_form: form.getFieldValue('dosage_form')
                  }
                },
                recommendations: analysisResult?.recommendations?.map((r: any) => ({
                  type: r.priority === 'high' ? 'critical' : r.priority === 'medium' ? 'warning' : 'info',
                  category: r.category,
                  content: r.title + ': ' + r.description
                })) || []
              }}
              onReportGenerated={(report) => {
                message.success('报告生成成功！');
              }}
            />
            
            <Divider />
            
            {/* 保留原有的导出选项作为备选 */}
            <Collapse ghost>
              <Panel header="其他导出选项" key="1">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Alert
                    message="传统导出功能"
                    description="如需使用传统的Excel或Word导出功能，请使用以下选项"
                    type="info"
                  />
                  <Space>
                    <Button icon={<DownloadOutlined />}>导出Excel数据</Button>
                    <Button icon={<DownloadOutlined />}>导出Word文档</Button>
                  </Space>
                </Space>
              </Panel>
            </Collapse>
          </Card>
        </TabPane>
        </Tabs>
    </div>
  );
};

export default FormulationAnalysis; 