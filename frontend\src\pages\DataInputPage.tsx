import React, { useState, useEffect, useContext, useRef } from 'react';
import { message, Tabs, Card, Input, Button, Upload, Form, Table, AutoComplete, Select, Modal, Tooltip, Drawer, Row, Col } from 'antd';
import { apiFetch, postFeedback } from '../api';
import BatchImportModal from '../components/BatchImportModal';
import { useTranslation } from 'react-i18next';
import ExportHistoryList from '../components/ExportHistoryList';
import { ProjectContext } from '../App';
import { useNavigate } from 'react-router-dom';
import { MoleculeViewer } from '../components/MoleculeViewer/index';

const tabs = [
  { key: 'drug', label: '药物信息' },
  { key: 'excipient', label: '辅料信息' },
  { key: 'env', label: '环境参数' },
  { key: 'stability', label: '稳定性数据' },
];

const guidanceText =
  '请至少填写名称、分子式、CAS号或Drug SMILES中的一项，点击"联网识别"按钮后系统将自动补全其余信息并显示结构图。';

interface IdentifyResponse {
  name: string;
  formula: string;
  cas: string;
  smiles: string;
  structure_image_url: string;
}

interface DrugFields {
  name: string;
  formula: string;
  cas: string;
  smiles: string;
  structure: string;
}

interface IdentifySourceResult {
  source: string;
  confidence: number;
  detail: IdentifyResponse;
  used_en_name?: string;
}

interface ExternalSearchResponse {
  name: string;
  cas: string;
  formula: string;
  smiles: string;
  structure_image_url: string;
}

const DataInputPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [tab, setTab] = useState('drug');
  const { currentProject, setInputData, setAnalysisResult } = useContext(ProjectContext);
  const navigate = useNavigate();
  // 药物信息
  const [drugFields, setDrugFields] = useState<DrugFields>({ name: '', formula: '', cas: '', smiles: '', structure: '' });
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [feedback, setFeedback] = useState('');
  const [result, setResult] = useState<IdentifyResponse | null>(null);
  // 其他Tab数据
  const [excipientData, setExcipientData] = useState('');
  const [envData, setEnvData] = useState('');
  const [stabilityData, setStabilityData] = useState('');
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [excipientImportOpen, setExcipientImportOpen] = useState(false);
  const [envImportOpen, setEnvImportOpen] = useState(false);
  const [stabilityImportOpen, setStabilityImportOpen] = useState(false);
  // 字段动态配置
  const excipientFields = ['name', 'type', 'amount'];
  const envFields = ['temperature', 'humidity', 'light'];
  const stabilityFields = ['time', 'value', 'unit'];
  const [importHistory, setImportHistory] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [identifyResults, setIdentifyResults] = useState<IdentifySourceResult[]>([]);
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [feedbackTarget, setFeedbackTarget] = useState<any>(null);
  const feedbackInputRef = useRef<any>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [historyDrawerOpen, setHistoryDrawerOpen] = useState(false);
  const [historyList, setHistoryList] = useState<string[]>(() => JSON.parse(localStorage.getItem('drug_history') || '[]'));
  const [favoriteList, setFavoriteList] = useState<string[]>(() => JSON.parse(localStorage.getItem('drug_favorites') || '[]'));

  const handleDrugChange = async (field: keyof DrugFields, value: string) => {
    setDrugFields(prev => ({ ...prev, [field]: value }));
    if (['name', 'formula', 'cas', 'smiles'].includes(field) && value) {
      const data = await apiFetch<IdentifyResponse>('/api/identify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...drugFields, [field]: value }),
      });
      if (data) {
        setDrugFields({ name: data.name, formula: data.formula, cas: data.cas, smiles: data.smiles, structure: data.structure_image_url });
      }
    }
  };

  const handleIdentify = async () => {
    if (!drugFields.name && !drugFields.formula && !drugFields.cas && !drugFields.smiles) {
      setFeedback(t('请至少填写一项信息'));
      setStatus('error');
      message.warning(t('请至少填写一项信息'));
      return;
    }
    setStatus('loading');
    setFeedback(t('正在识别，请稍候…'));
    setResult(null);
    try {
      const data = await apiFetch<IdentifyResponse>('/api/identify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(drugFields),
      });
      if (data) {
        setResult(data);
        setStatus('success');
        setFeedback(t('识别成功，已补全信息并显示结构图'));
        message.success(t('识别成功'));
      } else {
        setStatus('error');
        setFeedback(t('未查到该药品信息，请检查名称或尝试其他字段'));
      }
    } catch (err: any) {
      setStatus('error');
      setFeedback(err?.detail || err?.message || t('识别失败，请检查输入或稍后重试'));
    }
  };

  const handleIdentifyV2 = async () => {
    if (!drugFields.name && !drugFields.formula && !drugFields.cas && !drugFields.smiles) {
      setFeedback(t('请至少填写一项信息'));
      setStatus('error');
      message.warning(t('请至少填写一项信息'));
      return;
    }
    setStatus('loading');
    setFeedback(t('正在识别，请稍候…'));
    setResult(null);
    setIdentifyResults([]);
    try {
      const data = await apiFetch<ExternalSearchResponse>(`/api/drugs/search-external/?name=${encodeURIComponent(drugFields.name)}`);
      
      if (data) {
        setDrugFields({
          name: data.name,
          formula: data.formula,
          cas: data.cas,
          smiles: data.smiles,
          structure: data.structure_image_url
        });
        setStatus('success');
        setFeedback(t('联网识别成功，信息已自动补全'));
        message.success(t('联网识别成功'));
      } else {
        setStatus('error');
        setFeedback(t('未在外部数据库中找到该药品信息，请检查名称或尝试手动填写'));
        message.error(t('未找到药品信息'));
      }
    } catch (err: any) {
      setStatus('error');
      setFeedback(err?.detail || err?.message || t('识别失败，请检查网络或稍后重试'));
      message.error(err?.detail || t('识别失败'));
    }
  };

  // 批量导入模拟
  const handleBatchImport = (tab: string) => {
    alert(`批量导入${tabs.find(t => t.key === tab)?.label}功能开发中...`);
  };

  // useEffect中fetch导入历史和错误，支持API
  useEffect(() => {
    apiFetch<any[]>('/api/import/history').then(res => setImportHistory(res || []));
    apiFetch<any[]>('/api/import/errors').then(res => setImportErrors(res || []));
  }, []);

  const handleSaveData = async () => {
    if (!currentProject?.id) {
      message.error('请先选择项目');
      return;
    }

    try {
      // 准备保存的数据 - 使用简单的格式
      const saveData = {
        drug_name: drugFields.name,
        cas_number: drugFields.cas,
        molecular_formula: drugFields.formula,
        smiles: drugFields.smiles,
        category: '药物',
        description: '通过数据输入页面保存',
        formulation: excipientData ? JSON.parse(excipientData || '[]') : [],
        packaging_storage: envData ? JSON.parse(envData || '{}') : {},
        production_process: '待定',
        notes: stabilityData || '无'
      };

      console.log('💾 准备保存的数据:', saveData);

      const response = await fetch(`/api/projects/${currentProject.id}/save-data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(saveData)
      });

      if (response.ok) {
        const result = await response.json();
        message.success('数据保存成功');
        console.log('保存结果:', result);
      } else {
        const errorData = await response.json();
        message.error(`保存失败: ${errorData.detail || '未知错误'}`);
      }
    } catch (error) {
      console.error('保存数据时出错:', error);
      message.error('保存失败，请检查网络连接');
    }
  };

  const handleSubmit = async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }

    // 先保存数据
    await handleSaveData();

    setInputData?.(drugFields);
    try {
      const res = await fetch('/api/analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...drugFields, projectId: currentProject?.id }),
      });
      if (res.ok) {
        const result = await res.json();
        setAnalysisResult?.(result);
      } else {
        setAnalysisResult?.(result);
        setAnalysisResult?.({ error: '后端分析失败' });
      }
    } catch {
      setAnalysisResult?.(result);
      setAnalysisResult?.({ error: '后端不可用，已降级为本地模式' });
    }
    navigate('/stability-prediction');
  };

  const handleFeedbackSubmit = async () => {
    if (!feedbackContent) return;
    setStatus('loading');
    await postFeedback(feedbackContent, feedbackTarget);
    setStatus('idle');
    setFeedbackModalOpen(false);
    setFeedbackContent('');
    setFeedbackTarget(null);
    message.success('感谢您的反馈，我们会持续优化！');
  };

  const handleClear = () => {
    setDrugFields({ name: '', formula: '', cas: '', smiles: '', structure: '' });
    setIdentifyResults([]);
    setResult(null);
    setFeedback('');
    setStatus('idle');
  };

  const handleDrugNameSearch = async (value: string) => {
    // 本地模糊+远程suggest
    let local = historyList.filter(h => h.includes(value));
    let remote: string[] = [];
    if (value) {
      const res = await apiFetch<string[]>('/api/identify/suggest?query=' + encodeURIComponent(value));
      if (res) remote = res;
    }
    setSuggestions(Array.from(new Set([...local, ...remote])));
  };

  const handleDrugNameSelect = (value: string) => {
    setDrugFields(prev => ({ ...prev, name: value }));
  };

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Card>
        <Tabs activeKey={tab} onChange={setTab} items={[
          { key: 'drug', label: '药物信息', children: (
            <div>
              <Row gutter={16}>
                <Col span={12}>
                  <Form form={form} layout="vertical">
                    <Form.Item label={<span>药物名称 <Tooltip title="支持中英文、别名、商品名、模糊输入，自动补全"><i style={{color:'#1976d2'}}>?</i></Tooltip></span>}>
                      <AutoComplete
                        value={drugFields.name}
                        options={suggestions.map(s => ({ value: s }))}
                        onSearch={handleDrugNameSearch}
                        onSelect={handleDrugNameSelect}
                        onChange={v => setDrugFields(prev => ({ ...prev, name: v }))}
                        style={{ width: '100%' }}
                        placeholder="请输入药物名称/别名/商品名"
                        allowClear
                      />
                    </Form.Item>
                    <Form.Item label="分子式"><Input value={drugFields.formula} onChange={e => handleDrugChange('formula', e.target.value)} /></Form.Item>
                    <Form.Item label="CAS号"><Input value={drugFields.cas} onChange={e => handleDrugChange('cas', e.target.value)} /></Form.Item>
                    <Form.Item label="SMILES"><Input value={drugFields.smiles} onChange={e => handleDrugChange('smiles', e.target.value)} /></Form.Item>
                    <Button type="primary" onClick={handleIdentifyV2} style={{ marginTop: 8, marginRight: 8 }}>多源联网识别</Button>
                    <Button onClick={handleClear} style={{ marginTop: 8, marginRight: 8 }}>清空</Button>
                    {status === 'loading' && <span style={{ color: '#1976d2', marginLeft: 8 }}>识别中...</span>}
                    <div style={{ color: status === 'error' ? 'red' : '#388e3c', marginTop: 8 }}>{feedback}</div>
                    <div style={{ marginTop: 16 }}>
                      <Button onClick={handleSaveData} style={{ marginRight: 8 }}>保存数据</Button>
                      <Button type="primary" onClick={handleSubmit}>提交并分析</Button>
                    </div>
                  </Form>
                </Col>
                <Col span={12}>
                  {/* 测试按钮 */}
                  <div style={{ marginBottom: 16 }}>
                    <Button
                      type="primary"
                      onClick={() => {
                        console.log('🧪 手动测试API调用');
                        console.log('当前SMILES:', drugFields.smiles);
                        // 强制设置一个测试SMILES
                        if (!drugFields.smiles) {
                          setDrugFields(prev => ({...prev, smiles: 'CCO'}));
                        }
                      }}
                    >
                      🧪 测试分子显示
                    </Button>
                    <span style={{ marginLeft: 8, fontSize: 12, color: '#666' }}>
                      当前SMILES: {drugFields.smiles || '未设置'}
                    </span>
                  </div>

                  {/* 强制显示MoleculeViewer */}
                  <div style={{ border: '2px solid #ff9800', padding: 8, borderRadius: 4, marginBottom: 16 }}>
                    <div style={{ background: '#fff3e0', padding: 8, marginBottom: 8 }}>
                      <strong>🔧 强制测试区域</strong>
                    </div>
                    <MoleculeViewer
                      smiles={drugFields.smiles || 'CCO'}
                      showProperties={true}
                      showFunctionalGroups={true}
                      height={400}
                    />
                  </div>

                  {/* 原始条件显示 */}
                  {drugFields.smiles && (
                    <div style={{ border: '1px solid #ccc', padding: 8, borderRadius: 4 }}>
                      <div style={{ background: '#f5f5f5', padding: 8, marginBottom: 8 }}>
                        <strong>📋 原始条件显示</strong>
                      </div>
                      <MoleculeViewer
                        smiles={drugFields.smiles}
                        showProperties={true}
                        showFunctionalGroups={true}
                        height={400}
                      />
                    </div>
                  )}
                </Col>
              </Row>
            </div>
          ) },
          { key: 'excipient', label: '辅料信息', children: (
            <div>
              <Button onClick={() => setExcipientImportOpen(true)} style={{ marginBottom: 8 }}>批量导入（Excel）</Button>
              <Form layout="vertical">
                <Form.Item name="excipientData" label="辅料信息（JSON或表格）">
                  <Input.TextArea rows={6} value={excipientData} onChange={e => setExcipientData(e.target.value)} placeholder='[{"name":"辅料A","type":"填充剂","amount":"10mg"}]' />
                </Form.Item>
              </Form>
              <BatchImportModal open={excipientImportOpen} onClose={() => setExcipientImportOpen(false)} onSuccess={data => setExcipientData(JSON.stringify(data))} templateUrl="/templates/excipient_import_template.xlsx" entityName="辅料" templateFields={['name','type','amount']} />
            </div>
          ) },
          { key: 'env', label: '环境参数', children: (
            <div>
              <Button onClick={() => setEnvImportOpen(true)} style={{ marginBottom: 8 }}>批量导入（Excel）</Button>
              <Form layout="vertical">
                <Form.Item name="envData" label="环境参数（JSON或表格）">
                  <Input.TextArea rows={4} value={envData} onChange={e => setEnvData(e.target.value)} placeholder='[{"temperature":"25","humidity":"60%","light":"暗"}]' />
                </Form.Item>
              </Form>
              <BatchImportModal open={envImportOpen} onClose={() => setEnvImportOpen(false)} onSuccess={data => setEnvData(JSON.stringify(data))} templateUrl="/templates/env_import_template.xlsx" entityName="环境参数" templateFields={['temperature','humidity','light']} />
            </div>
          ) },
          { key: 'stability', label: '稳定性数据', children: (
            <div>
              <Button onClick={() => setStabilityImportOpen(true)} style={{ marginBottom: 8 }}>批量导入（Excel）</Button>
              <Form layout="vertical">
                <Form.Item name="stabilityData" label="稳定性数据（JSON或表格）">
                  <Input.TextArea rows={6} value={stabilityData} onChange={e => setStabilityData(e.target.value)} placeholder='[{"time":"0月","value":100,"unit":"%"}]' />
                </Form.Item>
              </Form>
              <BatchImportModal open={stabilityImportOpen} onClose={() => setStabilityImportOpen(false)} onSuccess={data => setStabilityData(JSON.stringify(data))} templateUrl="/templates/stability_import_template.xlsx" entityName="稳定性数据" templateFields={['time','value','unit']} />
            </div>
          ) },
        ]} />
        <div style={{ marginTop: 24 }}>
          <h3>批量导入历史</h3>
          <ul>
            {importHistory.map((item, idx) => <li key={idx}>{JSON.stringify(item)}</li>)}
          </ul>
          <h4 style={{ marginTop: 16 }}>导入错误记录</h4>
          <ul>
            {importErrors.map((err, idx) => <li key={idx}>{JSON.stringify(err)}</li>)}
          </ul>
        </div>
        {/* 多源结果展示区 */}
        {identifyResults.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <h4>最可信结果：</h4>
            {identifyResults.slice(0,1).map((res, idx) => (
              <Card key={idx} style={{ marginBottom: 12, background: '#e3f2fd', border: '1px solid #1976d2' }}>
                <div><b>来源：</b>{res.source} <b>置信度：</b>{(res.confidence * 100).toFixed(0)}%</div>
                <div><b>名称：</b><span style={{color:'#1976d2'}}>{res.detail.name}</span></div>
                <div><b>分子式：</b><span style={{color:'#388e3c'}}>{res.detail.formula}</span></div>
                <div><b>CAS：</b><span style={{color:'#d32f2f'}}>{res.detail.cas}</span></div>
                <div><b>SMILES：</b><span style={{color:'#fbc02d'}}>{res.detail.smiles}</span></div>
                {res.detail.structure_image_url && <img src={res.detail.structure_image_url} alt="structure" style={{ maxWidth: 180, margin: '8px 0' }} />}
                <Button size="small" onClick={() => { setFeedbackTarget(res); setFeedbackModalOpen(true); setTimeout(() => feedbackInputRef.current?.focus(), 100); }}>反馈/收藏/填充</Button>
                <Button size="small" onClick={() => { navigator.clipboard.writeText(JSON.stringify(res.detail)); message.success('已复制'); }} style={{marginLeft:8}}>复制</Button>
              </Card>
            ))}
            {identifyResults.length > 1 && <h4>其他来源：</h4>}
            {identifyResults.slice(1).map((res, idx) => (
              <Card key={idx} style={{ marginBottom: 12, background: '#f9f9f9' }}>
                <div><b>来源：</b>{res.source} <b>置信度：</b>{(res.confidence * 100).toFixed(0)}%</div>
                <div><b>名称：</b>{res.detail.name}</div>
                <div><b>分子式：</b>{res.detail.formula}</div>
                <div><b>CAS：</b>{res.detail.cas}</div>
                <div><b>SMILES：</b>{res.detail.smiles}</div>
                {res.detail.structure_image_url && <img src={res.detail.structure_image_url} alt="structure" style={{ maxWidth: 180, margin: '8px 0' }} />}
                <Button size="small" onClick={() => { setFeedbackTarget(res); setFeedbackModalOpen(true); setTimeout(() => feedbackInputRef.current?.focus(), 100); }}>反馈/收藏/填充</Button>
                <Button size="small" onClick={() => { navigator.clipboard.writeText(JSON.stringify(res.detail)); message.success('已复制'); }} style={{marginLeft:8}}>复制</Button>
              </Card>
            ))}
          </div>
        )}
        <Modal
          open={feedbackModalOpen}
          title="结果反馈"
          onCancel={() => setFeedbackModalOpen(false)}
          onOk={handleFeedbackSubmit}
          okText="提交"
          cancelText="取消"
          confirmLoading={status === 'loading'}
          afterOpenChange={open => { if (open) setTimeout(() => feedbackInputRef.current?.focus(), 100); }}
        >
          <Input.TextArea
            ref={feedbackInputRef}
            rows={4}
            value={feedbackContent}
            onChange={e => setFeedbackContent(e.target.value)}
            placeholder="请描述您的修正建议或补充信息..."
            onKeyDown={e => { if (e.ctrlKey && e.key === 'Enter') handleFeedbackSubmit(); }}
          />
        </Modal>
        <Button onClick={() => setHistoryDrawerOpen(true)} style={{ marginLeft: 8 }}>识别历史/常用药物</Button>
        <Drawer open={historyDrawerOpen} onClose={() => setHistoryDrawerOpen(false)} title="识别历史与常用药物" width={320}>
          <h4>历史识别</h4>
          <ul>{historyList.map((h, i) => <li key={i}><a onClick={() => { setDrugFields(prev => ({ ...prev, name: h })); setHistoryDrawerOpen(false); }}>{h}</a></li>)}</ul>
          <h4>常用药物</h4>
          <ul>{favoriteList.map((f, i) => <li key={i}><a onClick={() => { setDrugFields(prev => ({ ...prev, name: f })); setHistoryDrawerOpen(false); }}>{f}</a></li>)}</ul>
        </Drawer>
      </Card>
    </div>
  );
};

export default DataInputPage; 