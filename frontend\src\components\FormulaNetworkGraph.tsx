import React, { useEffect, useState } from 'react';
import { Card, Spin, Empty, Radio, Switch, Space, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

interface FormulaNetworkGraphProps {
  drug: any;
  components: any[];
  interactions?: any[];
  width?: string | number;
  height?: number;
}

type GraphNode = {
  id: string;
  name: string;
  value: number;
  category: number;
  symbolSize: number;
  label?: any;
  itemStyle?: any;
};

type GraphLink = {
  source: string;
  target: string;
  value: number;
  lineStyle?: any;
  label?: any;
};

type GraphCategory = {
  name: string;
  itemStyle?: any;
};

type GraphData = {
  nodes: GraphNode[];
  links: GraphLink[];
  categories: GraphCategory[];
};

/**
 * 配方网络图组件
 * 用于展示药物配方中成分之间的相互作用关系
 */
const FormulaNetworkGraph: React.FC<FormulaNetworkGraphProps> = ({
  drug,
  components,
  interactions = [],
  width = '100%',
  height = 500
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [graphData, setGraphData] = useState<GraphData | null>(null);
  const [viewMode, setViewMode] = useState<'relation' | 'risk'>('relation');
  const [showLabels, setShowLabels] = useState<boolean>(true);

  useEffect(() => {
    if (!drug || !components || components.length === 0) {
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      // 根据实际交互关系或生成模拟数据
      const nodes: GraphNode[] = [];
      const links: GraphLink[] = [];
      const categories: GraphCategory[] = [
        { name: '主药' },
        { name: '辅料' },
        { name: '风险' }
      ];

      // 添加主药节点
      nodes.push({
        id: drug.id || 'drug',
        name: drug.name || '主药',
        value: 80,
        category: 0,
        symbolSize: 60,
        label: {
          show: showLabels,
          fontSize: 14
        },
        itemStyle: {
          color: '#5470c6'
        }
      });

      // 添加辅料节点
      components.forEach((component, index) => {
        const componentName = component.name || `辅料${index + 1}`;
        nodes.push({
          id: component.id || `component_${index}`,
          name: componentName,
          value: component.amount || 40,
          category: 1,
          symbolSize: (component.amount || 40) / 2 + 20,
          label: {
            show: showLabels,
            fontSize: 12
          }
        });
      });

      // 添加交互关系
      if (interactions && interactions.length > 0) {
        // 使用传入的交互数据
        interactions.forEach((interaction, index) => {
          const { source, target, type, risk_level } = interaction;
          
          // 如果是风险类型的视图，添加风险节点
          if (viewMode === 'risk' && risk_level && risk_level !== 'low') {
            const riskNodeId = `risk_${index}`;
            const riskLevel = risk_level.toLowerCase();
            
            // 添加风险节点
            nodes.push({
              id: riskNodeId,
              name: type || '未知风险',
              value: riskLevel === 'high' ? 60 : 40,
              category: 2,
              symbolSize: riskLevel === 'high' ? 40 : 30,
              itemStyle: {
                color: riskLevel === 'high' ? '#ff4d4f' : '#faad14'
              },
              label: {
                show: showLabels,
                fontSize: 12
              }
            });
            
            // 连接源和风险
            links.push({
              source,
              target: riskNodeId,
              value: riskLevel === 'high' ? 8 : 5,
              lineStyle: {
                width: riskLevel === 'high' ? 3 : 2,
                color: riskLevel === 'high' ? '#ff4d4f' : '#faad14',
                curveness: 0.2
              }
            });
            
            // 连接风险和目标
            links.push({
              source: riskNodeId,
              target,
              value: riskLevel === 'high' ? 8 : 5,
              lineStyle: {
                width: riskLevel === 'high' ? 3 : 2,
                color: riskLevel === 'high' ? '#ff4d4f' : '#faad14',
                curveness: 0.2
              }
            });
          } else {
            // 关系视图，直接连接源和目标
            links.push({
              source,
              target,
              value: type === 'incompatible' ? 8 : 5,
              lineStyle: {
                width: risk_level === 'high' ? 3 : 2,
                color: risk_level === 'high' ? '#ff4d4f' : risk_level === 'medium' ? '#faad14' : '#52c41a',
                curveness: 0.1
              },
              label: {
                show: showLabels,
                formatter: type || '交互',
                fontSize: 10
              }
            });
          }
        });
      } else {
        // 生成默认的交互关系
        components.forEach((component) => {
          const componentId = component.id || `component_${components.indexOf(component)}`;
          
          // 主药与每个辅料的连接
          links.push({
            source: drug.id || 'drug',
            target: componentId,
            value: 5,
            lineStyle: {
              width: 2,
              curveness: 0.1
            }
          });
          
          // 随机生成一些辅料之间的连接
          if (components.length > 1 && Math.random() > 0.5) {
            const otherComponents = components.filter(c => c !== component);
            const randomComponent = otherComponents[Math.floor(Math.random() * otherComponents.length)];
            const randomComponentId = randomComponent.id || `component_${components.indexOf(randomComponent)}`;
            
            if (links.findIndex(link => 
              (link.source === componentId && link.target === randomComponentId) ||
              (link.source === randomComponentId && link.target === componentId)
            ) === -1) {
              links.push({
                source: componentId,
                target: randomComponentId,
                value: 3,
                lineStyle: {
                  width: 1,
                  curveness: 0.1
                }
              });
            }
          }
        });
      }

      setGraphData({ nodes, links, categories });
    } catch (error) {
      console.error('生成网络图错误:', error);
    } finally {
      setLoading(false);
    }
  }, [drug, components, interactions, viewMode, showLabels]);

  const getChartOptions = () => {
    if (!graphData) return {};

    return {
      title: {
        text: '配方成分相互作用网络',
        subtext: viewMode === 'relation' ? '显示成分间关系' : '显示风险点',
        top: 'top',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          if (params.dataType === 'node') {
            return `<strong>${params.data.name}</strong><br/>类别: ${graphData.categories[params.data.category].name}`;
          } else if (params.dataType === 'edge') {
            const source = graphData.nodes.find(n => n.id === params.data.source);
            const target = graphData.nodes.find(n => n.id === params.data.target);
            return `${source?.name} → ${target?.name}`;
          }
          return params.name;
        }
      },
      legend: {
        data: graphData.categories.map(a => a.name),
        top: 40,
        right: 10,
        orient: 'vertical'
      },
      animationDuration: 1500,
      animationEasingUpdate: 'quinticInOut',
      series: [
        {
          name: '配方网络',
          type: 'graph',
          layout: 'force',
          data: graphData.nodes,
          links: graphData.links,
          categories: graphData.categories,
          roam: true,
          label: {
            position: 'right',
            formatter: '{b}'
          },
          lineStyle: {
            color: 'source',
            curveness: 0.3
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            }
          },
          force: {
            repulsion: 200,
            edgeLength: 100
          }
        }
      ]
    };
  };

  const handleViewModeChange = (e: any) => {
    setViewMode(e.target.value);
  };

  if (!drug || !components || components.length === 0) {
    return <Empty description="无法生成网络图，数据不足" />;
  }

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>配方成分网络图</span>
          <Tooltip title="展示药物配方中各成分之间的相互作用关系">
            <InfoCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
          <div style={{ marginLeft: 'auto' }}>
            <Space>
              <Radio.Group value={viewMode} onChange={handleViewModeChange} buttonStyle="solid" size="small">
                <Radio.Button value="relation">关系视图</Radio.Button>
                <Radio.Button value="risk">风险视图</Radio.Button>
              </Radio.Group>
              <Switch 
                checkedChildren="显示标签" 
                unCheckedChildren="隐藏标签" 
                checked={showLabels} 
                onChange={setShowLabels}
                size="small"
              />
            </Space>
          </div>
        </div>
      }
      bordered={false}
    >
      {loading ? (
        <div style={{ height, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="生成网络图..." />
        </div>
      ) : !graphData ? (
        <Empty description="无法生成网络图，数据不足" />
      ) : (
        <ReactECharts
          option={getChartOptions()}
          style={{ height, width }}
          opts={{ renderer: 'canvas' }}
        />
      )}
    </Card>
  );
};

export default FormulaNetworkGraph; 