{"mcpServers": {"description": "AI Agent Protocol: Self-Contained Mandatory Interactive Final Review Loop with Auto-Script Creation (Version 3)", "globs": [], "alwaysApply": true, "userAdvisory": {"title": "User Advisory & Safety", "content": "This is an advanced, experimental rule. It instructs the AI Agent to check for, potentially create, and then execute a Python script (final_review_gate.py) in your project's root directory to facilitate an interactive review loop. By using this rule, you acknowledge that the AI will attempt file system operations (read, write, execute) as directed. Ensure the AI's execution environment has appropriate permissions if you expect script auto-creation to work, and be aware of the security implications."}, "corePrinciple": {"title": "Core Principle & Context", "content": "Your primary task processing includes a mandatory final interactive review stage driven by user input through a specific Python script (final_review_gate.py). You are NOT to consider your overall task complete until this review stage explicitly signals completion. This protocol leverages your known capability to run commands in a sub-terminal (displayed within the chat interface) and read their output."}, "phases": {"phase0": {"title": "Ensure final_review_gate.py Script Exists", "description": "This phase is executed ONCE per user request that triggers this overall protocol, or if the script is missing or its content is incorrect.", "scriptDetails": {"name": "final_review_gate.py", "location": "Directly in the root of the current project/workspace", "content": "# final_review_gate.py\nimport sys\nimport os\n\nif __name__ == \"__main__\":\n    try:\n        sys.stdout = os.fdopen(sys.stdout.fileno(), 'w', buffering=1)\n    except Exception:\n        pass\n    try:\n        sys.stderr = os.fdopen(sys.stderr.fileno(), 'w', buffering=1)\n    except Exception:\n        pass\n    print(\"--- FINAL REVIEW GATE ACTIVE ---\", flush=True)\n    print(\"AI has completed its primary actions. Awaiting your review or further sub-prompts.\", flush=True)\n    print(\"Type your sub-prompt, or one of: 'TASK_COMPLETE', 'Done', 'Quit', 'q' to signal completion.\", flush=True)\n    active_session = True\n    while active_session:\n        try:\n            print(\"REVIEW_GATE_AWAITING_INPUT:\", end=\"\", flush=True)\n            line = sys.stdin.readline()\n            if not line:\n                print(\"--- REVIEW GATE: STDIN CLOSED (EOF), EXITING SCRIPT ---\", flush=True)\n                active_session = False\n                break\n            user_input = line.strip()\n            if user_input.upper() in ['TASK_COMPLETE', 'DONE', 'QUIT', 'Q']:\n                print(f\"--- REVIEW GATE: USER SIGNALED COMPLETION WITH '{user_input.upper()}' ---\", flush=True)\n                active_session = False\n                break\n            elif user_input:\n                print(f\"USER_REVIEW_SUB_PROMPT: {user_input}\", flush=True)\n        except KeyboardInterrupt:\n            print(\"--- REVIEW GATE: SESSION INTERRUPTED BY USER (KeyboardInterrupt) ---\", flush=True)\n            active_session = False\n            break\n        except Exception as e:\n            print(f\"--- REVIEW GATE SCRIPT ERROR: {e} ---\", flush=True)\n            active_session = False\n            break\n    print(\"--- FINAL REVIEW GATE SCRIPT EXITED ---\", flush=True)"}}, "phase1": {"title": "Primary Task Execution", "steps": {"taskExecution": "Address the user's main request as you normally would: perform analysis, make code changes, call tools, and run applications/tests in sub-terminals if the main task requires it.", "applicationConfirmation": "Before attempting to run an entire application (e.g., a web server, a full front-end build, a long-running background process), you MUST ask the user for explicit confirmation.", "confirmationExemption": "This confirmation is NOT required for running individual test scripts, linters, build tools for specific components, or other non-application-level commands necessary for task completion.", "adaptiveStrategy": "If the user indicates an application is already running or denies permission, adapt your strategy accordingly (e.g., focus on tests or specific file executions)."}}, "phase2": {"title": "Mandatory Final Interactive Review Script Execution", "steps": {"transitionPoint": "Once you believe you have completed all substantive actions for the user's initial request (Phase 1), DO NOT CONCLUDE THE CONVERSATION. You MUST now transition to this Final Interactive Review phase.", "scriptExecution": "Execute the final_review_gate.py script from the project root.", "terminalRequirements": "The terminal for this script MUST run directly within the chat window, and its output MUST be continuously visible to the user as it is generated."}}, "phase3": {"title": "Interactive Review Loop", "steps": {"monitoring": "Continuously monitor the standard output of the launched final_review_gate.py script as it appears live in the chat window.", "subPromptDetection": "Monitor for USER_REVIEW_SUB_PROMPT: <user's sub-prompt text> format", "processing": "Process sub-prompts as new, direct, actionable instructions", "completion": "Monitor for completion signals or script exit messages"}}, "phase4": {"title": "True Task Conclusion", "steps": {"termination": "Only conclude after script termination", "summary": "Provide final summary of all actions"}}}, "overridingBehavior": "This entire Final Interactive Review process is a strict, non-negotiable requirement that overrides any default tendency to end the conversation after completing Phase 1."}}