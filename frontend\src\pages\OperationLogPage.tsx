import React, { useEffect, useState } from 'react';
import { Card, Table, Button, Modal, Input, DatePicker, message } from 'antd';
import { listOperationLogsPaged, getOperationLogDetail, exportOperationLogs, OperationLog } from '../api';
import { useTranslation } from 'react-i18next';

const OperationLogPage: React.FC = () => {
  const { t } = useTranslation();
  const [logs, setLogs] = useState<OperationLog[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [filters, setFilters] = useState({ user: '', action: '', start: '', end: '' });
  const [detail, setDetail] = useState<OperationLog | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchLogs = async () => {
    setLoading(true);
    const res = await listOperationLogsPaged({ ...filters, page, page_size: pageSize });
    setLogs(res.data.logs);
    setTotal(res.data.total);
    setLoading(false);
  };

  useEffect(() => { fetchLogs(); }, [page, filters]);

  const handleExport = async () => {
    const res = await exportOperationLogs(filters);
    const url = window.URL.createObjectURL(new Blob([res.data as BlobPart]));
    const a = document.createElement('a');
    a.href = url;
    a.download = 'operation_logs.xlsx';
    a.click();
  };

  return (
    <Card title={t('操作日志审计')} style={{ margin: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <Input placeholder={t('用户')} style={{ width: 120, marginRight: 8 }} value={filters.user} onChange={e => setFilters(f => ({ ...f, user: e.target.value }))} />
        <Input placeholder={t('操作类型')} style={{ width: 120, marginRight: 8 }} value={filters.action} onChange={e => setFilters(f => ({ ...f, action: e.target.value }))} />
        <DatePicker.RangePicker style={{ marginRight: 8 }} onChange={v => setFilters(f => ({ ...f, start: v?.[0]?.format('YYYY-MM-DD') || '', end: v?.[1]?.format('YYYY-MM-DD') || '' }))} />
        <Button onClick={fetchLogs}>{t('筛选')}</Button>
        <Button onClick={handleExport} style={{ marginLeft: 8 }}>{t('导出')}</Button>
      </div>
      <Table dataSource={logs} rowKey="id" loading={loading} pagination={{ current: page, pageSize, total, onChange: setPage }} bordered
        columns={[
          { title: t('时间'), dataIndex: 'time' },
          { title: t('用户'), dataIndex: 'user' },
          { title: t('操作'), dataIndex: 'action' },
          { title: t('详情'), render: (_, log) => <Button onClick={() => getOperationLogDetail(log.id).then(res => setDetail(res.data))}>{t('查看')}</Button> },
        ]}
      />
      <Modal open={!!detail} onCancel={() => setDetail(null)} footer={null} title={t('日志详情')} width={600}>
        {detail && (
          <div>
            <div><b>{t('时间')}：</b>{detail.time}</div>
            <div><b>{t('用户')}：</b>{detail.user}</div>
            <div><b>{t('操作')}：</b>{detail.action}</div>
            <div><b>{t('对象类型')}：</b>{detail.object_type}</div>
            <div><b>{t('对象ID')}：</b>{detail.object_id}</div>
            <div><b>{t('详情')}：</b>{detail.detail}</div>
            <div><b>{t('变更前')}：</b><pre>{JSON.stringify(detail.before, null, 2)}</pre></div>
            <div><b>{t('变更后')}：</b><pre>{JSON.stringify(detail.after, null, 2)}</pre></div>
            <div><b>{t('IP')}：</b>{detail.ip}</div>
            <div><b>{t('设备')}：</b>{detail.device}</div>
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default OperationLogPage; 