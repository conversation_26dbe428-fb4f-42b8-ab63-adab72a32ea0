import React, { useState, useEffect, useContext } from 'react';
import {
  Card,
  Button,
  Table,
  Tag,
  Space,
  Tabs,
  Row,
  Col,
  Typography,
  Descriptions,
  message,
  Spin,
  Result,
  Empty
} from 'antd';
import {
  ProjectOutlined,
  HomeOutlined,
  DatabaseOutlined,
  SettingOutlined,
  SearchOutlined,
  BookOutlined,
  AlertOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { ProjectContext } from '../App';
import { saveAnalysisResult, AnalysisResultSaveRequest } from '../api';
import { useNavigate } from 'react-router-dom';
import MoleculeViewer from '../components/MoleculeViewer';
import ProjectSelector from '../components/ProjectSelector';

const { Title, Text } = Typography;

// 定义分析结果的数据结构
interface DrugProfile {
  name: string;
  chembl_id?: string;
  molecular_species?: string;
  max_phase?: number;
  molecular_weight?: number;
  structure_image?: string;
}

interface ExcipientProfile {
  name: string;
  cas?: string;
  formula?: string;
  synonyms?: string[];
  category?: string;
}

interface RuleBasedEvidence {
  drug_risk_group: string;
  drug_group_formula: string;
  excipient_risk_group: string;
  reaction_type: string;
  interaction_description: string;
}

interface ExcipientAnalysisResult {
  excipient_profile: ExcipientProfile;
  identified_evidence: any[];
  rule_based_evidence: RuleBasedEvidence[];
  identified_risk: 'Low risk' | 'Medium risk' | 'High risk';
  rule_based_risk: 'Low risk' | 'Medium risk' | 'High risk';
  recommended_risk: 'Low risk' | 'Medium risk' | 'High risk';
}

interface AnalysisResults {
  drug_profile: DrugProfile;
  excipient_analyses: ExcipientAnalysisResult[];
  overall_identified_risk: 'Low risk' | 'Medium risk' | 'High risk';
  overall_rule_based_risk: 'Low risk' | 'Medium risk' | 'High risk';
  overall_recommended_risk: 'Low risk' | 'Medium risk' | 'High risk';
}

const ExcipientAnalysis: React.FC = () => {
  const { currentProject, inputData, loadProjectData, setInputData } = useContext(ProjectContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [activeTab, setActiveTab] = useState('formulation');
  const [selectedExcipientIndex, setSelectedExcipientIndex] = useState(0);

  // 执行相容性分析
  const performAnalysis = React.useCallback(async () => {
    if (!inputData || !currentProject) {
      console.log('缺少必要数据，跳过分析:', { inputData: !!inputData, currentProject: !!currentProject });
      return;
    }

    console.log('开始执行相容性分析:', {
      drug_name: inputData.drug_name,
      excipients_count: inputData.excipients?.length,
      project_id: currentProject.id
    });

    setLoading(true);
    try {
      // 模拟分析过程
      await new Promise(resolve => setTimeout(resolve, 1500)); // 减少等待时间

      // 构建药物信息（API信息）
      const drugProfile: DrugProfile = {
        name: inputData.drug_name || 'Unknown Drug',
        chembl_id: inputData.chembl_id || 'Unknown ChEMBL ID',
        molecular_species: inputData.drug_formula || 'Unknown Formula',
        max_phase: inputData.max_phase || 4.0,
        molecular_weight: inputData.molecular_weight || 0,
        structure_image: inputData.drug_smiles ? `/api/structure/${encodeURIComponent(inputData.drug_smiles)}` : undefined
      };

      // 为每个辅料构建分析结果
      const excipientAnalyses: ExcipientAnalysisResult[] = inputData.excipients.map((excipient: any, index: number) => {
        // 根据辅料名称模拟不同的风险等级
        const getExcipientRisk = (name: string) => {
          const lowerName = name.toLowerCase();
          if (lowerName.includes('acid') || lowerName.includes('酸')) {
            return { identified: 'Medium risk', rule_based: 'High risk', recommended: 'High risk' };
          } else if (lowerName.includes('starch') || lowerName.includes('淀粉') || lowerName.includes('cellulose') || lowerName.includes('纤维素')) {
            return { identified: 'Low risk', rule_based: 'Low risk', recommended: 'Low risk' };
          } else {
            return { identified: 'Low risk', rule_based: 'Medium risk', recommended: 'Medium risk' };
          }
        };

        const risk = getExcipientRisk(excipient.name || '');

        // 根据辅料类型生成更真实的信息
        const getExcipientInfo = (name: string) => {
          const lowerName = name.toLowerCase();
          if (lowerName.includes('ascorbic') || lowerName.includes('维生素c')) {
            return {
              cas: 'L-(+)-Ascorbic acid [50-81-7]',
              formula: 'C6H8O6 176.13',
              synonyms: ['Acidum ascorbicum', 'C-97', 'cevitamic acid', '2,3-didehydro-L-threohexono-1,4-lactone', 'E300', '3-oxo-L-gulofuranolactone', 'enol form', 'vitamin C'],
              category: 'Antioxidant; therapeutic agent'
            };
          } else if (lowerName.includes('starch') || lowerName.includes('淀粉')) {
            return {
              cas: 'Starch [9005-25-8]',
              formula: '(C6H10O5)n',
              synonyms: ['Corn starch', 'Potato starch', 'Wheat starch'],
              category: 'Disintegrant; Filler'
            };
          } else if (lowerName.includes('cellulose') || lowerName.includes('纤维素')) {
            return {
              cas: 'Microcrystalline cellulose [9004-34-6]',
              formula: '(C6H10O5)n',
              synonyms: ['MCC', 'Avicel', 'Crystalline cellulose'],
              category: 'Filler; Disintegrant'
            };
          } else {
            return {
              cas: excipient.cas || 'Unknown CAS',
              formula: excipient.formula || 'Unknown Formula',
              synonyms: excipient.synonyms || [excipient.name || 'Unknown'],
              category: excipient.category || 'Unknown Category'
            };
          }
        };

        const excipientInfo = getExcipientInfo(excipient.name || '');

        return {
          excipient_profile: {
            name: excipient.name || `Excipient ${index + 1}`,
            ...excipientInfo
          },
          identified_evidence: [],
          rule_based_evidence: [
            {
              drug_risk_group: 'Amide (Carbonamide, sulfamide, phosphoramide)',
              drug_group_formula: 'RnE(O)xNR\'R\' (RC=O)NR\'R\'; RO=S(O)NR\'R\'; RP=O(NR\'R\')',
              excipient_risk_group: 'Such as carboxyl group, phenolic hydroxyl group, sulfonic acid group, etc.',
              reaction_type: 'Hydrolysis reaction',
              interaction_description: `Compounds containing Amide (Carbonamide, sulfamide, phosphoramide) will slowly hydrolyze under acidic or alkaline conditions and moisture when interacting with ${excipient.name}. Therefore, it is necessary to pay attention to the stability of such compounds and avoid contact with acidic or alkaline or Use the accessories with strong hygroscopicity together.`
            }
          ],
          identified_risk: risk.identified as 'Low risk' | 'Medium risk' | 'High risk',
          rule_based_risk: risk.rule_based as 'Low risk' | 'Medium risk' | 'High risk',
          recommended_risk: risk.recommended as 'Low risk' | 'Medium risk' | 'High risk'
        };
      });

      // 计算整体风险等级（取最高风险）
      const calculateOverallRisk = (riskType: 'identified_risk' | 'rule_based_risk' | 'recommended_risk') => {
        const risks = excipientAnalyses.map(analysis => analysis[riskType]);
        if (risks.includes('High risk')) return 'High risk';
        if (risks.includes('Medium risk')) return 'Medium risk';
        return 'Low risk';
      };

      const results: AnalysisResults = {
        drug_profile: drugProfile,
        excipient_analyses: excipientAnalyses,
        overall_identified_risk: calculateOverallRisk('identified_risk') as 'Low risk' | 'Medium risk' | 'High risk',
        overall_rule_based_risk: calculateOverallRisk('rule_based_risk') as 'Low risk' | 'Medium risk' | 'High risk',
        overall_recommended_risk: calculateOverallRisk('recommended_risk') as 'Low risk' | 'Medium risk' | 'High risk'
      };

      setAnalysisResults(results);

      // 保存分析结果到项目
      if (currentProject?.id) {
        try {
          const saveRequest: AnalysisResultSaveRequest = {
            analysis_type: 'compatibility',
            analysis_date: new Date().toISOString(),
            analysis_data: results,
            summary: `原辅料相容性分析完成，推荐风险等级：${results.overall_recommended_risk}`
          };

          console.log('正在保存分析结果到项目:', currentProject.id, saveRequest);
          await saveAnalysisResult(currentProject.id, saveRequest);
          console.log('分析结果已保存到项目');
        } catch (error) {
          console.error('保存分析结果失败:', error);
          // 不阻塞主流程，只记录错误
        }
      }

      message.success('原辅料相容性分析完成');

    } catch (error: any) {
      console.error('分析失败:', error);
      message.error('分析失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [inputData, currentProject]);

  // 初始化数据
  useEffect(() => {
    // 检查是否有项目数据，如果没有则显示引导页面
    if (!currentProject) {
      setDataLoading(false);
      return;
    }

    // 当项目切换时，自动加载项目数据
    if (loadProjectData) {
      setDataLoading(true);

      // 添加超时处理，防止无限加载
      const timeoutId = setTimeout(() => {
        setDataLoading(false);
        console.warn('数据加载超时，停止加载状态');
      }, 10000); // 10秒超时

      loadProjectData(currentProject.id)
        .finally(() => {
          clearTimeout(timeoutId);
          setDataLoading(false);
        });
    } else {
      setDataLoading(false);
    }
  }, [currentProject, loadProjectData]);

  // 当inputData更新时，检查是否可以进行分析
  useEffect(() => {
    if (currentProject && inputData && inputData.drug_name && inputData.excipients && inputData.excipients.length > 0) {
      // 自动执行分析
      performAnalysis();
    }
  }, [inputData, currentProject]); // 移除performAnalysis依赖，避免循环

  // 渲染风险标签
  const renderRiskTag = (risk: string) => {
    const riskConfig = {
      'Low risk': { color: '#52c41a', bgColor: '#f6ffed', text: 'Low risk' },
      'Medium risk': { color: '#faad14', bgColor: '#fffbe6', text: 'Medium risk' },
      'High risk': { color: '#ff4d4f', bgColor: '#fff2f0', text: 'High risk' }
    };

    const config = riskConfig[risk as keyof typeof riskConfig] || riskConfig['Low risk'];

    return (
      <Tag
        style={{
          color: config.color,
          backgroundColor: config.bgColor,
          border: `1px solid ${config.color}`,
          borderRadius: '4px',
          padding: '2px 8px'
        }}
      >
        {config.text}
      </Tag>
    );
  };

  // 渲染配方信息选项卡
  const renderFormulationInfo = () => {
    if (!analysisResults) return null;

    const currentExcipient = analysisResults.excipient_analyses[selectedExcipientIndex];

    return (
      <div>
        {/* 辅料选择器 */}
        {analysisResults.excipient_analyses.length > 1 && (
          <div style={{ marginBottom: '16px' }}>
            <Text strong>选择辅料: </Text>
            <Space wrap>
              {analysisResults.excipient_analyses.map((analysis, index) => (
                <Button
                  key={`excipient-btn-${index}`}
                  type={selectedExcipientIndex === index ? 'primary' : 'default'}
                  size="small"
                  onClick={() => setSelectedExcipientIndex(index)}
                >
                  {analysis.excipient_profile.name}
                </Button>
              ))}
            </Space>
          </div>
        )}

        <Row gutter={24}>
          {/* 药物信息（API信息） */}
          <Col span={12}>
            <Card
              title={
                <Space>
                  <DatabaseOutlined />
                  <Text strong>Drug profile (API)</Text>
                </Space>
              }
              style={{ height: '100%' }}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{
                    width: '100%',
                    height: '120px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#fafafa'
                  }}>
                    {/* 药物结构图 */}
                    {inputData.drug_smiles ? (
                      <MoleculeViewer
                        smiles={inputData.drug_smiles}
                        width={120}
                        height={120}
                        style={{ border: 'none', backgroundColor: 'transparent' }}
                      />
                    ) : (
                      <div style={{ textAlign: 'center' }}>
                        <DatabaseOutlined style={{ fontSize: '24px', color: '#999' }} />
                        <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                          No Structure
                        </div>
                      </div>
                    )}
                  </div>
                </Col>
                <Col span={16}>
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="Name (API)">
                      <Text strong style={{ color: '#1890ff' }}>{analysisResults.drug_profile.name}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="ChEMBL ID">
                      <Tag color="blue">{analysisResults.drug_profile.chembl_id}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Molecular Formula">
                      <Text code>{analysisResults.drug_profile.molecular_species}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="CAS Number">
                      <Text code>{inputData.drug_cas || 'Unknown CAS'}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="Molecular Weight">
                      <Text>{analysisResults.drug_profile.molecular_weight ? `${analysisResults.drug_profile.molecular_weight} g/mol` : 'Unknown'}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="Category">
                      <Tag color="green">{inputData.drug_category || 'Unknown Category'}</Tag>
                    </Descriptions.Item>
                  </Descriptions>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 当前选中的辅料信息 */}
          <Col span={12}>
            <Card
              title={
                <Space>
                  <SettingOutlined />
                  <Text strong>Excipient profile</Text>
                </Space>
              }
              style={{ height: '100%' }}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Excipient">
                  <Text strong>{currentExcipient.excipient_profile.name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Excipient CAS">
                  <Text code>{currentExcipient.excipient_profile.cas}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Excipient Formula">
                  {currentExcipient.excipient_profile.formula}
                </Descriptions.Item>
                <Descriptions.Item label="Synonyms">
                  <div style={{ maxHeight: '60px', overflowY: 'auto' }}>
                    {currentExcipient.excipient_profile.synonyms?.map((synonym, index) => (
                      <Tag key={`synonym-${index}`} style={{ marginBottom: '4px' }}>
                        {synonym}
                      </Tag>
                    ))}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="Category">
                  <Tag color="green">{currentExcipient.excipient_profile.category}</Tag>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染已识别证据选项卡
  const renderIdentifiedEvidence = () => {
    if (!analysisResults) return null;

    const currentExcipient = analysisResults.excipient_analyses[selectedExcipientIndex];

    if (currentExcipient.identified_evidence.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Empty
            description="Nothing is found!"
            style={{ color: '#999' }}
          />
        </div>
      );
    }

    // 如果有证据，在这里渲染证据列表
    return (
      <div>
        {/* 证据列表渲染逻辑 */}
      </div>
    );
  };

  // 渲染基于规则的证据选项卡
  const renderRuleBasedEvidence = () => {
    if (!analysisResults) return null;

    const currentExcipient = analysisResults.excipient_analyses[selectedExcipientIndex];

    const columns = [
      {
        title: 'Drug risk group',
        dataIndex: 'drug_risk_group',
        key: 'drug_risk_group',
        width: 200,
      },
      {
        title: 'Drug group formula',
        dataIndex: 'drug_group_formula',
        key: 'drug_group_formula',
        width: 250,
        render: (text: string) => <Text code style={{ fontSize: '12px' }}>{text}</Text>
      },
      {
        title: 'Excipient risk group',
        dataIndex: 'excipient_risk_group',
        key: 'excipient_risk_group',
        width: 200,
      },
      {
        title: 'Reaction type',
        dataIndex: 'reaction_type',
        key: 'reaction_type',
        width: 150,
        render: (text: string) => <Tag color="blue">{text}</Tag>
      },
      {
        title: 'Interaction description',
        dataIndex: 'interaction_description',
        key: 'interaction_description',
        render: (text: string) => (
          <div style={{ maxWidth: '400px', wordWrap: 'break-word' }}>
            {text}
          </div>
        )
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={currentExcipient.rule_based_evidence}
        pagination={false}
        size="small"
        rowKey={(record, index) => `rule-evidence-${index}`}
        style={{ backgroundColor: '#e6f7ff' }}
      />
    );
  };

  // 渲染风险等级区域
  const renderRiskLevels = () => {
    if (!analysisResults) return null;

    const currentExcipient = analysisResults.excipient_analyses[selectedExcipientIndex];

    return (
      <Card
        title={
          <Space>
            <AlertOutlined />
            <Text strong>Risk levels</Text>
          </Space>
        }
        style={{ marginTop: '16px' }}
      >
        {/* 当前辅料的风险等级 */}
        <div style={{ marginBottom: '16px' }}>
          <Text strong>当前辅料 ({currentExcipient.excipient_profile.name}) 风险等级:</Text>
          <Row gutter={24} style={{ marginTop: '8px' }}>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#e6f7ff',
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <Text strong>Identified risk: </Text>
                {renderRiskTag(currentExcipient.identified_risk)}
              </div>
            </Col>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#fff7e6',
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <Text strong>Rule-based risk: </Text>
                {renderRiskTag(currentExcipient.rule_based_risk)}
              </div>
            </Col>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#f6ffed',
                borderRadius: '6px',
                textAlign: 'center',
                border: '2px solid #52c41a'
              }}>
                <Text strong style={{ color: '#722ed1' }}>Recommended risk: </Text>
                {renderRiskTag(currentExcipient.recommended_risk)}
              </div>
            </Col>
          </Row>
        </div>

        {/* 整体风险等级 */}
        <div>
          <Text strong>整体配方风险等级:</Text>
          <Row gutter={24} style={{ marginTop: '8px' }}>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#e6f7ff',
                borderRadius: '6px',
                textAlign: 'center',
                border: '2px solid #1890ff'
              }}>
                <Text strong>Overall Identified risk: </Text>
                {renderRiskTag(analysisResults.overall_identified_risk)}
              </div>
            </Col>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#fff7e6',
                borderRadius: '6px',
                textAlign: 'center',
                border: '2px solid #faad14'
              }}>
                <Text strong>Overall Rule-based risk: </Text>
                {renderRiskTag(analysisResults.overall_rule_based_risk)}
              </div>
            </Col>
            <Col span={8}>
              <div style={{
                padding: '16px',
                backgroundColor: '#f6ffed',
                borderRadius: '6px',
                textAlign: 'center',
                border: '3px solid #722ed1'
              }}>
                <Text strong style={{ color: '#722ed1' }}>Overall Recommended risk: </Text>
                {renderRiskTag(analysisResults.overall_recommended_risk)}
              </div>
            </Col>
          </Row>
        </div>
      </Card>
    );
  };

  // 如果没有项目数据，显示引导页面
  if (!currentProject) {
    return (
      <div style={{ padding: '24px' }}>
        <Result
          icon={<ProjectOutlined style={{ color: '#1890ff' }} />}
          title="请先选择项目"
          subTitle="原辅料相容性分析需要基于项目数据进行，请先在项目管理页面选择或创建项目。"
          extra={
            <Button type="primary" onClick={() => navigate('/projects')}>
              <HomeOutlined />
              前往项目管理
            </Button>
          }
        />
      </div>
    );
  }

  // 手动重新加载数据
  const handleManualReload = () => {
    if (currentProject && loadProjectData) {
      setDataLoading(true);
      loadProjectData(currentProject.id).finally(() => {
        setDataLoading(false);
      });
    }
  };

  // 临时调试功能：设置测试数据
  const handleSetTestData = () => {
    const testData = {
      drug_name: "vonoprazan fumarate",
      drug_cas: "881681-01-2",
      drug_formula: "C17H16FN3O2·C4H4O4",
      drug_smiles: "CC1=CN=C(N1)C(=O)NC2=C(C=C(C=C2)F)C3=NC4=C(N3)C=CC(=C4)OC.C(=CC(=O)O)C(=O)O",
      drug_category: "质子泵抑制剂",
      drug_description: "vonoprazan fumarate是一种新型的质子泵抑制剂",
      excipients: [
        {"name": "微晶纤维素", "amount": "100mg", "function": "填充剂"},
        {"name": "交联羧甲基纤维素钠", "amount": "10mg", "function": "崩解剂"},
        {"name": "硬脂酸镁", "amount": "2mg", "function": "润滑剂"},
        {"name": "羟丙甲纤维素", "amount": "5mg", "function": "包衣材料"}
      ],
      packaging_storage: {
        packaging: "铝塑泡罩包装",
        storage: "密闭，在干燥处保存，避光"
      },
      production_process: "湿法制粒",
      notes: "注意控制湿度，避免API降解"
    };

    if (setInputData) {
      setInputData(testData);
      message.success('测试数据已加载');
    }
  };

  // 如果正在加载数据，显示加载状态
  if (dataLoading) {
    return (
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '16px' }}>
          <ProjectSelector showCreateButton={true} />
        </div>
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>正在加载项目数据...</Text>
          </div>
          <div style={{ marginTop: '16px' }}>
            <Button onClick={() => setDataLoading(false)}>
              停止加载
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // 如果没有输入数据，显示提示
  if (!inputData || !inputData.drug_name || !inputData.excipients || inputData.excipients.length === 0) {


    return (
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '16px' }}>
          <ProjectSelector showCreateButton={true} />
        </div>
        <Result
          icon={<DatabaseOutlined style={{ color: '#faad14' }} />}
          title="缺少分析数据"
          subTitle={
            <div>
              <p>进行原辅料相容性分析需要药物和辅料信息，请先在数据输入页面完善项目数据。</p>
              {currentProject && (
                <p style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  当前项目: {currentProject.name} (ID: {currentProject.id})
                </p>
              )}
              <p style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                数据状态: 药物信息 {inputData?.drug_name ? '✓' : '✗'},
                辅料信息 {inputData?.excipients?.length > 0 ? '✓' : '✗'}
                ({inputData?.excipients?.length || 0} 个)
              </p>
            </div>
          }
          extra={
            <Space>
              <Button type="primary" onClick={() => navigate('/data-input')}>
                <SettingOutlined />
                前往数据输入
              </Button>
              <Button onClick={handleManualReload}>
                <ReloadOutlined />
                重新加载数据
              </Button>
              <Button type="dashed" onClick={handleSetTestData}>
                🧪 加载测试数据
              </Button>
              <Button
                onClick={() => {
                  console.log('=== 调试信息 ===');
                  console.log('当前项目:', currentProject);
                  console.log('当前输入数据:', inputData);
                  console.log('loadProjectData函数:', loadProjectData);
                  console.log('dataLoading状态:', dataLoading);
                  message.info('调试信息已输出到控制台');
                }}
              >
                🔍 调试信息
              </Button>
            </Space>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 项目选择器 */}
      <div style={{ marginBottom: '16px' }}>
        <ProjectSelector showCreateButton={true} />
      </div>

      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={3}>Analysis results</Title>
          <Text type="secondary">
            项目: "{currentProject.name}" | 药物: "{inputData.drug_name}" | 辅料数量: {inputData.excipients?.length || 0}
          </Text>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>正在进行原辅料相容性分析...</Text>
            </div>
          </div>
        ) : (
          <>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'formulation',
                  label: (
                    <Space>
                      <DatabaseOutlined />
                      Formulation information
                    </Space>
                  ),
                  children: renderFormulationInfo()
                },
                {
                  key: 'identified',
                  label: (
                    <Space>
                      <SearchOutlined />
                      Identified evidence
                    </Space>
                  ),
                  children: renderIdentifiedEvidence()
                },
                {
                  key: 'rule-based',
                  label: (
                    <Space>
                      <BookOutlined />
                      Rule-based evidence
                    </Space>
                  ),
                  children: renderRuleBasedEvidence()
                }
              ]}
            />

            {renderRiskLevels()}
          </>
        )}
      </Card>
    </div>
  );
};

export default ExcipientAnalysis; 