import React, { useEffect, useState, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Tabs, Button, Progress, Modal, Checkbox, Card, Tag, message, Descriptions, Divider } from 'antd';
import { apiFetch, Project } from '../api';
import ReactECharts from 'echarts-for-react';
import { exportReportPDF, exportReportWord } from '../utils/exportReport';
import { useTranslation } from 'react-i18next';
import logo from '../logo.svg';
import ExportHistoryList, { ExportHistoryItem } from '../components/ExportHistoryList';
import { Modal as AntdModal } from 'antd';
import { useContext } from 'react';
import { ProjectContext } from '../App';



interface AISuggestion {
  id: number;
  title: string;
  desc: string;
  risk: string;
  detail: string;
  type?: string;
}

const API_KEY = 'test-api-key';

const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { projects, inputData, analysisResult: globalAnalysisResult, setCurrentProject } = useContext(ProjectContext);
  const [project, setProject] = useState<Project | null>(null);
  const [projectData, setProjectData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('info');
  // 分析Tab
  const [analyzing, setAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [localAnalysisResult, setLocalAnalysisResult] = useState<string>('');
  const [chartData] = useState({
    times: ['0月', '1月', '3月', '6月', '12月'],
    values: [100, 98, 95, 92, 88],
  });
  // 报告Tab
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [exportOptions, setExportOptions] = useState(['图表', '参数', 'AI建议', '原始数据', '分析结论']);
  const [exporting, setExporting] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  // AI建议Tab
  const [aiSuggestions, setAiSuggestions] = useState<AISuggestion[]>([]);
  const [aiLoading, setAiLoading] = useState(false);
  const [traceModalOpen, setTraceModalOpen] = useState(false);
  const [traceDetail, setTraceDetail] = useState<string>('');
  // 状态持久化
  const suggestionStatusKey = `ai_suggestion_status_${id}`;
  const suggestionFeedbackKey = `ai_suggestion_feedback_${id}`;
  const [suggestionStatus, setSuggestionStatus] = useState<{ [id: number]: string }>(() => {
    try {
      return JSON.parse(localStorage.getItem(suggestionStatusKey) || '{}');
    } catch {
      return {};
    }
  });
  const [suggestionFeedback, setSuggestionFeedback] = useState<{ [id: number]: 'helpful' | 'not_helpful' | undefined }>(() => {
    try {
      return JSON.parse(localStorage.getItem(suggestionFeedbackKey) || '{}');
    } catch {
      return {};
    }
  });
  // 状态持久化副作用
  useEffect(() => {
    localStorage.setItem(suggestionStatusKey, JSON.stringify(suggestionStatus));
  }, [suggestionStatus, suggestionStatusKey]);
  useEffect(() => {
    localStorage.setItem(suggestionFeedbackKey, JSON.stringify(suggestionFeedback));
  }, [suggestionFeedback, suggestionFeedbackKey]);
  // 新增：获取ECharts实例和导出PDF
  const chartRef = React.useRef<any>(null);
  const { t, i18n } = useTranslation();
  const token = localStorage.getItem('token');
  let username = '';
  if (token) {
    try {
      const payload: any = (window as any).jwt_decode ? (window as any).jwt_decode(token) : { sub: '' };
      username = payload.sub;
    } catch {}
  }
  // 获取主题色
  const themeColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color') || '#1976d2';
  // 获取logo为base64
  const [logoBase64, setLogoBase64] = React.useState<string | undefined>(undefined);
  React.useEffect(() => {
    fetch(logo)
      .then(r => r.blob())
      .then(blob => {
        const reader = new FileReader();
        reader.onload = () => setLogoBase64(reader.result as string);
        reader.readAsDataURL(blob);
      });
  }, []);

  // 导出自定义封面参数
  const [coverTitle, setCoverTitle] = useState('');
  const [coverSubtitle, setCoverSubtitle] = useState('');
  const [coverRemark, setCoverRemark] = useState('');
  const [customLogo, setCustomLogo] = useState<string | undefined>(undefined);
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => setCustomLogo(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const [exportHistory, setExportHistory] = useState<ExportHistoryItem[]>([]);
  const [filterExporter, setFilterExporter] = useState('');
  const [filterContent, setFilterContent] = useState('');
  const [filterDate, setFilterDate] = useState('');

  const [selectAllFeedback, setSelectAllFeedback] = useState<'helpful' | 'not_helpful' | ''>('');

  // 新增导出格式选择
  const [exportFormat, setExportFormat] = useState<'pdf' | 'word'>('pdf');

  // 主题切换
  const [dark, setDark] = useState(false);
  useEffect(() => {
    document.body.style.background = dark ? '#222' : '#f6f8fa';
  }, [dark]);

  useEffect(() => {
    if (!id) return;
    setLoading(true);

    // 加载项目基本信息
    apiFetch<Project[]>(`/api/projects`).then(data => {
      const found = data?.find(p => p.id === id);
      if (found) {
        setProject(found);
        setCurrentProject(found);
      } else {
        setError('未找到该项目');
      }
    }).catch(() => setError('加载项目信息失败'));

    // 加载项目数据
    apiFetch<any>(`/api/projects/${id}/data`).then(data => {
      if (data && data.success) {
        setProjectData(data.data);
      } else {
        console.log('项目暂无数据或数据加载失败');
      }
    }).catch(err => {
      console.error('加载项目数据失败:', err);
    }).finally(() => setLoading(false));
  }, [id, setCurrentProject]);

  // 获取导出历史
  useEffect(() => {
    if (!id) return;
    apiFetch<ExportHistoryItem[]>(`/api/report/export/history?project_id=${id}`, {
      headers: { 'x-api-key': API_KEY },
    }).then(data => setExportHistory(data || []));
  }, [id]);

  // 分析API联调
  const startAnalysis = async () => {
    if (!id) return;
    setAnalyzing(true);
    setProgress(0);
    setLocalAnalysisResult('');
    // 模拟进度
    let p = 0;
    const timer = setInterval(() => {
      p += 20;
      setProgress(p);
      if (p >= 100) {
        clearInterval(timer);
      }
    }, 600);
    // 调用后端API
    const res = await apiFetch<{ status: string; progress: number; result: string }>(
      '/api/analysis',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'x-api-key': API_KEY },
        body: JSON.stringify({ project_id: Number(id) }),
      }
    );
    setTimeout(() => {
      setAnalyzing(false);
      setProgress(100);
      if (res && res.status === 'done') {
        setLocalAnalysisResult(res.result);
        message.success('分析完成');
      } else {
        setLocalAnalysisResult('分析失败');
        message.error('分析失败');
      }
    }, 3200);
  };

  // 导出报告API联调
  const handleExport = async () => {
    if (!id) return;
    setExporting(true);
    setExportModalOpen(false);
    try {
      const res = await apiFetch<{ url: string }>(
        '/api/report/export',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'x-api-key': API_KEY },
          body: JSON.stringify({ project_id: Number(id), options: exportOptions, lang: i18n.language, format: exportFormat }),
        }
      );
      if (res && res.url) {
        message.success('报告导出成功');
        window.open(res.url, '_blank');
      } else {
        message.error('报告导出失败');
      }
    } catch (e) {
      message.error('报告导出异常');
    } finally {
      setExporting(false);
    }
  };

  // AI建议API联调
  useEffect(() => {
    if (activeTab === 'ai' && id) {
      setAiLoading(true);
      apiFetch<AISuggestion[]>(`/api/ai/suggestions?project_id=${id}&lang=zh`, {
        headers: { 'x-api-key': API_KEY },
      })
        .then(data => setAiSuggestions(data || []))
        .finally(() => setAiLoading(false));
    }
  }, [activeTab, id]);

  // AI建议交互
  const handleSuggestionAction = (id: number, action: string) => {
    setSuggestionStatus(s => ({ ...s, [id]: action }));
    message.info(t(action === 'adopt' ? '已采纳该建议' : action === 'ignore' ? '已忽略该建议' : '已标记为疑问'));
  };
  const handleSuggestionFeedback = async (id: number, feedback: 'helpful' | 'not_helpful') => {
    setSuggestionFeedback(f => ({ ...f, [id]: feedback }));
    await apiFetch('/api/ai/suggestions/feedback', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([{ suggestion_id: id, feedback }]),
    });
    message.success(t(feedback === 'helpful' ? '感谢您的反馈！' : '我们会持续优化建议'));
  };

  // 新增：获取ECharts实例和导出PDF
  const handleExportPDF = async () => {
    setExporting(true);
    try {
      let chartImage = undefined;
      if (exportOptions.includes('图表') && chartRef.current) {
        chartImage = chartRef.current.getEchartsInstance().getDataURL({ type: 'png', pixelRatio: 2 });
      }
      const created = project?.created || project?.created_at || '';
      const params = { 项目名: project?.name, 状态: project?.status, 创建时间: created };
      await exportReportPDF({
        projectName: project?.name || '项目',
        exportOptions,
        chartImage,
        params,
        aiSuggestions: aiSuggestions.map(s => ({ title: s.title, desc: s.desc, risk: s.risk })),
        username,
        themeColor,
        lang: i18n.language as 'zh' | 'en',
        logoBase64: customLogo || logoBase64,
        coverTitle,
        coverSubtitle,
        coverRemark,
      });
    } catch (e) {
      message.error('PDF导出异常');
    } finally {
      setExporting(false);
    }
  };

  const handleExportWord = async () => {
    setExporting(true);
    try {
      let chartImage = undefined;
      if (exportOptions.includes('图表') && chartRef.current) {
        chartImage = chartRef.current.getEchartsInstance().getDataURL({ type: 'png', pixelRatio: 2 });
      }
      const created = project?.created || project?.created_at || '';
      const params = { 项目名: project?.name, 状态: project?.status, 创建时间: created };
      await exportReportWord({
        projectName: project?.name || '项目',
        exportOptions,
        chartImage,
        params,
        aiSuggestions: aiSuggestions.map(s => ({ title: s.title, desc: s.desc, risk: s.risk })),
        username,
        themeColor,
        lang: i18n.language as 'zh' | 'en',
        logoBase64: customLogo || logoBase64,
        coverTitle,
        coverSubtitle,
        coverRemark,
      });
    } catch (e) {
      message.error('Word导出异常');
    } finally {
      setExporting(false);
    }
  };

  // 导出内容预览渲染
  const renderExportPreview = () => {
    return (
      <div style={{ maxWidth: 600 }}>
        {exportOptions.includes('原始数据') && (
          <div style={{ marginBottom: 16 }}>
            <b>{i18n.language === 'zh' ? '原始数据' : 'Raw Data'}：</b>
            <table style={{ borderCollapse: 'collapse', width: '100%', marginTop: 4 }}>
              <thead><tr><th>时间</th><th>含量(%)</th></tr></thead>
              <tbody>
                <tr><td>0月</td><td>100</td></tr>
                <tr><td>1月</td><td>98</td></tr>
                <tr><td>3月</td><td>95</td></tr>
                <tr><td>6月</td><td>92</td></tr>
                <tr><td>12月</td><td>88</td></tr>
              </tbody>
            </table>
          </div>
        )}
        {exportOptions.includes('分析结论') && (
          <div style={{ marginBottom: 16 }}>
            <b>{i18n.language === 'zh' ? '分析结论' : 'Conclusion'}：</b>
            <div>{i18n.language === 'zh' ? '本药物在12个月内含量下降至88%，符合稳定性要求。' : 'The drug content decreased to 88% within 12 months, meeting stability requirements.'}</div>
          </div>
        )}
        {exportOptions.includes('图表') && (
          <div style={{ marginBottom: 16 }}>
            <b>{i18n.language === 'zh' ? '稳定性趋势图' : 'Stability Trend Chart'}：</b>
            <div style={{ width: 320, height: 80, background: '#eee', textAlign: 'center', lineHeight: '80px', color: '#888', marginTop: 4 }}>图表预览</div>
          </div>
        )}
        {exportOptions.includes('参数') && (
          <div style={{ marginBottom: 16 }}>
            <b>{i18n.language === 'zh' ? '参数信息' : 'Parameters'}：</b>
            <div>项目名：{project?.name}，状态：{project?.status}，创建时间：{project?.created || project?.created_at || ''}</div>
          </div>
        )}
        {exportOptions.includes('AI建议') && (
          <div style={{ marginBottom: 16 }}>
            <b>{i18n.language === 'zh' ? 'AI智能建议' : 'AI Suggestions'}：</b>
            <ul>
              {aiSuggestions.map((s, i) => <li key={i}>{s.title} - {s.risk}</li>)}
            </ul>
          </div>
        )}
      </div>
    );
  };

  const submitBatchFeedback = useCallback(async (feedbackType: 'helpful' | 'not_helpful') => {
    const feedbacks = aiSuggestions.map(s => ({ suggestion_id: s.id, feedback: feedbackType }));
    await apiFetch('/api/ai/suggestions/feedback', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(feedbacks),
    });
  }, [aiSuggestions]);

  // 假设后端返回团队反馈统计（模拟）
  const teamFeedbackStats = aiSuggestions.map(s => ({
    id: s.id,
    helpful: Math.floor(Math.random() * 5),
    notHelpful: Math.floor(Math.random() * 3),
  }));

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: dark ? '#222' : '#f6f8fa', minHeight: '100vh', color: dark ? '#fff' : '#222' }}>
      <Button onClick={() => setDark(d => !d)} style={{ marginBottom: 16 }}>{dark ? '切换浅色' : '切换深色'}</Button>
      <Card title="项目详情" bordered>
        <Descriptions column={1} bordered>
          <Descriptions.Item label="项目名">{project?.name}</Descriptions.Item>
          <Descriptions.Item label="状态">{project?.status}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{project?.created || project?.created_at || ''}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <div style={{ marginBottom: 16, fontWeight: 500 }}>项目数据</div>
        {projectData ? (
          <div style={{ background: dark ? '#333' : '#f8f8f8', padding: 12, borderRadius: 4 }}>
            <div style={{ marginBottom: 12 }}>
              <strong>药物信息：</strong>
              <div>药物名称: {projectData.drug_name || '未填写'}</div>
              <div>CAS号: {projectData.cas_number || '未填写'}</div>
              <div>分子式: {projectData.molecular_formula || '未填写'}</div>
              <div>SMILES: {projectData.smiles || '未填写'}</div>
              <div>类别: {projectData.category || '未填写'}</div>
              <div>描述: {projectData.description || '未填写'}</div>
            </div>

            {projectData.formulation && projectData.formulation.length > 0 && (
              <div style={{ marginBottom: 12 }}>
                <strong>配方信息：</strong>
                {projectData.formulation.map((item: any, index: number) => (
                  <div key={index}>• {item.name}: {item.amount || '未指定'}mg</div>
                ))}
              </div>
            )}

            {projectData.packaging_storage && (
              <div style={{ marginBottom: 12 }}>
                <strong>包装储存：</strong>
                <div>包装: {projectData.packaging_storage.packaging || '未填写'}</div>
                <div>储存: {projectData.packaging_storage.storage || '未填写'}</div>
              </div>
            )}

            {projectData.production_process && (
              <div style={{ marginBottom: 12 }}>
                <strong>生产工艺：</strong> {projectData.production_process}
              </div>
            )}

            {projectData.notes && (
              <div>
                <strong>备注：</strong> {projectData.notes}
              </div>
            )}
          </div>
        ) : (
          <div style={{ color: '#999', fontStyle: 'italic' }}>该项目暂无数据输入</div>
        )}
        <Divider />
        <div style={{ marginBottom: 16, fontWeight: 500 }}>分析结果</div>
        {globalAnalysisResult && !globalAnalysisResult.error ? (
          <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: 16 }}>
            <thead><tr><th>时间</th><th>含量(%)</th><th>风险</th></tr></thead>
            <tbody>
              {(globalAnalysisResult.data || []).map((row: any, idx: number) => (
                <tr key={idx} style={{ background: row.risk === '高' ? '#ffeaea' : row.risk === '中' ? '#fffbe6' : '#eaffea' }}>
                  <td>{row.time}</td><td>{row.value}</td><td>{row.risk}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div style={{ color: 'red' }}>{globalAnalysisResult?.error || '暂无分析结果'}</div>
        )}
        <Button type="primary" style={{ marginRight: 8 }}>导出PDF</Button>
        <Button type="default">导出Excel</Button>
      </Card>
    </div>
  );
};

export default ProjectDetailPage; 