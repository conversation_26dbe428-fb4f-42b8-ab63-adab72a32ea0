import React, { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, message } from 'antd';
import { login, TokenResponse } from '../api';
import { jwtDecode } from 'jwt-decode';

interface LoginPageProps {
  setRole: Dispatch<SetStateAction<'' | 'admin' | 'user'>>;
}

const LoginPage: React.FC<LoginPageProps> = ({ setRole }) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (localStorage.getItem('token')) {
      navigate('/');
    }
  }, [navigate]);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      console.log('开始登录请求:', values);
      const response = await login(values);
      console.log('登录响应:', response);

      if (response.data && response.data.access_token) {
        const token = response.data.access_token;
        localStorage.setItem('token', token);

        // 解码token并设置角色
        try {
          const payload: any = jwtDecode(token);
          console.log('Token payload:', payload);
          setRole(payload.role || 'user');
        } catch (e) {
          console.error("Token decoding failed", e);
          setRole('user'); // Fallback role
        }

        message.success('登录成功');
        navigate('/');
      } else {
        console.error('登录响应格式错误:', response);
        message.error('登录失败');
      }
    } catch (error: any) {
      console.error('登录错误:', error);
      const errorMessage = error.response?.data?.detail || error.message || '用户名或密码错误';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const [form] = Form.useForm();

  const fillTestCredentials = () => {
    form.setFieldsValue({
      username: 'admin',
      password: 'admin123'
    });
  };

  return (
    <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f6f8fa' }}>
      <Card style={{ minWidth: 320, borderRadius: 12, fontFamily: 'SimSun, serif' }}>
        <h2 style={{ textAlign: 'center', marginBottom: 24 }}>登录</h2>

        {/* 测试账号提示 */}
        <div style={{ marginBottom: 16, padding: 12, background: '#f0f9ff', border: '1px solid #bae6fd', borderRadius: 6 }}>
          <div style={{ fontSize: 12, color: '#0369a1', marginBottom: 8 }}>测试账号：</div>
          <div style={{ fontSize: 12, color: '#0369a1' }}>
            管理员：admin / admin123<br/>
            普通用户：user / user123
          </div>
          <Button
            size="small"
            type="link"
            onClick={fillTestCredentials}
            style={{ padding: 0, height: 'auto', fontSize: 12 }}
          >
            一键填入管理员账号
          </Button>
        </div>

        <Form form={form} onFinish={onFinish} layout="vertical">
          <Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名' }]}>
            <Input autoFocus autoComplete="off" placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item name="password" label="密码" rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password autoComplete="off" placeholder="请输入密码" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block style={{ borderRadius: 6 }}>登录</Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default LoginPage; 