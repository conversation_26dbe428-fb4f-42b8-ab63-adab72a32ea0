#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""
import os
import sys
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 将项目根目录添加到Python路径
sys.path.insert(0, str(BASE_DIR))

# 导入数据库配置和模型
from app.config.database import engine
from app.models.base import Base
from app.models import (
    UserORM,
    APIKeyORM,
    OperationLogORM,
    ExportHistory,
    AISuggestion,
    ProjectORM,
    DrugORM,
    DegradationKineticModelORM,
    ExcipientInteractionORM
)
from app.models.excipient import populate_initial_interactions

def init_db():
    """
    初始化数据库
    """
    print("===== 初始化数据库 =====")
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✓ 数据库表创建成功")
        
        # Populate initial interaction data
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        try:
            populate_initial_interactions(session)
            print("✓ 辅料相互作用数据填充成功")
        except Exception as e:
            print(f"✗ 辅料相互作用数据填充失败: {e}")
            session.rollback()
        finally:
            session.close()
        
        # 检查数据库文件是否存在
        db_path = os.path.join(BASE_DIR, 'test.db')
        if os.path.exists(db_path):
            print(f"✓ 数据库文件已创建: {db_path}")
            print(f"✓ 数据库文件大小: {os.path.getsize(db_path)} 字节")
        else:
            print(f"✗ 数据库文件未找到: {db_path}")
        
        # 检查是否需要迁移现有数据
        migrate_existing_data()
            
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def migrate_existing_data():
    """迁移现有数据以支持新字段"""
    try:
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 检查是否有现有项目需要迁移
        projects = session.query(ProjectORM).all()
        if projects:
            print(f"发现 {len(projects)} 个现有项目，检查是否需要迁移...")
            
            for project in projects:
                # 如果项目缺少新字段的默认值，则设置它们
                updated = False
                
                if not hasattr(project, 'drug_name') or project.drug_name is None:
                    # 这些字段在新模型中已经设置为nullable=True，所以不需要特殊处理
                    pass
                
                if updated:
                    session.commit()
                    print(f"✓ 项目 {project.name} 迁移完成")
        
        session.close()
        print("✓ 数据迁移检查完成")
        
    except Exception as e:
        print(f"⚠ 数据迁移检查失败: {e}")
        # 不抛出异常，因为这可能是首次初始化

if __name__ == "__main__":
    init_db() 