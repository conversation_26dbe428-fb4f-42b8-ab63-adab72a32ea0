from sqlalchemy import Column, Integer, String, Float, Text, DateTime, Boolean, JSON
from sqlalchemy.orm import relationship
import datetime
# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base

class DrugORM(Base):
    __tablename__ = 'drugs'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    synonyms = Column(JSON, nullable=True) # List of alternative names
    formula = Column(String, nullable=True)
    cas = Column(String, unique=True, index=True, nullable=True) # Should be unique
    smiles = Column(String, nullable=True)
    inchi = Column(String, nullable=True)  # InChI标识符
    inchi_key = Column(String, nullable=True)  # InChI Key
    structure_image_url = Column(String, nullable=True)
    
    # 物理化学性质
    molecular_weight = Column(Float, nullable=True)
    logp = Column(Float, nullable=True)  # 辛醇-水分配系数
    pka = Column(JSON, nullable=True)  # pKa值列表
    solubility = Column(String, nullable=True)  # 溶解性描述
    melting_point = Column(Float, nullable=True)  # 熔点(°C)
    
    # 结构特征
    hydrogen_bond_donors = Column(Integer, nullable=True)  # 氢键供体数
    hydrogen_bond_acceptors = Column(Integer, nullable=True)  # 氢键受体数
    rotatable_bonds = Column(Integer, nullable=True)  # 可旋转键数
    aromatic_rings = Column(Integer, nullable=True)  # 芳香环数
    heavy_atoms = Column(Integer, nullable=True)  # 重原子数
    tpsa = Column(Float, nullable=True)  # 拓扑极性表面积
    
    # 稳定性相关属性
    hygroscopicity = Column(String, nullable=True)  # 吸湿性
    photosensitivity = Column(Boolean, nullable=True)  # 光敏感性
    thermal_sensitivity = Column(Boolean, nullable=True)  # 热敏感性
    oxidation_sensitivity = Column(Boolean, nullable=True)  # 氧化敏感性
    hydrolysis_sensitivity = Column(Boolean, nullable=True)  # 水解敏感性
    
    # 降解途径
    degradation_pathways = Column(JSON, nullable=True)  # 可能的降解途径
    
    # 功能团标记
    has_ester = Column(Boolean, nullable=True)  # 含有酯基
    has_amide = Column(Boolean, nullable=True)  # 含有酰胺基
    has_carboxylic_acid = Column(Boolean, nullable=True)  # 含有羧酸基
    has_amine = Column(Boolean, nullable=True)  # 含有胺基
    has_phenol = Column(Boolean, nullable=True)  # 含有酚羟基
    has_aldehyde = Column(Boolean, nullable=True)  # 含有醛基
    has_ketone = Column(Boolean, nullable=True)  # 含有酮基
    has_thiol = Column(Boolean, nullable=True)  # 含有巯基
    has_sulfide = Column(Boolean, nullable=True)  # 含有硫醚
    has_nitro = Column(Boolean, nullable=True)  # 含有硝基
    
    # 其他信息
    therapeutic_category = Column(String, nullable=True)  # 治疗类别
    data_source = Column(String, nullable=True) # e.g., 'PubChem', 'In-house'
    pharmacophore = Column(JSON, nullable=True)  # 药效团
    references = Column(JSON, nullable=True)  # 参考文献
    notes = Column(Text, nullable=True)  # 备注
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Relationship to Project
    projects = relationship("ProjectORM", back_populates="drug")
    
    # 计算属性和方法可以在ORM之外实现 