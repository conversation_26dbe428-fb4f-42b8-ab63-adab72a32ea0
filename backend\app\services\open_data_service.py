"""
开放数据源服务
从免费公开的数据源获取药物和辅料信息
"""
import os
import json
import csv
import sqlite3
import logging
import requests
import zipfile
import pandas as pd
from typing import Dict, List, Optional, Any
from pathlib import Path
import aiohttp
import asyncio

logger = logging.getLogger(__name__)

# 数据缓存目录
DATA_DIR = Path("data/open_sources")
DATA_DIR.mkdir(parents=True, exist_ok=True)

# 开放数据源配置
OPEN_DATA_SOURCES = {
    "fda_inactive_ingredients": {
        "url": "https://www.fda.gov/media/80614/download",
        "description": "FDA Inactive Ingredient Database",
        "local_file": DATA_DIR / "fda_inactive_ingredients.zip",
        "extracted_dir": DATA_DIR / "fda_inactive_ingredients"
    },
    "guidetopharmacology": {
        "ligands_csv": "https://www.guidetopharmacology.org/DATA/ligands.csv",
        "targets_csv": "https://www.guidetopharmacology.org/DATA/targets_and_families.csv",
        "interactions_csv": "https://www.guidetopharmacology.org/DATA/interactions.csv",
        "description": "Guide to PHARMACOLOGY open data",
        "local_dir": DATA_DIR / "guidetopharmacology"
    }
}

class OpenDataService:
    """开放数据源服务类"""
    
    def __init__(self):
        self.db_path = DATA_DIR / "excipients.db"
        self._ensure_database()
    
    def _ensure_database(self):
        """确保数据库存在并创建必要的表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建增强版辅料信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS excipients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                -- 基本信息
                name TEXT NOT NULL,
                cas_number TEXT,
                synonyms TEXT,
                category TEXT,
                function TEXT,
                
                -- 化学信息
                molecular_formula TEXT,
                molecular_weight REAL,
                smiles TEXT,
                
                -- 理化性质
                appearance TEXT,
                melting_point TEXT,
                boiling_point TEXT,
                density TEXT,
                pka TEXT,
                logp REAL,
                solubility_water TEXT,
                solubility_organic TEXT,
                hygroscopicity TEXT,
                particle_size TEXT,
                specific_surface_area TEXT,
                angle_of_repose TEXT,
                compressibility_index TEXT,
                
                -- 稳定性与贮藏
                stability_conditions TEXT,
                storage_requirements TEXT,
                incompatibilities TEXT,
                light_sensitivity TEXT,
                moisture_sensitivity TEXT,
                temperature_sensitivity TEXT,
                oxidation_sensitivity TEXT,
                
                -- 应用信息
                typical_concentration TEXT,
                dosage_forms TEXT,
                
                -- 安全性
                safety_profile TEXT,
                regulatory_status TEXT,
                
                -- 来源和更新
                source TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                -- 药典信息
                usp_nf_info TEXT,
                ph_eur_info TEXT,
                jp_info TEXT,
                chp_info TEXT,
                
                -- 其他
                description TEXT,
                notes TEXT,
                
                UNIQUE(name, cas_number)
            )
        """)
        
        # 创建药物-辅料相互作用表（增强版）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS drug_excipient_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                drug_name TEXT NOT NULL,
                drug_functional_groups TEXT,
                excipient_name TEXT NOT NULL,
                excipient_functional_groups TEXT,
                interaction_type TEXT,
                severity TEXT,
                mechanism TEXT,
                conditions TEXT,
                consequences TEXT,
                prevention_strategies TEXT,
                evidence TEXT,
                reference TEXT,
                source TEXT,
                confidence_score REAL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建文献证据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS literature_evidence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                drug_name TEXT,
                excipient_name TEXT,
                title TEXT,
                authors TEXT,
                journal TEXT,
                year INTEGER,
                pmid TEXT,
                doi TEXT,
                abstract TEXT,
                findings TEXT,
                relevance_score REAL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建辅料物理性质详情表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS excipient_physical_properties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                excipient_id INTEGER,
                property_name TEXT,
                property_value TEXT,
                unit TEXT,
                temperature TEXT,
                conditions TEXT,
                reference TEXT,
                FOREIGN KEY (excipient_id) REFERENCES excipients(id)
            )
        """)
        
        # 创建辅料功能应用表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS excipient_applications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                excipient_id INTEGER,
                dosage_form TEXT,
                function_in_formulation TEXT,
                typical_concentration_min REAL,
                typical_concentration_max REAL,
                unit TEXT,
                notes TEXT,
                FOREIGN KEY (excipient_id) REFERENCES excipients(id)
            )
        """)
        
        # 创建辅料稳定性数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS excipient_stability_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                excipient_id INTEGER,
                test_condition TEXT,
                parameter_tested TEXT,
                initial_value TEXT,
                final_value TEXT,
                change_percentage REAL,
                time_period TEXT,
                acceptable_change TEXT,
                reference TEXT,
                FOREIGN KEY (excipient_id) REFERENCES excipients(id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    async def download_fda_data(self):
        """下载FDA Inactive Ingredient数据"""
        try:
            fda_config = OPEN_DATA_SOURCES["fda_inactive_ingredients"]
            
            # 检查是否已下载
            if fda_config["local_file"].exists():
                logger.info("FDA数据已存在，跳过下载")
                return True
            
            logger.info("开始下载FDA Inactive Ingredient数据...")
            async with aiohttp.ClientSession() as session:
                async with session.get(fda_config["url"]) as response:
                    if response.status == 200:
                        content = await response.read()
                        with open(fda_config["local_file"], 'wb') as f:
                            f.write(content)
                        logger.info("FDA数据下载完成")
                        
                        # 解压文件
                        with zipfile.ZipFile(fda_config["local_file"], 'r') as zip_ref:
                            zip_ref.extractall(fda_config["extracted_dir"])
                        
                        return True
                    else:
                        logger.error(f"下载FDA数据失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"下载FDA数据时出错: {e}")
            return False
    
    async def download_gtopdb_data(self):
        """下载Guide to PHARMACOLOGY数据"""
        try:
            gtopdb_config = OPEN_DATA_SOURCES["guidetopharmacology"]
            gtopdb_config["local_dir"].mkdir(parents=True, exist_ok=True)
            
            async with aiohttp.ClientSession() as session:
                for data_type, url in gtopdb_config.items():
                    if data_type in ["description", "local_dir"]:
                        continue
                    
                    local_file = gtopdb_config["local_dir"] / f"{data_type}"
                    
                    # 检查是否已下载
                    if local_file.exists():
                        logger.info(f"{data_type}已存在，跳过下载")
                        continue
                    
                    logger.info(f"下载{data_type}...")
                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.text()
                            with open(local_file, 'w', encoding='utf-8') as f:
                                f.write(content)
                            logger.info(f"{data_type}下载完成")
                        else:
                            logger.error(f"下载{data_type}失败: {response.status}")
            
            return True
            
        except Exception as e:
            logger.error(f"下载GtoPdb数据时出错: {e}")
            return False
    
    def parse_fda_data(self):
        """解析FDA数据并导入数据库"""
        try:
            # 查找解压后的文件
            extracted_dir = OPEN_DATA_SOURCES["fda_inactive_ingredients"]["extracted_dir"]
            
            # FDA数据通常是Excel或CSV格式
            for file_path in extracted_dir.glob("*.xlsx"):
                df = pd.read_excel(file_path)
                self._import_fda_to_db(df)
                break
            else:
                for file_path in extracted_dir.glob("*.csv"):
                    df = pd.read_csv(file_path)
                    self._import_fda_to_db(df)
                    break
                    
        except Exception as e:
            logger.error(f"解析FDA数据时出错: {e}")
    
    def _import_fda_to_db(self, df):
        """将FDA数据导入数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for _, row in df.iterrows():
            try:
                # 根据FDA数据的实际列名调整
                name = str(row.get('Ingredient Name', row.get('INGREDIENT_NAME', '')))
                cas = str(row.get('CAS', row.get('CAS_NUMBER', '')))
                function = str(row.get('Function', row.get('FUNCTION', '')))
                
                if name:
                    cursor.execute("""
                        INSERT OR IGNORE INTO excipients 
                        (name, cas_number, function, source)
                        VALUES (?, ?, ?, ?)
                    """, (name, cas, function, 'FDA'))
                    
            except Exception as e:
                logger.error(f"导入FDA数据行时出错: {e}")
                continue
        
        conn.commit()
        conn.close()
        logger.info(f"成功导入{len(df)}条FDA数据")
    
    def get_excipient_info(self, name: str = None, cas: str = None) -> Optional[Dict[str, Any]]:
        """从本地数据库获取辅料信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if name:
                cursor.execute("""
                    SELECT * FROM excipients 
                    WHERE LOWER(name) = LOWER(?) OR synonyms LIKE ?
                """, (name, f'%{name}%'))
            elif cas:
                cursor.execute("""
                    SELECT * FROM excipients 
                    WHERE cas_number = ?
                """, (cas,))
            else:
                return None
            
            row = cursor.fetchone()
            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            
            return None
            
        finally:
            conn.close()
    
    def get_excipient_synonyms(self, name: str) -> List[str]:
        """获取辅料的同义词"""
        info = self.get_excipient_info(name=name)
        if info and info.get('synonyms'):
            return [s.strip() for s in info['synonyms'].split(',')]
        return []
    
    def get_excipient_category(self, name: str) -> Optional[str]:
        """获取辅料的类别"""
        info = self.get_excipient_info(name=name)
        return info.get('category') if info else None
    
    def search_interactions(self, drug_name: str = None, 
                          excipient_name: str = None) -> List[Dict[str, Any]]:
        """搜索药物-辅料相互作用"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            query = "SELECT * FROM drug_excipient_interactions WHERE 1=1"
            params = []
            
            if drug_name:
                query += " AND LOWER(drug_name) = LOWER(?)"
                params.append(drug_name)
            
            if excipient_name:
                query += " AND LOWER(excipient_name) = LOWER(?)"
                params.append(excipient_name)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            if rows:
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
            
            return []
            
        finally:
            conn.close()
    
    async def initialize_data(self):
        """初始化所有开放数据源"""
        logger.info("开始初始化开放数据源...")
        
        # 下载FDA数据
        await self.download_fda_data()
        self.parse_fda_data()
        
        # 下载Guide to PHARMACOLOGY数据
        await self.download_gtopdb_data()
        
        # 导入初始知识库（基于药物化学原理）
        self._import_initial_knowledge()
        
        logger.info("开放数据源初始化完成")
    
    def _import_initial_knowledge(self):
        """导入基于药物化学原理的初始知识库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 常见的药物-辅料相互作用知识
        interactions = [
            {
                "drug_name": "含胺基药物",
                "excipient_name": "还原糖（乳糖、葡萄糖）",
                "interaction_type": "美拉德反应",
                "severity": "中等",
                "mechanism": "胺基与还原糖的羰基发生缩合反应，生成席夫碱，导致药物降解和变色",
                "evidence": "文献支持",
                "reference": "Wirth DD et al. J Pharm Sci. 1998;87(1):31-39"
            },
            {
                "drug_name": "酸性药物",
                "excipient_name": "碳酸钙",
                "interaction_type": "酸碱反应",
                "severity": "高",
                "mechanism": "酸性药物与碱性辅料反应，影响药物稳定性和溶出",
                "evidence": "理论预测",
                "reference": "药物化学原理"
            },
            {
                "drug_name": "酯类药物",
                "excipient_name": "硬脂酸镁",
                "interaction_type": "水解催化",
                "severity": "中等",
                "mechanism": "硬脂酸镁中的金属离子可能催化酯键水解",
                "evidence": "案例报告",
                "reference": "Pharmaceutical Development and Technology"
            }
        ]
        
        for interaction in interactions:
            cursor.execute("""
                INSERT OR IGNORE INTO drug_excipient_interactions
                (drug_name, excipient_name, interaction_type, severity, 
                 mechanism, evidence, reference, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                interaction["drug_name"],
                interaction["excipient_name"],
                interaction["interaction_type"],
                interaction["severity"],
                interaction["mechanism"],
                interaction["evidence"],
                interaction["reference"],
                "知识库"
            ))
        
        conn.commit()
        conn.close()

# 创建全局实例
open_data_service = OpenDataService() 