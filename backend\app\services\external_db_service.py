"""
外部数据库服务
用于从PubChem、ChEMBL等外部数据库获取药物信息
"""
import aiohttp
import asyncio
import logging
from typing import Optional, Dict, Any
import json
import requests

logger = logging.getLogger(__name__)

# API配置
PUBCHEM_BASE_URL = "https://pubchem.ncbi.nlm.nih.gov/rest/pug"
CHEMBL_BASE_URL = "https://www.ebi.ac.uk/chembl/api/data"

def get_drug_info_from_pubchem_sync(name: Optional[str] = None, 
                                    cas: Optional[str] = None,
                                    smiles: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """从PubChem获取药物信息 (同步版本)"""
    try:
        # 构建查询URL
        if name:
            search_url = f"{PUBCHEM_BASE_URL}/compound/name/{name}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
        elif cas:
            search_url = f"{PUBCHEM_BASE_URL}/compound/name/{cas}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
        elif smiles:
            search_url = f"{PUBCHEM_BASE_URL}/compound/smiles/{smiles}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
        else:
            return None
        
        response = requests.get(search_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if "PropertyTable" in data and "Properties" in data["PropertyTable"]:
                props = data["PropertyTable"]["Properties"][0]
                
                # 获取CID以构建结构图URL
                cid = props.get("CID", "")
                structure_url = f"https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid={cid}&t=l" if cid else ""
                
                return {
                    "name": props.get("IUPACName", name or ""),
                    "cas": cas or "",
                    "formula": props.get("MolecularFormula", ""),
                    "molecular_weight": props.get("MolecularWeight", ""),
                    "smiles": props.get("CanonicalSMILES", smiles or ""),
                    "structure_image_url": structure_url,
                    "source": "PubChem"
                }
            
    except requests.exceptions.Timeout:
        logger.warning("PubChem API请求超时 (sync)")
    except Exception as e:
        logger.error(f"PubChem API错误 (sync): {e}")
    
    return None

async def get_drug_info_from_pubchem(name: Optional[str] = None, 
                                   cas: Optional[str] = None,
                                   smiles: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """从PubChem获取药物信息"""
    try:
        async with aiohttp.ClientSession() as session:
            # 构建查询URL
            if name:
                search_url = f"{PUBCHEM_BASE_URL}/compound/name/{name}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
            elif cas:
                search_url = f"{PUBCHEM_BASE_URL}/compound/name/{cas}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
            elif smiles:
                search_url = f"{PUBCHEM_BASE_URL}/compound/smiles/{smiles}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,IUPACName/JSON"
            else:
                return None
            
            async with session.get(search_url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if "PropertyTable" in data and "Properties" in data["PropertyTable"]:
                        props = data["PropertyTable"]["Properties"][0]
                        
                        # 获取CID以构建结构图URL
                        cid = props.get("CID", "")
                        structure_url = f"https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid={cid}&t=l" if cid else ""
                        
                        return {
                            "name": props.get("IUPACName", name or ""),
                            "cas": cas or "",
                            "formula": props.get("MolecularFormula", ""),
                            "molecular_weight": props.get("MolecularWeight", ""),
                            "smiles": props.get("CanonicalSMILES", smiles or ""),
                            "structure_image_url": structure_url,
                            "source": "PubChem"
                        }
                    
    except asyncio.TimeoutError:
        logger.warning("PubChem API请求超时")
    except Exception as e:
        logger.error(f"PubChem API错误: {e}")
    
    return None

async def get_drug_info_from_chembl(name: Optional[str] = None,
                                  cas: Optional[str] = None,
                                  smiles: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """从ChEMBL获取药物信息"""
    try:
        # 注意：ChEMBL API需要申请密钥才能使用
        # 这里提供基本框架
        async with aiohttp.ClientSession() as session:
            # ChEMBL搜索实现
            search_params = {}
            if name:
                search_params["molecule_synonyms__icontains"] = name
            elif smiles:
                search_params["molecule_structures__canonical_smiles"] = smiles
            
            if search_params:
                # 实际使用需要配置API密钥
                search_url = f"{CHEMBL_BASE_URL}/molecule"
                async with session.get(search_url, params=search_params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("molecules"):
                            mol = data["molecules"][0]
                            return {
                                "name": mol.get("pref_name", name or ""),
                                "cas": cas or "",
                                "formula": mol.get("molecule_properties", {}).get("full_molformula", ""),
                                "molecular_weight": mol.get("molecule_properties", {}).get("full_mwt", ""),
                                "smiles": mol.get("molecule_structures", {}).get("canonical_smiles", ""),
                                "drug_class": mol.get("molecule_type", ""),
                                "source": "ChEMBL"
                            }
    
    except asyncio.TimeoutError:
        logger.warning("ChEMBL API请求超时")
    except Exception as e:
        logger.error(f"ChEMBL API错误: {e}")
    
    return None

# 常见药物数据库（本地数据库）
LOCAL_DRUG_DATABASE = {
    # 中文名称映射
    "阿司匹林": {
        "name": "阿司匹林",
        "english_name": "Aspirin",
        "cas_number": "50-78-2",
        "molecular_formula": "C9H8O4",
        "molecular_weight": 180.16,
        "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
        "category": "解热镇痛药",
        "description": "非甾体抗炎药，用于解热镇痛",
        "melting_point": "135°C",
        "solubility": "微溶于水，易溶于乙醇",
        "stability": "在湿热条件下易水解"
    },
    "对乙酰氨基酚": {
        "name": "对乙酰氨基酚",
        "english_name": "Acetaminophen",
        "cas_number": "103-90-2",
        "molecular_formula": "C8H9NO2",
        "molecular_weight": 151.16,
        "smiles": "CC(=O)NC1=CC=C(C=C1)O",
        "category": "解热镇痛药",
        "description": "解热镇痛药，肝毒性较小",
        "melting_point": "169-170.5°C",
        "solubility": "溶于热水，微溶于冷水",
        "stability": "稳定，避光保存"
    },
    "布洛芬": {
        "name": "布洛芬",
        "english_name": "Ibuprofen",
        "cas_number": "15687-27-1",
        "molecular_formula": "C13H18O2",
        "molecular_weight": 206.28,
        "smiles": "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O",
        "category": "非甾体抗炎药",
        "description": "非甾体抗炎药，用于消炎镇痛",
        "melting_point": "75-77°C",
        "solubility": "几乎不溶于水，溶于乙醇",
        "stability": "稳定，避光保存"
    },
    "咖啡因": {
        "name": "咖啡因",
        "english_name": "Caffeine",
        "cas_number": "58-08-2",
        "molecular_formula": "C8H10N4O2",
        "molecular_weight": 194.19,
        "smiles": "CN1C=NC2=C1C(=O)N(C(=O)N2C)C",
        "category": "中枢神经兴奋药",
        "description": "中枢神经兴奋剂，常用于复方制剂",
        "melting_point": "235-237°C",
        "solubility": "微溶于水，溶于热水",
        "stability": "稳定，避光保存"
    },
    "维生素C": {
        "name": "维生素C",
        "english_name": "Ascorbic Acid",
        "cas_number": "50-81-7",
        "molecular_formula": "C6H8O6",
        "molecular_weight": 176.12,
        "smiles": "C([C@@H]([C@@H]([C@H](C(=O)O)O)O)O)O",
        "category": "维生素类",
        "description": "水溶性维生素，抗氧化剂",
        "melting_point": "190-192°C",
        "solubility": "易溶于水",
        "stability": "不稳定，易氧化，避光保存"
    },
    # 英文名称映射
    "aspirin": {
        "name": "阿司匹林",
        "english_name": "Aspirin",
        "cas_number": "50-78-2",
        "molecular_formula": "C9H8O4",
        "molecular_weight": 180.16,
        "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
        "category": "解热镇痛药",
        "description": "非甾体抗炎药，用于解热镇痛",
        "melting_point": "135°C",
        "solubility": "微溶于水，易溶于乙醇",
        "stability": "在湿热条件下易水解"
    },
    "acetaminophen": {
        "name": "对乙酰氨基酚",
        "english_name": "Acetaminophen",
        "cas_number": "103-90-2",
        "molecular_formula": "C8H9NO2",
        "molecular_weight": 151.16,
        "smiles": "CC(=O)NC1=CC=C(C=C1)O",
        "category": "解热镇痛药",
        "description": "解热镇痛药，肝毒性较小",
        "melting_point": "169-170.5°C",
        "solubility": "溶于热水，微溶于冷水",
        "stability": "稳定，避光保存"
    },
    "ibuprofen": {
        "name": "布洛芬",
        "english_name": "Ibuprofen",
        "cas_number": "15687-27-1",
        "molecular_formula": "C13H18O2",
        "molecular_weight": 206.28,
        "smiles": "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O",
        "category": "非甾体抗炎药",
        "description": "非甾体抗炎药，用于消炎镇痛",
        "melting_point": "75-77°C",
        "solubility": "几乎不溶于水，溶于乙醇",
        "stability": "稳定，避光保存"
    },
    "caffeine": {
        "name": "咖啡因",
        "english_name": "Caffeine",
        "cas_number": "58-08-2",
        "molecular_formula": "C8H10N4O2",
        "molecular_weight": 194.19,
        "smiles": "CN1C=NC2=C1C(=O)N(C(=O)N2C)C",
        "category": "中枢神经兴奋药",
        "description": "中枢神经兴奋剂，常用于复方制剂",
        "melting_point": "235-237°C",
        "solubility": "微溶于水，溶于热水",
        "stability": "稳定，避光保存"
    },
    "vonoprazan fumarate": {
        "name": "富马酸沃诺拉赞",
        "english_name": "Vonoprazan Fumarate",
        "cas_number": "881681-01-2",
        "molecular_formula": "C17H16FN3O2·C4H4O4",
        "molecular_weight": 425.39,
        "smiles": "CC1=C(C=C(C=C1)F)C2=NC(=NC=C2)N(C)C3=CC=CC=C3OC",
        "category": "质子泵抑制剂",
        "description": "新型钾离子竞争性酸阻滞剂，用于治疗胃食管反流病和消化性溃疡",
        "melting_point": "约200°C",
        "solubility": "微溶于水，溶于有机溶剂",
        "stability": "在酸性条件下稳定，避光保存"
    },
    "vonoprazan": {
        "name": "沃诺拉赞",
        "english_name": "Vonoprazan",
        "cas_number": "881681-00-1",
        "molecular_formula": "C17H16FN3O2",
        "molecular_weight": 313.33,
        "smiles": "CC1=C(C=C(C=C1)F)C2=NC(=NC=C2)N(C)C3=CC=CC=C3OC",
        "category": "质子泵抑制剂",
        "description": "钾离子竞争性酸阻滞剂，抑制胃酸分泌",
        "melting_point": "约180°C",
        "solubility": "微溶于水",
        "stability": "在酸性条件下稳定"
    },
    "奥美拉唑": {
        "name": "奥美拉唑",
        "english_name": "Omeprazole",
        "cas_number": "73590-58-6",
        "molecular_formula": "C17H19N3O3S",
        "molecular_weight": 345.42,
        "smiles": "COC1=CC2=C(C=C1)N=C(N2)S(=O)CC3=NC=C(C=C3C)OC",
        "category": "质子泵抑制剂",
        "description": "质子泵抑制剂，用于治疗胃溃疡和胃食管反流病",
        "melting_point": "156°C",
        "solubility": "微溶于水，溶于乙醇",
        "stability": "在酸性条件下不稳定，需肠溶包衣"
    },
    "omeprazole": {
        "name": "奥美拉唑",
        "english_name": "Omeprazole",
        "cas_number": "73590-58-6",
        "molecular_formula": "C17H19N3O3S",
        "molecular_weight": 345.42,
        "smiles": "COC1=CC2=C(C=C1)N=C(N2)S(=O)CC3=NC=C(C=C3C)OC",
        "category": "质子泵抑制剂",
        "description": "质子泵抑制剂，用于治疗胃溃疡和胃食管反流病",
        "melting_point": "156°C",
        "solubility": "微溶于水，溶于乙醇",
        "stability": "在酸性条件下不稳定，需肠溶包衣"
    }
}

# CAS号映射
LOCAL_CAS_DATABASE = {
    "50-78-2": LOCAL_DRUG_DATABASE["阿司匹林"],
    "103-90-2": LOCAL_DRUG_DATABASE["对乙酰氨基酚"],
    "15687-27-1": LOCAL_DRUG_DATABASE["布洛芬"],
    "58-08-2": LOCAL_DRUG_DATABASE["咖啡因"],
    "50-81-7": LOCAL_DRUG_DATABASE["维生素C"],
    "881681-01-2": LOCAL_DRUG_DATABASE["vonoprazan fumarate"],
    "881681-00-1": LOCAL_DRUG_DATABASE["vonoprazan"],
    "73590-58-6": LOCAL_DRUG_DATABASE["奥美拉唑"]
}

# SMILES映射
LOCAL_SMILES_DATABASE = {
    "CC(=O)OC1=CC=CC=C1C(=O)O": LOCAL_DRUG_DATABASE["阿司匹林"],
    "CC(=O)NC1=CC=C(C=C1)O": LOCAL_DRUG_DATABASE["对乙酰氨基酚"],
    "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O": LOCAL_DRUG_DATABASE["布洛芬"],
    "CN1C=NC2=C1C(=O)N(C(=O)N2C)C": LOCAL_DRUG_DATABASE["咖啡因"],
    "C([C@@H]([C@@H]([C@H](C(=O)O)O)O)O)O": LOCAL_DRUG_DATABASE["维生素C"]
}

def search_local_database(name: Optional[str] = None,
                         cas: Optional[str] = None,
                         smiles: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """从本地数据库搜索药物信息"""
    try:
        # 优先通过CAS号搜索
        if cas and cas in LOCAL_CAS_DATABASE:
            logger.info(f"通过CAS号 {cas} 在本地数据库找到药物信息")
            return LOCAL_CAS_DATABASE[cas].copy()

        # 通过SMILES搜索
        if smiles and smiles in LOCAL_SMILES_DATABASE:
            logger.info(f"通过SMILES {smiles} 在本地数据库找到药物信息")
            return LOCAL_SMILES_DATABASE[smiles].copy()

        # 通过名称搜索（支持模糊匹配）
        if name:
            name_lower = name.lower().strip()

            # 移除常见的后缀和前缀
            name_cleaned = name_lower.replace(" fumarate", "").replace(" hydrochloride", "").replace(" sodium", "").replace(" potassium", "")

            # 精确匹配
            if name_lower in LOCAL_DRUG_DATABASE:
                logger.info(f"通过名称精确匹配 {name} 在本地数据库找到药物信息")
                return LOCAL_DRUG_DATABASE[name_lower].copy()

            # 清理后的名称精确匹配
            if name_cleaned in LOCAL_DRUG_DATABASE:
                logger.info(f"通过清理名称精确匹配 {name} -> {name_cleaned} 在本地数据库找到药物信息")
                return LOCAL_DRUG_DATABASE[name_cleaned].copy()

            # 模糊匹配
            for drug_name, drug_info in LOCAL_DRUG_DATABASE.items():
                drug_name_lower = drug_name.lower()
                english_name_lower = drug_info.get("english_name", "").lower()
                chinese_name_lower = drug_info.get("name", "").lower()

                # 多种匹配策略
                if (name_lower in drug_name_lower or
                    drug_name_lower in name_lower or
                    name_lower in english_name_lower or
                    english_name_lower in name_lower or
                    name_lower in chinese_name_lower or
                    chinese_name_lower in name_lower or
                    name_cleaned in drug_name_lower or
                    drug_name_lower in name_cleaned or
                    name_cleaned in english_name_lower or
                    english_name_lower in name_cleaned or
                    name_cleaned in chinese_name_lower or
                    chinese_name_lower in name_cleaned):
                    logger.info(f"通过名称模糊匹配 {name} -> {drug_name} 在本地数据库找到药物信息")
                    return drug_info.copy()

        return None

    except Exception as e:
        logger.error(f"本地数据库搜索时发生错误: {e}")
        return None

async def get_drug_info_from_external(name: Optional[str] = None,
                                    cas: Optional[str] = None,
                                    smiles: Optional[str] = None,
                                    sync: bool = False) -> Optional[Dict[str, Any]]:
    """
    从外部数据库获取药物信息
    优先级：本地数据库 > PubChem > ChEMBL
    """
    # 首先尝试本地数据库
    local_result = search_local_database(name=name, cas=cas, smiles=smiles)
    if local_result:
        local_result["source"] = "local_database"
        return local_result

    # 然后尝试PubChem
    if sync:
        drug_info = get_drug_info_from_pubchem_sync(name, cas, smiles)
    else:
        drug_info = await get_drug_info_from_pubchem(name, cas, smiles)

    if drug_info:
        logger.info(f"成功从PubChem获取药物信息: {drug_info.get('name')}")
        drug_info["source"] = "pubchem"
        return drug_info

    # 如果PubChem失败，尝试ChEMBL
    drug_info = await get_drug_info_from_chembl(name, cas, smiles)
    if drug_info:
        logger.info(f"成功从ChEMBL获取药物信息: {drug_info.get('name')}")
        drug_info["source"] = "chembl"
        return drug_info

    # 都失败了，返回None
    return None