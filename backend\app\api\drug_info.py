"""
药物信息API端点
支持从外部数据库获取药物信息
"""
from fastapi import APIRouter, HTTPException, Query, Depends, Body
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from redis import Redis
import logging
import sys
import importlib.util

from ..services.drug_info_service import DrugInfoService
from ..config.database import get_db
from ..config.redis import get_redis_dependency
from app.models.drug import DrugORM
from app.schemas import DrugBase, DrugCreate, Drug

logger = logging.getLogger(__name__)
router = APIRouter(
    prefix="/drugs",
    tags=["drugs"],
)

# 检查aiohttp是否已安装
aiohttp_installed = importlib.util.find_spec("aiohttp") is not None
if not aiohttp_installed:
    # 使用日志记录而不是直接打印
    logger.warning("aiohttp未安装，外部数据库查询功能将受限")

# 尝试导入外部数据库服务
try:
    from app.services.external_db_service import get_drug_info_from_external
    EXTERNAL_DB_SERVICE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入外部数据库服务: {e}")
    EXTERNAL_DB_SERVICE_AVAILABLE = False

# 尝试导入本地药物信息服务
try:
    from app.services.drug_info_service import DrugInfoService
    LOCAL_DRUG_SERVICE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入本地药物信息服务: {e}")
    LOCAL_DRUG_SERVICE_AVAILABLE = False

# Dependency to create DrugInfoService instance
def get_drug_info_service(
    db: Session = Depends(get_db),
    redis_client: Redis = Depends(get_redis_dependency)
) -> DrugInfoService:
    return DrugInfoService(db=db, redis_client=redis_client)

@router.post("/", response_model=Drug, status_code=201)
def create_drug(
    drug_data: DrugCreate,
    service: DrugInfoService = Depends(get_drug_info_service)
):
    """
    向数据库中添加一个新的药物条目。
    """
    db_drug = service.get_drug_by_name(drug_data.name)
    if db_drug:
        raise HTTPException(status_code=400, detail="同名药物已存在")
    return service.add_drug(drug_data=drug_data)

@router.get("/", response_model=List[Drug])
def list_drugs(
    skip: int = 0,
    limit: int = 100,
    service: DrugInfoService = Depends(get_drug_info_service)
):
    """
    获取药物列表。
    """
    drugs = service.get_drugs(skip=skip, limit=limit)
    return drugs

@router.get("/{drug_id}", response_model=Drug)
def get_drug(
    drug_id: int,
    service: DrugInfoService = Depends(get_drug_info_service)
):
    """
    获取单个药物的详细信息。
    """
    db_drug = service.get_drug(drug_id=drug_id)
    if db_drug is None:
        raise HTTPException(status_code=404, detail="未找到该药物")
    return db_drug

@router.put("/{drug_id}", response_model=Drug)
def update_drug(
    drug_id: int,
    drug_data: DrugCreate,
    service: DrugInfoService = Depends(get_drug_info_service)
):
    """
    更新现有药物的信息。
    """
    db_drug = service.update_drug(drug_id=drug_id, drug_data=drug_data)
    if db_drug is None:
        raise HTTPException(status_code=404, detail="未找到该药物")
    return db_drug

@router.delete("/{drug_id}", status_code=204)
def delete_drug(
    drug_id: int,
    service: DrugInfoService = Depends(get_drug_info_service)
):
    """
    删除一个药物。
    """
    success = service.delete_drug(drug_id=drug_id)
    if not success:
        raise HTTPException(status_code=404, detail="未找到该药物")
    return {"ok": True}

@router.get("/search-external/")
def search_external_drug_info(
    name: str = Query(..., min_length=2, description="要查询的药物名称"),
    cas: Optional[str] = Query(None, description="要查询的CAS号")
):
    """
    从外部数据库（如PubChem）联网搜索药物信息。
    """
    if not EXTERNAL_DB_SERVICE_AVAILABLE:
        raise HTTPException(status_code=503, detail="外部数据库服务不可用，请检查服务器配置")

    # 使用同步模式调用，因此不使用await
    drug_info = get_drug_info_from_external(name=name, cas=cas, sync=True)

    if drug_info and "error" not in drug_info:
        return drug_info
    
    # 尝试仅使用名称搜索，如果之前使用了CAS
    if cas and (not drug_info or "error" in drug_info):
        drug_info = get_drug_info_from_external(name=name, sync=True)
        if drug_info and "error" not in drug_info:
            return drug_info

    detail_message = drug_info.get("error", "在外部数据库中未找到相关药物的详细信息") if drug_info else "在外部数据库中未找到相关药物的详细信息"
    raise HTTPException(status_code=404, detail=detail_message)

@router.post("/validate-smiles")
async def validate_smiles(smiles: str) -> Dict[str, Any]:
    """
    验证SMILES结构的有效性
    """
    try:
        # 尝试导入rdkit
        try:
            from rdkit import Chem
            RDKIT_AVAILABLE = True
        except ImportError:
            RDKIT_AVAILABLE = False
            return {
                "valid": False,
                "message": "无法验证SMILES，RDKit未安装"
            }
        
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {
                "valid": False,
                "message": "无效的SMILES结构"
            }
            
        # 获取标准化的SMILES
        canonical_smiles = Chem.MolToSmiles(mol)
        
        return {
            "valid": True,
            "canonical_smiles": canonical_smiles,
            "message": "SMILES结构有效"
        }
        
    except Exception as e:
        logger.error(f"验证SMILES失败: {e}")
        return {
            "valid": False,
            "message": f"验证失败: {str(e)}"
        } 