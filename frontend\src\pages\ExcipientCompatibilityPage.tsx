import React, { useState } from 'react';
import { assessCompatibility, ExcipientInfo, CompatibilityResult } from '../api';
import { Button, Input, Table, message } from 'antd';

const ExcipientCompatibilityPage: React.FC = () => {
  const [drugName, setDrugName] = useState('');
  const [drugStructure, setDrugStructure] = useState('');
  const [excipients, setExcipients] = useState<ExcipientInfo[]>([]);
  const [excipientInput, setExcipientInput] = useState<ExcipientInfo>({ name: '' });
  const [result, setResult] = useState<CompatibilityResult | null>(null);
  const [loading, setLoading] = useState(false);

  const addExcipient = () => {
    if (!excipientInput.name) return message.warning('请输入辅料名称');
    setExcipients([...excipients, excipientInput]);
    setExcipientInput({ name: '' });
  };

  const handleAssess = async () => {
    if (!drugName || !drugStructure || excipients.length === 0) {
      message.warning('请完整填写主药、结构式和辅料信息');
      return;
    }
    setLoading(true);
    try {
      const res = await assessCompatibility({ drug_name: drugName, drug_structure: drugStructure, excipients });
      setResult(res?.data || null);
    } catch {
      message.error('评估失败');
    }
    setLoading(false);
  };

  return (
    <div style={{ maxWidth: 800, margin: '2rem auto', background: '#fff', padding: 24, borderRadius: 8 }}>
      <h2>原辅料相容性风险评估</h2>
      <div style={{ marginBottom: 16 }}>
        <Input placeholder="主药名称" value={drugName} onChange={e => setDrugName(e.target.value)} style={{ width: 200, marginRight: 8 }} />
        <Input placeholder="主药结构式（SMILES）" value={drugStructure} onChange={e => setDrugStructure(e.target.value)} style={{ width: 300, marginRight: 8 }} />
      </div>
      <div style={{ marginBottom: 16 }}>
        <Input placeholder="辅料名称" value={excipientInput.name} onChange={e => setExcipientInput({ ...excipientInput, name: e.target.value })} style={{ width: 160, marginRight: 8 }} />
        <Input placeholder="批号" value={excipientInput.batch} onChange={e => setExcipientInput({ ...excipientInput, batch: e.target.value })} style={{ width: 100, marginRight: 8 }} />
        <Input placeholder="供应商" value={excipientInput.supplier} onChange={e => setExcipientInput({ ...excipientInput, supplier: e.target.value })} style={{ width: 120, marginRight: 8 }} />
        <Input placeholder="结构式（SMILES）" value={excipientInput.structure} onChange={e => setExcipientInput({ ...excipientInput, structure: e.target.value })} style={{ width: 180, marginRight: 8 }} />
        <Button onClick={addExcipient}>添加辅料</Button>
      </div>
      <ul>
        {excipients.map((e, idx) => <li key={idx}>{e.name} | 批号: {e.batch} | 供应商: {e.supplier} | 结构式: {e.structure}</li>)}
      </ul>
      <Button type="primary" onClick={handleAssess} loading={loading}>评估风险</Button>
      {result && (
        <div style={{ marginTop: 32 }}>
          <h3>风险评估报告</h3>
          <Table
            dataSource={result.results}
            columns={[
              { title: '辅料', dataIndex: 'excipient' },
              { title: '风险等级', dataIndex: 'risk_level', render: (v: string) => v === '高' ? <span style={{color: 'red', fontWeight: 600}}>{v}</span> : v === '中' ? <span style={{color: '#faad14', fontWeight: 600}}>{v}</span> : v },
              { title: '风险类型', dataIndex: 'risk_type' },
              { title: '证据链', dataIndex: 'evidence', render: (v: string[]) => v.map((e, i) => <div key={i} style={{color: e.includes('AI模型') ? '#faad14' : e.includes('DOI') || e.includes('专利') ? '#1976d2' : undefined}}>{e}</div>) },
              { title: '建议', dataIndex: 'suggestion' },
            ]}
            rowKey={r => r.excipient}
            pagination={false}
          />
        </div>
      )}
    </div>
  );
};

export default ExcipientCompatibilityPage; 