import React, { useState, useContext } from 'react';
import { Table, Button, Space, Card, Input, Modal, Form, Select, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ProjectContext } from '../App';
import type { Project } from '../api';

const { Option } = Select;

const ProjectList: React.FC = () => {
  const { 
    projects, 
    currentProject, 
    addProject, 
    editProject, 
    deleteProject: deleteProjectFromContext,
    setCurrentProject 
  } = useContext(ProjectContext);
  
  const [loading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingProject, setEditingProject] = useState<any>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const navigate = useNavigate();

  const handleAddProject = () => {
    setEditingProject(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditProject = (record: any) => {
    setEditingProject(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDeleteProject = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个项目吗？此操作不可恢复。',
      onOk() {
        deleteProjectFromContext(id);
        message.success('项目已删除');
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingProject === null) {
        // 添加新项目
        const now = new Date();
        const newProject: Project = {
          ...values,
          id: Date.now().toString(),
          status: '新建',
          created: now.toISOString().slice(0,10),
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
          description: values.description || '',
        };
        addProject({ name: newProject.name, status: newProject.status, description: newProject.description, created: newProject.created });
        setCurrentProject?.(newProject);
        
        // 可选：创建后跳转到数据输入页面
        setTimeout(() => {
          navigate('/data-input');
        }, 1000);
      } else {
        // 更新现有项目
        const updatedProject = {
          ...editingProject,
          name: values.name,
          status: values.status,
          description: values.description || editingProject.description
        };
        editProject(updatedProject);
        message.success('项目更新成功');
      }
      setModalVisible(false);
    });
  };

  const handleSelectProject = (project: any) => {
    setCurrentProject(project);
    message.success(`已选择项目: ${project.name}`);
    navigate('/data-input');
  };

  // 过滤项目
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <button onClick={() => handleSelectProject(record)} style={{ fontWeight: 'bold', background: 'none', border: 'none', color: '#1890ff', cursor: 'pointer' }} aria-label={`选择项目 ${text}`}>
            {text}
          </button>
          {currentProject?.id === record.id && (
            <span style={{ marginLeft: 8, color: '#52c41a', fontSize: '12px' }}>
              (当前项目)
            </span>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        if (status === '进行中') color = '#1890ff';
        else if (status === '已完成') color = '#52c41a';
        else if (status === '计划中') color = '#faad14';
        return <span style={{ color, fontWeight: 'bold' }}>{status}</span>;
      },
    },
    {
      title: '创建日期',
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => text || '暂无描述'
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: any) => (
        <Space size="middle">
          <Button 
            type="text" 
            size="small"
            onClick={() => handleSelectProject(record)}
            style={{ color: '#1890ff' }}
          >
            选择
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditProject(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteProject(record.id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card 
        title={
          <div>
            项目管理
            {currentProject && (
              <span style={{ marginLeft: 16, fontSize: '14px', color: '#666' }}>
                当前项目: <strong style={{ color: '#1890ff' }}>{currentProject.name}</strong>
              </span>
            )}
          </div>
        }
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddProject}>
            新建项目
          </Button>
        }
      >
        <Space style={{ marginBottom: 16 }}>
          <Input.Search
            placeholder="搜索项目名称"
            allowClear
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select 
            value={statusFilter} 
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">全部状态</Option>
            <Option value="进行中">进行中</Option>
            <Option value="已完成">已完成</Option>
            <Option value="计划中">计划中</Option>
          </Select>
        </Space>
        
        {projects.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <p>暂无项目，点击"新建项目"开始创建您的第一个项目</p>
          </div>
        ) : (
          <Table 
            columns={columns} 
            dataSource={filteredProjects} 
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
            rowClassName={(record) => 
              currentProject?.id === record.id ? 'ant-table-row-selected' : ''
            }
          />
        )}
      </Card>

      <Modal
        title={editingProject === null ? '新建项目' : '编辑项目'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          <Form.Item
            name="status"
            label="项目状态"
            rules={[{ required: true, message: '请选择项目状态' }]}
          >
            <Select placeholder="请选择项目状态">
              <Option value="计划中">计划中</Option>
              <Option value="进行中">进行中</Option>
              <Option value="已完成">已完成</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="description"
            label="项目描述"
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请输入项目描述（可选）" 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectList; 