import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  zh: {
    translation: {
      '首页': '首页',
      '数据输入': '数据输入',
      '项目管理': '项目管理',
      '新建项目': '新建项目',
      '批量导入': '批量导入',
      '分析': '分析',
      '报告': '报告',
      'AI建议': 'AI建议',
      '导出报告': '导出报告',
      '基础信息': '基础信息',
      '状态': '状态',
      '创建时间': '创建时间',
      '项目名': '项目名',
      '药物信息': '药物信息',
      '辅料信息': '辅料信息',
      '环境参数': '环境参数',
      '稳定性数据': '稳定性数据',
      '导入成功': '导入成功',
      '部分成功，部分数据有错误': '部分成功，部分数据有错误',
      '导入失败': '导入失败',
      '下载模板': '下载模板',
      '采纳': '采纳',
      '忽略': '忽略',
      '标记为疑问': '标记为疑问',
      '风险': '风险',
      '高': '高',
      '中': '中',
      '低': '低',
      '分析中...': '分析中...',
      '发起分析': '发起分析',
      '分析完成': '分析完成',
      '报告导出成功（模拟）': '报告导出成功（模拟）',
      '报告导出失败': '报告导出失败',
      '加载中...': '加载中...',
      '返回': '返回',
      '导出历史': '导出历史',
      '清空历史': '清空历史',
      '内容预览': '内容预览',
      '报告导出成功': '报告导出成功',
      '文件下载失败': '文件下载失败',
      '暂无导出历史': '暂无导出历史',
      '系统管理': '系统管理',
      '用户管理': '用户管理',
    }
  },
  en: {
    translation: {
      '首页': 'Home',
      '数据输入': 'Data Input',
      '项目管理': 'Project Management',
      '新建项目': 'New Project',
      '批量导入': 'Batch Import',
      '分析': 'Analysis',
      '报告': 'Report',
      'AI建议': 'AI Suggestions',
      '导出报告': 'Export Report',
      '基础信息': 'Basic Info',
      '状态': 'Status',
      '创建时间': 'Created At',
      '项目名': 'Project Name',
      '药物信息': 'Drug Info',
      '辅料信息': 'Excipient Info',
      '环境参数': 'Environment',
      '稳定性数据': 'Stability Data',
      '导入成功': 'Import Success',
      '部分成功，部分数据有错误': 'Partial Success, Some Errors',
      '导入失败': 'Import Failed',
      '下载模板': 'Download Template',
      '采纳': 'Adopt',
      '忽略': 'Ignore',
      '标记为疑问': 'Mark as Question',
      '风险': 'Risk',
      '高': 'High',
      '中': 'Medium',
      '低': 'Low',
      '分析中...': 'Analyzing...',
      '发起分析': 'Start Analysis',
      '分析完成': 'Analysis Complete',
      '报告导出失败': 'Report ExportAILED',
      '加载中...': 'Loading...',
      '返回': 'Back',
      '导出历史': 'Export History',
      '清空历史': 'Clear History',
      '内容预览': 'Content Preview',
      '报告导出成功': 'Report Exported Successfully',
      '文件下载失败': 'File Download Failed',
      '暂无导出历史': 'No Export History',
      '系统管理': 'System Management',
      '用户管理': 'User Management',
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh',
    interpolation: { escapeValue: false },
    detection: { order: ['querystring', 'cookie', 'localStorage', 'navigator'], caches: ['localStorage', 'cookie'] },
  });

export default i18n; 