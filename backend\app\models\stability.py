from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, JSON
import datetime
from .base import Base

class StabilityDataORM(Base):
    __tablename__ = 'stability_data'
    id = Column(String, primary_key=True, index=True)
    project_id = Column(String, nullable=False)
    drug_id = Column(String, nullable=False)
    excipient_id = Column(String, ForeignKey("excipients.id"), nullable=True)
    environment_id = Column(String, ForeignKey("environments.id"), nullable=True)
    time_point = Column(Float, nullable=True)  # 月份，如0, 1, 3, 6, 9, 12, 18, 24
    value = Column(Float, nullable=True)
    item = Column(String, nullable=True)  # 测试项目，如"含量"、"杂质A"等
    unit = Column(String, nullable=True)  # 单位，如"%"、"mg/mL"等
    specification = Column(String, nullable=True)  # 规格，如"90.0-110.0%"
    method = Column(String, nullable=True)  # 测试方法，如"HPLC"
    batch = Column(String, nullable=True)  # 批号
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class DegradationProductORM(Base):
    """降解产物表"""
    __tablename__ = 'degradation_products'
    id = Column(String, primary_key=True, index=True)
    drug_id = Column(String, ForeignKey("drugs.id"), nullable=False)
    name = Column(String, nullable=False)  # 降解产物名称
    structure = Column(String, nullable=True)  # SMILES
    formation_condition = Column(String, nullable=True)  # 形成条件
    degradation_pathway = Column(String, nullable=True)  # 降解途径
    detection_method = Column(String, nullable=True)  # 检测方法
    relative_retention_time = Column(Float, nullable=True)  # 相对保留时间
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class StabilityPredictionORM(Base):
    """稳定性预测结果表"""
    __tablename__ = 'stability_predictions'
    id = Column(String, primary_key=True, index=True)
    project_id = Column(String, nullable=False)
    drug_id = Column(String, nullable=False)
    model_type = Column(String, nullable=True)  # 模型类型，如"zero-order"、"first-order"等
    parameters = Column(JSON, nullable=True)  # 模型参数
    r_squared = Column(Float, nullable=True)  # 拟合优度
    prediction_conditions = Column(JSON, nullable=True)  # 预测条件
    prediction_results = Column(JSON, nullable=True)  # 预测结果
    shelf_life = Column(Float, nullable=True)  # 货架期(月)
    confidence_interval = Column(JSON, nullable=True)  # 置信区间
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class StabilityStudyORM(Base):
    """稳定性研究表"""
    __tablename__ = 'stability_studies'
    id = Column(String, primary_key=True, index=True)
    project_id = Column(String, nullable=False)
    study_type = Column(String, nullable=False)  # 研究类型，如"长期"、"加速"、"中间条件"等
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    temperature = Column(Float, nullable=True)  # 温度(°C)
    humidity = Column(Float, nullable=True)  # 湿度(%)
    packaging = Column(String, nullable=True)  # 包装材料
    time_points = Column(JSON, nullable=True)  # 时间点列表
    test_items = Column(JSON, nullable=True)  # 测试项目列表
    status = Column(String, nullable=True)  # 状态，如"进行中"、"已完成"等
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow) 