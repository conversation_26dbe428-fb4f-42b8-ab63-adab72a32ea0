/* 项目管理页面专用样式 */

.project-management-container {
  padding: 0;
  background: var(--background-color);
  min-height: 100vh;
}

.project-management-card {
  margin: 0;
  border-radius: 0;
  border: none;
  box-shadow: none;
  background: var(--content-background);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 0 0 24px 0;
  border-bottom: 1px solid var(--border-light);
}

.project-header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color);
}

.project-header-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
  margin-bottom: 8px;
}

.current-project-indicator {
  margin-top: 12px;
  font-size: 14px;
  color: var(--text-secondary);
  padding: 8px 12px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.current-project-name {
  color: var(--primary-color);
  font-weight: 600;
}

.project-actions {
  display: flex;
  gap: 12px;
}

.project-action-btn {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.project-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.project-search-row {
  margin-bottom: 24px;
}

.project-search-input {
  height: 40px;
  font-size: 14px;
  border-radius: var(--border-radius);
}

.project-table-container {
  background: var(--content-background);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.project-table {
  font-size: 14px;
}

.project-table .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid var(--border-light);
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
  padding: 16px;
  height: 56px;
}

.project-table .ant-table-tbody > tr > td {
  padding: 16px;
  font-size: 14px;
  border-bottom: 1px solid var(--border-light);
  height: 64px;
  vertical-align: middle;
}

.project-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.project-table .ant-table-pagination {
  padding: 16px 24px;
  border-top: 1px solid var(--border-light);
  text-align: center;
  margin: 0;
}

.project-id-cell {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.project-name-cell {
  font-size: 14px;
}

.project-name-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  transition: color 0.2s ease;
}

.project-name-link:hover {
  color: var(--primary-color);
}

.project-name-link.current-project {
  font-weight: 600;
  color: var(--primary-color);
}

.current-project-badge {
  margin-left: 8px;
  color: var(--success-color);
  font-size: 12px;
  font-weight: 500;
}

.project-status-tag {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: var(--border-radius);
}

.project-date-cell {
  font-size: 14px;
  color: var(--text-secondary);
}

.project-actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
}

.project-action-button {
  font-size: 14px;
  height: 28px;
  width: 28px;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.project-action-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.project-select-button {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.project-select-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 模态框样式 */
.project-modal .ant-modal-content {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
}

.project-modal .ant-modal-header {
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.project-modal .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
}

.project-form-item {
  margin-bottom: 16px;
}

.project-form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.project-form-input {
  height: 40px;
  font-size: 14px;
  border-radius: var(--border-radius);
}

.project-form-select {
  height: 40px;
  font-size: 14px;
}

.project-form-select .ant-select-selector {
  height: 40px;
  border-radius: var(--border-radius);
}

/* 空状态优化 */
.project-empty-state {
  padding: 60px 20px;
  text-align: center;
}

.project-empty-state .ant-empty-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .project-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .project-management-container {
    padding: 16px;
  }
  
  .project-header-left h2 {
    font-size: 24px;
  }
  
  .project-header-subtitle {
    font-size: 14px;
  }
  
  .project-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .project-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .project-table .ant-table-thead > tr > th,
  .project-table .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .project-actions-cell {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .project-header {
    padding: 0 0 16px 0;
  }
  
  .project-search-row .ant-col {
    width: 100%;
  }
  
  .project-table .ant-table-thead > tr > th,
  .project-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
}
