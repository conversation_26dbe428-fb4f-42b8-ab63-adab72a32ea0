import React, { useState } from 'react';
import { Card, Form, Input, Button, Table, message, Select, Upload, Row, Col, Steps, Tabs, Descriptions, Divider } from 'antd';
import { assessCompatibility, predictStabilityV2, ExcipientInfo, StabilityInput, CompatibilityResult, StabilityPredictionResult, suggestIdentify } from '../api';
import api from '../api';
import { UploadOutlined } from '@ant-design/icons';
import * as XLSX from 'xlsx';

const { Option } = Select;

const ProjectAnalysisPage: React.FC = () => {
  // 原料药
  const [drug, setDrug] = useState({ name: '', cas: '', formula: '', smiles: '', supplier: '', batch: '' });
  // 辅料
  const [excipients, setExcipients] = useState<ExcipientInfo[]>([]);
  const [excipientInput, setExcipientInput] = useState<ExcipientInfo>({ name: '' });
  // 包材
  const [packaging, setPackaging] = useState({ type: '', supplier: '', batch: '', material: '', spec: '' });
  // 环境与工艺
  const [env, setEnv] = useState({ temperature: '', humidity: '', process: '', description: '' });
  // 结果
  const [compatResult, setCompatResult] = useState<CompatibilityResult | null>(null);
  const [stabilityResult, setStabilityResult] = useState<StabilityPredictionResult | null>(null);
  const [loading, setLoading] = useState(false);

  // 智能补全下拉
  const [drugOptions, setDrugOptions] = useState<string[]>([]);
  const [excipientOptions, setExcipientOptions] = useState<string[]>([]);
  const [packagingOptions, setPackagingOptions] = useState<string[]>([]);

  // 辅料批量导入（Excel）
  const handleExcipientImport = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        // Assuming Excel columns are: Name, Batch, Supplier, Structure
        const json = XLSX.utils.sheet_to_json<any>(worksheet);
        
        const newExcipients = json.map(row => ({
          name: row['Name'] || row['名称'],
          batch: row['Batch'] || row['批号'],
          supplier: row['Supplier'] || row['供应商'],
          structure: row['Structure'] || row['结构式']
        })).filter(ex => ex.name); // Filter out rows without a name

        if(newExcipients.length > 0) {
            setExcipients(prev => [...prev, ...newExcipients]);
            message.success(`成功导入 ${newExcipients.length} 个辅料`);
        } else {
            message.warning('未在文件中找到有效的辅料数据。请确保列标题为 "名称", "批号", "供应商", "结构式" 等。');
        }
      } catch (error) {
        message.error("解析Excel文件失败，请确保文件格式正确。");
        console.error(error);
      }
    };
    reader.readAsArrayBuffer(file);
    return false; // Prevent antd's default upload action
  };

  // 智能补全（如输入药名自动补全结构式等）
  const handleDrugAutoComplete = async () => {
    if (!drug.name) {
      return message.warning('请输入药品名称');
    }
    try {
      // Define the expected response type for the API call
      interface PubChemSearchResponse {
        compound: {
          smiles?: string;
          cas?: string;
          molecular_formula?: string;
        }
      }
      const response = await api.post<PubChemSearchResponse>('/pubchem/search/compound', { query: drug.name });
      const compound = response.data.compound;
      if (compound) {
        setDrug({
          ...drug,
          smiles: compound.smiles || '',
          cas: compound.cas || '',
          formula: compound.molecular_formula || ''
        });
        message.success('已自动补全信息');
      } else {
        message.warning('未能找到该药品的详细信息');
      }
    } catch (error) {
      message.error('信息补全失败，请检查网络或后台服务');
    }
  };

  // 添加辅料
  const addExcipient = () => {
    if (!excipientInput.name) return message.warning('请输入辅料名称');
    setExcipients([...excipients, excipientInput]);
    setExcipientInput({ name: '' });
  };

  // 一键分析
  const handleAnalyze = async () => {
    if (!drug.name || !drug.smiles || excipients.length === 0) {
      message.warning('请完整填写主药结构式和辅料信息');
      return;
    }
    setLoading(true);
    try {
      const compat = await assessCompatibility({ drug_name: drug.name, drug_structure: drug.smiles, excipients });
      setCompatResult(compat.data);
      const stability: StabilityInput = {
        drug_name: drug.name,
        excipients: excipients.map(e => e.name),
        process: env.process,
        packaging: packaging.type,
        environment: `${env.temperature}/${env.humidity}`,
        history_data: []
      };
      const stab = await predictStabilityV2(stability);
      setStabilityResult(stab.data);
      message.success('分析完成');
    } catch {
      message.error('分析失败');
    }
    setLoading(false);
  };

  return (
    <div style={{ maxWidth: 1100, margin: '2rem auto', background: '#fff', padding: 32, borderRadius: 8 }}>
      <h2>项目一站式分析</h2>
      <Steps current={1} style={{ marginBottom: 32 }} items={[
        { title: '信息录入' },
        { title: '分析与预测' },
        { title: '结果导出' },
      ]} />
      <Row gutter={24}>
        <Col span={12}>
          <Card title="原料药信息" bordered style={{ marginBottom: 16 }}>
            <Form layout="vertical">
              <Form.Item label="药品名称" required>
                <Select
                  showSearch
                  value={drug.name}
                  onSearch={async (v) => {
                    const res = await suggestIdentify(v, 'drug');
                    setDrugOptions(res.data || []);
                  }}
                  onChange={v => setDrug({ ...drug, name: v })}
                  style={{ width: 200, marginRight: 8 }}
                  options={drugOptions.map(d => ({ value: d, label: d }))}
                  filterOption={false}
                  allowClear
                  placeholder="请输入或选择药品"
                />
                <Button onClick={handleDrugAutoComplete}>智能补全</Button>
              </Form.Item>
              <Form.Item label="结构式（SMILES）">
                <Input value={drug.smiles} onChange={e => setDrug({ ...drug, smiles: e.target.value })} style={{ width: 300 }} />
              </Form.Item>
              <Form.Item label="CAS号">
                <Input value={drug.cas} onChange={e => setDrug({ ...drug, cas: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="分子式">
                <Input value={drug.formula} onChange={e => setDrug({ ...drug, formula: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="供应商">
                <Input value={drug.supplier} onChange={e => setDrug({ ...drug, supplier: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="批号">
                <Input value={drug.batch} onChange={e => setDrug({ ...drug, batch: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
            </Form>
          </Card>
          <Card title="辅料信息" bordered style={{ marginBottom: 16 }}>
            <Upload beforeUpload={handleExcipientImport} showUploadList={false}>
              <Button icon={<UploadOutlined />}>批量导入Excel</Button>
            </Upload>
            <div style={{ margin: '12px 0' }}>
              <Input
                placeholder="辅料名称"
                value={excipientInput.name}
                onChange={async e => {
                  setExcipientInput({ ...excipientInput, name: e.target.value });
                  const res = await suggestIdentify(e.target.value, 'excipient');
                  setExcipientOptions(res.data || []);
                }}
                style={{ width: 120, marginRight: 8 }}
                list="excipient-list"
              />
              <datalist id="excipient-list">
                {excipientOptions.map(opt => <option value={opt} key={opt} />)}
              </datalist>
              <Input placeholder="批号" value={excipientInput.batch} onChange={e => setExcipientInput({ ...excipientInput, batch: e.target.value })} style={{ width: 100, marginRight: 8 }} />
              <Input placeholder="供应商" value={excipientInput.supplier} onChange={e => setExcipientInput({ ...excipientInput, supplier: e.target.value })} style={{ width: 120, marginRight: 8 }} />
              <Input placeholder="结构式（SMILES）" value={excipientInput.structure} onChange={e => setExcipientInput({ ...excipientInput, structure: e.target.value })} style={{ width: 180, marginRight: 8 }} />
              <Button onClick={addExcipient}>添加辅料</Button>
            </div>
            <Table dataSource={excipients} columns={[
              { title: '名称', dataIndex: 'name' },
              { title: '批号', dataIndex: 'batch' },
              { title: '供应商', dataIndex: 'supplier' },
              { title: '结构式', dataIndex: 'structure' },
            ]} rowKey={r => r.name + (r.batch || '')} size="small" pagination={false} />
          </Card>
          <Card title="包材信息" bordered style={{ marginBottom: 16 }}>
            <Form layout="vertical">
              <Form.Item label="类型">
                <Select
                  showSearch
                  value={packaging.type}
                  onSearch={async (v) => {
                    const res = await suggestIdentify(v, 'packaging');
                    setPackagingOptions(res.data || []);
                  }}
                  onChange={v => setPackaging({ ...packaging, type: v })}
                  style={{ width: 200 }}
                  options={packagingOptions.map(d => ({ value: d, label: d }))}
                  filterOption={false}
                  allowClear
                  placeholder="请输入或选择包材类型"
                />
              </Form.Item>
              <Form.Item label="供应商">
                <Input value={packaging.supplier} onChange={e => setPackaging({ ...packaging, supplier: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="批号">
                <Input value={packaging.batch} onChange={e => setPackaging({ ...packaging, batch: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="材质">
                <Input value={packaging.material} onChange={e => setPackaging({ ...packaging, material: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="规格">
                <Input value={packaging.spec} onChange={e => setPackaging({ ...packaging, spec: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
            </Form>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="环境与工艺参数" bordered style={{ marginBottom: 16 }}>
            <Form layout="vertical">
              <Form.Item label="温度">
                <Input value={env.temperature} onChange={e => setEnv({ ...env, temperature: e.target.value })} style={{ width: 120 }} placeholder="如25" />
              </Form.Item>
              <Form.Item label="湿度">
                <Input value={env.humidity} onChange={e => setEnv({ ...env, humidity: e.target.value })} style={{ width: 120 }} placeholder="如60" />
              </Form.Item>
              <Form.Item label="工艺">
                <Input value={env.process} onChange={e => setEnv({ ...env, process: e.target.value })} style={{ width: 200 }} />
              </Form.Item>
              <Form.Item label="工艺描述">
                <Input.TextArea value={env.description} onChange={e => setEnv({ ...env, description: e.target.value })} rows={2} />
              </Form.Item>
            </Form>
          </Card>
          <Button type="primary" onClick={handleAnalyze} loading={loading} style={{ width: '100%', marginBottom: 16 }}>一键分析</Button>
          <Divider />
          <Tabs defaultActiveKey="compat" items={[{
            key: 'compat',
            label: '原辅料相容性分析',
            children: compatResult ? (
              <Table
                dataSource={compatResult.results}
                columns={[
                  { title: '辅料', dataIndex: 'excipient' },
                  { title: '风险等级', dataIndex: 'risk_level', render: (v: string) => v === '高' ? <span style={{color: 'red', fontWeight: 600}}>{v}</span> : v === '中' ? <span style={{color: '#faad14', fontWeight: 600}}>{v}</span> : v },
                  { title: '风险类型', dataIndex: 'risk_type' },
                  { title: '证据链', dataIndex: 'evidence', render: (v: string[]) => v.map((e, i) => <div key={i} style={{color: e.includes('AI模型') ? '#faad14' : e.includes('DOI') || e.includes('专利') ? '#1976d2' : undefined}}>{e}</div>) },
                  { title: '建议', dataIndex: 'suggestion' },
                ]}
                rowKey={r => r.excipient}
                pagination={false}
              />
            ) : <div style={{ color: '#888' }}>请先填写信息并分析</div>
          }, {
            key: 'stability',
            label: '稳定性风险预测',
            children: stabilityResult ? (
              <div>
                <Descriptions bordered column={1} size="small">
                  <Descriptions.Item label="长期t90">{stabilityResult.prediction.long_term.t90} 月 (置信区间: {stabilityResult.prediction.long_term.ci[0]}~{stabilityResult.prediction.long_term.ci[1]})</Descriptions.Item>
                  <Descriptions.Item label="加速t90">{stabilityResult.prediction.accelerated.t90} 月 (置信区间: {stabilityResult.prediction.accelerated.ci[0]}~{stabilityResult.prediction.accelerated.ci[1]})</Descriptions.Item>
                  <Descriptions.Item label="法规比对"><span style={{color: stabilityResult.regulatory_check.includes('不符合') ? 'red' : '#388e3c', fontWeight: 600}}>{stabilityResult.regulatory_check}</span></Descriptions.Item>
                  <Descriptions.Item label="敏感性分析">
                    <Table
                      dataSource={stabilityResult.sensitivity.sort((a, b) => b.impact - a.impact)}
                      columns={[
                        { title: '影响因子', dataIndex: 'factor' },
                        { title: '影响程度', dataIndex: 'impact' },
                      ]}
                      rowKey={r => r.factor}
                      size="small"
                      pagination={false}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="模型可解释性（SHAP值等）">
                    <div style={{fontFamily: 'monospace', color: '#1976d2'}}>{stabilityResult.explain}</div>
                  </Descriptions.Item>
                </Descriptions>
              </div>
            ) : <div style={{ color: '#888' }}>请先填写信息并分析</div>
          }]} />
        </Col>
      </Row>
    </div>
  );
};

export default ProjectAnalysisPage; 