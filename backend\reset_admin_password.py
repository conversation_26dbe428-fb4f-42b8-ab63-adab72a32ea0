from app.models import UserORM
from app.models.base import Base
from app.config.database import engine
from sqlalchemy.orm import sessionmaker

# 确保所有表结构已创建
Base.metadata.create_all(bind=engine)

Session = sessionmaker(bind=engine)
session = Session()

admin = session.query(UserORM).filter_by(username='admin').first()
if not admin:
    # 自动创建admin账号
    admin = UserORM(
        username='admin',
        email='<EMAIL>',
        role='admin',
        status='active'
    )
    admin.set_password('admin123')
    session.add(admin)
    session.commit()
    print("admin账号已创建，密码：admin123")
else:
    admin.set_password('admin123')
    session.commit()
    print("admin密码已重置为：admin123") 