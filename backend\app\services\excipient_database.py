"""
辅料数据库服务
包含常见药用辅料的详细信息
参考最新版药用辅料手册
"""
from typing import List, Dict, Any
import json

class ExcipientDatabase:
    """辅料数据库类"""
    
    @staticmethod
    def get_excipient_categories() -> Dict[str, List[str]]:
        """获取辅料分类"""
        return {
            "填充剂": [
                "乳糖", "微晶纤维素", "淀粉", "预胶化淀粉", "甘露醇", 
                "山梨醇", "磷酸氢钙", "碳酸钙", "硫酸钙", "木糖醇"
            ],
            "黏合剂": [
                "聚维酮(PVP)", "羟丙纤维素(HPC)", "羟丙甲纤维素(HPMC)", 
                "甲基纤维素(MC)", "乙基纤维素(EC)", "淀粉浆", "明胶",
                "阿拉伯胶", "海藻酸钠", "卡波姆"
            ],
            "崩解剂": [
                "交联聚维酮(PVPP)", "交联羧甲纤维素钠(CCNa)", "羧甲淀粉钠(CMS-Na)",
                "低取代羟丙纤维素(L-HPC)", "藻酸", "微晶纤维素", "淀粉"
            ],
            "润滑剂": [
                "硬脂酸镁", "硬脂酸", "硬脂酸钙", "滑石粉", "微粉硅胶",
                "聚乙二醇", "硬脂富马酸钠", "月桂醇硫酸钠", "甘油二硬脂酸酯"
            ],
            "助流剂": [
                "二氧化硅", "微粉硅胶", "滑石粉", "硬脂酸镁", "淀粉"
            ],
            "包衣材料": [
                "羟丙甲纤维素(HPMC)", "聚乙烯醇(PVA)", "乙基纤维素(EC)",
                "聚丙烯酸树脂", "虫胶", "羟丙甲纤维素邻苯二甲酸酯(HPMCP)",
                "聚乙烯吡咯烷酮(PVP)", "醋酸纤维素(CA)", "邻苯二甲酸醋酸纤维素(CAP)"
            ],
            "增塑剂": [
                "聚乙二醇", "三醋酸甘油酯", "三乙酸甘油酯", "邻苯二甲酸二乙酯",
                "柠檬酸三乙酯", "己二酸二辛酯"
            ],
            "抗氧剂": [
                "抗坏血酸", "维生素E", "BHA", "BHT", "没食子酸丙酯",
                "亚硫酸钠", "焦亚硫酸钠", "硫代硫酸钠"
            ],
            "防腐剂": [
                "苯甲酸", "苯甲酸钠", "对羟基苯甲酸甲酯", "对羟基苯甲酸乙酯",
                "对羟基苯甲酸丙酯", "对羟基苯甲酸丁酯", "山梨酸", "山梨酸钾"
            ],
            "表面活性剂": [
                "吐温80", "吐温20", "司盘80", "司盘20", "月桂醇硫酸钠(SLS)",
                "泊洛沙姆", "卵磷脂", "胆酸钠", "脱氧胆酸钠"
            ],
            "溶剂": [
                "水", "乙醇", "丙二醇", "聚乙二醇400", "甘油", "异丙醇",
                "苯甲醇", "二甲基亚砜(DMSO)", "N-甲基吡咯烷酮(NMP)"
            ],
            "pH调节剂": [
                "盐酸", "硫酸", "磷酸", "氢氧化钠", "氢氧化钾", "碳酸钠",
                "碳酸氢钠", "柠檬酸", "醋酸", "三乙醇胺"
            ],
            "渗透促进剂": [
                "氮酮", "薄荷醇", "油酸", "月桂氮卓酮", "二甲基亚砜",
                "丙二醇", "聚山梨酯", "胆酸盐"
            ],
            "着色剂": [
                "二氧化钛", "氧化铁红", "氧化铁黄", "氧化铁黑", "食用色素",
                "胭脂红", "日落黄", "亮蓝"
            ],
            "矫味剂": [
                "蔗糖", "乳糖", "甘露醇", "山梨醇", "阿斯巴甜", "糖精钠",
                "安赛蜜", "三氯蔗糖", "薄荷脑", "香草醛"
            ]
        }
    
    @staticmethod
    def get_excipient_details() -> List[Dict[str, Any]]:
        """获取常见辅料的详细信息"""
        return [
            # 填充剂
            {
                "id": "lactose",
                "name": "乳糖",
                "chemical_name": "4-O-β-D-吡喃半乳糖基-D-葡萄糖",
                "cas": "63-42-3",
                "function": "填充剂",
                "molecular_weight": 342.3,
                "solubility": "易溶于水",
                "hygroscopicity": "低",
                "thermal_stability": "稳定，加热可能发生美拉德反应",
                "known_incompatibilities": ["含伯氨基药物"],
                "has_reducing_sugar": True,
                "grade": "药用级"
            },
            {
                "id": "mcc",
                "name": "微晶纤维素",
                "chemical_name": "纤维素",
                "cas": "9004-34-6",
                "function": "填充剂、崩解剂、黏合剂",
                "molecular_weight": None,
                "solubility": "不溶于水",
                "hygroscopicity": "中等",
                "thermal_stability": "稳定",
                "known_incompatibilities": [],
                "grade": "药用级"
            },
            {
                "id": "mannitol",
                "name": "甘露醇",
                "chemical_name": "D-甘露糖醇",
                "cas": "69-65-8",
                "function": "填充剂、矫味剂",
                "molecular_weight": 182.17,
                "solubility": "易溶于水",
                "hygroscopicity": "极低",
                "thermal_stability": "稳定",
                "known_incompatibilities": [],
                "has_reducing_sugar": False,
                "grade": "药用级"
            },
            
            # 黏合剂
            {
                "id": "pvp",
                "name": "聚维酮",
                "chemical_name": "聚乙烯吡咯烷酮",
                "cas": "9003-39-8",
                "function": "黏合剂",
                "molecular_weight": "10000-360000",
                "solubility": "易溶于水和乙醇",
                "hygroscopicity": "高",
                "thermal_stability": "稳定",
                "known_incompatibilities": ["某些酚类化合物"],
                "grade": "药用级"
            },
            {
                "id": "hpmc",
                "name": "羟丙甲纤维素",
                "chemical_name": "羟丙基甲基纤维素",
                "cas": "9004-65-3",
                "function": "黏合剂、包衣材料、缓释材料",
                "molecular_weight": "10000-1500000",
                "solubility": "溶于冷水",
                "hygroscopicity": "中等",
                "thermal_stability": "稳定",
                "known_incompatibilities": [],
                "grade": "药用级"
            },
            
            # 崩解剂
            {
                "id": "pvpp",
                "name": "交联聚维酮",
                "chemical_name": "交联聚乙烯吡咯烷酮",
                "cas": "25249-54-1",
                "function": "崩解剂",
                "molecular_weight": ">1000000",
                "solubility": "不溶于水",
                "hygroscopicity": "高",
                "thermal_stability": "稳定",
                "known_incompatibilities": [],
                "grade": "药用级"
            },
            {
                "id": "ccna",
                "name": "交联羧甲纤维素钠",
                "chemical_name": "交联羧甲基纤维素钠",
                "cas": "74811-65-7",
                "function": "崩解剂",
                "molecular_weight": None,
                "solubility": "不溶但溶胀",
                "hygroscopicity": "高",
                "thermal_stability": "稳定",
                "known_incompatibilities": ["强酸"],
                "grade": "药用级"
            },
            
            # 润滑剂
            {
                "id": "mg_stearate",
                "name": "硬脂酸镁",
                "chemical_name": "十八烷酸镁盐",
                "cas": "557-04-0",
                "function": "润滑剂",
                "molecular_weight": 591.24,
                "solubility": "不溶于水",
                "hygroscopicity": "低",
                "thermal_stability": "稳定",
                "known_incompatibilities": ["强酸", "强碱"],
                "has_metal_ion": True,
                "grade": "药用级"
            },
            
            # 表面活性剂
            {
                "id": "tween80",
                "name": "吐温80",
                "chemical_name": "聚山梨酯80",
                "cas": "9005-65-6",
                "function": "表面活性剂、增溶剂",
                "molecular_weight": 1310,
                "solubility": "易溶于水",
                "hygroscopicity": "低",
                "thermal_stability": "可能发生自氧化",
                "oxygen_sensitivity": True,
                "known_incompatibilities": ["酚类", "鞣酸"],
                "grade": "药用级"
            },
            {
                "id": "sls",
                "name": "月桂醇硫酸钠",
                "chemical_name": "十二烷基硫酸钠",
                "cas": "151-21-3",
                "function": "表面活性剂、润湿剂",
                "molecular_weight": 288.38,
                "solubility": "易溶于水",
                "hygroscopicity": "低",
                "thermal_stability": "稳定",
                "known_incompatibilities": ["阳离子表面活性剂"],
                "grade": "药用级"
            },
            
            # 抗氧剂
            {
                "id": "vitamin_c",
                "name": "抗坏血酸",
                "chemical_name": "L-抗坏血酸",
                "cas": "50-81-7",
                "function": "抗氧剂",
                "molecular_weight": 176.12,
                "solubility": "易溶于水",
                "hygroscopicity": "低",
                "thermal_stability": "热敏感",
                "light_stability": "光敏感",
                "has_acidic_group": True,
                "grade": "药用级"
            },
            {
                "id": "vitamin_e",
                "name": "维生素E",
                "chemical_name": "α-生育酚",
                "cas": "59-02-9",
                "function": "抗氧剂",
                "molecular_weight": 430.71,
                "solubility": "脂溶性",
                "hygroscopicity": "极低",
                "thermal_stability": "稳定",
                "oxygen_sensitivity": True,
                "grade": "药用级"
            }
        ]
    
    @staticmethod
    def get_packaging_materials() -> List[Dict[str, Any]]:
        """获取包装材料信息"""
        return [
            {
                "id": "hdpe_bottle",
                "name": "高密度聚乙烯瓶",
                "category": "塑料瓶",
                "water_vapor_permeability": "低",
                "oxygen_permeability": "中等",
                "light_protection": "需要额外遮光",
                "suitable_for": ["固体制剂", "半固体制剂"]
            },
            {
                "id": "glass_bottle",
                "name": "玻璃瓶",
                "category": "玻璃容器",
                "subcategory": ["钠钙玻璃", "硼硅玻璃"],
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "棕色玻璃可遮光",
                "suitable_for": ["液体制剂", "固体制剂"]
            },
            {
                "id": "alu_alu_blister",
                "name": "铝铝泡罩",
                "category": "泡罩包装",
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "优秀",
                "suitable_for": ["片剂", "胶囊"]
            },
            {
                "id": "pvc_pvdc_blister",
                "name": "PVC/PVDC泡罩",
                "category": "泡罩包装",
                "water_vapor_permeability": "低",
                "oxygen_permeability": "低",
                "light_protection": "需要外包装遮光",
                "suitable_for": ["片剂", "胶囊"]
            },
            {
                "id": "pvc_pe_blister",
                "name": "PVC/PE泡罩",
                "category": "泡罩包装",
                "water_vapor_permeability": "中等",
                "oxygen_permeability": "中等",
                "light_protection": "需要外包装遮光",
                "suitable_for": ["片剂", "胶囊"]
            },
            {
                "id": "ampoule_low_borosilicate",
                "name": "低硼硅玻璃安瓿",
                "category": "安瓿",
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "棕色可遮光",
                "suitable_for": ["注射剂"]
            },
            {
                "id": "ampoule_high_borosilicate",
                "name": "高硼硅玻璃安瓿",
                "category": "安瓿",
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "棕色可遮光",
                "suitable_for": ["注射剂"],
                "note": "耐热性更好，适用于终端灭菌"
            },
            {
                "id": "vial_glass",
                "name": "玻璃西林瓶",
                "category": "西林瓶",
                "subcategory": ["Ⅰ型", "Ⅱ型", "Ⅲ型"],
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "棕色可遮光",
                "suitable_for": ["注射剂", "冻干粉针"]
            },
            {
                "id": "aluminum_tube",
                "name": "铝管",
                "category": "软管",
                "water_vapor_permeability": "极低",
                "oxygen_permeability": "极低",
                "light_protection": "优秀",
                "suitable_for": ["软膏", "凝胶"]
            },
            {
                "id": "plastic_tube",
                "name": "塑料复合管",
                "category": "软管",
                "water_vapor_permeability": "低到中等",
                "oxygen_permeability": "低到中等",
                "light_protection": "需要考虑材质",
                "suitable_for": ["软膏", "凝胶", "乳膏"]
            },
            {
                "id": "strip_package",
                "name": "条形包装",
                "category": "复合膜包装",
                "material": "铝塑复合膜",
                "water_vapor_permeability": "低",
                "oxygen_permeability": "低",
                "light_protection": "良好",
                "suitable_for": ["颗粒剂", "粉剂"]
            }
        ]
    
    @staticmethod
    def get_storage_conditions() -> List[Dict[str, str]]:
        """获取储存条件"""
        return [
            {"id": "room_temp", "name": "常温", "temperature": "10-30°C", "humidity": "35-75%RH"},
            {"id": "cool", "name": "阴凉处", "temperature": "不超过20°C", "humidity": "35-75%RH"},
            {"id": "cold", "name": "冷处", "temperature": "2-10°C", "humidity": "35-75%RH"},
            {"id": "freeze", "name": "冷冻", "temperature": "-25至-10°C", "humidity": "N/A"},
            {"id": "dry", "name": "干燥处", "temperature": "常温", "humidity": "不超过45%RH"},
            {"id": "protected_light", "name": "避光", "temperature": "按其他要求", "humidity": "按其他要求"},
            {"id": "airtight", "name": "密闭", "temperature": "按其他要求", "humidity": "按其他要求"},
            {"id": "sealed", "name": "密封", "temperature": "按其他要求", "humidity": "按其他要求"}
        ]
    
    @staticmethod
    def get_production_processes() -> List[Dict[str, Any]]:
        """获取生产工艺"""
        return [
            # 固体制剂工艺
            {
                "id": "direct_compression",
                "name": "直接压片",
                "category": "片剂",
                "description": "原辅料混合后直接压片",
                "suitable_for": ["片剂"]
            },
            {
                "id": "wet_granulation",
                "name": "湿法制粒",
                "category": "片剂/胶囊/颗粒剂",
                "description": "加入黏合剂溶液制粒",
                "suitable_for": ["片剂", "胶囊", "颗粒剂"]
            },
            {
                "id": "dry_granulation",
                "name": "干法制粒",
                "category": "片剂/胶囊",
                "description": "压片后粉碎制粒",
                "suitable_for": ["片剂", "胶囊"]
            },
            {
                "id": "fluid_bed_granulation",
                "name": "流化床制粒",
                "category": "片剂/胶囊/颗粒剂",
                "description": "一步制粒工艺",
                "suitable_for": ["片剂", "胶囊", "颗粒剂"]
            },
            {
                "id": "hot_melt_extrusion",
                "name": "热熔挤出",
                "category": "固体制剂",
                "description": "热熔挤出成型",
                "suitable_for": ["片剂", "植入剂"]
            },
            
            # 液体制剂工艺
            {
                "id": "solution_preparation",
                "name": "溶液配制",
                "category": "液体制剂",
                "description": "直接溶解配制",
                "suitable_for": ["口服液", "注射液", "滴眼液"]
            },
            {
                "id": "suspension_preparation",
                "name": "混悬液配制",
                "category": "液体制剂",
                "description": "分散法或凝聚法制备",
                "suitable_for": ["混悬液", "混悬型注射液"]
            },
            {
                "id": "emulsion_preparation",
                "name": "乳剂配制",
                "category": "液体制剂",
                "description": "乳化法制备",
                "suitable_for": ["乳剂", "静脉乳剂"]
            },
            
            # 半固体制剂工艺
            {
                "id": "ointment_preparation",
                "name": "软膏制备",
                "category": "半固体制剂",
                "description": "研和法或熔和法",
                "suitable_for": ["软膏"]
            },
            {
                "id": "gel_preparation",
                "name": "凝胶制备",
                "category": "半固体制剂",
                "description": "溶胀法制备",
                "suitable_for": ["凝胶"]
            },
            
            # 无菌制剂工艺
            {
                "id": "terminal_sterilization",
                "name": "终端灭菌",
                "category": "无菌制剂",
                "description": "灌装后灭菌",
                "suitable_for": ["注射液", "滴眼液"]
            },
            {
                "id": "aseptic_processing",
                "name": "无菌生产",
                "category": "无菌制剂",
                "description": "无菌条件下生产",
                "suitable_for": ["注射液", "冻干粉针", "生物制品"]
            },
            {
                "id": "lyophilization",
                "name": "冷冻干燥",
                "category": "无菌制剂",
                "description": "冷冻干燥工艺",
                "suitable_for": ["冻干粉针剂"]
            }
        ] 