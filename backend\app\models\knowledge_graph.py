"""
药物稳定性知识图谱模型
简化版本，提供基本的知识查询功能
"""

from typing import Dict, List, Any, Optional
import json

class DrugKnowledgeGraph:
    """药物知识图谱类"""
    
    def __init__(self):
        # 模拟知识库
        self.knowledge_base = {
            "degradation_mechanisms": {
                "水解": {
                    "description": "在水分存在下，化学键断裂",
                    "common_groups": ["酯键", "酰胺键", "糖苷键"],
                    "prevention": ["控制湿度", "使用干燥剂", "pH调节"]
                },
                "氧化": {
                    "description": "与氧气反应导致的化学变化",
                    "common_groups": ["酚羟基", "硫醇", "不饱和键"],
                    "prevention": ["抗氧化剂", "充氮包装", "避光"]
                },
                "光解": {
                    "description": "光照引起的化学分解",
                    "common_groups": ["芳香环", "共轭体系"],
                    "prevention": ["避光包装", "紫外线吸收剂"]
                }
            },
            "excipient_interactions": {
                "微晶纤维素": {
                    "compatibility": "good",
                    "interactions": ["物理吸附"],
                    "notes": "通常相容性良好"
                },
                "乳糖": {
                    "compatibility": "moderate",
                    "interactions": ["美拉德反应"],
                    "notes": "与胺类药物可能发生反应"
                },
                "硬脂酸镁": {
                    "compatibility": "good",
                    "interactions": ["络合反应"],
                    "notes": "与某些金属敏感药物需注意"
                }
            },
            "stability_cases": [
                {
                    "drug": "阿司匹林",
                    "issue": "水解",
                    "solution": "控制湿度，使用肠溶包衣",
                    "reference": "经典案例"
                },
                {
                    "drug": "维生素C",
                    "issue": "氧化",
                    "solution": "添加抗氧化剂，充氮包装",
                    "reference": "经典案例"
                }
            ]
        }
    
    def query_knowledge(self, query_type: str, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询知识图谱"""
        try:
            if query_type == "degradation_mechanism":
                mechanism = parameters.get("mechanism", "")
                if mechanism in self.knowledge_base["degradation_mechanisms"]:
                    return [self.knowledge_base["degradation_mechanisms"][mechanism]]
                return []
            
            elif query_type == "excipient_compatibility":
                excipient = parameters.get("excipient", "")
                if excipient in self.knowledge_base["excipient_interactions"]:
                    return [self.knowledge_base["excipient_interactions"][excipient]]
                return []
            
            elif query_type == "similar_cases":
                issue = parameters.get("issue", "")
                cases = []
                for case in self.knowledge_base["stability_cases"]:
                    if issue.lower() in case["issue"].lower():
                        cases.append(case)
                return cases
            
            else:
                return []
                
        except Exception as e:
            print(f"知识图谱查询错误: {e}")
            return []
    
    def predict_degradation_pathway(self, smiles: str, conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """预测降解途径"""
        # 简化的降解途径预测
        pathways = []
        
        # 基于条件预测
        if conditions.get("humidity", 0) > 60:
            pathways.append({
                "pathway": "水解",
                "probability": 0.7,
                "conditions": "高湿度环境"
            })
        
        if conditions.get("temperature", 25) > 40:
            pathways.append({
                "pathway": "热降解",
                "probability": 0.6,
                "conditions": "高温环境"
            })
        
        # 默认氧化途径
        pathways.append({
            "pathway": "氧化",
            "probability": 0.3,
            "conditions": "常规条件"
        })
        
        return pathways
    
    def get_stability_recommendations(self, drug_name: str, issues: List[str]) -> List[str]:
        """获取稳定性建议"""
        recommendations = []
        
        for issue in issues:
            if "水解" in issue:
                recommendations.extend([
                    "控制包装材料的水蒸气透过率",
                    "添加适量干燥剂",
                    "优化pH值至稳定范围"
                ])
            elif "氧化" in issue:
                recommendations.extend([
                    "添加抗氧化剂如BHT、BHA",
                    "使用充氮包装",
                    "选择低透氧性包装材料"
                ])
            elif "光解" in issue:
                recommendations.extend([
                    "使用避光包装",
                    "添加紫外线吸收剂",
                    "控制储存环境光照"
                ])
        
        # 去重
        return list(set(recommendations))

# 为了兼容性，也提供这个别名
DrugStabilityKnowledgeGraph = DrugKnowledgeGraph

# 兼容旧接口：定义KnowledgeGraph为DrugKnowledgeGraph的别名
KnowledgeGraph = DrugKnowledgeGraph 