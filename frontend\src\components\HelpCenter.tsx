import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>, List } from 'antd';
import { useTranslation } from 'react-i18next';

const faq = {
  zh: [
    { q: '如何新建项目？', a: '点击首页或项目管理页的"新建项目"按钮，填写项目信息后保存。' },
    { q: '如何批量导入数据？', a: '在数据输入页选择对应Tab，点击"批量导入（Excel）"按钮，上传模板文件。' },
  ],
  en: [
    { q: 'How to create a new project?', a: 'Click the "New Project" button on the homepage or project management page, fill in the information and save.' },
    { q: 'How to batch import data?', a: 'Go to Data Input, select the tab, click "Batch Import (Excel)", and upload the template.' },
  ]
};
const guide = {
  zh: [
    { q: '药物信息填写', a: '至少填写名称、分子式、CAS号或SMILES中的一项。' },
    { q: '分析结果导出', a: '在分析结果页点击"导出"按钮，可下载PDF或Excel报告。' },
  ],
  en: [
    { q: 'Drug info input', a: 'Fill at least one of name, formula, CAS or SMILES.' },
    { q: 'Export analysis', a: 'Click "Export" on result page to download PDF/Excel.' },
  ]
};
const terms = {
  zh: [
    { q: 'SMILES', a: '一种描述分子结构的字符串格式。' },
    { q: 'Zone IV', a: '高温高湿稳定性试验区。' },
  ],
  en: [
    { q: 'SMILES', a: 'A string format describing molecular structure.' },
    { q: 'Zone IV', a: 'High temp/humidity stability test zone.' },
  ]
};

const HelpCenter: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { t, i18n } = useTranslation();
  const lang = i18n.language.startsWith('en') ? 'en' : 'zh';
  return (
    <>
      <Button style={{ position: 'fixed', right: 32, bottom: 32, zIndex: 1000 }} onClick={() => setOpen(true)} aria-label={t('Help Center')}>{t('Help')}</Button>
      <Modal open={open} onCancel={() => setOpen(false)} footer={null} title={t('Help Center')} width={600}>
        <Tabs defaultActiveKey="faq" items={[
          { key: 'faq', label: t('FAQ'), children: <List dataSource={faq[lang]} renderItem={item => <List.Item><b>{item.q}</b><div>{item.a}</div></List.Item>} /> },
          { key: 'guide', label: t('Guide'), children: <List dataSource={guide[lang]} renderItem={item => <List.Item><b>{item.q}</b><div>{item.a}</div></List.Item>} /> },
          { key: 'terms', label: t('Terms'), children: <List dataSource={terms[lang]} renderItem={item => <List.Item><b>{item.q}</b><div>{item.a}</div></List.Item>} /> },
        ]} />
      </Modal>
    </>
  );
};

export default HelpCenter; 