"""
性能监控和指标收集配置
"""

import os
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from functools import wraps
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.enable_monitoring = os.getenv("ENABLE_METRICS", "false").lower() == "true"
        self.metrics_port = int(os.getenv("METRICS_PORT", "9090"))
        
        # 性能指标存储
        self.request_times = defaultdict(deque)  # 请求响应时间
        self.request_counts = defaultdict(int)   # 请求计数
        self.error_counts = defaultdict(int)     # 错误计数
        self.active_connections = 0              # 活跃连接数
        
        # 系统指标
        self.system_metrics = {
            "cpu_usage": deque(maxlen=100),
            "memory_usage": deque(maxlen=100),
            "disk_usage": deque(maxlen=100),
            "network_io": deque(maxlen=100)
        }
        
        # 数据库指标
        self.db_metrics = {
            "query_times": deque(maxlen=1000),
            "connection_pool_size": 0,
            "active_connections": 0
        }
        
        # 缓存指标
        self.cache_metrics = {
            "hits": 0,
            "misses": 0,
            "hit_rate": 0.0
        }
    
    def record_request(self, endpoint: str, method: str, response_time: float, status_code: int):
        """记录请求指标"""
        if not self.enable_monitoring:
            return
        
        key = f"{method}:{endpoint}"
        
        # 记录响应时间
        self.request_times[key].append(response_time)
        if len(self.request_times[key]) > 1000:  # 保持最近1000个记录
            self.request_times[key].popleft()
        
        # 记录请求计数
        self.request_counts[key] += 1
        
        # 记录错误计数
        if status_code >= 400:
            self.error_counts[key] += 1
    
    def record_db_query(self, query_time: float):
        """记录数据库查询时间"""
        if not self.enable_monitoring:
            return
        
        self.db_metrics["query_times"].append(query_time)
    
    def record_cache_hit(self, hit: bool):
        """记录缓存命中"""
        if not self.enable_monitoring:
            return
        
        if hit:
            self.cache_metrics["hits"] += 1
        else:
            self.cache_metrics["misses"] += 1
        
        total = self.cache_metrics["hits"] + self.cache_metrics["misses"]
        if total > 0:
            self.cache_metrics["hit_rate"] = self.cache_metrics["hits"] / total
    
    def collect_system_metrics(self):
        """收集系统指标"""
        if not self.enable_monitoring:
            return
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.system_metrics["cpu_usage"].append(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.system_metrics["memory_usage"].append(memory.percent)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.system_metrics["disk_usage"].append(disk_percent)
            
            # 网络IO
            network = psutil.net_io_counters()
            self.system_metrics["network_io"].append({
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "timestamp": datetime.now()
            })
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "requests": {},
            "system": {},
            "database": {},
            "cache": self.cache_metrics.copy()
        }
        
        # 请求指标摘要
        for endpoint, times in self.request_times.items():
            if times:
                summary["requests"][endpoint] = {
                    "count": self.request_counts[endpoint],
                    "errors": self.error_counts[endpoint],
                    "avg_response_time": sum(times) / len(times),
                    "min_response_time": min(times),
                    "max_response_time": max(times),
                    "error_rate": self.error_counts[endpoint] / self.request_counts[endpoint] if self.request_counts[endpoint] > 0 else 0
                }
        
        # 系统指标摘要
        for metric, values in self.system_metrics.items():
            if values and metric != "network_io":
                summary["system"][metric] = {
                    "current": values[-1] if values else 0,
                    "average": sum(values) / len(values) if values else 0,
                    "max": max(values) if values else 0
                }
        
        # 数据库指标摘要
        if self.db_metrics["query_times"]:
            query_times = list(self.db_metrics["query_times"])
            summary["database"] = {
                "avg_query_time": sum(query_times) / len(query_times),
                "max_query_time": max(query_times),
                "total_queries": len(query_times),
                "connection_pool_size": self.db_metrics["connection_pool_size"],
                "active_connections": self.db_metrics["active_connections"]
            }
        
        return summary
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        try:
            # 系统资源检查
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent
            disk_usage = psutil.disk_usage('/').percent
            
            status["checks"]["cpu"] = {
                "status": "healthy" if cpu_usage < 80 else "warning" if cpu_usage < 95 else "critical",
                "usage": cpu_usage
            }
            
            status["checks"]["memory"] = {
                "status": "healthy" if memory_usage < 80 else "warning" if memory_usage < 95 else "critical",
                "usage": memory_usage
            }
            
            status["checks"]["disk"] = {
                "status": "healthy" if disk_usage < 80 else "warning" if disk_usage < 95 else "critical",
                "usage": disk_usage
            }
            
            # 数据库连接检查
            try:
                from app.config.database_config import DatabaseHealthCheck
                db_healthy = DatabaseHealthCheck.check_connection()
                status["checks"]["database"] = {
                    "status": "healthy" if db_healthy else "critical",
                    "connected": db_healthy
                }
            except Exception as e:
                status["checks"]["database"] = {
                    "status": "critical",
                    "error": str(e)
                }
            
            # 缓存检查
            try:
                from app.config.cache_config import cache
                cache_stats = cache.get_stats()
                status["checks"]["cache"] = {
                    "status": "healthy",
                    "type": cache_stats.get("type", "unknown")
                }
            except Exception as e:
                status["checks"]["cache"] = {
                    "status": "warning",
                    "error": str(e)
                }
            
            # 整体状态评估
            critical_count = sum(1 for check in status["checks"].values() if check["status"] == "critical")
            warning_count = sum(1 for check in status["checks"].values() if check["status"] == "warning")
            
            if critical_count > 0:
                status["status"] = "critical"
            elif warning_count > 0:
                status["status"] = "warning"
            
        except Exception as e:
            status["status"] = "critical"
            status["error"] = str(e)
        
        return status

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(endpoint_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not performance_monitor.enable_monitoring:
                return func(*args, **kwargs)
            
            start_time = time.time()
            status_code = 200
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = 500
                raise
            finally:
                end_time = time.time()
                response_time = end_time - start_time
                
                endpoint = endpoint_name or func.__name__
                performance_monitor.record_request(
                    endpoint=endpoint,
                    method="UNKNOWN",  # 在实际使用中需要从请求中获取
                    response_time=response_time,
                    status_code=status_code
                )
        
        return wrapper
    return decorator

def monitor_db_query(func):
    """数据库查询监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not performance_monitor.enable_monitoring:
            return func(*args, **kwargs)
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            query_time = end_time - start_time
            performance_monitor.record_db_query(query_time)
    
    return wrapper

class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.alert_thresholds = {
            "cpu_usage": 90,
            "memory_usage": 90,
            "disk_usage": 85,
            "response_time": 5.0,  # 秒
            "error_rate": 0.1      # 10%
        }
        
        self.alert_history = deque(maxlen=1000)
    
    def check_alerts(self):
        """检查告警条件"""
        alerts = []
        
        try:
            # 系统资源告警
            cpu_usage = psutil.cpu_percent()
            if cpu_usage > self.alert_thresholds["cpu_usage"]:
                alerts.append({
                    "type": "cpu_high",
                    "message": f"CPU使用率过高: {cpu_usage}%",
                    "severity": "critical",
                    "timestamp": datetime.now()
                })
            
            memory_usage = psutil.virtual_memory().percent
            if memory_usage > self.alert_thresholds["memory_usage"]:
                alerts.append({
                    "type": "memory_high",
                    "message": f"内存使用率过高: {memory_usage}%",
                    "severity": "critical",
                    "timestamp": datetime.now()
                })
            
            # 响应时间告警
            for endpoint, times in performance_monitor.request_times.items():
                if times:
                    avg_time = sum(times) / len(times)
                    if avg_time > self.alert_thresholds["response_time"]:
                        alerts.append({
                            "type": "slow_response",
                            "message": f"接口响应缓慢: {endpoint}, 平均响应时间: {avg_time:.2f}s",
                            "severity": "warning",
                            "timestamp": datetime.now()
                        })
            
            # 错误率告警
            for endpoint in performance_monitor.request_counts:
                total_requests = performance_monitor.request_counts[endpoint]
                error_requests = performance_monitor.error_counts[endpoint]
                if total_requests > 0:
                    error_rate = error_requests / total_requests
                    if error_rate > self.alert_thresholds["error_rate"]:
                        alerts.append({
                            "type": "high_error_rate",
                            "message": f"接口错误率过高: {endpoint}, 错误率: {error_rate:.2%}",
                            "severity": "critical",
                            "timestamp": datetime.now()
                        })
            
            # 记录告警
            for alert in alerts:
                self.alert_history.append(alert)
                logger.warning(f"告警: {alert['message']}")
            
        except Exception as e:
            logger.error(f"告警检查失败: {e}")
        
        return alerts

# 全局告警管理器实例
alert_manager = AlertManager()
