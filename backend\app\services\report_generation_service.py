"""
报告生成服务
生成专业的PDF稳定性研究报告
"""
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import io
from datetime import datetime
import base64
import json

# PDF生成相关
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    PageBreak, Image, KeepTogether, ListFlowable, ListItem
)
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.lineplots import LinePlot
from reportlab.graphics.widgets.markers import makeMarker
from reportlab.graphics import renderPDF

# 图表生成
import matplotlib
matplotlib.use('Agg')  # 非GUI后端
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from io import BytesIO

from sqlalchemy.orm import Session

from app.models.project import ProjectORM  # Assuming a model to fetch analysis data
from app.models.export import ExportHistory # 修正导入，原为 ExportHistoryORM

logger = logging.getLogger(__name__)


class ReportGenerationService:
    """报告生成服务类"""
    
    def __init__(self, db: Session, config: Dict[str, Any]):
        self.db = db
        self.config = config
        self.template_dir = Path("templates/reports")
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir = Path("temp/reports")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 注册中文字体（需要提供字体文件）
        self._register_fonts()
        
        # 样式配置
        self.styles = self._create_styles()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载系统配置文件"""
        # Config is now injected, this method is no longer needed in this form
        # It can be removed or kept for other purposes if necessary
        return self.config

    def _register_fonts(self):
        """注册中文字体"""
        try:
            # 从配置中获取字体路径
            font_path_str = self.config.get("font_path", "fonts/SourceHanSansCN-Regular.otf")
            font_path = Path(font_path_str)

            if font_path.exists():
                pdfmetrics.registerFont(TTFont('SourceHanSans', str(font_path)))
                self.chinese_font = 'SourceHanSans'
            else:
                # 使用默认字体
                self.chinese_font = 'Helvetica'
                logger.warning(f"中文字体 '{font_path}' 未找到，使用默认字体")
        except Exception as e:
            logger.error(f"字体注册失败: {e}")
            self.chinese_font = 'Helvetica'
    
    def _create_styles(self) -> Dict[str, ParagraphStyle]:
        """创建报告样式"""
        styles = getSampleStyleSheet()
        
        # 标题样式
        styles.add(ParagraphStyle(
            name='ChineseTitle',
            parent=styles['Title'],
            fontName=self.chinese_font,
            fontSize=24,
            textColor=colors.HexColor('#1a5490'),
            spaceAfter=30,
            alignment=TA_CENTER
        ))
        
        # 章节标题
        styles.add(ParagraphStyle(
            name='ChineseHeading1',
            parent=styles['Heading1'],
            fontName=self.chinese_font,
            fontSize=18,
            textColor=colors.HexColor('#1a5490'),
            spaceAfter=12,
            spaceBefore=12
        ))
        
        # 子标题
        styles.add(ParagraphStyle(
            name='ChineseHeading2',
            parent=styles['Heading2'],
            fontName=self.chinese_font,
            fontSize=14,
            textColor=colors.HexColor('#2c5aa0'),
            spaceAfter=10,
            spaceBefore=10
        ))
        
        # 正文
        styles.add(ParagraphStyle(
            name='ChineseNormal',
            parent=styles['Normal'],
            fontName=self.chinese_font,
            fontSize=11,
            leading=16,
            alignment=TA_JUSTIFY,
            spaceAfter=8
        ))
        
        # 表格标题
        styles.add(ParagraphStyle(
            name='TableHeader',
            parent=styles['Normal'],
            fontName=self.chinese_font,
            fontSize=10,
            textColor=colors.white,
            alignment=TA_CENTER
        ))
        
        return styles
    
    def _save_report(self, pdf_data: bytes, filename: str) -> Path:
        """Saves the PDF data to a file in the temp directory."""
        output_path = self.temp_dir / filename
        with open(output_path, "wb") as f:
            f.write(pdf_data)
        logger.info(f"Report saved to {output_path}")
        return output_path

    async def generate_and_save_stability_report(
        self,
        analysis_data: Dict[str, Any],
        language: str = "zh",
        include_raw_data: bool = False
    ) -> Dict[str, Any]:
        """
        Generates and saves a stability analysis report.
        This is the function to be run in the background.
        """
        try:
            # 创建PDF文档
            buffer = BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # 构建报告内容
            story = []
            
            # 1. 封面
            story.extend(self._create_cover_page(analysis_data, language))
            story.append(PageBreak())
            
            # 2. 执行摘要
            story.extend(self._create_executive_summary(analysis_data, language))
            story.append(PageBreak())
            
            # 3. 药物信息
            story.extend(self._create_drug_info_section(analysis_data, language))
            
            # 4. 稳定性预测结果
            story.extend(self._create_stability_prediction_section(analysis_data, language))
            
            # 5. 相容性评估
            if 'compatibility_assessment' in analysis_data:
                story.extend(self._create_compatibility_section(analysis_data, language))
            
            # 6. 图表
            story.extend(await self._create_charts_section(analysis_data, language))
            
            # 7. 建议
            story.extend(self._create_recommendations_section(analysis_data, language))
            
            # 8. 附录（原始数据）
            if include_raw_data:
                story.append(PageBreak())
                story.extend(self._create_appendix(analysis_data, language))
            
            # 生成PDF
            doc.build(story)
            
            # 获取PDF数据
            pdf_data = buffer.getvalue()
            buffer.close()
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            drug_name = analysis_data.get('drug_name', 'unknown')
            filename = f"stability_report_{drug_name}_{timestamp}.pdf"

            # 保存文件
            output_path = self._save_report(pdf_data, filename)
            
            # Here, you would typically update a database record with the task status and file path.
            # For now, we'll just log it.
            logger.info(f"Background report generation complete for {filename}.")
            
            # This return value is for logging/debugging the background task, not sent to the client.
            return {
                "success": True,
                "filename": filename,
                "path": str(output_path),
                "size": len(pdf_data)
            }
            
        except Exception as e:
            logger.error(f"Background report generation failed: {e}", exc_info=True)
            # Log failure to a database or monitoring system.
            return {"success": False, "error": str(e)}
    
    def _create_cover_page(self, data: Dict, language: str) -> List:
        """创建封面页"""
        elements = []
        
        # 标题
        title = "药物稳定性研究报告" if language == "zh" else "Drug Stability Study Report"
        elements.append(Spacer(1, 3*inch))
        elements.append(Paragraph(title, self.styles['ChineseTitle']))
        
        # 药物名称
        drug_name = data.get('drug_name', 'N/A')
        elements.append(Spacer(1, 0.5*inch))
        elements.append(Paragraph(
            f"<b>{drug_name}</b>",
            ParagraphStyle(
                'DrugName',
                parent=self.styles['ChineseNormal'],
                fontSize=20,
                alignment=TA_CENTER
            )
        ))
        
        # 报告信息
        elements.append(Spacer(1, 2*inch))
        info_data = [
            ["报告日期", datetime.now().strftime("%Y年%m月%d日")],
            ["分析类型", "综合稳定性分析"],
            ["报告版本", "1.0"]
        ]
        
        info_table = Table(info_data, colWidths=[3*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        elements.append(info_table)
        
        return elements
    
    def _create_executive_summary(self, data: Dict, language: str) -> List:
        """创建执行摘要"""
        elements = []
        
        elements.append(Paragraph("执行摘要", self.styles['ChineseHeading1']))
        
        # 提取关键信息
        drug_name = data.get('drug_name', 'N/A')
        prediction = data.get('prediction', {})
        recommendations = data.get('recommendations', [])
        
        # 摘要内容
        summary_points = []
        
        # 稳定性预测
        if prediction and 'long_term' in prediction:
            t90 = prediction['long_term'].get('t90')
            if t90:
                summary_points.append(
                    f"预测长期稳定性（25°C/60%RH）：{t90}个月（90%含量）"
                )
        
        # 关键风险
        risk_factors = prediction.get('risk_factors', [])
        if risk_factors:
            summary_points.append(
                f"主要降解风险：{', '.join(risk_factors[:3])}"
            )
        
        # 相容性
        compatibility = data.get('compatibility_assessment', {})
        if compatibility:
            overall_risk = compatibility.get('overall_risk', 'unknown')
            risk_text = {
                'low': '低风险',
                'medium': '中等风险',
                'high': '高风险'
            }.get(overall_risk, overall_risk)
            summary_points.append(f"原辅料相容性：{risk_text}")
        
        # 生成摘要列表
        for point in summary_points:
            elements.append(Paragraph(f"• {point}", self.styles['ChineseNormal']))
        
        # 关键建议
        if recommendations:
            elements.append(Spacer(1, 0.3*inch))
            elements.append(Paragraph("关键建议：", self.styles['ChineseHeading2']))
            
            # 只显示前3个最重要的建议
            for i, rec in enumerate(recommendations[:3], 1):
                content = rec.get('content', rec) if isinstance(rec, dict) else str(rec)
                elements.append(Paragraph(
                    f"{i}. {content}",
                    self.styles['ChineseNormal']
                ))
        
        return elements
    
    def _create_drug_info_section(self, data: Dict, language: str) -> List:
        """创建药物信息部分"""
        elements = []
        
        elements.append(Paragraph("1. 药物信息", self.styles['ChineseHeading1']))
        
        # 基本信息表格
        drug_info = []
        drug_info.append(["项目", "内容"])
        drug_info.append(["药物名称", data.get('drug_name', 'N/A')])
        
        # 结构信息
        structure_analysis = data.get('structure_analysis', {})
        if structure_analysis:
            properties = structure_analysis.get('properties', {})
            drug_info.append(["分子量", f"{properties.get('molecular_weight', 'N/A')} g/mol"])
            drug_info.append(["LogP", str(properties.get('logp', 'N/A'))])
            drug_info.append(["TPSA", f"{properties.get('tpsa', 'N/A')} Ų"])
            
            # 官能团
            functional_groups = structure_analysis.get('functional_groups', {})
            active_groups = [k for k, v in functional_groups.items() if v]
            if active_groups:
                drug_info.append(["活性官能团", ", ".join(active_groups)])
        
        # 创建表格
        info_table = Table(drug_info, colWidths=[2*inch, 4*inch])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1a5490')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f0f0f0')]),
        ]))
        
        elements.append(info_table)
        
        return elements
    
    def _create_stability_prediction_section(self, data: Dict, language: str) -> List:
        """创建稳定性预测部分"""
        elements = []
        
        elements.append(Spacer(1, 0.3*inch))
        elements.append(Paragraph("2. 稳定性预测结果", self.styles['ChineseHeading1']))
        
        prediction = data.get('prediction', {})
        
        # 长期稳定性
        if 'long_term' in prediction:
            elements.append(Paragraph("2.1 长期稳定性预测", self.styles['ChineseHeading2']))
            
            long_term = prediction['long_term']
            stability_data = []
            stability_data.append(["条件", "t90 (月)", "t95 (月)", "置信区间"])
            
            t90 = long_term.get('t90', 'N/A')
            t95 = long_term.get('t95', 'N/A')
            ci = long_term.get('ci', ['N/A', 'N/A'])
            
            stability_data.append([
                "25°C/60%RH",
                str(t90),
                str(t95),
                f"[{ci[0]}, {ci[1]}]"
            ])
            
            # 加速试验
            if 'accelerated' in prediction:
                acc = prediction['accelerated']
                stability_data.append([
                    "40°C/75%RH",
                    str(acc.get('t90', 'N/A')),
                    str(acc.get('t95', 'N/A')),
                    f"[{acc.get('ci', ['N/A', 'N/A'])[0]}, {acc.get('ci', ['N/A', 'N/A'])[1]}]"
                ])
            
            stability_table = Table(stability_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 2*inch])
            stability_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1a5490')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f0f0f0')]),
            ]))
            
            elements.append(stability_table)
        
        # 降解风险
        risk_factors = prediction.get('risk_factors', [])
        if risk_factors:
            elements.append(Spacer(1, 0.2*inch))
            elements.append(Paragraph("2.2 主要降解风险", self.styles['ChineseHeading2']))
            
            for risk in risk_factors:
                elements.append(Paragraph(f"• {risk}", self.styles['ChineseNormal']))
        
        return elements
    
    def _create_compatibility_section(self, data: Dict, language: str) -> List:
        """创建相容性评估部分"""
        elements = []
        
        elements.append(Spacer(1, 0.3*inch))
        elements.append(Paragraph("3. 原辅料相容性评估", self.styles['ChineseHeading1']))
        
        compatibility = data.get('compatibility_assessment', {})
        results = compatibility.get('compatibility_results', [])
        
        if results:
            # 创建相容性表格
            compat_data = []
            compat_data.append(["辅料名称", "风险等级", "主要问题", "建议措施"])
            
            for result in results:
                risk_level = result.get('risk_level', 'unknown')
                risk_color = {
                    'low': '低',
                    'medium': '中',
                    'high': '高'
                }.get(risk_level, risk_level)
                
                incompatibilities = result.get('incompatibilities', [])
                recommendations = result.get('recommendations', [])
                
                compat_data.append([
                    result.get('excipient', 'N/A'),
                    risk_color,
                    '\n'.join(incompatibilities[:2]) if incompatibilities else '无',
                    '\n'.join(recommendations[:2]) if recommendations else '无'
                ])
            
            compat_table = Table(
                compat_data,
                colWidths=[1.5*inch, 1*inch, 2*inch, 2.5*inch]
            )
            
            # 设置表格样式，包括风险等级的颜色编码
            style_commands = [
                ('FONTNAME', (0, 0), (-1, -1), self.chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1a5490')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ]
            
            # 根据风险等级设置行背景色
            for i, result in enumerate(results, 1):
                risk_level = result.get('risk_level', 'unknown')
                if risk_level == 'high':
                    style_commands.append(
                        ('BACKGROUND', (1, i), (1, i), colors.HexColor('#ffcccc'))
                    )
                elif risk_level == 'medium':
                    style_commands.append(
                        ('BACKGROUND', (1, i), (1, i), colors.HexColor('#fff3cd'))
                    )
                elif risk_level == 'low':
                    style_commands.append(
                        ('BACKGROUND', (1, i), (1, i), colors.HexColor('#d4edda'))
                    )
            
            compat_table.setStyle(TableStyle(style_commands))
            elements.append(compat_table)
        
        return elements
    
    async def _create_charts_section(self, data: Dict, language: str) -> List:
        """创建图表部分"""
        elements = []
        
        elements.append(Spacer(1, 0.3*inch))
        elements.append(Paragraph("4. 数据可视化", self.styles['ChineseHeading1']))
        
        # 4.1 稳定性曲线
        if 'kinetic_analysis' in data and data['kinetic_analysis']:
            elements.append(Paragraph("4.1 稳定性动力学曲线", self.styles['ChineseHeading2']))
            
            # 生成稳定性曲线图
            stability_plot = await self._generate_stability_plot(data['kinetic_analysis'])
            if stability_plot:
                elements.append(Image(stability_plot, width=5*inch, height=3*inch))
            
            elements.append(Spacer(1, 0.2*inch))
        
        # 4.2 SHAP特征重要性图
        if 'explainability' in data and data['explainability']:
            elements.append(Paragraph("4.2 预测模型特征重要性", self.styles['ChineseHeading2']))
            
            explainability = data['explainability']
            if 'summary_plot' in explainability and explainability['summary_plot']:
                # 如果有Base64编码的图片
                try:
                    img_data = explainability['summary_plot'].split(',')[1]
                    img_bytes = base64.b64decode(img_data)
                    img_buffer = BytesIO(img_bytes)
                    elements.append(Image(img_buffer, width=5*inch, height=3*inch))
                except Exception as e:
                    logger.error(f"SHAP图表加载失败: {e}")
            
            elements.append(Spacer(1, 0.2*inch))
        
        # 4.3 相容性风险分布图
        if 'compatibility_assessment' in data:
            elements.append(Paragraph("4.3 相容性风险分布", self.styles['ChineseHeading2']))
            
            risk_chart = await self._generate_compatibility_risk_chart(
                data['compatibility_assessment']
            )
            if risk_chart:
                elements.append(Image(risk_chart, width=4*inch, height=3*inch))
        
        return elements
    
    def _create_recommendations_section(self, data: Dict, language: str) -> List:
        """创建建议部分"""
        elements = []
        
        elements.append(Spacer(1, 0.3*inch))
        elements.append(Paragraph("5. 建议与结论", self.styles['ChineseHeading1']))
        
        recommendations = data.get('recommendations', [])
        
        # 按类别组织建议
        critical_recs = []
        caution_recs = []
        info_recs = []
        
        for rec in recommendations:
            if isinstance(rec, dict):
                rec_type = rec.get('type', 'info')
                content = rec.get('content', '')
                
                if rec_type == 'critical':
                    critical_recs.append(content)
                elif rec_type in ['warning', 'caution']:
                    caution_recs.append(content)
                else:
                    info_recs.append(content)
            else:
                info_recs.append(str(rec))
        
        # 关键建议
        if critical_recs:
            elements.append(Paragraph("5.1 关键建议", self.styles['ChineseHeading2']))
            for rec in critical_recs:
                elements.append(Paragraph(
                    f"⚠️ {rec}",
                    ParagraphStyle(
                        'Critical',
                        parent=self.styles['ChineseNormal'],
                        textColor=colors.red
                    )
                ))
        
        # 注意事项
        if caution_recs:
            elements.append(Paragraph("5.2 注意事项", self.styles['ChineseHeading2']))
            for rec in caution_recs:
                elements.append(Paragraph(f"• {rec}", self.styles['ChineseNormal']))
        
        # 一般建议
        if info_recs:
            elements.append(Paragraph("5.3 一般建议", self.styles['ChineseHeading2']))
            for rec in info_recs:
                elements.append(Paragraph(f"• {rec}", self.styles['ChineseNormal']))
        
        # 结论
        elements.append(Spacer(1, 0.3*inch))
        elements.append(Paragraph("5.4 结论", self.styles['ChineseHeading2']))
        
        # 生成总结性结论
        conclusion = self._generate_conclusion(data)
        elements.append(Paragraph(conclusion, self.styles['ChineseNormal']))
        
        return elements
    
    def _create_appendix(self, data: Dict, language: str) -> List:
        """创建附录（原始数据）"""
        elements = []
        
        elements.append(Paragraph("附录：原始数据", self.styles['ChineseHeading1']))
        
        # 将原始数据格式化为表格
        # 这里简化处理，实际可能需要更复杂的格式化
        
        elements.append(Paragraph(
            "注：完整的原始数据请参考系统导出的JSON文件。",
            self.styles['ChineseNormal']
        ))
        
        return elements
    
    async def _generate_stability_plot(self, kinetic_data: Dict) -> Optional[BytesIO]:
        """生成稳定性曲线图"""
        try:
            plt.figure(figsize=(8, 6))
            plt.style.use('seaborn-v0_8-darkgrid')
            
            # 提取数据
            if kinetic_data and 'data_points' in kinetic_data:
                times = [p['time'] for p in kinetic_data['data_points']]
                values = [p['value'] for p in kinetic_data['data_points']]
                
                # 绘制散点图
                plt.scatter(times, values, color='blue', s=50, alpha=0.6, label='实测值')
                
                # 如果有拟合曲线
                if 'fitted_curve' in kinetic_data:
                    fit_times = kinetic_data['fitted_curve']['times']
                    fit_values = kinetic_data['fitted_curve']['values']
                    plt.plot(fit_times, fit_values, 'r-', linewidth=2, label='拟合曲线')
                
                # 添加90%线
                plt.axhline(y=90, color='green', linestyle='--', label='90%含量线')
                
                plt.xlabel('时间 (月)', fontsize=12)
                plt.ylabel('含量 (%)', fontsize=12)
                plt.title('稳定性动力学曲线', fontsize=14)
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                # 保存到BytesIO
                buffer = BytesIO()
                plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
                plt.close()
                
                buffer.seek(0)
                return buffer
            
            return None
            
        except Exception as e:
            logger.error(f"生成稳定性曲线失败: {e}")
            return None
    
    async def _generate_compatibility_risk_chart(self, compatibility_data: Dict) -> Optional[BytesIO]:
        """生成相容性风险分布图"""
        try:
            results = compatibility_data.get('compatibility_results', [])
            if not results:
                return None
            
            # 统计风险等级
            risk_counts = {'low': 0, 'medium': 0, 'high': 0}
            for result in results:
                risk_level = result.get('risk_level', 'unknown')
                if risk_level in risk_counts:
                    risk_counts[risk_level] += 1
            
            # 创建饼图
            plt.figure(figsize=(6, 6))
            
            labels = ['低风险', '中等风险', '高风险']
            sizes = [risk_counts['low'], risk_counts['medium'], risk_counts['high']]
            colors = ['#28a745', '#ffc107', '#dc3545']
            
            # 只显示非零的部分
            non_zero_indices = [i for i, size in enumerate(sizes) if size > 0]
            labels = [labels[i] for i in non_zero_indices]
            sizes = [sizes[i] for i in non_zero_indices]
            colors = [colors[i] for i in non_zero_indices]
            
            if sizes:
                plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                       startangle=90, textprops={'fontsize': 12})
                plt.title('辅料相容性风险分布', fontsize=14)
                
                # 保存到BytesIO
                buffer = BytesIO()
                plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
                plt.close()
                
                buffer.seek(0)
                return buffer
            
            return None
            
        except Exception as e:
            logger.error(f"生成相容性风险图失败: {e}")
            return None
    
    def _generate_conclusion(self, data: Dict) -> str:
        """生成总结性结论"""
        drug_name = data.get('drug_name', '该药物')
        prediction = data.get('prediction', {})
        
        # 获取预测货架期
        t90 = None
        if 'long_term' in prediction:
            t90 = prediction['long_term'].get('t90')
        
        # 获取相容性风险
        compatibility = data.get('compatibility_assessment', {})
        overall_risk = compatibility.get('overall_risk', 'unknown')
        
        # 生成结论
        conclusion_parts = []
        
        # 稳定性结论
        if t90:
            if t90 >= 24:
                conclusion_parts.append(
                    f"基于综合分析，{drug_name}在推荐的储存条件下预计可达到{t90}个月的货架期，"
                    "满足常规药品的稳定性要求。"
                )
            elif t90 >= 12:
                conclusion_parts.append(
                    f"分析表明，{drug_name}的预测货架期为{t90}个月，"
                    "建议通过配方优化或包装改进来延长产品稳定性。"
                )
            else:
                conclusion_parts.append(
                    f"{drug_name}的稳定性存在挑战，预测货架期仅为{t90}个月，"
                    "强烈建议重新设计配方或采取特殊保护措施。"
                )
        
        # 相容性结论
        risk_text = {
            'low': '原辅料相容性良好，无明显相互作用风险。',
            'medium': '存在中等程度的相容性风险，需要进行确认性试验。',
            'high': '存在严重的相容性问题，必须更换部分辅料或采取隔离措施。'
        }
        
        if overall_risk in risk_text:
            conclusion_parts.append(risk_text[overall_risk])
        
        # 合并结论
        return ' '.join(conclusion_parts)

    async def generate_stability_report(
        self,
        analysis_data: Dict[str, Any],
        language: str = "zh",
        include_raw_data: bool = False
    ) -> Dict[str, Any]:
        """
        Generates and returns a stability analysis report as base64 data. (Synchronous version)
        """
        try:
            # This logic is duplicated from the save function for simplicity,
            # in a real refactor, you would have a core "build" function that both call.
            buffer = BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            story = []
            story.extend(self._create_cover_page(analysis_data, language))
            story.append(PageBreak())
            story.extend(self._create_executive_summary(analysis_data, language))
            story.append(PageBreak())
            story.extend(self._create_drug_info_section(analysis_data, language))
            story.extend(self._create_stability_prediction_section(analysis_data, language))
            if 'compatibility_assessment' in analysis_data:
                story.extend(self._create_compatibility_section(analysis_data, language))
            story.extend(await self._create_charts_section(analysis_data, language))
            story.extend(self._create_recommendations_section(analysis_data, language))
            if include_raw_data:
                story.append(PageBreak())
                story.extend(self._create_appendix(analysis_data, language))
            
            doc.build(story)
            pdf_data = buffer.getvalue()
            buffer.close()
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            drug_name = analysis_data.get('drug_name', 'unknown')
            filename = f"stability_report_{drug_name}_{timestamp}.pdf"
            
            return {
                "success": True,
                "filename": filename,
                "pdf_data": base64.b64encode(pdf_data).decode('utf-8'),
                "size": len(pdf_data),
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Report generation failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    def get_analysis_data(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """从数据库获取分析数据 (Placeholder)"""
        # This is a placeholder implementation.
        # In a real scenario, you would query your database for the analysis record.
        project = self.db.query(ProjectORM).get(analysis_id)
        if project:
            # Convert ORM object to a dictionary suitable for report generation
            return {"drug_name": project.name, "prediction": {"long_term": {"t90": 24}}}
        return None

    def get_report_history(self, limit: int, offset: int) -> List[ExportHistory]:
        """从数据库获取报告历史"""
        return self.db.query(ExportHistory).order_by(ExportHistory.export_time.desc()).offset(offset).limit(limit).all()

    def get_report_by_id(self, report_id: str) -> Optional[Path]:
        """通过ID查找报告文件"""
        # This is a simplified lookup. A real implementation would query a DB record
        # for the file path.
        filename = f"{report_id}.pdf" # This assumes a direct mapping, which is not robust
        potential_path = self.temp_dir / filename
        if potential_path.exists():
            return potential_path
        
        # Search for files that start with the report_id
        for f in self.temp_dir.glob(f"{report_id}*.pdf"):
            if f.is_file():
                return f
        return None


# 移除全局实例
# report_generation_service = ReportGenerationService() 