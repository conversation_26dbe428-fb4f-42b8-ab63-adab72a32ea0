import React, { useState, useEffect } from 'react';
import {
  Card,
  Spin,
  Alert,
  Button,
  Space,
  Tag,
  Tooltip,
  Descriptions,
  Row,
  Col,
  Checkbox,
  message,
  Typography
} from 'antd';
import {
  ReloadOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import api from '../../api';

interface MoleculeViewerProps {
  smiles: string;
  showProperties?: boolean;
  showFunctionalGroups?: boolean;
  height?: number;
  onPropertiesCalculated?: (properties: any) => void;
}

interface MolecularProperties {
  molecular_weight?: number;
  logp?: number;
  tpsa?: number;
  num_hbd?: number;
  num_hba?: number;
  num_rotatable_bonds?: number;
  molecular_formula?: string;
}

interface FunctionalGroup {
  id: string;
  name: string;
  count: number;
  reactivity: string;
  possible_reactions: string[];
}

interface ValidationResponse {
  valid: boolean;
  canonical_smiles?: string;
  errors: string[];
  warnings: string[];
}

interface VisualizeResponse {
  image: string;
}

interface FunctionalGroupsResponse {
  smiles: string;
  functional_groups: FunctionalGroup[];
  total_groups: number;
}

const MoleculeViewer: React.FC<MoleculeViewerProps> = ({
  smiles,
  showProperties = true,
  showFunctionalGroups = true,
  height = 400,
  onPropertiesCalculated
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [properties, setProperties] = useState<MolecularProperties | null>(null);
  const [functionalGroups, setFunctionalGroups] = useState<FunctionalGroup[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const { Text } = Typography;

  // 验证并获取分子图像
  const loadMolecule = React.useCallback(async (highlightGroups?: string[]) => {
    if (!smiles) {
      setError('请提供SMILES字符串');
      return;
    }

    console.log('🧪 MoleculeViewer: 开始加载分子', { smiles, showProperties, showFunctionalGroups });
    setLoading(true);
    setError(null);

    try {
      // 验证SMILES
      console.log('🔍 验证SMILES...');
      const validationResponse = await api.post<ValidationResponse>('/api/validate', {
        smiles
      });
      console.log('✅ SMILES验证结果:', validationResponse.data);

      if (!validationResponse.data.valid) {
        setError(validationResponse.data.errors.join(', '));
        return;
      }

      // 获取分子图像
      console.log('🖼️ 获取分子图像...');
      const visualResponse = await api.post<VisualizeResponse>('/api/visualize', {
        smiles,
        width: 400,
        height: 400,
        highlight_groups: highlightGroups
      });
      console.log('✅ 分子图像获取成功:', visualResponse.data.image ? '有图像数据' : '无图像数据');

      setImageUrl(visualResponse.data.image);

      // 获取分子性质
      if (showProperties) {
        console.log('📊 获取分子性质...');
        try {
          const propertiesResponse = await api.post<MolecularProperties>('/api/properties', {
            smiles
          });
          console.log('✅ 分子性质:', propertiesResponse.data);
          console.log('🔧 设置properties状态...');
          setProperties(propertiesResponse.data);
          onPropertiesCalculated?.(propertiesResponse.data);
          console.log('✅ properties状态已设置');
        } catch (error) {
          console.error('❌ 分子性质计算失败:', error);
          setProperties(null);
        }
      } else {
        console.log('⚠️ showProperties为false，跳过性质计算');
        setProperties(null);
      }

      // 识别官能团
      if (showFunctionalGroups) {
        console.log('🧬 识别官能团...');
        try {
          const groupsResponse = await api.post<FunctionalGroupsResponse>('/api/functional-groups', {
            smiles
          });
          console.log('✅ 官能团识别结果:', groupsResponse.data);
          console.log('🔧 设置functionalGroups状态...');
          setFunctionalGroups(groupsResponse.data.functional_groups || []);
          console.log('✅ functionalGroups状态已设置');
        } catch (error) {
          console.error('❌ 官能团识别失败:', error);
          setFunctionalGroups([]);
        }
      } else {
        console.log('⚠️ showFunctionalGroups为false，跳过官能团识别');
        setFunctionalGroups([]);
      }

    } catch (err: any) {
      console.error('❌ 分子加载失败:', err);
      setError(err.response?.data?.detail || '加载分子失败');
    } finally {
      setLoading(false);
      console.log('🏁 分子加载完成', {
        hasImage: !!imageUrl,
        hasProperties: !!properties,
        functionalGroupsCount: functionalGroups.length
      });
    }
  }, [smiles, showProperties, showFunctionalGroups, onPropertiesCalculated]);

  useEffect(() => {
    loadMolecule();
  }, [loadMolecule]);

  // 调试信息
  useEffect(() => {
    console.log('🔍 MoleculeViewer渲染状态:', {
      showProperties,
      hasProperties: !!properties,
      showFunctionalGroups,
      functionalGroupsCount: functionalGroups.length
    });
  }, [showProperties, properties, showFunctionalGroups, functionalGroups]);

  // 处理官能团高亮
  const handleGroupHighlight = (checkedGroups: string[]) => {
    setSelectedGroups(checkedGroups);
    if (checkedGroups.length > 0) {
      loadMolecule(checkedGroups);
    } else {
      loadMolecule();
    }
  };

  // 下载图像
  const downloadImage = () => {
    if (!imageUrl) return;
    
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `molecule_${smiles.substring(0, 10)}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('图像已下载');
  };

  // 渲染性质标签
  const renderPropertyTag = (label: string, value: any, type: 'number' | 'string' = 'number') => {
    if (value === null || value === undefined) return null;
    
    let color = 'default';
    if (type === 'number') {
      // 根据数值范围设置颜色
      if (label === 'LogP' && value > 5) color = 'orange';
      if (label === '分子量' && value > 500) color = 'orange';
    }
    
    return (
      <Tag color={color}>
        {label}: {type === 'number' ? value.toFixed(2) : value}
      </Tag>
    );
  };

  return (
    <Card 
      title="分子结构可视化"
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => loadMolecule(selectedGroups)}
            disabled={loading}
          >
            刷新
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={downloadImage}
            disabled={!imageUrl || loading}
          >
            下载
          </Button>
        </Space>
      }
    >
      <Spin spinning={loading}>
        {error ? (
          <Alert message={error} type="error" showIcon />
        ) : (
          <Row gutter={16}>
            {/* 分子图像 */}
            <Col span={12}>
              <div style={{ 
                height, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #f0f0f0',
                borderRadius: 4,
                backgroundColor: '#fafafa'
              }}>
                {imageUrl ? (
                  <img 
                    src={imageUrl} 
                    alt="Molecule structure"
                    style={{ maxWidth: '100%', maxHeight: '100%' }}
                  />
                ) : (
                  <Alert message="请输入有效的SMILES字符串" type="info" />
                )}
              </div>
            </Col>

            {/* 分子信息 */}
            <Col span={12}>
              {/* 强制显示调试信息 */}
              <div style={{ marginBottom: 16, padding: 12, background: '#fff3cd', border: '2px solid #ffeaa7', borderRadius: 4, fontSize: 14 }}>
                <div><strong>🔍 调试状态:</strong></div>
                <div>showProperties: <span style={{color: showProperties ? 'green' : 'red'}}>{String(showProperties)}</span></div>
                <div>properties: <span style={{color: properties ? 'green' : 'red'}}>{properties ? 'loaded' : 'null'}</span></div>
                <div>showFunctionalGroups: <span style={{color: showFunctionalGroups ? 'green' : 'red'}}>{String(showFunctionalGroups)}</span></div>
                <div>functionalGroups: <span style={{color: functionalGroups.length > 0 ? 'green' : 'red'}}>{functionalGroups.length} items</span></div>
                <div>loading: <span style={{color: loading ? 'orange' : 'green'}}>{String(loading)}</span></div>
                <div>error: <span style={{color: error ? 'red' : 'green'}}>{error || 'none'}</span></div>
                {properties && <div>properties.molecular_weight: <span style={{color: 'blue'}}>{properties.molecular_weight}</span></div>}
                <div>imageUrl: <span style={{color: imageUrl ? 'green' : 'red'}}>{imageUrl ? 'loaded' : 'none'}</span></div>
              </div>

              {/* 强制显示分子性质 - 无条件渲染用于测试 */}
              <div style={{ marginBottom: 16, padding: 12, background: '#e8f5e8', border: '1px solid #4caf50', borderRadius: 4 }}>
                <h4>🧪 分子性质 (强制显示)</h4>
                {properties ? (
                  <div>
                    <div>分子量: {properties.molecular_weight}</div>
                    <div>LogP: {properties.logp}</div>
                    <div>TPSA: {properties.tpsa}</div>
                    <div>氢键供体: {properties.num_hbd}</div>
                    <div>氢键受体: {properties.num_hba}</div>
                    <div>可旋转键: {properties.num_rotatable_bonds}</div>
                    <div>分子式: {properties.molecular_formula}</div>
                  </div>
                ) : (
                  <div style={{color: 'red'}}>❌ 分子性质数据未加载</div>
                )}
              </div>

              {/* 强制显示官能团 - 无条件渲染用于测试 */}
              <div style={{ marginBottom: 16, padding: 12, background: '#e3f2fd', border: '1px solid #2196f3', borderRadius: 4 }}>
                <h4>🧬 官能团 (强制显示)</h4>
                {functionalGroups.length > 0 ? (
                  <div>
                    {functionalGroups.map((group, index) => (
                      <div key={index} style={{ marginBottom: 8, padding: 8, background: 'white', borderRadius: 4 }}>
                        <div><strong>{group.name}</strong>: {group.count}个</div>
                        <div>反应性: {group.reactivity}</div>
                        <div>可能反应: {group.possible_reactions.join('、')}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{color: 'red'}}>❌ 官能团数据未加载</div>
                )}
              </div>

              {/* 分子性质 */}
              {showProperties && properties && (
                <div style={{ marginBottom: 16 }}>
                  <h4>分子性质</h4>
                  <Space wrap>
                    {renderPropertyTag('分子量', properties.molecular_weight)}
                    {renderPropertyTag('LogP', properties.logp)}
                    {renderPropertyTag('TPSA', properties.tpsa)}
                    {renderPropertyTag('氢键供体', properties.num_hbd)}
                    {renderPropertyTag('氢键受体', properties.num_hba)}
                    {renderPropertyTag('可旋转键', properties.num_rotatable_bonds)}
                  </Space>
                  {properties.molecular_formula && (
                    <div style={{ marginTop: 8 }}>
                      <Tag color="blue">分子式: {properties.molecular_formula}</Tag>
                    </div>
                  )}
                </div>
              )}

              {/* 官能团 */}
              {showFunctionalGroups && functionalGroups.length > 0 && (
                <div>
                  <h4>官能团 
                    <Tooltip title="选择要高亮显示的官能团">
                      <InfoCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
                    </Tooltip>
                  </h4>
                  <Checkbox.Group
                    value={selectedGroups}
                    onChange={handleGroupHighlight}
                    style={{ width: '100%' }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {functionalGroups.map(group => (
                        <div key={group.id} style={{ 
                          padding: 8, 
                          border: '1px solid #f0f0f0',
                          borderRadius: 4,
                          marginBottom: 8
                        }}>
                          <Checkbox value={group.id}>
                            <Space>
                              <span>{group.name}</span>
                              <Tag color="blue">{group.count}个</Tag>
                              <Tag color={
                                group.reactivity === '高' ? 'red' : 
                                group.reactivity === '中' ? 'orange' : 'green'
                              }>
                                反应性: {group.reactivity}
                              </Tag>
                            </Space>
                          </Checkbox>
                          <div style={{ marginTop: 4, marginLeft: 24 }}>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              可能的反应: {group.possible_reactions.join('、')}
                            </Text>
                          </div>
                        </div>
                      ))}
                    </Space>
                  </Checkbox.Group>
                </div>
              )}
            </Col>
          </Row>
        )}
      </Spin>
    </Card>
  );
};

export { MoleculeViewer };
export default MoleculeViewer;