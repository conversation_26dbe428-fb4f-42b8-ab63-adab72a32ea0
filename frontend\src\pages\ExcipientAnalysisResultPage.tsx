import React from 'react';
import { Card, Table, Tag, Button, Row, Col } from 'antd';

const excipients = [
  { name: '辅料A', function: '填充剂', amount: '10mg', risk: '高', remark: '与主药有反应' },
  { name: '辅料B', function: '崩解剂', amount: '5mg', risk: '低', remark: '' },
];
const drug = { name: '药物A', formula: 'C8H9NO2', cas: '50-78-2', smiles: 'CC(=O)OC1=CC=CC=C1C(=O)O' };

const ExcipientAnalysisResultPage: React.FC = () => {
  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Row gutter={24}>
        <Col xs={24} md={8}>
          <Card title="结构图" bordered style={{ marginBottom: 24 }}>
            <div style={{ minHeight: 180, background: '#fafbfc', borderRadius: 8, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <span>结构图预览（开发中）</span>
            </div>
          </Card>
          <Card title="药物属性" bordered>
            <div>名称：{drug.name}</div>
            <div>分子式：{drug.formula}</div>
            <div>CAS号：{drug.cas}</div>
            <div>SMILES：{drug.smiles}</div>
          </Card>
        </Col>
        <Col xs={24} md={16}>
          <Card title="辅料信息" bordered extra={<Button>导出分析结果</Button>}>
            <Table columns={[
              { title: '辅料名称', dataIndex: 'name', key: 'name' },
              { title: '功能', dataIndex: 'function', key: 'function' },
              { title: '用量', dataIndex: 'amount', key: 'amount' },
              { title: '风险等级', dataIndex: 'risk', key: 'risk', render: (risk: string) => risk === '高' ? <Tag color="red">高</Tag> : <Tag color="green">低</Tag> },
              { title: '备注', dataIndex: 'remark', key: 'remark' },
            ]} dataSource={excipients} pagination={false} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ExcipientAnalysisResultPage; 