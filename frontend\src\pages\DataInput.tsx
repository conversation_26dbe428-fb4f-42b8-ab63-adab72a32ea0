import React, { useState, useEffect, useContext } from 'react';
import { Card, Form, Input, Select, Button, Divider, Alert, Row, Col, Typography, Upload, message, Spin, Space, AutoComplete, InputNumber, notification, Modal } from 'antd';
import { UploadOutlined, PlusOutlined, ProjectOutlined, SearchOutlined, SaveOutlined, CheckCircleOutlined, CloudUploadOutlined } from '@ant-design/icons';
import { ProjectContext } from '../App';
import { useNavigate } from 'react-router-dom';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import api from '../api';
import { BatchImporter } from '../components/BatchImporter';
import MoleculeViewer from '../components/MoleculeViewer';
import { ImportHistory } from '../components/ImportHistory';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const DataInput: React.FC = () => {
  const [form] = Form.useForm();
  const { currentProject, inputData, setInputData, loadProjectData } = useContext(ProjectContext);
  const [error, setError] = useState<string | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchingDrug, setSearchingDrug] = useState(false);
  const [excipientCategories, setExcipientCategories] = useState<any>({});
  const [packagingMaterials, setPackagingMaterials] = useState<any[]>([]);
  const [storageConditions, setStorageConditions] = useState<any[]>([]);
  const [productionProcesses, setProductionProcesses] = useState<any[]>([]);
  const [selectedDosageForm, setSelectedDosageForm] = useState<string>('');
  const [filteredPackaging, setFilteredPackaging] = useState<any[]>([]);
  const [filteredStorage, setFilteredStorage] = useState<any[]>([]);
  const [customExcipients, setCustomExcipients] = useState<string[]>([]);
  const [excipientDetails, setExcipientDetails] = useState<any[]>([]);
  const [batchImportVisible, setBatchImportVisible] = useState(false);
  const navigate = useNavigate();
  const moleculeViewerTimer = React.useRef<NodeJS.Timeout | null>(null);
  const [currentSmiles, setCurrentSmiles] = useState<string>('');

  // 加载辅料、包装材料、储存条件等数据
  useEffect(() => {
    loadBasicData();
  }, []);

  const loadBasicData = async () => {
    try {
      console.log('开始加载基础数据...');

      // 加载辅料分类
      try {
        const excipientRes = await api.get('/excipient/categories');
        console.log('辅料分类响应:', excipientRes.data);
        setExcipientCategories(excipientRes.data || {});
      } catch (error) {
        console.error('加载辅料分类失败:', error);
        throw error;
      }

      // 加载辅料详情
      try {
        const excipientDetailsRes = await api.get('/excipient/details');
        console.log('辅料详情响应:', excipientDetailsRes.data);
        setExcipientDetails((excipientDetailsRes.data as any[]) || []);
      } catch (error) {
        console.error('加载辅料详情失败:', error);
        throw error;
      }

      // 加载包装材料
      try {
        const packagingRes = await api.get('/excipient/packaging-materials');
        console.log('包装材料响应:', packagingRes.data);
        const packagingData = Array.isArray(packagingRes.data) ? packagingRes.data : [];
        setPackagingMaterials(packagingData);
        setFilteredPackaging(packagingData);
      } catch (error) {
        console.error('加载包装材料失败:', error);
        throw error;
      }

      // 加载储存条件
      try {
        const storageRes = await api.get('/excipient/storage-conditions');
        console.log('储存条件响应:', storageRes.data);
        const storageData = Array.isArray(storageRes.data) ? storageRes.data : [];
        setStorageConditions(storageData);
        setFilteredStorage(storageData);
      } catch (error) {
        console.error('加载储存条件失败:', error);
        throw error;
      }

      // 加载生产工艺
      try {
        const processRes = await api.get('/excipient/production-processes');
        console.log('生产工艺响应:', processRes.data);
        setProductionProcesses(Array.isArray(processRes.data) ? processRes.data : []);
      } catch (error) {
        console.error('加载生产工艺失败:', error);
        throw error;
      }

      console.log('基础数据加载完成');
    } catch (error: any) {
      console.error('加载基础数据失败:', error);
      message.error(`加载基础数据失败: ${error.response?.data?.detail || error.message}`);
    }
  };

  // 自动加载当前项目数据（如果数据为空）
  useEffect(() => {
    if (currentProject && (!inputData || Object.keys(inputData).length === 0 || !inputData.drug_name)) {
      console.log('检测到当前项目但无数据，自动加载项目数据:', currentProject.id);
      loadProjectData(currentProject.id.toString()).catch(error => {
        console.error('自动加载项目数据失败:', error);
      });
    }
  }, [currentProject, loadProjectData]);

  // 从项目数据恢复表单状态
  useEffect(() => {
    if (currentProject && inputData) {
      console.log('从项目数据恢复表单状态:', inputData);

      // 设置表单值
      form.setFieldsValue({
        drug_name: inputData.drug_name || '',
        drug_cas: inputData.drug_cas || '',
        drug_formula: inputData.drug_formula || '',
        drug_smiles: inputData.drug_smiles || '',
        drug_category: inputData.drug_category || '',
        drug_description: inputData.drug_description || currentProject.description || '',
        excipients: inputData.excipients || [],
        packaging_storage: inputData.packaging_storage,
        process: inputData.production_process || '',
        notes: inputData.notes || ''
      });

      // 设置当前SMILES用于分子可视化
      if (inputData.drug_smiles) {
        setCurrentSmiles(inputData.drug_smiles);
      }

      // 如果有药物结构文件，设置文件列表
      if (inputData.drug_structure_file) {
        setFileList([
          {
            uid: '-1',
            name: inputData.drug_structure_file.name || '药物结构.mol',
            status: 'done',
            url: inputData.drug_structure_file.url,
          },
        ]);
      }
    }
  }, [currentProject, inputData, form]);

  // 智能识别输入类型
  const detectInputType = (input: string): 'name' | 'cas' | 'smiles' | 'unknown' => {
    if (!input || !input.trim()) return 'unknown';

    const trimmed = input.trim();

    // CAS号格式检测 (例: 50-78-2, 123-45-6)
    if (/^\d{1,7}-\d{2}-\d$/.test(trimmed)) {
      return 'cas';
    }

    // SMILES格式检测 (包含化学键符号)
    if (/[=\-#\(\)\[\]@+\\/\\]/.test(trimmed) && trimmed.length > 3) {
      return 'smiles';
    }

    // 默认为名称
    return 'name';
  };

  // 搜索药物信息
  const searchDrugInfo = async () => {
    const drugName = form.getFieldValue('drug_name');
    const cas = form.getFieldValue('drug_cas');
    const smiles = form.getFieldValue('drug_smiles');

    // 智能识别输入类型
    let searchParams: { name?: string; cas?: string; smiles?: string } = {};

    if (drugName) {
      const type = detectInputType(drugName);
      if (type === 'cas') {
        searchParams.cas = drugName;
      } else if (type === 'smiles') {
        searchParams.smiles = drugName;
      } else {
        searchParams.name = drugName;
      }
    }

    if (cas) {
      searchParams.cas = cas;
    }

    if (smiles) {
      searchParams.smiles = smiles;
    }

    if (!searchParams.name && !searchParams.cas && !searchParams.smiles) {
      message.warning('请输入药物名称、CAS号或SMILES结构中的至少一项');
      return;
    }

    setSearchingDrug(true);
    try {
      console.log('开始搜索药物信息:', searchParams);

      const response = await api.get('/drug-info/search', {
        params: searchParams
      });

      console.log('药物信息搜索响应:', response.data);

      const responseData = response.data as {
        success: boolean;
        message?: string;
        data?: any;
        source?: string;
        error?: string;
      };

      if (responseData.success && responseData.data) {
        const drugData = responseData.data;

        // 更新表单字段 - 修正字段映射
        const updateFields: any = {};

        // 药物名称
        if (drugData.name && drugData.name !== '未知药物') {
          updateFields.drug_name = drugData.name;
        } else if (drugData.english_name) {
          updateFields.drug_name = drugData.english_name;
        }

        // 分子式
        if (drugData.molecular_formula) {
          updateFields.drug_formula = drugData.molecular_formula;
        } else if (drugData.formula) {
          updateFields.drug_formula = drugData.formula;
        }

        // CAS号
        if (drugData.cas_number) {
          updateFields.drug_cas = drugData.cas_number;
        } else if (drugData.cas) {
          updateFields.drug_cas = drugData.cas;
        }

        // SMILES结构
        if (drugData.smiles) {
          updateFields.drug_smiles = drugData.smiles;
          // 立即更新分子可视化
          setCurrentSmiles(drugData.smiles);
        }

        // 药物类别
        if (drugData.category) {
          updateFields.drug_category = drugData.category;
        } else if (drugData.drug_class) {
          updateFields.drug_category = drugData.drug_class;
        }

        // 药物描述
        if (drugData.description) {
          updateFields.drug_description = drugData.description;
        }

        // 批量更新表单字段
        form.setFieldsValue(updateFields);

        // 显示成功消息，包含数据源信息
        const sourceText = responseData.source === 'local_database' ? '本地数据库' :
                          responseData.source === 'external_database' ? '外部数据库' :
                          responseData.source === 'pubchem' ? 'PubChem数据库' : '手动输入';

        message.success(`成功从${sourceText}获取药物信息`);

        // 如果有额外信息，显示通知
        if (drugData.molecular_weight || drugData.melting_point || drugData.solubility || drugData.stability) {
          notification.info({
            message: '获取到额外药物信息',
            description: (
              <div>
                {drugData.molecular_weight && <div>分子量: {drugData.molecular_weight}</div>}
                {drugData.melting_point && <div>熔点: {drugData.melting_point}</div>}
                {drugData.solubility && <div>溶解性: {drugData.solubility}</div>}
                {drugData.stability && <div>稳定性: {drugData.stability}</div>}
              </div>
            ),
            duration: 8
          });
        }

      } else {
        // 处理未找到信息的情况
        if (responseData.data && responseData.data.description) {
          message.info(responseData.message || '未找到完整药物信息，请手动补充');

          // 如果有基本信息，也填充到表单
          if (responseData.data.name && responseData.data.name !== '未知药物') {
            form.setFieldValue('drug_name', responseData.data.name);
          }
        } else {
          message.warning('未找到相关药物信息，请检查输入或手动填写');
        }
      }

    } catch (error: any) {
      console.error('搜索药物信息失败:', error);

      // 更详细的错误处理
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        if (status === 400) {
          message.error('请求参数错误，请检查输入');
        } else if (status === 500) {
          message.error('服务器内部错误，请稍后重试');
        } else {
          message.error(`搜索失败: ${errorData.detail || error.message}`);
        }
      } else if (error.request) {
        message.error('网络连接失败，请检查网络连接');
      } else {
        message.error('搜索药物信息失败，请稍后重试');
      }

    } finally {
      setSearchingDrug(false);
    }
  };

  const handleSubmit = async (values: any) => {
    if (!currentProject) {
      setError('请先选择或创建一个项目');
      return;
    }
    
    setLoading(true);
    try {
      // 处理辅料信息，包含用量
      const excipientData = values.excipients?.map((excipient: any) => {
        if (typeof excipient === 'string') {
          return { name: excipient, amount: null };
        }
        return excipient;
      });
      
      // 转换数据格式以匹配后端期望的格式
      const backendData = {
        drug_name: values.drug_name,
        cas_number: values.drug_cas,
        molecular_formula: values.drug_formula,
        smiles: values.drug_smiles,
        category: values.drug_category,
        description: values.drug_description,
        formulation: excipientData,
        packaging_storage: values.packaging_storage,
        production_process: values.process,
        notes: values.notes,
        drug_structure_file: fileList.length > 0 ? {
          name: fileList[0].name,
          url: fileList[0].url || URL.createObjectURL(fileList[0].originFileObj as Blob),
        } : undefined,
      };

      // 更新项目信息（前端格式）
      const newValues = {
        ...values,
        excipients: excipientData,
        drug_structure_file: fileList.length > 0 ? {
          name: fileList[0].name,
          url: fileList[0].url || URL.createObjectURL(fileList[0].originFileObj as Blob),
        } : undefined,
      };

      // 保存到后端（使用后端期望的格式）
      console.log('保存数据到后端:', backendData);
      const saveResponse = await api.post(`/projects/${currentProject.id}/save-data`, backendData);
      console.log('保存响应:', saveResponse.data);

      // 检查保存是否成功
      if (!saveResponse.data || !(saveResponse.data as any).success) {
        throw new Error((saveResponse.data as any)?.message || '保存失败');
      }
      
      setInputData?.({
        ...inputData,
        ...newValues,
      });
      
      // 显示保存成功的通知，并提供下一步操作选项
      const hasExcipients = newValues.excipients && newValues.excipients.length > 0;

      notification.success({
        message: '数据保存成功',
        description: (
          <div>
            <p>项目数据已成功保存到数据库</p>
            <p style={{ marginTop: 8 }}>
              <strong>项目名称：</strong>{currentProject.name}
            </p>
            {hasExcipients && (
              <div style={{ marginTop: 12, padding: 8, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
                <p style={{ margin: 0, color: '#52c41a' }}>
                  <CheckCircleOutlined style={{ marginRight: 4 }} />
                  检测到您已添加了 {newValues.excipients.length} 种辅料
                </p>
                <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#389e0d' }}>
                  建议进行原辅料相容性分析以评估配方风险
                </p>
              </div>
            )}
          </div>
        ),
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
        duration: 8,  // 延长显示时间
        placement: 'topRight',
        style: {
          width: 450,
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        },
        btn: hasExcipients ? (
          <Space>
            <Button size="small" onClick={() => navigate('/projects')}>
              返回项目管理
            </Button>
            <Button
              type="primary"
              size="small"
              onClick={() => navigate('/excipient-analysis')}
            >
              进行相容性分析
            </Button>
          </Space>
        ) : (
          <Button size="small" onClick={() => navigate('/projects')}>
            返回项目管理
          </Button>
        )
      });

      // 同时显示一个简短的message提示
      message.success('数据已保存', 2);
    } catch (error: any) {
      console.error('保存数据失败:', error);

      // 提取详细错误信息
      let errorMessage = '数据保存失败';
      let errorDescription = '请检查网络连接并重试';

      if (error.response) {
        // HTTP错误响应
        errorMessage = `保存失败 (${error.response.status})`;
        errorDescription = error.response.data?.detail || error.response.data?.message || '服务器返回错误';
      } else if (error.message) {
        // 其他错误
        errorDescription = error.message;
      }

      notification.error({
        message: errorMessage,
        description: errorDescription,
        duration: 6,
        placement: 'topRight',
        style: {
          width: 400,
        }
      });

      // 同时显示简短的message提示
      message.error('保存失败', 3);
    } finally {
      setLoading(false);
    }
  };

  // 获取所有辅料选项
  const getAllExcipients = () => {
    const allExcipients: string[] = [];
    Object.values(excipientCategories).forEach((category: any) => {
      if (Array.isArray(category)) {
        allExcipients.push(...category);
      }
    });
    return [...allExcipients, ...customExcipients];
  };

  // 处理自定义辅料添加
  const handleAddCustomExcipient = (value: string) => {
    if (value && !getAllExcipients().includes(value)) {
      setCustomExcipients([...customExcipients, value]);
      message.success(`已添加自定义辅料: ${value}`);
    }
  };

  // 根据剂型筛选包材和储存条件
  const handleDosageFormChange = async (dosageForm: string) => {
    setSelectedDosageForm(dosageForm);

    try {
      // 获取适合该剂型的包材
      const packagingRes = await api.get('/excipient/packaging-by-dosage-form', {
        params: { dosage_form: dosageForm }
      });

      if (Array.isArray(packagingRes.data)) {
        setFilteredPackaging(packagingRes.data);
      } else {
        // 如果返回的是分组数据，展平处理
        const flattenedPackaging: any[] = [];
        if (packagingRes.data && typeof packagingRes.data === 'object') {
          Object.values(packagingRes.data as Record<string, any>).forEach((group: any) => {
            if (Array.isArray(group)) {
              flattenedPackaging.push(...group);
            }
          });
        }
        setFilteredPackaging(flattenedPackaging);
      }

      // 根据剂型推荐储存条件
      let recommendedStorage = storageConditions;

      // 根据剂型特点推荐储存条件
      if (dosageForm.includes('注射') || dosageForm.includes('生物制品')) {
        recommendedStorage = storageConditions.filter(condition =>
          condition.test_type === '冷藏' || condition.test_type === '冷冻' ||
          condition.name.includes('冷藏') || condition.name.includes('冷冻')
        );
      } else if (dosageForm.includes('眼用') || dosageForm.includes('滴眼')) {
        recommendedStorage = storageConditions.filter(condition =>
          condition.test_type === '冷藏' || condition.name.includes('避光')
        );
      } else if (dosageForm.includes('外用') || dosageForm.includes('软膏')) {
        recommendedStorage = storageConditions.filter(condition =>
          condition.temperature && condition.temperature.includes('25') &&
          !condition.name.includes('冷藏')
        );
      } else {
        // 口服制剂等常规剂型
        recommendedStorage = storageConditions.filter(condition =>
          condition.ich_zone === 'Zone II' || condition.test_type === '长期试验' ||
          condition.test_type === '加速试验'
        );
      }

      setFilteredStorage(recommendedStorage.length > 0 ? recommendedStorage : storageConditions);

    } catch (error) {
      console.error('筛选包材失败:', error);
      setFilteredPackaging(packagingMaterials);
      setFilteredStorage(storageConditions);
    }
  };

  const uploadProps: UploadProps = {
    onRemove: (file) => {
      setFileList([]);
    },
    beforeUpload: (file) => {
      const isMolOrSdf = file.name.endsWith('.mol') || file.name.endsWith('.sdf');
      if (!isMolOrSdf) {
        message.error('只能上传MOL或SDF格式的分子结构文件!');
        return Upload.LIST_IGNORE;
      }
      
      setFileList([file]);
      return false;
    },
    fileList,
    maxCount: 1,
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>项目基本数据</Title>
        {currentProject && (
          <Card size="small" style={{ minWidth: 300 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <ProjectOutlined style={{ color: '#1890ff', marginRight: 8 }} />
              <div>
                <Text strong style={{ color: '#1890ff' }}>{currentProject.name}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  状态: {currentProject.status} | 创建: {currentProject.created || currentProject.created_at || ''}
                </Text>
              </div>
            </div>
          </Card>
        )}
      </div>
      
      {error && <Alert message={error} type="error" showIcon style={{ marginBottom: 16 }} />}
      
      {!currentProject && (
        <Alert 
          message="请先选择或创建一个项目" 
          description={
            <div>
              您需要先创建或选择一个项目才能输入数据。
              <Button 
                type="link" 
                onClick={() => navigate('/projects')}
                style={{ padding: 0, marginLeft: 8 }}
              >
                前往项目管理
              </Button>
            </div>
          }
          type="warning" 
          showIcon 
          style={{ marginBottom: 16 }} 
        />
      )}
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={!currentProject}
      >
        <Card
          title="药物信息"
          style={{ marginBottom: 24 }}
          extra={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                输入任一项即可搜索
              </Text>
              <Button
                icon={<SearchOutlined />}
                onClick={searchDrugInfo}
                loading={searchingDrug}
                disabled={!currentProject}
                type="primary"
              >
                联网搜索药物信息
              </Button>
            </div>
          }
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="drug_name"
                label="药物名称"
                rules={[{ required: true, message: '请输入药物名称' }]}
                tooltip="可输入中文名、英文名，或直接输入CAS号、SMILES结构进行智能识别"
              >
                <Input placeholder="输入药物名称（如：阿司匹林、Aspirin）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="drug_cas"
                label="CAS号"
                tooltip="化学文摘社登记号，格式如：50-78-2"
              >
                <Input placeholder="输入CAS号（如：50-78-2）" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="drug_formula"
                label="分子式"
              >
                <Input placeholder="输入分子式" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="drug_category"
                label="药物类别"
              >
                <Select placeholder="选择药物类别">
                  <Option value="抗生素">抗生素</Option>
                  <Option value="解热镇痛药">解热镇痛药</Option>
                  <Option value="心血管药物">心血管药物</Option>
                  <Option value="中枢神经系统药物">中枢神经系统药物</Option>
                  <Option value="消化系统药物">消化系统药物</Option>
                  <Option value="激素类药物">激素类药物</Option>
                  <Option value="抗肿瘤药物">抗肿瘤药物</Option>
                  <Option value="生物制品">生物制品</Option>
                  <Option value="中药制剂">中药制剂</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="drug_smiles"
            label="SMILES结构"
            tooltip="简化分子线性输入规范，输入后将自动显示分子结构预览"
          >
            <TextArea
              rows={2}
              placeholder="输入SMILES格式的分子结构（如：CC(=O)OC1=CC=CC=C1C(=O)O）"
              onChange={(e) => {
                // 为分子可视化设置一个延迟，避免频繁渲染
                if (moleculeViewerTimer.current) {
                  clearTimeout(moleculeViewerTimer.current);
                }
                moleculeViewerTimer.current = setTimeout(() => {
                  setCurrentSmiles(e.target.value);
                }, 500);
              }}
            />
          </Form.Item>
          
          {/* 分子结构可视化 */}
          {currentSmiles && currentSmiles.trim() && (
            <Form.Item label="分子结构预览">
              <MoleculeViewer
                smiles={currentSmiles}
                width={400}
                height={300}
              />
            </Form.Item>
          )}
          
          <Form.Item
            name="drug_structure_file"
            label="分子结构文件"
            valuePropName="fileList"
            getValueFromEvent={(e) => {
              if (Array.isArray(e)) {
                return e;
              }
              return e?.fileList || [];
            }}
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>上传分子结构文件(MOL/SDF)</Button>
            </Upload>
          </Form.Item>
          
          <Form.Item
            name="drug_description"
            label="药物描述"
          >
            <TextArea 
              rows={4} 
              placeholder="输入药物描述信息" 
            />
          </Form.Item>
        </Card>
        
        <Card title="配方信息" style={{ marginBottom: 24 }}>
          <Form.Item label="辅料组成">
            <Form.List name="excipients">
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, index) => (
                    <Row key={field.key} gutter={16} style={{ marginBottom: 8 }}>
                      <Col span={10}>
                        <Form.Item
                          {...field}
                          name={[field.name, 'name']}
                          rules={[{ required: true, message: '请选择或输入辅料' }]}
                        >
                          <AutoComplete
                            placeholder="选择或输入辅料名称"
                            options={getAllExcipients().map(name => ({ value: name }))}
                            filterOption={(inputValue, option) =>
                              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                            }
                            onSelect={handleAddCustomExcipient}
                            allowClear
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...field}
                          name={[field.name, 'amount']}
                        >
                          <InputNumber
                            placeholder="用量"
                            min={0}
                            style={{ width: '100%' }}
                            addonAfter="mg"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...field}
                          name={[field.name, 'percentage']}
                        >
                          <InputNumber
                            placeholder="占比"
                            min={0}
                            max={100}
                            style={{ width: '100%' }}
                            addonAfter="%"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Button 
                          type="link" 
                          danger 
                          onClick={() => remove(field.name)}
                        >
                          删除
                        </Button>
                      </Col>
                    </Row>
                  ))}
                  <Form.Item>
                    <Button 
                      type="dashed" 
                      onClick={() => add()} 
                      block 
                      icon={<PlusOutlined />}
                    >
                      添加辅料
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
          
          <Form.Item
            name="process"
            label="生产工艺"
          >
            <Select placeholder="选择生产工艺">
              {productionProcesses.map(process => (
                <Option key={process.id} value={process.id}>
                  {process.name} - {process.category}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Card>
        
        <Card title="包装与储存信息" style={{ marginBottom: 24 }}>
          {/* 剂型选择 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={24}>
              <Form.Item
                name="dosage_form"
                label="剂型"
                tooltip="选择剂型后系统将推荐适合的包装材料和储存条件"
              >
                <Select
                  placeholder="选择药物剂型"
                  onChange={handleDosageFormChange}
                  showSearch
                  allowClear
                >
                  <Option value="片剂">片剂</Option>
                  <Option value="胶囊剂">胶囊剂</Option>
                  <Option value="颗粒剂">颗粒剂</Option>
                  <Option value="散剂">散剂</Option>
                  <Option value="糖浆剂">糖浆剂</Option>
                  <Option value="溶液剂">溶液剂</Option>
                  <Option value="悬浮液">悬浮液</Option>
                  <Option value="口服液">口服液</Option>
                  <Option value="注射液">注射液</Option>
                  <Option value="冻干粉针">冻干粉针</Option>
                  <Option value="大容量注射液">大容量注射液</Option>
                  <Option value="软膏剂">软膏剂</Option>
                  <Option value="凝胶剂">凝胶剂</Option>
                  <Option value="乳膏剂">乳膏剂</Option>
                  <Option value="洗剂">洗剂</Option>
                  <Option value="喷雾剂">喷雾剂</Option>
                  <Option value="贴剂">贴剂</Option>
                  <Option value="定量吸入剂">定量吸入剂(MDI)</Option>
                  <Option value="干粉吸入剂">干粉吸入剂(DPI)</Option>
                  <Option value="雾化液">雾化液</Option>
                  <Option value="滴眼液">滴眼液</Option>
                  <Option value="眼膏">眼膏</Option>
                  <Option value="鼻喷雾剂">鼻喷雾剂</Option>
                  <Option value="鼻滴液">鼻滴液</Option>
                  <Option value="栓剂">栓剂</Option>
                  <Option value="生物制品">生物制品</Option>
                  <Option value="疫苗">疫苗</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="packaging"
                label="包装材料"
                tooltip="根据剂型智能推荐的包装材料"
              >
                <Select
                  placeholder={selectedDosageForm ? `选择适合${selectedDosageForm}的包装材料` : "请先选择剂型"}
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  disabled={!selectedDosageForm}
                >
                  {(filteredPackaging.length > 0 ? filteredPackaging : packagingMaterials).map(material => (
                    <Option key={material.id} value={material.id} title={material.description || ''}>
                      <div>
                        <div style={{ fontWeight: 'bold' }}>
                          {material.name || ''}
                          {material.color && <span style={{ color: '#1890ff', marginLeft: 4 }}>({material.color})</span>}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {material.material_type || material.dosage_form || ''} |
                          {material.specification && <span> {material.specification}</span>}
                        </div>
                        {material.barrier_properties && typeof material.barrier_properties === 'object' ? (
                          <div style={{ fontSize: '11px', color: '#999' }}>
                            湿度阻隔: {material.barrier_properties.moisture_permeability || 'N/A'}
                          </div>
                        ) : (
                          <div style={{ fontSize: '11px', color: '#999' }}>
                            {material.barrier_properties || ''}
                          </div>
                        )}
                      </div>
                    </Option>
                  ))}
                  <Option value="custom">其他（自定义）</Option>
                </Select>
              </Form.Item>

              {form.getFieldValue('packaging') === 'custom' && (
                <Form.Item
                  name="custom_packaging"
                  label="自定义包装材料"
                  rules={[{ required: true, message: '请输入包装材料' }]}
                >
                  <Input placeholder="请输入自定义的包装材料" />
                </Form.Item>
              )}
            </Col>
            <Col span={12}>
              <Form.Item
                name="storage_condition"
                label="储存条件"
                tooltip="参考ICH指南的标准储存条件"
              >
                <Select
                  placeholder={selectedDosageForm ? `选择适合${selectedDosageForm}的储存条件` : "选择储存条件"}
                  showSearch
                  allowClear
                  optionFilterProp="children"
                >
                  {(filteredStorage.length > 0 ? filteredStorage : storageConditions).map(condition => (
                    <Option key={condition.id} value={condition.id} title={condition.description || ''}>
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{condition.name || ''}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {condition.temperature || ''} | {condition.humidity || ''} | {condition.ich_zone || ''}
                        </div>
                      </div>
                    </Option>
                  ))}
                  <Option value="custom">其他（自定义）</Option>
                </Select>
              </Form.Item>

              {form.getFieldValue('storage_condition') === 'custom' && (
                <Form.Item
                  name="custom_storage"
                  label="自定义储存条件"
                  rules={[{ required: true, message: '请输入储存条件' }]}
                >
                  <Input placeholder="请输入自定义的储存条件" />
                </Form.Item>
              )}
            </Col>
          </Row>

          {/* 显示选中的包材和储存条件的详细信息 */}
          {(form.getFieldValue('packaging') || form.getFieldValue('storage_condition')) && (
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={24}>
                <div style={{
                  background: '#f6f8fa',
                  padding: 16,
                  borderRadius: 6,
                  border: '1px solid #e1e4e8'
                }}>
                  <Text strong style={{ color: '#0366d6' }}>选择详情：</Text>
                  {form.getFieldValue('packaging') && form.getFieldValue('packaging') !== 'custom' && (
                    <div style={{ marginTop: 8 }}>
                      {(() => {
                        const selectedPackaging = (filteredPackaging.length > 0 ? filteredPackaging : packagingMaterials)
                          .find(m => m.id === form.getFieldValue('packaging'));
                        return selectedPackaging ? (
                          <div>
                            <Text strong>包装材料：</Text>{selectedPackaging.name || ''}
                            {selectedPackaging.color && <span style={{ color: '#1890ff' }}>({selectedPackaging.color})</span>}<br/>

                            {selectedPackaging.material_type && (
                              <><Text type="secondary">材质类型：{selectedPackaging.material_type}</Text><br/></>
                            )}
                            {selectedPackaging.specification && (
                              <><Text type="secondary">规格：{selectedPackaging.specification}</Text><br/></>
                            )}

                            {selectedPackaging.barrier_properties && typeof selectedPackaging.barrier_properties === 'object' ? (
                              <div style={{ marginTop: 8 }}>
                                <Text strong style={{ color: '#52c41a' }}>阻隔性能参数：</Text><br/>
                                <Text type="secondary">• 湿度透过率：{selectedPackaging.barrier_properties.moisture_permeability || 'N/A'}</Text><br/>
                                <Text type="secondary">• 氧气透过率：{selectedPackaging.barrier_properties.oxygen_permeability || 'N/A'}</Text><br/>
                                <Text type="secondary">• 光透过率：{selectedPackaging.barrier_properties.light_transmission || 'N/A'}</Text><br/>
                                <Text type="secondary">• 耐温范围：{selectedPackaging.barrier_properties.temperature_resistance || 'N/A'}</Text><br/>
                                <Text type="secondary">• UV防护：{selectedPackaging.barrier_properties.uv_protection || 'N/A'}</Text><br/>
                              </div>
                            ) : (
                              <><Text type="secondary">阻隔性能：{selectedPackaging.barrier_properties || ''}</Text><br/></>
                            )}

                            {selectedPackaging.advantages && (
                              <div style={{ marginTop: 8 }}>
                                <Text strong style={{ color: '#1890ff' }}>优点：</Text>
                                <Text type="secondary">{selectedPackaging.advantages.join(', ')}</Text><br/>
                              </div>
                            )}
                            {selectedPackaging.disadvantages && (
                              <div>
                                <Text strong style={{ color: '#ff4d4f' }}>缺点：</Text>
                                <Text type="secondary">{selectedPackaging.disadvantages.join(', ')}</Text><br/>
                              </div>
                            )}
                            {selectedPackaging.suitable_drugs && (
                              <div>
                                <Text strong style={{ color: '#722ed1' }}>适用药物：</Text>
                                <Text type="secondary">{selectedPackaging.suitable_drugs.join(', ')}</Text><br/>
                              </div>
                            )}

                            <Text type="secondary">描述：{selectedPackaging.description || ''}</Text>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                  {form.getFieldValue('storage_condition') && form.getFieldValue('storage_condition') !== 'custom' && (
                    <div style={{ marginTop: 8 }}>
                      {(() => {
                        const selectedStorage = (filteredStorage.length > 0 ? filteredStorage : storageConditions)
                          .find(c => c.id === form.getFieldValue('storage_condition'));
                        return selectedStorage ? (
                          <div>
                            <Text strong>储存条件：</Text>{selectedStorage.name || ''}<br/>
                            <Text type="secondary">温度：{selectedStorage.temperature || ''}</Text><br/>
                            <Text type="secondary">湿度：{selectedStorage.humidity || ''}</Text><br/>
                            <Text type="secondary">ICH区域：{selectedStorage.ich_zone || ''}</Text><br/>
                            <Text type="secondary">试验类型：{selectedStorage.test_type || ''}</Text><br/>
                            <Text type="secondary">描述：{selectedStorage.description || ''}</Text>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          )}
        </Card>
        
        <Card title="备注信息">
          <Form.Item
            name="notes"
            label="其他备注"
          >
            <TextArea 
              rows={4} 
              placeholder="输入其他相关信息" 
            />
          </Form.Item>
        </Card>
        
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Space>
            <Button
              icon={<CloudUploadOutlined />}
              onClick={() => setBatchImportVisible(true)}
              disabled={!currentProject}
            >
              批量导入
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              disabled={!currentProject}
              loading={loading}
              icon={<SaveOutlined />}
            >
              保存数据
            </Button>
            {!currentProject && (
              <Button 
                onClick={() => navigate('/projects')}
              >
                选择项目
              </Button>
            )}
          </Space>
        </div>
      </Form>
      
      {/* 批量导入模态框 */}
      <Modal
        title="批量导入数据"
        open={batchImportVisible}
        onCancel={() => setBatchImportVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <BatchImporter 
          onImportComplete={(data) => {
            message.success('批量导入成功！');
            setBatchImportVisible(false);
            // 刷新页面数据
            loadBasicData();
          }}
        />
      </Modal>
      
      {/* 导入历史记录 */}
      {currentProject && (
        <div style={{ marginTop: 24 }}>
          <ImportHistory 
            projectId={currentProject.id}
            onRefresh={loadBasicData}
          />
        </div>
      )}
    </div>
  );
};

export default DataInput; 