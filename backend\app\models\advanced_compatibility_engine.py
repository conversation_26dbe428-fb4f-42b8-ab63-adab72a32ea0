"""
高级药物-辅料相容性评估引擎
基于药物化学、有机化学和制剂学原理的专业评估系统
"""

from typing import Dict, List, Any, Optional, Tuple
import math
import numpy as np
import re
from dataclasses import dataclass
from enum import Enum

class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

class ReactionMechanism(Enum):
    """反应机理枚举"""
    HYDROLYSIS = "hydrolysis"
    OXIDATION = "oxidation"
    MAILLARD = "maillard"
    TRANSESTERIFICATION = "transesterification"
    ACID_BASE = "acid_base"
    COMPLEXATION = "complexation"
    PHOTODEGRADATION = "photodegradation"
    THERMAL_DEGRADATION = "thermal_degradation"
    SOLID_STATE_REACTION = "solid_state_reaction"

@dataclass
class FunctionalGroup:
    """官能团数据类"""
    name: str
    smarts: str
    reactivity: float  # 反应活性 0-1
    stability: float   # 稳定性 0-1
    ph_sensitivity: bool
    moisture_sensitivity: bool
    temperature_sensitivity: bool

@dataclass
class ExcipientProfile:
    """辅料特性档案"""
    name: str
    category: str
    ph_range: Tuple[float, float]
    moisture_content: float
    reducing_potential: bool
    oxidizing_potential: bool
    metal_content: Dict[str, float]
    ionic_strength: float
    buffer_capacity: float
    common_impurities: List[str]

class AdvancedCompatibilityEngine:
    """高级相容性评估引擎"""
    
    def __init__(self):
        """初始化引擎"""
        self.functional_groups = self._init_functional_groups()
        self.excipient_profiles = self._init_excipient_profiles()
        self.reaction_rules = self._init_reaction_rules()
        self.degradation_pathways = self._init_degradation_pathways()
        self.literature_database = self._init_literature_database()
    
    def _init_functional_groups(self) -> Dict[str, FunctionalGroup]:
        """初始化官能团数据库"""
        return {
            "ester": FunctionalGroup(
                name="酯基",
                smarts="[C,c](=O)[O,o][C,c]",
                reactivity=0.8,
                stability=0.3,
                ph_sensitivity=True,
                moisture_sensitivity=True,
                temperature_sensitivity=True
            ),
            "amide": FunctionalGroup(
                name="酰胺基",
                smarts="[C,c](=O)[N,n]",
                reactivity=0.6,
                stability=0.5,
                ph_sensitivity=True,
                moisture_sensitivity=True,
                temperature_sensitivity=True
            ),
            "beta_lactam": FunctionalGroup(
                name="β-内酰胺环",
                smarts="[C,c]1[C,c](=O)[N,n][C,c]1",
                reactivity=0.9,
                stability=0.2,
                ph_sensitivity=True,
                moisture_sensitivity=True,
                temperature_sensitivity=True
            ),
            "phenol": FunctionalGroup(
                name="酚羟基",
                smarts="[c][O,o][H,h]",
                reactivity=0.7,
                stability=0.4,
                ph_sensitivity=True,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            ),
            "primary_amine": FunctionalGroup(
                name="伯胺",
                smarts="[N,n][H,h][H,h]",
                reactivity=0.8,
                stability=0.4,
                ph_sensitivity=True,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            ),
            "aldehyde": FunctionalGroup(
                name="醛基",
                smarts="[C,c][H,h]=O",
                reactivity=0.9,
                stability=0.2,
                ph_sensitivity=False,
                moisture_sensitivity=True,
                temperature_sensitivity=True
            ),
            "ketone": FunctionalGroup(
                name="酮基",
                smarts="[C,c](=O)[C,c]",
                reactivity=0.5,
                stability=0.6,
                ph_sensitivity=False,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            ),
            "carboxylic_acid": FunctionalGroup(
                name="羧酸",
                smarts="[C,c](=O)[O,o][H,h]",
                reactivity=0.7,
                stability=0.5,
                ph_sensitivity=True,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            ),
            "sulfide": FunctionalGroup(
                name="硫醚",
                smarts="[C,c][S,s][C,c]",
                reactivity=0.6,
                stability=0.4,
                ph_sensitivity=False,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            ),
            "nitro": FunctionalGroup(
                name="硝基",
                smarts="[N,n](=O)=O",
                reactivity=0.4,
                stability=0.7,
                ph_sensitivity=False,
                moisture_sensitivity=False,
                temperature_sensitivity=True
            )
        }
    
    def _init_excipient_profiles(self) -> Dict[str, ExcipientProfile]:
        """初始化辅料特性数据库"""
        return {
            "乳糖": ExcipientProfile(
                name="乳糖",
                category="填充剂",
                ph_range=(4.0, 6.5),
                moisture_content=0.05,
                reducing_potential=True,
                oxidizing_potential=False,
                metal_content={"Fe": 0.0001, "Cu": 0.00005},
                ionic_strength=0.001,
                buffer_capacity=0.1,
                common_impurities=["甲醛", "乙醛", "还原糖"]
            ),
            "微晶纤维素": ExcipientProfile(
                name="微晶纤维素",
                category="填充剂",
                ph_range=(5.0, 7.5),
                moisture_content=0.05,
                reducing_potential=False,
                oxidizing_potential=False,
                metal_content={"Fe": 0.0002},
                ionic_strength=0.0,
                buffer_capacity=0.0,
                common_impurities=["木质素", "半纤维素"]
            ),
            "硬脂酸镁": ExcipientProfile(
                name="硬脂酸镁",
                category="润滑剂",
                ph_range=(6.0, 8.0),
                moisture_content=0.06,
                reducing_potential=False,
                oxidizing_potential=False,
                metal_content={"Mg": 0.04, "Ca": 0.005, "Fe": 0.0001},
                ionic_strength=0.01,
                buffer_capacity=0.2,
                common_impurities=["游离脂肪酸", "重金属"]
            ),
            "聚维酮": ExcipientProfile(
                name="聚维酮",
                category="黏合剂",
                ph_range=(3.0, 7.0),
                moisture_content=0.05,
                reducing_potential=False,
                oxidizing_potential=True,
                metal_content={"Fe": 0.0001},
                ionic_strength=0.0,
                buffer_capacity=0.0,
                common_impurities=["过氧化物", "乙烯基吡咯烷酮"]
            ),
            "羟丙甲纤维素": ExcipientProfile(
                name="羟丙甲纤维素",
                category="崩解剂",
                ph_range=(5.0, 8.0),
                moisture_content=0.05,
                reducing_potential=False,
                oxidizing_potential=False,
                metal_content={"Fe": 0.0001},
                ionic_strength=0.0,
                buffer_capacity=0.0,
                common_impurities=["甲醇", "丙醇"]
            ),
            "淀粉": ExcipientProfile(
                name="淀粉",
                category="崩解剂",
                ph_range=(4.5, 7.0),
                moisture_content=0.12,
                reducing_potential=True,
                oxidizing_potential=False,
                metal_content={"Fe": 0.0002},
                ionic_strength=0.001,
                buffer_capacity=0.1,
                common_impurities=["糊精", "葡萄糖"]
            ),
            "二氧化硅": ExcipientProfile(
                name="二氧化硅",
                category="助流剂",
                ph_range=(3.5, 4.5),
                moisture_content=0.015,
                reducing_potential=False,
                oxidizing_potential=False,
                metal_content={"Fe": 0.0005, "Al": 0.001},
                ionic_strength=0.0,
                buffer_capacity=0.0,
                common_impurities=["重金属", "氯化物"]
            )
        }
    
    def _init_reaction_rules(self) -> List[Dict[str, Any]]:
        """初始化反应规则数据库"""
        return [
            {
                "name": "酯水解反应",
                "drug_groups": ["ester"],
                "excipient_conditions": ["high_moisture", "alkaline_ph"],
                "mechanism": ReactionMechanism.HYDROLYSIS,
                "activation_energy": 80.0,  # kJ/mol
                "ph_dependence": True,
                "temperature_dependence": True,
                "products": ["羧酸", "醇"],
                "kinetics": "first_order",
                "catalysts": ["OH-", "H+", "金属离子"]
            },
            {
                "name": "β-内酰胺环开环",
                "drug_groups": ["beta_lactam"],
                "excipient_conditions": ["high_moisture", "extreme_ph"],
                "mechanism": ReactionMechanism.HYDROLYSIS,
                "activation_energy": 75.0,
                "ph_dependence": True,
                "temperature_dependence": True,
                "products": ["开环产物"],
                "kinetics": "first_order",
                "catalysts": ["OH-", "H+", "β-内酰胺酶"]
            },
            {
                "name": "Maillard反应",
                "drug_groups": ["primary_amine"],
                "excipient_conditions": ["reducing_sugar"],
                "mechanism": ReactionMechanism.MAILLARD,
                "activation_energy": 85.0,
                "ph_dependence": True,
                "temperature_dependence": True,
                "products": ["褐变产物", "AGEs"],
                "kinetics": "complex",
                "catalysts": ["金属离子", "酸性条件"]
            },
            {
                "name": "酚氧化反应",
                "drug_groups": ["phenol"],
                "excipient_conditions": ["oxidizing_agent", "metal_catalyst"],
                "mechanism": ReactionMechanism.OXIDATION,
                "activation_energy": 65.0,
                "ph_dependence": True,
                "temperature_dependence": True,
                "products": ["醌类", "聚合物"],
                "kinetics": "auto_catalytic",
                "catalysts": ["Fe3+", "Cu2+", "过氧化物"]
            },
            {
                "name": "硫醚氧化反应",
                "drug_groups": ["sulfide"],
                "excipient_conditions": ["oxidizing_agent"],
                "mechanism": ReactionMechanism.OXIDATION,
                "activation_energy": 60.0,
                "ph_dependence": False,
                "temperature_dependence": True,
                "products": ["亚砜", "砜"],
                "kinetics": "second_order",
                "catalysts": ["过氧化物", "金属离子"]
            },
            {
                "name": "酸碱中和反应",
                "drug_groups": ["carboxylic_acid"],
                "excipient_conditions": ["basic_excipient"],
                "mechanism": ReactionMechanism.ACID_BASE,
                "activation_energy": 40.0,
                "ph_dependence": True,
                "temperature_dependence": False,
                "products": ["盐"],
                "kinetics": "instantaneous",
                "catalysts": []
            }
        ]
    
    def _init_degradation_pathways(self) -> Dict[str, List[Dict[str, Any]]]:
        """初始化降解途径数据库"""
        return {
            "阿司匹林": [
                {
                    "pathway": "酯水解",
                    "products": ["水杨酸", "乙酸"],
                    "conditions": ["碱性环境", "高湿度"],
                    "kinetics": "一级反应",
                    "ea": 80.0
                },
                {
                    "pathway": "酚酯转移",
                    "products": ["聚合物"],
                    "conditions": ["高温", "碱性"],
                    "kinetics": "二级反应",
                    "ea": 95.0
                }
            ],
            "对乙酰氨基酚": [
                {
                    "pathway": "酰胺水解",
                    "products": ["对氨基酚", "乙酸"],
                    "conditions": ["强酸强碱", "高温"],
                    "kinetics": "一级反应",
                    "ea": 100.0
                },
                {
                    "pathway": "氧化反应",
                    "products": ["醌类", "聚合物"],
                    "conditions": ["氧化剂", "金属催化"],
                    "kinetics": "自催化",
                    "ea": 70.0
                }
            ],
            "阿莫西林": [
                {
                    "pathway": "β-内酰胺环开环",
                    "products": ["青霉噻唑酸", "青霉胺"],
                    "conditions": ["水分", "极端pH"],
                    "kinetics": "一级反应",
                    "ea": 75.0
                },
                {
                    "pathway": "聚合反应",
                    "products": ["高分子聚合物"],
                    "conditions": ["高浓度", "碱性"],
                    "kinetics": "二级反应",
                    "ea": 85.0
                }
            ]
        }
    
    def _init_literature_database(self) -> Dict[Tuple[str, str], List[Dict[str, Any]]]:
        """初始化文献数据库"""
        return {
            ("阿司匹林", "乳糖"): [
                {
                    "reference": "J. Pharm. Sci. 2019, 108, 1234-1245",
                    "finding": "乳糖中的还原糖与阿司匹林发生Maillard反应",
                    "risk_level": "medium",
                    "conditions": "40°C/75%RH, 6个月"
                }
            ],
            ("对乙酰氨基酚", "聚维酮"): [
                {
                    "reference": "Int. J. Pharm. 2020, 587, 119654",
                    "finding": "聚维酮中的过氧化物引起对乙酰氨基酚氧化",
                    "risk_level": "high",
                    "conditions": "25°C/60%RH, 12个月"
                }
            ]
        }
    
    def assess_compatibility(self, drug_name: str, drug_smiles: str, 
                           excipient_name: str, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估药物与辅料的相容性
        
        参数:
            drug_name: 药物名称
            drug_smiles: 药物SMILES结构
            excipient_name: 辅料名称
            conditions: 环境条件 (温度、湿度、pH等)
            
        返回:
            详细的相容性评估结果
        """
        # 1. 结构分析
        functional_groups = self._analyze_functional_groups(drug_smiles)
        
        # 2. 辅料特性分析
        excipient_profile = self.excipient_profiles.get(excipient_name)
        if not excipient_profile:
            return self._generate_unknown_excipient_result(drug_name, excipient_name)
        
        # 3. 反应风险评估
        reaction_risks = self._assess_reaction_risks(
            functional_groups, excipient_profile, conditions
        )
        
        # 4. 文献证据检索
        literature_evidence = self._search_literature_evidence(drug_name, excipient_name)
        
        # 5. 综合风险评分
        overall_risk = self._calculate_overall_risk(reaction_risks, literature_evidence, conditions)
        
        # 6. 生成建议
        recommendations = self._generate_recommendations(
            overall_risk, reaction_risks, excipient_profile, conditions
        )
        
        return {
            "drug_name": drug_name,
            "excipient_name": excipient_name,
            "risk_level": overall_risk["level"],
            "risk_score": overall_risk["score"],
            "confidence": overall_risk["confidence"],
            "functional_groups": functional_groups,
            "reaction_risks": reaction_risks,
            "literature_evidence": literature_evidence,
            "recommendations": recommendations,
            "detailed_analysis": {
                "excipient_profile": excipient_profile.__dict__,
                "environmental_factors": self._analyze_environmental_factors(conditions),
                "degradation_pathways": self._predict_degradation_pathways(
                    drug_name, functional_groups, excipient_profile, conditions
                )
            }
        }
    
    def _analyze_functional_groups(self, smiles: str) -> List[Dict[str, Any]]:
        """分析分子中的官能团"""
        # 这里应该使用RDKit进行SMARTS匹配
        # 简化实现，返回模拟结果
        detected_groups = []
        
        # 简单的字符串匹配（实际应用中应使用RDKit）
        if "C(=O)O" in smiles:
            detected_groups.append({
                "name": "羧酸",
                "count": smiles.count("C(=O)O"),
                "reactivity": 0.7,
                "stability": 0.5
            })
        
        if "C(=O)N" in smiles:
            detected_groups.append({
                "name": "酰胺基",
                "count": smiles.count("C(=O)N"),
                "reactivity": 0.6,
                "stability": 0.5
            })
        
        return detected_groups
    
    def _assess_reaction_risks(self, functional_groups: List[Dict[str, Any]], 
                             excipient_profile: ExcipientProfile, 
                             conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """评估反应风险"""
        risks = []
        
        for group in functional_groups:
            for rule in self.reaction_rules:
                if group["name"] in [fg.name for fg in self.functional_groups.values() 
                                   if fg.name in rule["drug_groups"]]:
                    
                    # 检查辅料条件是否满足
                    if self._check_excipient_conditions(rule["excipient_conditions"], 
                                                       excipient_profile):
                        
                        # 计算反应速率常数
                        rate_constant = self._calculate_rate_constant(
                            rule, conditions
                        )
                        
                        # 评估风险等级
                        risk_level = self._evaluate_risk_level(rate_constant, rule)
                        
                        risks.append({
                            "reaction_name": rule["name"],
                            "mechanism": rule["mechanism"].value,
                            "risk_level": risk_level,
                            "rate_constant": rate_constant,
                            "products": rule["products"],
                            "conditions_met": True,
                            "mitigation_strategies": self._suggest_mitigation(rule, conditions)
                        })
        
        return risks
    
    def _check_excipient_conditions(self, required_conditions: List[str], 
                                   excipient_profile: ExcipientProfile) -> bool:
        """检查辅料是否满足反应条件"""
        for condition in required_conditions:
            if condition == "high_moisture" and excipient_profile.moisture_content < 0.05:
                return False
            elif condition == "alkaline_ph" and excipient_profile.ph_range[1] < 7.5:
                return False
            elif condition == "reducing_sugar" and not excipient_profile.reducing_potential:
                return False
            elif condition == "oxidizing_agent" and not excipient_profile.oxidizing_potential:
                return False
            elif condition == "metal_catalyst" and sum(excipient_profile.metal_content.values()) < 0.0001:
                return False
        
        return True
    
    def _calculate_rate_constant(self, rule: Dict[str, Any], 
                               conditions: Dict[str, Any]) -> float:
        """计算反应速率常数"""
        # Arrhenius方程: k = A * exp(-Ea/RT)
        R = 8.314  # J/(mol·K)
        T = conditions.get("temperature", 25) + 273.15  # K
        Ea = rule["activation_energy"] * 1000  # J/mol
        A = 1e10  # 预指数因子（简化）
        
        k = A * math.exp(-Ea / (R * T))
        
        # pH影响
        if rule.get("ph_dependence"):
            ph = conditions.get("ph", 7.0)
            if rule["mechanism"] == ReactionMechanism.HYDROLYSIS:
                # 酸碱催化水解
                k *= (10**(ph-7) + 10**(7-ph))
        
        # 湿度影响
        if "moisture" in str(rule["mechanism"]).lower():
            humidity = conditions.get("humidity", 60) / 100
            k *= humidity
        
        return k
    
    def _evaluate_risk_level(self, rate_constant: float, rule: Dict[str, Any]) -> str:
        """评估风险等级"""
        # 基于反应速率常数评估风险
        if rate_constant > 1e-6:
            return "very_high"
        elif rate_constant > 1e-8:
            return "high"
        elif rate_constant > 1e-10:
            return "medium"
        elif rate_constant > 1e-12:
            return "low"
        else:
            return "very_low"
    
    def _search_literature_evidence(self, drug_name: str, 
                                   excipient_name: str) -> List[Dict[str, Any]]:
        """搜索文献证据"""
        return self.literature_database.get((drug_name, excipient_name), [])
    
    def _calculate_overall_risk(self, reaction_risks: List[Dict[str, Any]], 
                              literature_evidence: List[Dict[str, Any]], 
                              conditions: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合风险评分"""
        if not reaction_risks and not literature_evidence:
            return {"level": "low", "score": 2.0, "confidence": 0.5}
        
        # 基于反应风险的评分
        reaction_score = 0
        for risk in reaction_risks:
            level_scores = {
                "very_high": 10, "high": 8, "medium": 5, "low": 2, "very_low": 1
            }
            reaction_score = max(reaction_score, level_scores.get(risk["risk_level"], 0))
        
        # 基于文献证据的评分
        literature_score = 0
        for evidence in literature_evidence:
            level_scores = {"high": 8, "medium": 5, "low": 2}
            literature_score = max(literature_score, level_scores.get(evidence["risk_level"], 0))
        
        # 综合评分
        final_score = max(reaction_score, literature_score)
        
        # 环境因素修正
        temp = conditions.get("temperature", 25)
        humidity = conditions.get("humidity", 60)
        if temp > 40:
            final_score *= 1.5
        if humidity > 75:
            final_score *= 1.2
        
        # 确定风险等级
        if final_score >= 8:
            level = "high"
        elif final_score >= 5:
            level = "medium"
        elif final_score >= 2:
            level = "low"
        else:
            level = "very_low"
        
        # 置信度评估
        confidence = 0.8 if literature_evidence else 0.6
        
        return {
            "level": level,
            "score": final_score,
            "confidence": confidence
        }
    
    def _generate_recommendations(self, overall_risk: Dict[str, Any], 
                                reaction_risks: List[Dict[str, Any]], 
                                excipient_profile: ExcipientProfile, 
                                conditions: Dict[str, Any]) -> List[str]:
        """生成专业建议"""
        recommendations = []
        
        if overall_risk["level"] in ["high", "very_high"]:
            recommendations.append("强烈建议更换辅料或调整处方")
            recommendations.append("如必须使用，需要进行强制降解试验验证")
        
        elif overall_risk["level"] == "medium":
            recommendations.append("建议进行相容性试验验证")
            recommendations.append("考虑优化储存条件")
        
        # 针对具体反应的建议
        for risk in reaction_risks:
            if risk["risk_level"] in ["high", "very_high"]:
                if risk["mechanism"] == "hydrolysis":
                    recommendations.append("控制环境湿度，使用干燥剂")
                    recommendations.append("调整pH至中性范围")
                elif risk["mechanism"] == "oxidation":
                    recommendations.append("添加抗氧化剂（如BHT、BHA）")
                    recommendations.append("使用惰性气体保护")
                elif risk["mechanism"] == "maillard":
                    recommendations.append("使用无水乳糖或其他非还原性填充剂")
        
        # 包装建议
        if conditions.get("humidity", 60) > 65:
            recommendations.append("使用高阻隔性包装材料")
        
        if conditions.get("temperature", 25) > 30:
            recommendations.append("建议冷藏储存")
        
        return list(set(recommendations))  # 去重
    
    def _analyze_environmental_factors(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """分析环境因素影响"""
        return {
            "temperature_impact": self._assess_temperature_impact(conditions.get("temperature", 25)),
            "humidity_impact": self._assess_humidity_impact(conditions.get("humidity", 60)),
            "ph_impact": self._assess_ph_impact(conditions.get("ph", 7.0)),
            "light_impact": self._assess_light_impact(conditions.get("light_exposure", False))
        }
    
    def _assess_temperature_impact(self, temperature: float) -> str:
        """评估温度影响"""
        if temperature > 40:
            return "高温显著加速降解反应"
        elif temperature > 30:
            return "温度升高会增加反应速率"
        elif temperature < 5:
            return "低温有利于稳定性"
        else:
            return "温度条件适宜"
    
    def _assess_humidity_impact(self, humidity: float) -> str:
        """评估湿度影响"""
        if humidity > 75:
            return "高湿度显著促进水解反应"
        elif humidity > 60:
            return "湿度偏高，需要注意水解风险"
        else:
            return "湿度条件可接受"
    
    def _assess_ph_impact(self, ph: float) -> str:
        """评估pH影响"""
        if ph < 3 or ph > 9:
            return "极端pH条件显著增加降解风险"
        elif ph < 5 or ph > 8:
            return "pH偏离中性，可能影响稳定性"
        else:
            return "pH条件适宜"
    
    def _assess_light_impact(self, light_exposure: bool) -> str:
        """评估光照影响"""
        return "光照可能引起光降解反应" if light_exposure else "避光条件良好"
    
    def _predict_degradation_pathways(self, drug_name: str, 
                                    functional_groups: List[Dict[str, Any]], 
                                    excipient_profile: ExcipientProfile, 
                                    conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """预测降解途径"""
        pathways = self.degradation_pathways.get(drug_name, [])
        
        # 基于官能团和条件预测可能的降解途径
        predicted_pathways = []
        for pathway in pathways:
            probability = self._calculate_pathway_probability(
                pathway, functional_groups, excipient_profile, conditions
            )
            if probability > 0.1:  # 只包含概率大于10%的途径
                predicted_pathways.append({
                    **pathway,
                    "probability": probability
                })
        
        return predicted_pathways
    
    def _calculate_pathway_probability(self, pathway: Dict[str, Any], 
                                     functional_groups: List[Dict[str, Any]], 
                                     excipient_profile: ExcipientProfile, 
                                     conditions: Dict[str, Any]) -> float:
        """计算降解途径概率"""
        # 简化的概率计算
        base_probability = 0.5
        
        # 根据条件调整概率
        for condition in pathway["conditions"]:
            if "碱性" in condition and excipient_profile.ph_range[1] > 8:
                base_probability *= 1.5
            elif "高湿度" in condition and conditions.get("humidity", 60) > 75:
                base_probability *= 1.3
            elif "高温" in condition and conditions.get("temperature", 25) > 40:
                base_probability *= 1.4
        
        return min(base_probability, 1.0)
    
    def _suggest_mitigation(self, rule: Dict[str, Any], 
                          conditions: Dict[str, Any]) -> List[str]:
        """建议缓解策略"""
        strategies = []
        
        if rule["mechanism"] == ReactionMechanism.HYDROLYSIS:
            strategies.extend([
                "控制环境湿度至50%以下",
                "调整pH至6.0-7.5范围",
                "使用干燥剂包装"
            ])
        
        elif rule["mechanism"] == ReactionMechanism.OXIDATION:
            strategies.extend([
                "添加抗氧化剂",
                "使用惰性气体包装",
                "避光储存"
            ])
        
        elif rule["mechanism"] == ReactionMechanism.MAILLARD:
            strategies.extend([
                "使用非还原性辅料",
                "控制储存温度",
                "降低水分活度"
            ])
        
        return strategies
    
    def _generate_unknown_excipient_result(self, drug_name: str, 
                                         excipient_name: str) -> Dict[str, Any]:
        """为未知辅料生成结果"""
        return {
            "drug_name": drug_name,
            "excipient_name": excipient_name,
            "risk_level": "unknown",
            "risk_score": 5.0,
            "confidence": 0.2,
            "functional_groups": [],
            "reaction_risks": [],
            "literature_evidence": [],
            "recommendations": [
                f"辅料 '{excipient_name}' 不在数据库中",
                "建议进行相容性试验验证",
                "查阅相关文献资料"
            ],
            "detailed_analysis": {
                "excipient_profile": None,
                "environmental_factors": {},
                "degradation_pathways": []
            }
        } 