import React from 'react';
import { Card, Row, Col, Tag, Typography, Alert } from 'antd';

const { Text } = Typography;

// ICH标准试验条件
export const STANDARD_CONDITIONS = {
  // 长期储存条件
  LONG_TERM_ZONE_I: { 
    name: 'Zone I 长期', 
    temperature: 21, 
    humidity: 45, 
    description: '21°C ± 2°C/45% ± 5% RH',
    zone: 'I',
    type: 'long_term'
  },
  LONG_TERM_ZONE_II: { 
    name: 'Zone II 长期', 
    temperature: 25, 
    humidity: 60, 
    description: '25°C ± 2°C/60% ± 5% RH',
    zone: 'II',
    type: 'long_term'
  },
  LONG_TERM_ZONE_III: { 
    name: 'Zone III 长期', 
    temperature: 30, 
    humidity: 35, 
    description: '30°C ± 2°C/35% ± 5% RH',
    zone: 'III',
    type: 'long_term'
  },
  LONG_TERM_ZONE_IVA: { 
    name: 'Zone IVa 长期', 
    temperature: 30, 
    humidity: 65, 
    description: '30°C ± 2°C/65% ± 5% RH',
    zone: 'IVa',
    type: 'long_term'
  },
  LONG_TERM_ZONE_IVB: { 
    name: 'Zone IVb 长期', 
    temperature: 30, 
    humidity: 75, 
    description: '30°C ± 2°C/75% ± 5% RH',
    zone: 'IVb',
    type: 'long_term'
  },
  
  // 中间条件
  INTERMEDIATE_ZONE_II: { 
    name: 'Zone II 中间', 
    temperature: 30, 
    humidity: 65, 
    description: '30°C ± 2°C/65% ± 5% RH',
    zone: 'II',
    type: 'intermediate'
  },
  INTERMEDIATE_ZONE_IVA: { 
    name: 'Zone IVa 中间', 
    temperature: 30, 
    humidity: 65, 
    description: '30°C ± 2°C/65% ± 5% RH',
    zone: 'IVa',
    type: 'intermediate'
  },
  INTERMEDIATE_ZONE_IVB: { 
    name: 'Zone IVb 中间', 
    temperature: 30, 
    humidity: 75, 
    description: '30°C ± 2°C/75% ± 5% RH',
    zone: 'IVb',
    type: 'intermediate'
  },
  
  // 加速条件
  ACCELERATED_ZONE_I: { 
    name: 'Zone I 加速', 
    temperature: 30, 
    humidity: 65, 
    description: '30°C ± 2°C/65% ± 5% RH',
    zone: 'I',
    type: 'accelerated'
  },
  ACCELERATED_ZONE_II: { 
    name: 'Zone II 加速', 
    temperature: 40, 
    humidity: 75, 
    description: '40°C ± 2°C/75% ± 5% RH',
    zone: 'II',
    type: 'accelerated'
  },
  ACCELERATED_ZONE_III: { 
    name: 'Zone III 加速', 
    temperature: 40, 
    humidity: 75, 
    description: '40°C ± 2°C/75% ± 5% RH',
    zone: 'III',
    type: 'accelerated'
  },
  ACCELERATED_ZONE_IVA: { 
    name: 'Zone IVa 加速', 
    temperature: 40, 
    humidity: 75, 
    description: '40°C ± 2°C/75% ± 5% RH',
    zone: 'IVa',
    type: 'accelerated'
  },
  ACCELERATED_ZONE_IVB: { 
    name: 'Zone IVb 加速', 
    temperature: 40, 
    humidity: 75, 
    description: '40°C ± 2°C/75% ± 5% RH',
    zone: 'IVb',
    type: 'accelerated'
  },
  
  // 特殊储存条件
  REFRIGERATED: { 
    name: '冷藏', 
    temperature: 5, 
    humidity: 0, 
    description: '5°C ± 3°C',
    type: 'special'
  },
  FREEZER: { 
    name: '冷冻', 
    temperature: -20, 
    humidity: 0, 
    description: '-20°C ± 5°C',
    type: 'special'
  },
  ULTRA_LOW: { 
    name: '超低温', 
    temperature: -70, 
    humidity: 0, 
    description: '-70°C ± 10°C',
    type: 'special'
  }
};

interface StabilityConditionsSelectorProps {
  onConditionSelect: (condition: typeof STANDARD_CONDITIONS.LONG_TERM_ZONE_II) => void;
  selectedDataPointIndex?: number;
}

const StabilityConditionsSelector: React.FC<StabilityConditionsSelectorProps> = ({
  onConditionSelect,
  selectedDataPointIndex
}) => {
  const getTagColor = (type: string) => {
    switch (type) {
      case 'long_term': return 'blue';
      case 'intermediate': return 'orange';
      case 'accelerated': return 'red';
      case 'special': return 'purple';
      default: return 'default';
    }
  };

  const renderConditionGroup = (title: string, conditions: any[], type: string) => (
    <Card size="small" title={title} style={{ marginBottom: 16 }}>
      <Row gutter={[8, 8]}>
        {conditions.map((condition, index) => (
          <Col span={12} key={index}>
            <Tag
              color={getTagColor(type)}
              style={{ 
                cursor: 'pointer', 
                padding: '8px 12px', 
                width: '100%',
                textAlign: 'center',
                marginBottom: 8,
                fontSize: '12px',
                lineHeight: '1.2'
              }}
              onClick={() => onConditionSelect(condition)}
            >
              <div>
                <Text strong style={{ color: 'inherit', fontSize: '11px' }}>
                  {condition.name}
                </Text>
                <br />
                <Text style={{ color: 'inherit', fontSize: '10px' }}>
                  {condition.description}
                </Text>
              </div>
            </Tag>
          </Col>
        ))}
      </Row>
    </Card>
  );

  const longTermConditions = Object.values(STANDARD_CONDITIONS).filter(c => c.type === 'long_term');
  const intermediateConditions = Object.values(STANDARD_CONDITIONS).filter(c => c.type === 'intermediate');
  const acceleratedConditions = Object.values(STANDARD_CONDITIONS).filter(c => c.type === 'accelerated');
  const specialConditions = Object.values(STANDARD_CONDITIONS).filter(c => c.type === 'special');

  return (
    <Card title="ICH标准试验条件" style={{ marginBottom: 16 }}>
      <Alert 
        message="标准条件快速应用" 
        description={`点击以下标准条件可以快速应用到${selectedDataPointIndex !== undefined ? `第${selectedDataPointIndex + 1}个` : '选中的'}数据点`}
        type="info" 
        showIcon 
        style={{ marginBottom: 16 }}
      />
      
      <Row gutter={16}>
        <Col span={12}>
          {renderConditionGroup('长期储存条件', longTermConditions, 'long_term')}
          {renderConditionGroup('中间条件', intermediateConditions, 'intermediate')}
        </Col>
        <Col span={12}>
          {renderConditionGroup('加速条件', acceleratedConditions, 'accelerated')}
          {renderConditionGroup('特殊储存条件', specialConditions, 'special')}
        </Col>
      </Row>
      
      <Alert
        message="条件说明"
        description={
          <div>
            <Text>• Zone I: 温带气候 (北欧、加拿大)</Text><br />
            <Text>• Zone II: 地中海/亚热带气候 (美国、欧洲南部、日本)</Text><br />
            <Text>• Zone III: 炎热干燥气候 (伊朗、伊拉克)</Text><br />
            <Text>• Zone IVa: 炎热潮湿气候 (巴西、加纳、泰国)</Text><br />
            <Text>• Zone IVb: 炎热高湿气候 (巴西、印尼、巴拿马)</Text>
          </div>
        }
        type="warning"
        style={{ marginTop: 16 }}
      />
    </Card>
  );
};

export default StabilityConditionsSelector; 