from fastapi import APIRouter, Body, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Session
from app.services import get_db
from app.models.ai_models import StabilityPredictionSystem

router = APIRouter()

class StabilityInput(BaseModel):
    drug_id: str
    drug_name: str
    drug_features: Optional[Dict[str, Any]] = None
    stability_data: List[Dict[str, Any]]
    prediction_conditions: List[Dict[str, float]] = [
        {'temperature': 25, 'humidity': 60},  # 常温
        {'temperature': 30, 'humidity': 65},  # 中间条件
        {'temperature': 40, 'humidity': 75}   # 加速
    ]
    packaging_type: Optional[str] = None

@router.post('/ai/stability/predict')
def predict_stability(req: StabilityInput, db: Session = Depends(get_db)):
    try:
        # 创建预测系统
        prediction_system = StabilityPredictionSystem()
        
        # 执行综合预测
        results = prediction_system.predict_comprehensive_stability(
            drug_data=req.drug_features or {},
            stability_data=req.stability_data,
            prediction_conditions=req.prediction_conditions,
            packaging_type=req.packaging_type
        )
        
        return {
            'status': 'success',
            'drug_id': req.drug_id,
            'drug_name': req.drug_name,
            'results': results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
