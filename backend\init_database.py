#!/usr/bin/env python3
"""
简单的数据库初始化脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import engine
from app.models.base import Base

# 导入所有模型以确保它们被注册
from app.models.user import UserORM
from app.models.api_key import APIKeyORM
from app.models.operation_log import OperationLogORM
from app.models.drug import DrugORM
from app.models.excipient import ExcipientORM, ExcipientInteraction
from app.models.environment import EnvironmentORM
from app.models.stability import StabilityDataORM, DegradationProductORM, StabilityPredictionORM, StabilityStudyORM
from app.models.project import ProjectORM
from app.models.export import ExportHistory
from app.models.analysis import AISuggestion

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
        # 检查创建的表
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"✅ 创建了 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = init_database()
    if success:
        print("\n🎉 数据库初始化完成！")
    else:
        print("\n💥 数据库初始化失败！")
        sys.exit(1)
