import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Tooltip,
  Modal,
  List,
  Alert,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Descriptions
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  DownloadOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { Title, Text } = Typography;

interface ImportRecord {
  id: string;
  import_time: string;
  file_name: string;
  data_type: 'drug' | 'excipient' | 'stability' | 'formulation';
  total_records: number;
  success_count: number;
  error_count: number;
  status: 'success' | 'partial' | 'failed';
  errors?: Array<{
    row: number;
    field: string;
    error: string;
  }>;
  user?: string;
}

interface ImportHistoryProps {
  projectId?: string;
  onRefresh?: () => void;
}

export const ImportHistory: React.FC<ImportHistoryProps> = ({ projectId, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<ImportRecord[]>([]);
  const [selectedRecord, setSelectedRecord] = useState<ImportRecord | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    success: 0,
    failed: 0,
    totalRecords: 0
  });

  // 加载历史记录
  const loadHistory = async () => {
    setLoading(true);
    try {
      const response = await axios.get<{ history: ImportRecord[] }>('/api/data-import/history', {
        params: projectId ? { project_id: projectId } : {}
      });
      
      const records = response.data.history || [];
      setHistory(records);
      
      // 计算统计数据
      const stats = records.reduce((acc: any, record: ImportRecord) => {
        acc.total++;
        if (record.status === 'success') acc.success++;
        if (record.status === 'failed') acc.failed++;
        acc.totalRecords += record.total_records;
        return acc;
      }, { total: 0, success: 0, failed: 0, totalRecords: 0 });
      
      setStats(stats);
    } catch (error) {
      console.error('加载导入历史失败:', error);
      message.error('加载导入历史失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, [projectId]);

  // 下载模板
  const downloadTemplate = async (dataType: string) => {
    try {
      const response = await axios.get(`/api/data-import/template/${dataType}`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(response.data as Blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${dataType}_template.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 查看详情
  const viewDetails = (record: ImportRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  // 清理历史记录
  const clearHistory = async () => {
    Modal.confirm({
      title: '确认清理',
      content: '确定要清理所有导入历史记录吗？此操作不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await axios.delete('/api/data-import/history/clear');
          message.success('历史记录已清理');
          loadHistory();
        } catch (error) {
          console.error('清理历史失败:', error);
          message.error('清理历史失败');
        }
      }
    });
  };

  const columns = [
    {
      title: '导入时间',
      dataIndex: 'import_time',
      key: 'import_time',
      width: 180,
      render: (time: string) => moment(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '文件名',
      dataIndex: 'file_name',
      key: 'file_name',
      ellipsis: true
    },
    {
      title: '数据类型',
      dataIndex: 'data_type',
      key: 'data_type',
      width: 100,
      render: (type: string) => {
        const typeMap: Record<string, { color: string; text: string }> = {
          drug: { color: 'blue', text: '药物' },
          excipient: { color: 'green', text: '辅料' },
          stability: { color: 'orange', text: '稳定性' },
          formulation: { color: 'purple', text: '配方' }
        };
        return <Tag color={typeMap[type]?.color}>{typeMap[type]?.text || type}</Tag>;
      }
    },
    {
      title: '记录数',
      dataIndex: 'total_records',
      key: 'total_records',
      width: 80,
      align: 'center' as const
    },
    {
      title: '成功/失败',
      key: 'result',
      width: 120,
      render: (_: any, record: ImportRecord) => (
        <Space>
          <Text type="success">{record.success_count}</Text>
          <Text>/</Text>
          <Text type="danger">{record.error_count}</Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap: Record<string, { color: string; icon: any; text: string }> = {
          success: { color: 'success', icon: <CheckCircleOutlined />, text: '成功' },
          partial: { color: 'warning', icon: <ExclamationCircleOutlined />, text: '部分成功' },
          failed: { color: 'error', icon: <CloseCircleOutlined />, text: '失败' }
        };
        const config = statusMap[status];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.text}
          </Tag>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: ImportRecord) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => viewDetails(record)}
            />
          </Tooltip>
          {record.error_count > 0 && (
            <Tooltip title="下载错误报告">
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => {
                  // 下载错误报告
                  message.info('错误报告下载功能开发中');
                }}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <Card 
      title="批量导入历史"
      extra={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadHistory}>
            刷新
          </Button>
          {history.length > 0 && (
            <Button danger icon={<DeleteOutlined />} onClick={clearHistory}>
              清理历史
            </Button>
          )}
        </Space>
      }
    >
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Statistic
            title="总导入次数"
            value={stats.total}
            prefix={<FileTextOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="成功次数"
            value={stats.success}
            valueStyle={{ color: '#3f8600' }}
            prefix={<CheckCircleOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="失败次数"
            value={stats.failed}
            valueStyle={{ color: '#cf1322' }}
            prefix={<CloseCircleOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="总记录数"
            value={stats.totalRecords}
          />
        </Col>
      </Row>

      {/* 历史记录表格 */}
      <Table
        loading={loading}
        dataSource={history}
        columns={columns}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        locale={{
          emptyText: '暂无导入记录'
        }}
      />

      {/* 详情模态框 */}
      <Modal
        title="导入详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedRecord && (
          <div>
            <Descriptions bordered column={2} size="small">
              <Descriptions.Item label="文件名" span={2}>
                {selectedRecord.file_name}
              </Descriptions.Item>
              <Descriptions.Item label="导入时间">
                {moment(selectedRecord.import_time).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="操作用户">
                {selectedRecord.user || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="数据类型">
                {selectedRecord.data_type}
              </Descriptions.Item>
              <Descriptions.Item label="总记录数">
                {selectedRecord.total_records}
              </Descriptions.Item>
              <Descriptions.Item label="成功数">
                <Text type="success">{selectedRecord.success_count}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="失败数">
                <Text type="danger">{selectedRecord.error_count}</Text>
              </Descriptions.Item>
            </Descriptions>

            {selectedRecord.errors && selectedRecord.errors.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>错误详情</Title>
                <Alert
                  type="error"
                  message={`共有 ${selectedRecord.errors.length} 个错误`}
                  style={{ marginBottom: 8 }}
                />
                <List
                  size="small"
                  dataSource={selectedRecord.errors.slice(0, 10)}
                  renderItem={(error) => (
                    <List.Item>
                      <Space>
                        <Tag color="red">第{error.row}行</Tag>
                        <Text strong>{error.field}:</Text>
                        <Text>{error.error}</Text>
                      </Space>
                    </List.Item>
                  )}
                />
                {selectedRecord.errors.length > 10 && (
                  <Alert
                    type="info"
                    message={`还有 ${selectedRecord.errors.length - 10} 个错误未显示`}
                    style={{ marginTop: 8 }}
                  />
                )}
              </div>
            )}
          </div>
        )}
      </Modal>
    </Card>
  );
}; 