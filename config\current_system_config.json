{"system_info": {"last_update": "2025-06-22", "version": "1.0.0-dev", "status": "stable", "description": "药物稳定性研究助手 - 项目管理功能优化版本"}, "services": {"backend": {"framework": "FastAPI", "host": "0.0.0.0", "port": 8001, "reload": true, "main_module": "app.main:app", "database": "SQLite", "database_file": "backend/app.db"}, "frontend": {"framework": "React", "host": "localhost", "port": 3000, "build_tool": "Create React App", "package_manager": "npm"}}, "key_features": {"project_management": {"status": "enhanced", "component": "ProjectManagementPage", "route": "/projects", "features": ["CRUD operations", "Modern UI with Ant Design", "Project selection", "Auto-navigation to data input", "Delete functionality", "Search and filter", "Pagination"]}, "project_context": {"status": "integrated", "provider": "ProjectContext", "shared_state": ["currentProject", "projects list", "CRUD operations"]}, "user_workflow": {"status": "restored", "flow": ["Project Management → Create/Select Project", "Auto-navigation → Data Input Page", "Data Input → Analysis Functions", "Analysis → Results View"]}}, "recent_fixes": {"2025-06-22": [{"issue": "Delete function not working", "solution": "Fixed API response format and component props", "files_modified": ["backend/app/api/project.py", "frontend/src/components/ConfirmButton.tsx", "frontend/src/App.tsx"]}, {"issue": "UI modernization needed", "solution": "Replaced ProjectList with enhanced ProjectManagementPage", "files_modified": ["frontend/src/pages/ProjectManagementPage.tsx", "frontend/src/App.tsx", "frontend/src/components/Sidebar.tsx"]}, {"issue": "Lost functional connections", "solution": "Integrated ProjectContext and restored user workflow", "files_modified": ["frontend/src/pages/ProjectManagementPage.tsx"]}]}, "routes": {"frontend": {"/": "Dashboard", "/projects": "ProjectManagementPage (Enhanced)", "/project-management": "ProjectManagementPage (Backup route)", "/projects/:id": "ProjectDetail", "/data-input": "DataInput", "/stability-prediction": "StabilityPrediction", "/excipient-analysis": "ExcipientAnalysis", "/formulation-analysis": "FormulationAnalysis", "/user-management": "UserManagement", "/system-settings": "SystemSettings", "/user-center": "UserCenterPage", "/help": "HelpCenter", "/pubchem-search": "PubChemSearch", "/ai-analysis": "AIAnalysis"}, "backend": {"/api/projects": "Project CRUD operations", "/api/projects/{id}": "Project detail operations", "/docs": "API documentation", "/": "Root endpoint"}}, "dependencies": {"frontend": {"react": "^18.x", "antd": "^5.x", "react-router-dom": "^6.x", "typescript": "^4.x", "@ant-design/icons": "^5.x"}, "backend": {"fastapi": "^0.x", "uvicorn": "^0.x", "sqlalchemy": "^2.x", "pydantic": "^2.x"}}, "database_schema": {"projects": {"id": "INTEGER PRIMARY KEY", "name": "VARCHAR", "status": "VARCHAR", "description": "TEXT", "created_at": "DATETIME", "updated_at": "DATETIME", "data": "JSON"}}, "environment_variables": {"development": {"REACT_APP_API_URL": "http://localhost:8001", "NODE_ENV": "development"}}, "startup_commands": {"backend": "cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8001", "frontend": "cd frontend && npm start", "health_check": "scripts/health_check.ps1", "full_startup": "scripts/start_dev_environment.ps1"}, "testing": {"manual_test_flow": ["1. Access http://localhost:3000/projects", "2. Create a new project", "3. Verify auto-navigation to data input", "4. Select an existing project", "5. Test delete functionality", "6. Verify project context sharing"], "api_endpoints_to_test": ["GET /api/projects", "POST /api/projects", "PUT /api/projects/{id}", "DELETE /api/projects/{id}"]}, "known_issues": {"none": "All major issues resolved as of 2025-06-22"}, "performance_notes": {"frontend": "Optimized with Ant Design components", "backend": "SQLite suitable for development", "recommendations": ["Consider PostgreSQL for production", "Implement caching for large datasets", "Add virtual scrolling for large project lists"]}, "security_notes": {"authentication": "JWT-based (implemented)", "authorization": "Role-based (basic implementation)", "recommendations": ["Implement proper session management", "Add API rate limiting", "Enhance input validation"]}}