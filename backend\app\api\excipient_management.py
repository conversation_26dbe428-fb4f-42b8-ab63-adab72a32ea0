"""
辅料管理API端点
支持增强的辅料信息管理
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import json

from app.services import get_db
from app.api.auth import get_current_user
from app.services.open_data_service import open_data_service
from app.services.pharmacopoeia_service import pharmacopoeia_service
from app.services.knowledge_base_service import knowledge_base_service

router = APIRouter()

class ExcipientDetailedInfo(BaseModel):
    """辅料详细信息模型"""
    # 基本信息
    name: str
    cas_number: Optional[str] = None
    synonyms: Optional[List[str]] = None
    category: Optional[str] = None
    function: Optional[str] = None
    
    # 理化性质
    appearance: Optional[str] = None
    melting_point: Optional[str] = None
    boiling_point: Optional[str] = None
    density: Optional[str] = None
    pka: Optional[str] = None
    logp: Optional[float] = None
    solubility_water: Optional[str] = None
    solubility_organic: Optional[str] = None
    hygroscopicity: Optional[str] = None
    particle_size: Optional[str] = None
    specific_surface_area: Optional[str] = None
    angle_of_repose: Optional[str] = None
    compressibility_index: Optional[str] = None
    
    # 稳定性与贮藏
    stability_conditions: Optional[str] = None
    storage_requirements: Optional[str] = None
    incompatibilities: Optional[str] = None
    light_sensitivity: Optional[str] = None
    moisture_sensitivity: Optional[str] = None
    temperature_sensitivity: Optional[str] = None
    oxidation_sensitivity: Optional[str] = None
    
    # 应用信息
    typical_concentration: Optional[str] = None
    dosage_forms: Optional[List[str]] = None
    
    # 安全性
    safety_profile: Optional[str] = None
    regulatory_status: Optional[str] = None
    
    # 药典信息
    usp_nf_info: Optional[str] = None
    ph_eur_info: Optional[str] = None
    jp_info: Optional[str] = None
    chp_info: Optional[str] = None
    
    # 其他
    description: Optional[str] = None
    notes: Optional[str] = None

class ExcipientSearchRequest(BaseModel):
    """辅料搜索请求"""
    query: Optional[str] = None
    category: Optional[str] = None
    cas_number: Optional[str] = None
    has_pharmacopoeia_info: Optional[bool] = None

@router.post("/excipients")
async def create_excipient(
    excipient: ExcipientDetailedInfo,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """创建新的辅料信息"""
    try:
        # 保存到数据库
        import sqlite3
        conn = sqlite3.connect(open_data_service.db_path)
        cursor = conn.cursor()
        
        # 插入主表
        cursor.execute("""
            INSERT INTO excipients (
                name, cas_number, synonyms, category, function,
                appearance, melting_point, boiling_point, density,
                pka, logp, solubility_water, solubility_organic,
                hygroscopicity, particle_size, specific_surface_area,
                angle_of_repose, compressibility_index,
                stability_conditions, storage_requirements, incompatibilities,
                light_sensitivity, moisture_sensitivity, temperature_sensitivity,
                oxidation_sensitivity, typical_concentration, dosage_forms,
                safety_profile, regulatory_status,
                usp_nf_info, ph_eur_info, jp_info, chp_info,
                description, notes, source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            excipient.name,
            excipient.cas_number,
            ','.join(excipient.synonyms) if excipient.synonyms else None,
            excipient.category,
            excipient.function,
            excipient.appearance,
            excipient.melting_point,
            excipient.boiling_point,
            excipient.density,
            excipient.pka,
            excipient.logp,
            excipient.solubility_water,
            excipient.solubility_organic,
            excipient.hygroscopicity,
            excipient.particle_size,
            excipient.specific_surface_area,
            excipient.angle_of_repose,
            excipient.compressibility_index,
            excipient.stability_conditions,
            excipient.storage_requirements,
            excipient.incompatibilities,
            excipient.light_sensitivity,
            excipient.moisture_sensitivity,
            excipient.temperature_sensitivity,
            excipient.oxidation_sensitivity,
            excipient.typical_concentration,
            ','.join(excipient.dosage_forms) if excipient.dosage_forms else None,
            excipient.safety_profile,
            excipient.regulatory_status,
            excipient.usp_nf_info,
            excipient.ph_eur_info,
            excipient.jp_info,
            excipient.chp_info,
            excipient.description,
            excipient.notes,
            f"User: {current_user['username']}"
        ))
        
        excipient_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {
            "status": "success",
            "id": excipient_id,
            "message": f"辅料 {excipient.name} 创建成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/excipients/{excipient_name}")
async def get_excipient(
    excipient_name: str,
    include_pharmacopoeia: bool = True,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取辅料详细信息"""
    try:
        # 从本地数据库获取
        excipient_info = open_data_service.get_excipient_info(name=excipient_name)
        
        if not excipient_info:
            raise HTTPException(status_code=404, detail=f"未找到辅料: {excipient_name}")
        
        # 获取药典信息
        if include_pharmacopoeia:
            pharmacopoeia_info = pharmacopoeia_service.get_excipient_pharmacopoeia_info(excipient_name)
            excipient_info["pharmacopoeia_info"] = pharmacopoeia_info
        
        # 获取知识库中的详细信息
        detailed_info = knowledge_base_service.get_detailed_excipient_info(excipient_name)
        
        # 合并信息
        result = {
            **excipient_info,
            "physical_properties": detailed_info.get("physical_properties", {}),
            "stability_info": detailed_info.get("stability_info", {}),
            "applications": detailed_info.get("applications", {})
        }
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/excipients/{excipient_id}")
async def update_excipient(
    excipient_id: int,
    excipient: ExcipientDetailedInfo,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """更新辅料信息"""
    try:
        import sqlite3
        conn = sqlite3.connect(open_data_service.db_path)
        cursor = conn.cursor()
        
        # 更新辅料信息
        cursor.execute("""
            UPDATE excipients SET
                name = ?, cas_number = ?, synonyms = ?, category = ?, function = ?,
                appearance = ?, melting_point = ?, boiling_point = ?, density = ?,
                pka = ?, logp = ?, solubility_water = ?, solubility_organic = ?,
                hygroscopicity = ?, particle_size = ?, specific_surface_area = ?,
                angle_of_repose = ?, compressibility_index = ?,
                stability_conditions = ?, storage_requirements = ?, incompatibilities = ?,
                light_sensitivity = ?, moisture_sensitivity = ?, temperature_sensitivity = ?,
                oxidation_sensitivity = ?, typical_concentration = ?, dosage_forms = ?,
                safety_profile = ?, regulatory_status = ?,
                usp_nf_info = ?, ph_eur_info = ?, jp_info = ?, chp_info = ?,
                description = ?, notes = ?, last_updated = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (
            excipient.name,
            excipient.cas_number,
            ','.join(excipient.synonyms) if excipient.synonyms else None,
            excipient.category,
            excipient.function,
            excipient.appearance,
            excipient.melting_point,
            excipient.boiling_point,
            excipient.density,
            excipient.pka,
            excipient.logp,
            excipient.solubility_water,
            excipient.solubility_organic,
            excipient.hygroscopicity,
            excipient.particle_size,
            excipient.specific_surface_area,
            excipient.angle_of_repose,
            excipient.compressibility_index,
            excipient.stability_conditions,
            excipient.storage_requirements,
            excipient.incompatibilities,
            excipient.light_sensitivity,
            excipient.moisture_sensitivity,
            excipient.temperature_sensitivity,
            excipient.oxidation_sensitivity,
            excipient.typical_concentration,
            ','.join(excipient.dosage_forms) if excipient.dosage_forms else None,
            excipient.safety_profile,
            excipient.regulatory_status,
            excipient.usp_nf_info,
            excipient.ph_eur_info,
            excipient.jp_info,
            excipient.chp_info,
            excipient.description,
            excipient.notes,
            excipient_id
        ))
        
        if cursor.rowcount == 0:
            conn.close()
            raise HTTPException(status_code=404, detail=f"未找到ID为 {excipient_id} 的辅料")
        
        conn.commit()
        conn.close()
        
        return {
            "status": "success",
            "message": f"辅料 {excipient.name} 更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/excipients/search")
async def search_excipients(
    request: ExcipientSearchRequest,
    current_user: dict = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """搜索辅料"""
    try:
        import sqlite3
        conn = sqlite3.connect(open_data_service.db_path)
        cursor = conn.cursor()
        
        # 构建查询
        query = "SELECT * FROM excipients WHERE 1=1"
        params = []
        
        if request.query:
            query += " AND (name LIKE ? OR synonyms LIKE ? OR description LIKE ?)"
            params.extend([f"%{request.query}%"] * 3)
        
        if request.category:
            query += " AND category = ?"
            params.append(request.category)
        
        if request.cas_number:
            query += " AND cas_number = ?"
            params.append(request.cas_number)
        
        if request.has_pharmacopoeia_info:
            query += " AND (usp_nf_info IS NOT NULL OR ph_eur_info IS NOT NULL OR jp_info IS NOT NULL OR chp_info IS NOT NULL)"
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 转换为字典
        columns = [description[0] for description in cursor.description]
        results = []
        for row in rows:
            excipient = dict(zip(columns, row))
            # 处理列表字段
            if excipient.get('synonyms'):
                excipient['synonyms'] = excipient['synonyms'].split(',')
            if excipient.get('dosage_forms'):
                excipient['dosage_forms'] = excipient['dosage_forms'].split(',')
            results.append(excipient)
        
        conn.close()
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/excipients/{excipient_name}/compatibility-profile")
async def get_excipient_compatibility_profile(
    excipient_name: str,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取辅料的相容性概况"""
    try:
        # 获取辅料类别
        excipient_group = knowledge_base_service.get_excipient_group(excipient_name)
        
        # 获取已知的相互作用
        interactions = open_data_service.search_interactions(excipient_name=excipient_name)
        
        # 获取药典中的配伍禁忌信息
        pharmacopoeia_info = pharmacopoeia_service.get_excipient_pharmacopoeia_info(excipient_name)
        incompatibilities = None
        if "usp" in pharmacopoeia_info:
            incompatibilities = pharmacopoeia_info["usp"].get("incompatibilities")
        
        # 构建相容性概况
        profile = {
            "excipient_name": excipient_name,
            "chemical_group": excipient_group,
            "known_interactions": interactions,
            "pharmacopoeia_incompatibilities": incompatibilities,
            "risk_assessment": {
                "with_amine_drugs": "高" if excipient_group == "reducing_sugar" else "低",
                "with_acid_drugs": "高" if excipient_group == "metal_salt" else "低",
                "with_ester_drugs": "中" if excipient_group == "metal_salt" else "低"
            },
            "recommendations": _generate_compatibility_recommendations(excipient_name, excipient_group)
        }
        
        return profile
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def _generate_compatibility_recommendations(excipient_name: str, excipient_group: str) -> List[str]:
    """生成相容性建议"""
    recommendations = []
    
    if excipient_group == "reducing_sugar":
        recommendations.extend([
            "避免与含胺基的药物配伍",
            "控制水分含量<0.5%",
            "考虑使用非还原糖替代"
        ])
    elif excipient_group == "metal_salt":
        recommendations.extend([
            "注意与酸性或酯类药物的相互作用",
            "进行加速稳定性试验验证",
            "考虑添加螯合剂"
        ])
    
    # 通用建议
    recommendations.append("进行药物-辅料相容性试验验证")
    
    return recommendations

@router.post("/excipients/batch-import")
async def batch_import_excipients(
    excipients: List[ExcipientDetailedInfo],
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """批量导入辅料信息"""
    try:
        success_count = 0
        failed_items = []
        
        for excipient in excipients:
            try:
                # 调用创建函数
                await create_excipient(excipient, current_user, db)
                success_count += 1
            except Exception as e:
                failed_items.append({
                    "name": excipient.name,
                    "error": str(e)
                })
        
        return {
            "status": "completed",
            "success_count": success_count,
            "failed_count": len(failed_items),
            "failed_items": failed_items
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 