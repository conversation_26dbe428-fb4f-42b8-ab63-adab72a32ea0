"""
专业稳定性预测引擎
基于药物降解动力学、阿伦尼乌斯方程和ICH指导原则的稳定性预测系统
"""

from typing import Dict, List, Any, Optional, Tuple
import math
import numpy as np
from dataclasses import dataclass
from enum import Enum
import scipy.stats as stats
from scipy.optimize import curve_fit
from datetime import datetime

class DegradationModel(Enum):
    """降解动力学模型"""
    ZERO_ORDER = "zero_order"
    FIRST_ORDER = "first_order"
    SECOND_ORDER = "second_order"
    SQUARE_ROOT = "square_root"
    CUBE_ROOT = "cube_root"
    AUTO_SELECT = "auto_select"

class ICHCondition(Enum):
    """ICH试验条件"""
    LONG_TERM = "long_term"      # 25°C/60%RH
    INTERMEDIATE = "intermediate" # 30°C/65%RH
    ACCELERATED = "accelerated"   # 40°C/75%RH

@dataclass
class StabilityDataPoint:
    """稳定性数据点"""
    time: float          # 时间（月）
    temperature: float   # 温度（°C）
    humidity: float      # 湿度（%RH）
    value: float         # 检测值（%）
    item: str           # 检测项目
    ph: Optional[float] = None
    light_exposure: bool = False

@dataclass
class KineticParameters:
    """动力学参数"""
    model: DegradationModel
    rate_constant: float
    activation_energy: float  # kJ/mol
    r_squared: float
    confidence_interval: Tuple[float, float]
    arrhenius_plot_data: List[Dict[str, float]]

class StabilityPredictionEngine:
    """专业稳定性预测引擎"""
    
    def __init__(self):
        """初始化预测引擎"""
        self.ich_conditions = {
            ICHCondition.LONG_TERM: {"temp": 25, "humidity": 60},
            ICHCondition.INTERMEDIATE: {"temp": 30, "humidity": 65},
            ICHCondition.ACCELERATED: {"temp": 40, "humidity": 75}
        }
        
        # 常见药物的典型活化能（kJ/mol）
        self.typical_activation_energies = {
            "酯类": 80.0,
            "酰胺类": 100.0,
            "β-内酰胺类": 75.0,
            "酚类": 65.0,
            "醛类": 70.0,
            "默认": 85.0
        }
    
    def predict_stability(
        self,
        data_points: List[StabilityDataPoint],
        drug_name: str,
        prediction_months: int = 24,
        confidence_level: float = 0.95,
        model_selection: str = "auto"
    ) -> Dict[str, Any]:
        """
        预测药物稳定性
        
        参数:
            data_points: 稳定性数据点列表
            drug_name: 药物名称
            prediction_months: 预测时长(月)
            confidence_level: 置信水平
            model_selection: 模型选择策略
            
        返回:
            Dict[str, Any]: 预测结果
        """
        # 按检测项目分组
        items_data = {}
        for point in data_points:
            if point.item not in items_data:
                items_data[point.item] = []
            items_data[point.item].append(point)
        
        # 对每个检测项目进行预测
        results = {}
        all_recommendations = []
        
        for item, points in items_data.items():
            # 排序数据点
            sorted_points = sorted(points, key=lambda p: p.time)
            
            # 选择降解模型
            if model_selection == "auto":
                selected_model = self._select_best_model(sorted_points, item)
            else:
                selected_model = DegradationModel[model_selection.upper().replace("-", "_")]
            
            # 构建温度-湿度降解动力学模型
            degradation_params = self._fit_degradation_model(sorted_points, selected_model)
            
            # 预测不同条件下的稳定性
            predictions = {}
            for condition in ICHCondition:
                prediction = self._predict_for_condition(
                    degradation_params,
                    selected_model,
                    condition,
                    sorted_points[0].value,  # 使用实际初始值
                    prediction_months,
                    confidence_level
                )
                predictions[condition.value] = prediction
            
            # 计算实际货架期（基于初始值计算T90）
            initial_value = sorted_points[0].value
            target_value = initial_value * 0.9  # 计算实际的90%值
            
            shelf_life = self._calculate_shelf_life(
                degradation_params,
                selected_model,
                ICHCondition.LONG_TERM,
                initial_value,
                target_value,  # 使用计算出的目标值
                confidence_level
            )
            
            # 基于数据趋势分析降解途径
            degradation_pathways = self._analyze_degradation_pathways(
                sorted_points, 
                degradation_params
            )
            
            # 生成针对性建议
            recommendations = self._generate_recommendations(
                item,
                shelf_life,
                degradation_pathways,
                drug_name
            )
            all_recommendations.extend(recommendations)
            
            results[item] = {
                "model": selected_model.value,
                "degradation_parameters": degradation_params,
                "predictions": predictions,
                "shelf_life": shelf_life,
                "degradation_pathways": degradation_pathways,
                "data_quality": self._assess_data_quality(sorted_points),
                "initial_value": initial_value,
                "target_value": target_value
            }
        
        # 总体评估
        overall_assessment = self._overall_assessment(results)
        
        # 法规符合性评估
        regulatory_compliance = self._check_regulatory_compliance(
            data_points,
            results,
            prediction_months
        )
        
        return {
            "drug_name": drug_name,
            "analysis_date": datetime.now().isoformat(),
            "results": results,
            "overall_assessment": overall_assessment,
            "regulatory_compliance": regulatory_compliance,
            "recommendations": list(set(all_recommendations))[:10]  # 去重并限制数量
        }
    
    def _group_data_by_item(self, data_points: List[StabilityDataPoint]) -> Dict[str, List[StabilityDataPoint]]:
        """按检测项目分组数据"""
        grouped = {}
        for point in data_points:
            if point.item not in grouped:
                grouped[point.item] = []
            grouped[point.item].append(point)
        return grouped
    
    def _group_data_by_temperature(self, data_points: List[StabilityDataPoint]) -> Dict[float, List[StabilityDataPoint]]:
        """按温度分组数据"""
        grouped = {}
        for point in data_points:
            temp = point.temperature
            if temp not in grouped:
                grouped[temp] = []
            grouped[temp].append(point)
        return grouped
    
    def _fit_kinetic_model(self, data_points: List[StabilityDataPoint], 
                          model_selection: str) -> KineticParameters:
        """拟合动力学模型"""
        times = np.array([p.time for p in data_points])
        values = np.array([p.value for p in data_points])
        
        if model_selection == "auto":
            # 自动选择最佳模型
            models_to_test = [
                DegradationModel.ZERO_ORDER,
                DegradationModel.FIRST_ORDER,
                DegradationModel.SECOND_ORDER,
                DegradationModel.SQUARE_ROOT
            ]
            
            best_model = None
            best_r_squared = -1
            best_params = None
            
            for model in models_to_test:
                try:
                    params = self._fit_specific_model(times, values, model)
                    if params and params.r_squared > best_r_squared:
                        best_r_squared = params.r_squared
                        best_model = model
                        best_params = params
                except:
                    continue
            
            return best_params if best_params else self._fit_specific_model(times, values, DegradationModel.FIRST_ORDER)
        
        else:
            # 使用指定模型
            model_enum = DegradationModel(model_selection)
            return self._fit_specific_model(times, values, model_enum)
    
    def _fit_specific_model(self, times: np.ndarray, values: np.ndarray, 
                           model: DegradationModel) -> KineticParameters:
        """拟合特定动力学模型"""
        try:
            if model == DegradationModel.ZERO_ORDER:
                # C = C0 - kt
                def zero_order(t, c0, k):
                    return c0 - k * t
                
                popt, pcov = curve_fit(zero_order, times, values)
                c0, k = popt
                predicted = zero_order(times, c0, k)
                
            elif model == DegradationModel.FIRST_ORDER:
                # ln(C) = ln(C0) - kt
                def first_order(t, c0, k):
                    return c0 * np.exp(-k * t)
                
                popt, pcov = curve_fit(first_order, times, values, bounds=([0, 0], [200, 10]))
                c0, k = popt
                predicted = first_order(times, c0, k)
                
            elif model == DegradationModel.SECOND_ORDER:
                # 1/C = 1/C0 + kt
                def second_order(t, c0, k):
                    return 1 / (1/c0 + k * t)
                
                popt, pcov = curve_fit(second_order, times, values, bounds=([0, 0], [200, 10]))
                c0, k = popt
                predicted = second_order(times, c0, k)
                
            elif model == DegradationModel.SQUARE_ROOT:
                # C = C0 - kt^0.5
                def square_root(t, c0, k):
                    return c0 - k * np.sqrt(t)
                
                popt, pcov = curve_fit(square_root, times, values)
                c0, k = popt
                predicted = square_root(times, c0, k)
            
            # 计算R²
            ss_res = np.sum((values - predicted) ** 2)
            ss_tot = np.sum((values - np.mean(values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot)
            
            # 计算置信区间
            residuals = values - predicted
            mse = np.mean(residuals ** 2)
            std_error = np.sqrt(mse)
            
            # 简化的置信区间计算
            ci_lower = k - 1.96 * std_error
            ci_upper = k + 1.96 * std_error
            
            return KineticParameters(
                model=model,
                rate_constant=k,
                activation_energy=0.0,  # 将在后续计算
                r_squared=r_squared,
                confidence_interval=(ci_lower, ci_upper),
                arrhenius_plot_data=[]
            )
            
        except Exception as e:
            # 如果拟合失败，返回默认的一级动力学
            print(f"模型拟合失败: {e}")
            return KineticParameters(
                model=DegradationModel.FIRST_ORDER,
                rate_constant=0.01,
                activation_energy=85.0,
                r_squared=0.5,
                confidence_interval=(0.005, 0.015),
                arrhenius_plot_data=[]
            )
    
    def _calculate_activation_energy(self, kinetic_models: Dict[float, KineticParameters]) -> float:
        """计算活化能"""
        if len(kinetic_models) < 2:
            return self.typical_activation_energies["默认"]
        
        try:
            # 使用阿伦尼乌斯方程: ln(k) = ln(A) - Ea/(RT)
            temperatures = []
            rate_constants = []
            
            for temp, model in kinetic_models.items():
                temperatures.append(1 / (temp + 273.15))  # 1/T (K^-1)
                rate_constants.append(np.log(model.rate_constant))  # ln(k)
            
            # 线性回归
            slope, intercept, r_value, p_value, std_err = stats.linregress(temperatures, rate_constants)
            
            # 活化能 = -slope * R (R = 8.314 J/(mol·K))
            activation_energy = -slope * 8.314 / 1000  # kJ/mol
            
            # 确保活化能在合理范围内
            if activation_energy < 20 or activation_energy > 200:
                return self.typical_activation_energies["默认"]
            
            return activation_energy
            
        except Exception as e:
            print(f"活化能计算失败: {e}")
            return self.typical_activation_energies["默认"]
    
    def _predict_ich_conditions(self, kinetic_models: Dict[float, KineticParameters], 
                               activation_energy: float, 
                               prediction_months: int,
                               confidence_level: float) -> Dict[str, Any]:
        """预测ICH条件下的稳定性"""
        predictions = {}
        
        # 选择参考温度（通常选择最接近25°C的温度）
        ref_temp = min(kinetic_models.keys(), key=lambda x: abs(x - 25))
        ref_model = kinetic_models[ref_temp]
        
        for condition, params in self.ich_conditions.items():
            target_temp = params["temp"]
            
            # 使用阿伦尼乌斯方程外推速率常数
            k_target = self._extrapolate_rate_constant(
                ref_model.rate_constant, ref_temp, target_temp, activation_energy
            )
            
            # 预测降解曲线
            time_points = np.linspace(0, prediction_months, prediction_months + 1)
            predicted_values = self._predict_degradation_curve(
                time_points, k_target, ref_model.model, initial_value=100.0
            )
            
            # 计算t90（降解到90%的时间）
            t90 = self._calculate_t90(k_target, ref_model.model)
            
            predictions[condition.value] = {
                "temperature": target_temp,
                "humidity": params["humidity"],
                "rate_constant": k_target,
                "t90": t90,
                "predicted_curve": [
                    {"time": t, "value": v, "lower_ci": v-2, "upper_ci": v+2}
                    for t, v in zip(time_points, predicted_values)
                ]
            }
        
        return predictions
    
    def _extrapolate_rate_constant(self, k_ref: float, temp_ref: float, 
                                  temp_target: float, activation_energy: float) -> float:
        """使用阿伦尼乌斯方程外推速率常数"""
        R = 8.314  # J/(mol·K)
        T_ref = temp_ref + 273.15  # K
        T_target = temp_target + 273.15  # K
        
        # k2 = k1 * exp((Ea/R) * (1/T1 - 1/T2))
        k_target = k_ref * math.exp(
            (activation_energy * 1000 / R) * (1/T_ref - 1/T_target)
        )
        
        return k_target
    
    def _predict_degradation_curve(self, time_points: np.ndarray, rate_constant: float,
                                  model: DegradationModel, initial_value: float = 100.0) -> np.ndarray:
        """预测降解曲线"""
        if model == DegradationModel.ZERO_ORDER:
            return initial_value - rate_constant * time_points
        elif model == DegradationModel.FIRST_ORDER:
            return initial_value * np.exp(-rate_constant * time_points)
        elif model == DegradationModel.SECOND_ORDER:
            return 1 / (1/initial_value + rate_constant * time_points)
        elif model == DegradationModel.SQUARE_ROOT:
            return initial_value - rate_constant * np.sqrt(time_points)
        else:
            # 默认使用一级动力学
            return initial_value * np.exp(-rate_constant * time_points)
    
    def _calculate_t90(self, rate_constant: float, model: DegradationModel) -> float:
        """计算t90（降解到90%的时间）"""
        try:
            if model == DegradationModel.ZERO_ORDER:
                # C = C0 - kt, 当C = 0.9*C0时
                return 10 / rate_constant  # 假设初始值为100
            elif model == DegradationModel.FIRST_ORDER:
                # C = C0 * exp(-kt), 当C = 0.9*C0时
                return math.log(1/0.9) / rate_constant
            elif model == DegradationModel.SECOND_ORDER:
                # 1/C = 1/C0 + kt, 当C = 0.9*C0时
                return (1/90 - 1/100) / rate_constant
            else:
                # 默认使用一级动力学
                return math.log(1/0.9) / rate_constant
        except:
            return 24.0  # 默认24个月
    
    def _calculate_shelf_life(self, degradation_params: Dict[str, Any], 
                             model: DegradationModel, condition: ICHCondition,
                             initial_value: float, target_value: float,
                             confidence_level: float) -> Dict[str, Any]:
        """计算货架期"""
        # 使用长期条件计算
        target_temp = self.ich_conditions[condition]["temp"]
        kinetic_models = degradation_params.get("kinetic_models", {})
        activation_energy = degradation_params.get("activation_energy", 85.0)
        
        # 获取或外推25°C的速率常数
        if target_temp in kinetic_models:
            k_25 = kinetic_models[target_temp].rate_constant
        else:
            # 外推
            ref_temp = list(kinetic_models.keys())[0] if kinetic_models else 40.0
            ref_k = kinetic_models[ref_temp].rate_constant if ref_temp in kinetic_models else 0.01
            k_25 = self._extrapolate_rate_constant(ref_k, ref_temp, target_temp, activation_energy)
        
        # 计算达到目标值的时间
        if model == DegradationModel.ZERO_ORDER:
            t90 = (initial_value - target_value) / k_25
        elif model == DegradationModel.FIRST_ORDER:
            t90 = -math.log(target_value / initial_value) / k_25
        elif model == DegradationModel.SECOND_ORDER:
            t90 = (1/target_value - 1/initial_value) / k_25
        elif model == DegradationModel.SQUARE_ROOT:
            t90 = ((initial_value - target_value) / k_25) ** 2
        else:
            t90 = -math.log(target_value / initial_value) / k_25
        
        return {
            "t90_months": max(0, t90),
            "ci": [max(0, t90 * 0.8), t90 * 1.2],
            "confidence_level": confidence_level
        }
    
    def _predict_degradation_pathways(self, drug_name: str, item: str) -> List[Dict[str, Any]]:
        """预测降解途径"""
        # 基于药物名称和检测项目预测可能的降解途径
        pathways = []
        
        if "含量" in item:
            if "阿司匹林" in drug_name:
                pathways.append({
                    "pathway": "酯水解",
                    "products": ["水杨酸", "乙酸"],
                    "mechanism": "亲核取代",
                    "conditions": ["高湿度", "碱性pH"],
                    "prevention": ["控制湿度", "调节pH"]
                })
            elif "对乙酰氨基酚" in drug_name:
                pathways.append({
                    "pathway": "氧化反应",
                    "products": ["醌类化合物"],
                    "mechanism": "自由基氧化",
                    "conditions": ["氧气存在", "金属催化"],
                    "prevention": ["添加抗氧化剂", "惰性气体包装"]
                })
        
        elif "杂质" in item or "有关物质" in item:
            pathways.append({
                "pathway": "降解产物累积",
                "products": ["各种降解产物"],
                "mechanism": "多种机理",
                "conditions": ["储存时间延长"],
                "prevention": ["优化储存条件"]
            })
        
        return pathways
    
    def _check_humidity_effect(self, data_points: List[StabilityDataPoint]) -> bool:
        """检查湿度对降解的影响"""
        # 简单检查：如果有多个湿度条件的数据，分析其影响
        humidities = set(p.humidity for p in data_points)
        return len(humidities) > 1
    
    def _generate_recommendations(self, item: str, shelf_life: Dict[str, Any],
                                degradation_pathways: List[Dict[str, Any]], 
                                drug_name: str) -> List[str]:
        """生成稳定性建议"""
        recommendations = []
        
        # 基于货架期的建议
        t90 = shelf_life.get("t90_months", 24)
        if t90 < 12:
            recommendations.append(f"⚠️ {item}的货架期较短({t90:.1f}个月)，建议优化处方或改善储存条件")
        elif t90 < 24:
            recommendations.append(f"🟡 {item}的货架期适中({t90:.1f}个月)，建议进行长期稳定性研究验证")
        else:
            recommendations.append(f"✅ {item}的预测货架期良好({t90:.1f}个月)，符合一般药品要求")
        
        # 基于降解途径的建议
        for pathway in degradation_pathways:
            if pathway.get("prevention"):
                recommendations.append(f"🛡️ 针对{pathway['pathway']}，建议: {', '.join(pathway['prevention'])}")
        
        return recommendations
    
    def _overall_assessment(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成总体评估"""
        stable_items = 0
        unstable_items = 0
        total_items = len(results)
        
        for item, result in results.items():
            shelf_life = result.get("shelf_life", {})
            t90 = shelf_life.get("t90_months", 0)
            
            if t90 >= 24:
                stable_items += 1
            else:
                unstable_items += 1
        
        if unstable_items == 0:
            status = "stable"
            message = "所有检测项目均显示良好的稳定性"
        elif unstable_items < stable_items:
            status = "mostly_stable"
            message = "大部分检测项目稳定，但需要关注个别项目"
        else:
            status = "unstable"
            message = "多个检测项目显示稳定性问题，需要优化处方"
        
        return {
            "status": status,
            "message": message,
            "stable_items": stable_items,
            "unstable_items": unstable_items,
            "total_items": total_items
        }
    
    def _check_regulatory_compliance(self, data_points: List[StabilityDataPoint],
                                   results: Dict[str, Any], 
                                   prediction_months: int) -> Dict[str, Any]:
        """检查法规符合性"""
        compliance = {
            "ich_q1a": True,
            "ich_q1b": True,
            "ich_q1c": True,
            "issues": []
        }
        
        # 检查数据点数量
        if len(data_points) < 3:
            compliance["ich_q1a"] = False
            compliance["issues"].append("数据点少于3个，不符合ICH Q1A要求")
        
        # 检查温度条件
        temperatures = set(p.temperature for p in data_points)
        if len(temperatures) < 2:
            compliance["ich_q1b"] = False
            compliance["issues"].append("缺乏多温度条件数据")
        
        # 检查每个项目的稳定性
        for item, result in results.items():
            shelf_life = result.get("shelf_life", {})
            t90 = shelf_life.get("t90_months", 0)
            
            if t90 < 12:
                compliance["ich_q1a"] = False
                compliance["issues"].append(f"{item}: 预测货架期不足12个月")
        
        return compliance
    
    def _assess_data_quality(self, data_points: List[StabilityDataPoint]) -> Dict[str, Any]:
        """评估数据质量"""
        quality = {
            "score": 100,
            "issues": []
        }
        
        # 检查数据点数量
        if len(data_points) < 3:
            quality["score"] -= 30
            quality["issues"].append("数据点过少")
        elif len(data_points) < 6:
            quality["score"] -= 10
            quality["issues"].append("建议增加数据点")
        
        # 检查时间分布
        times = [p.time for p in data_points]
        if max(times) < 6:
            quality["score"] -= 20
            quality["issues"].append("缺乏长期数据")
        
        # 检查数据变异性
        values = [p.value for p in data_points]
        if len(values) > 1:
            cv = np.std(values) / np.mean(values) * 100
            if cv > 10:
                quality["score"] -= 15
                quality["issues"].append("数据变异性较大")
        
        return quality
    
    def _select_best_model(self, data_points: List[StabilityDataPoint], item: str) -> DegradationModel:
        """自动选择最佳降解动力学模型"""
        times = np.array([p.time for p in data_points])
        values = np.array([p.value for p in data_points])
        
        models_to_test = [
            DegradationModel.ZERO_ORDER,
            DegradationModel.FIRST_ORDER,
            DegradationModel.SECOND_ORDER,
            DegradationModel.SQUARE_ROOT
        ]
        
        best_model = DegradationModel.FIRST_ORDER
        best_r_squared = -1
        
        for model in models_to_test:
            try:
                params = self._fit_specific_model(times, values, model)
                if params.r_squared > best_r_squared:
                    best_r_squared = params.r_squared
                    best_model = model
            except:
                continue
        
        return best_model
    
    def _fit_degradation_model(self, data_points: List[StabilityDataPoint], 
                              model: DegradationModel) -> Dict[str, Any]:
        """拟合降解动力学模型"""
        # 按温度分组数据
        temp_groups = self._group_data_by_temperature(data_points)
        
        # 对每个温度拟合动力学模型
        kinetic_models = {}
        for temp, points in temp_groups.items():
            sorted_points = sorted(points, key=lambda p: p.time)
            times = np.array([p.time for p in sorted_points])
            values = np.array([p.value for p in sorted_points])
            
            params = self._fit_specific_model(times, values, model)
            kinetic_models[temp] = params
        
        # 计算活化能
        activation_energy = self._calculate_activation_energy(kinetic_models)
        
        return {
            "model": model.value,
            "kinetic_models": kinetic_models,
            "activation_energy": activation_energy
        }
    
    def _predict_for_condition(self, degradation_params: Dict[str, Any], 
                              model: DegradationModel, condition: ICHCondition, 
                              initial_value: float, prediction_months: int, 
                              confidence_level: float) -> Dict[str, Any]:
        """预测特定条件下的稳定性"""
        target_temp = self.ich_conditions[condition]["temp"]
        target_humidity = self.ich_conditions[condition]["humidity"]
        
        # 获取或外推该温度下的速率常数
        kinetic_models = degradation_params.get("kinetic_models", {})
        activation_energy = degradation_params.get("activation_energy", 85.0)
        
        # 寻找最接近的温度数据
        ref_temp = min(kinetic_models.keys(), key=lambda x: abs(x - target_temp)) if kinetic_models else 25.0
        ref_model = kinetic_models.get(ref_temp, None)
        
        if ref_model:
            # 外推速率常数
            k_target = self._extrapolate_rate_constant(
                ref_model.rate_constant, ref_temp, target_temp, activation_energy
            )
        else:
            # 使用默认值
            k_target = 0.01
        
        # 预测降解曲线
        time_points = np.linspace(0, prediction_months, prediction_months + 1)
        predicted_values = self._predict_degradation_curve(
            time_points, k_target, model, initial_value
        )
        
        # 计算t90
        t90 = self._calculate_t90(k_target, model)
        
        # 简单的置信区间（±2%）
        ci_width = 2.0
        
        return {
            "temperature": target_temp,
            "humidity": target_humidity,
            "rate_constant": k_target,
            "t90": t90,
            "ci": [t90 * 0.8, t90 * 1.2],  # 简化的置信区间
            "predicted_values": [
                {
                    "time": t,
                    "value": v,
                    "lower_ci": max(0, v - ci_width),
                    "upper_ci": min(100, v + ci_width)
                }
                for t, v in zip(time_points, predicted_values)
            ]
        }
    
    def _analyze_degradation_pathways(self, data_points: List[StabilityDataPoint], 
                                     degradation_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析降解途径"""
        pathways = []
        
        # 根据降解速率和条件推断可能的降解途径
        activation_energy = degradation_params.get("activation_energy", 85.0)
        
        if activation_energy < 50:
            pathways.append({
                "pathway": "扩散控制降解",
                "mechanism": "物理降解",
                "indicators": ["低活化能"],
                "prevention": ["改善包装密封性"]
            })
        elif activation_energy > 100:
            pathways.append({
                "pathway": "化学降解",
                "mechanism": "化学反应",
                "indicators": ["高活化能"],
                "prevention": ["控制温度", "调节pH"]
            })
        
        # 检查湿度影响
        humidity_effect = self._check_humidity_effect(data_points)
        if humidity_effect:
            pathways.append({
                "pathway": "水解反应",
                "mechanism": "水分诱导降解",
                "indicators": ["湿度敏感"],
                "prevention": ["使用惰性气体包装"]
            })
        
        return pathways 