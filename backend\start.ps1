# PowerShell启动脚本
# 用于Windows环境下快速启动后端服务

# 进入脚本所在目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

Write-Host "正在启动药物稳定性助手后端服务..." -ForegroundColor Green

# 激活虚拟环境（如果存在）
$venvPath = Join-Path -Path (Split-Path -Parent $scriptPath) -ChildPath ".venv"
$activateScript = Join-Path -Path $venvPath -ChildPath "Scripts\Activate.ps1"

if (Test-Path $activateScript) {
    Write-Host "正在激活虚拟环境..." -ForegroundColor Yellow
    & $activateScript
} else {
    Write-Host "未找到虚拟环境，使用系统Python环境" -ForegroundColor Yellow
}

# 执行Python启动脚本
Write-Host "启动后端服务..." -ForegroundColor Green
python start.py

# 如果服务启动失败，显示错误并等待
if ($LASTEXITCODE -ne 0) {
    Write-Host "服务启动失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
    Write-Host "按任意键退出..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} 