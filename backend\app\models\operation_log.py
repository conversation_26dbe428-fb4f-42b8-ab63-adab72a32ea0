from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class OperationLogORM(Base):
    """操作日志模型"""
    __tablename__ = "operation_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    action = Column(String, nullable=False)
    detail = Column(Text, nullable=True)
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    time = Column(DateTime, default=datetime.now)
    
    # 关系
    user = relationship("UserORM", back_populates="operation_logs")
    
    def __repr__(self):
        return f"<OperationLog(id={self.id}, action='{self.action}', time={self.time})>" 