import React, { useState } from 'react';
import { Card, Tabs, Button, Upload, Input, message, Modal, List } from 'antd';
import { UploadOutlined, FilePdfOutlined, FileExcelOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { analyzeExcipient, exportExcipientAnalysis } from '../api';

const ExcipientAnalysisPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [tab, setTab] = useState('excipient');
  const [smiles, setSmiles] = useState('');
  const [structureUrl, setStructureUrl] = useState('');
  const [uploading, setUploading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);
  const [evidence, setEvidence] = useState<any[]>([]);
  const [exportModal, setExportModal] = useState(false);
  const [exportFormat, setExportFormat] = useState('pdf');

  // 结构图识别
  const handleIdentify = async () => {
    setUploading(true);
    const res = await fetch('/api/identify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ smiles })
    });
    const data = await res.json();
    setStructureUrl(data.structure_image_url);
    setUploading(false);
  };

  // 分析
  const handleAnalyze = async () => {
    const { data } = await analyzeExcipient({ smiles });
    const result = data as { result: any; suggestions: any[]; evidence: any[] };
    setAnalysisResult(result.result);
    setAiSuggestions(result.suggestions || []);
    setEvidence(result.evidence || []);
  };

  // 导出
  const handleExport = async () => {
    setExportModal(false);
    const { data } = await exportExcipientAnalysis({ smiles, format: exportFormat, lang: i18n.language });
    const blob = new Blob([data as BlobPart], { type: exportFormat === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = exportFormat === 'excel' ? 'excipient_analysis.xlsx' : 'excipient_analysis.pdf';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Card>
        <Tabs activeKey={tab} onChange={setTab} items={[
          { key: 'excipient', label: t('原辅料'), children: (
            <div style={{ maxWidth: 600, margin: '0 auto' }}>
              <Input value={smiles} onChange={e => setSmiles(e.target.value)} placeholder={t('输入SMILES或分子式')} style={{ marginBottom: 12 }} />
              <Button onClick={handleIdentify} loading={uploading} style={{ marginRight: 8 }}>{t('结构图识别')}</Button>
              <Upload showUploadList={false} beforeUpload={file => {
                // 可扩展图片识别
                message.info(t('图片识别开发中'));
                return false;
              }}>
                <Button icon={<UploadOutlined />}>{t('上传图片')}</Button>
              </Upload>
              {structureUrl && <div style={{ marginTop: 16 }}><img src={structureUrl} alt="structure" style={{ maxWidth: 300 }} /></div>}
              <Button type="primary" onClick={handleAnalyze} style={{ marginTop: 16 }}>{t('分析相容性')}</Button>
            </div>
          ) },
          { key: 'result', label: t('分析结果'), children: (
            <div style={{ maxWidth: 700, margin: '0 auto' }}>
              {analysisResult ? (
                <div>
                  <h3>{t('风险等级')}: <span style={{ color: analysisResult.risk === '高' ? 'red' : analysisResult.risk === '中' ? '#faad14' : '#388e3c' }}>{t(analysisResult.risk)}</span></h3>
                  <div style={{ margin: '12px 0' }}>{t('风险解释')}: {analysisResult.explanation}</div>
                  <List
                    header={t('AI建议')}
                    dataSource={aiSuggestions}
                    renderItem={item => <List.Item><Card>{item.title}：{item.desc}</Card></List.Item>}
                  />
                  <List
                    header={t('证据')}
                    dataSource={evidence}
                    renderItem={item => <List.Item><Card>{item.type}：{item.desc}</Card></List.Item>}
                  />
                  <Button icon={<FilePdfOutlined />} onClick={() => { setExportFormat('pdf'); setExportModal(true); }} style={{ marginRight: 8 }}>{t('导出PDF')}</Button>
                  <Button icon={<FileExcelOutlined />} onClick={() => { setExportFormat('excel'); setExportModal(true); }}>{t('导出Excel')}</Button>
                </div>
              ) : <div style={{ color: '#888' }}>{t('请先进行结构识别与分析')}</div>}
            </div>
          ) },
        ]} />
      </Card>
      <Modal open={exportModal} onCancel={() => setExportModal(false)} onOk={handleExport} okText={t('导出')} cancelText={t('取消')}>
        {t('确定导出分析结果为')}{exportFormat.toUpperCase()}?
      </Modal>
    </div>
  );
};

export default ExcipientAnalysisPage; 