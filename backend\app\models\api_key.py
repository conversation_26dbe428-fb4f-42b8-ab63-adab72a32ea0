from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class APIKeyORM(Base):
    """API密钥模型"""
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    key = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=True)
    enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    last_used = Column(DateTime, nullable=True)
    
    # 关系
    user = relationship("UserORM", back_populates="api_keys")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, key='{self.key[:8]}...', enabled={self.enabled})>" 