import React, { useEffect, useState } from 'react';
import { Card, Table, Button, Modal, Checkbox, message } from 'antd';
import { listRoles, listPermissions, getRolePermissions, setRolePermissions, Role, Permission } from '../api';
import { useTranslation } from 'react-i18next';

const RolePermissionPage: React.FC = () => {
  const { t } = useTranslation();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [rolePerms, setRolePerms] = useState<number[]>([]);
  const [editModal, setEditModal] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    listRoles().then(res => setRoles(res.data));
    listPermissions().then(res => setPermissions(res.data));
  }, []);

  const handleEdit = async (role: Role) => {
    setSelectedRole(role);
    setEditModal(true);
    const res = await getRolePermissions(role.id);
    setRolePerms(res.data.map((p: Permission) => p.id));
  };

  const handleSave = async () => {
    if (!selectedRole) return;
    setLoading(true);
    await setRolePermissions(selectedRole.id, rolePerms);
    setLoading(false);
    setEditModal(false);
    message.success(t('权限已更新'));
  };

  return (
    <Card title={t('角色与权限管理')} style={{ margin: 24 }}>
      <Table dataSource={roles} rowKey="id" pagination={false} bordered
        columns={[{ title: t('角色名'), dataIndex: 'name' }, { title: t('描述'), dataIndex: 'description' }, {
          title: t('操作'), render: (_, role) => <Button onClick={() => handleEdit(role)}>{t('编辑权限')}</Button>
        }]} />
      <Modal open={editModal} onCancel={() => setEditModal(false)} onOk={handleSave} okText={t('保存')} confirmLoading={loading} title={t('编辑权限')}>
        <Checkbox.Group style={{ width: '100%' }} value={rolePerms} onChange={v => setRolePerms(v as number[])}>
          {permissions.map(p => <Checkbox key={p.id} value={p.id}>{p.name}</Checkbox>)}
        </Checkbox.Group>
      </Modal>
    </Card>
  );
};

export default RolePermissionPage; 