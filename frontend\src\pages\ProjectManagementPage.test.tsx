import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectManagementPage from './ProjectManagementPage';

describe('ProjectManagementPage', () => {
  it('批量导入历史与错误修正入口渲染', async () => {
    render(<ProjectManagementPage />);
    expect(await screen.findByText('批量导入历史')).toBeInTheDocument();
    expect(await screen.findByText('导入错误记录')).toBeInTheDocument();
    expect(screen.getAllByText('修正').length).toBeGreaterThan(0);
  });

  it('新建、编辑、删除项目交互', async () => {
    render(<ProjectManagementPage />);
    // 新建
    fireEvent.click(screen.getByText('新建项目'));
    fireEvent.change(screen.getByPlaceholderText('项目名称'), { target: { value: '测试项目' } });
    fireEvent.click(screen.getByText('创建'));
    await waitFor(() => expect(screen.queryByText('新建项目')).not.toBeInTheDocument());
    // 编辑
    fireEvent.click(screen.getAllByText('编辑')[0]);
    fireEvent.change(screen.getByPlaceholderText('项目名称'), { target: { value: '已编辑项目' } });
    fireEvent.click(screen.getByText('保存'));
    await waitFor(() => expect(screen.queryByText('编辑项目')).not.toBeInTheDocument());
    // 删除
    fireEvent.click(screen.getAllByText('删除')[0]);
    fireEvent.click(screen.getByText('删除', { selector: 'button' }));
    await waitFor(() => expect(screen.queryByText('确认删除')).not.toBeInTheDocument());
  });

  it('国际化切换下主要功能可用', async () => {
    render(<ProjectManagementPage />);
    // 假设有语言切换按钮
    // fireEvent.click(screen.getByLabelText('切换到英文'));
    // expect(await screen.findByText('Batch Import History')).toBeInTheDocument();
  });
}); 