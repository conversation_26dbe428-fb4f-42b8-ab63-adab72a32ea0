/**
 * 导出报告相关工具函数
 * @module exportReport
 */
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, HeadingLevel, ImageRun } from 'docx';

/**
 * 导出报告的选项类型
 */
export interface ReportExportOptions {
  projectName: string;
  exportOptions: string[];
  chartImage?: string; // base64
  params?: Record<string, any>;
  aiSuggestions?: { title: string; desc: string; risk: string }[];
  username?: string;
  themeColor?: string;
  lang?: 'zh' | 'en';
  logoBase64?: string;
  coverTitle?: string;
  coverSubtitle?: string;
  coverRemark?: string;
}

const i18nText = {
  zh: {
    report: '稳定性研究报告',
    chart: '稳定性趋势图',
    params: '参数信息',
    ai: 'AI智能建议',
    raw: '原始数据',
    conclusion: '分析结论',
    exportedBy: '导出人',
    exportDate: '导出时间',
  },
  en: {
    report: 'Stability Study Report',
    chart: 'Stability Trend Chart',
    params: 'Parameters',
    ai: 'AI Suggestions',
    raw: 'Raw Data',
    conclusion: 'Conclusion',
    exportedBy: 'Exported by',
    exportDate: 'Export Date',
  }
};

/**
 * 导出稳定性研究报告为PDF
 * @param options ReportExportOptions
 */
export function exportReportPDF(options: ReportExportOptions): void {
  const { projectName, exportOptions, chartImage, params, aiSuggestions = [], username, themeColor = '#1976d2', lang = 'zh', logoBase64, coverTitle, coverSubtitle, coverRemark } = options;
  const t = i18nText[lang];
  const doc = new jsPDF();
  // 封面
  if (logoBase64) {
    doc.addImage(logoBase64, 'PNG', 14, 16, 24, 24);
  }
  doc.setTextColor(themeColor);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(22);
  doc.text(coverTitle || `${projectName} ${t.report}`, 42, 32);
  if (coverSubtitle) {
    doc.setFontSize(14);
    doc.text(coverSubtitle, 42, 42);
  }
  doc.setFontSize(12);
  doc.setTextColor('#222');
  doc.setFont('helvetica', 'normal');
  doc.text(`${t.exportedBy}：${username || ''}`, 14, 50);
  doc.text(`${t.exportDate}：${new Date().toLocaleString(lang === 'zh' ? 'zh-CN' : 'en-US')}`, 14, 60);
  if (coverRemark) {
    doc.setFontSize(11);
    doc.setTextColor('#888');
    doc.text(coverRemark, 14, 68);
  }
  doc.setDrawColor(themeColor);
  doc.setLineWidth(1.2);
  doc.line(14, 75, 196, 75);
  // 目录页
  doc.addPage();
  doc.setFontSize(16);
  doc.setTextColor(themeColor);
  doc.text(lang === 'zh' ? '目录' : 'Contents', 14, 20);
  doc.setFontSize(12);
  doc.setTextColor('#222');
  let tocY = 32;
  const tocItems: { title: string; page: number }[] = [];
  let pageNum = 3;
  const allSections = [
    { key: 'raw', label: t.raw },
    { key: 'conclusion', label: t.conclusion },
    { key: 'chart', label: t.chart },
    { key: 'params', label: t.params },
    { key: 'ai', label: t.ai },
  ];
  allSections.forEach(sec => {
    if (exportOptions.includes(sec.key) || exportOptions.includes(sec.label) || exportOptions.includes(sec.label === t.raw ? '原始数据' : sec.label === t.conclusion ? '分析结论' : sec.label)) {
      tocItems.push({ title: sec.label, page: pageNum });
      doc.text(`${sec.label} ............................................. ${pageNum}`, 18, tocY);
      tocY += 10;
      pageNum++;
    }
  });
  // 内容页
  doc.addPage();
  doc.setFontSize(18);
  doc.setTextColor(themeColor);
  doc.text(coverTitle || `${projectName} ${t.report}`, 14, 20);
  doc.setFontSize(12);
  doc.setTextColor('#222');
  let y = 32;
  // 内容区美化：各部分标题加粗、间距美观
  if (exportOptions.includes('raw') || exportOptions.includes(t.raw) || exportOptions.includes('原始数据')) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${t.raw}：`, 14, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    (doc as any).autoTable({
      head: [[t.raw, lang === 'zh' ? '值' : 'Value']],
      body: [
        ['时间', '0月, 1月, 3月, 6月, 12月'],
        ['含量(%)', '100, 98, 95, 92, 88'],
      ],
      startY: y,
      styles: { font: 'helvetica', fontSize: 11, cellPadding: 2 },
      headStyles: { fillColor: [25, 118, 210], textColor: 255, fontStyle: 'bold' },
      alternateRowStyles: { fillColor: [245, 245, 245] },
      margin: { left: 14, right: 14 },
    });
    y = (doc as any).lastAutoTable.finalY + 12;
  }
  if (exportOptions.includes('conclusion') || exportOptions.includes(t.conclusion) || exportOptions.includes('分析结论')) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${t.conclusion}：`, 14, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text(lang === 'zh' ? '本药物在12个月内含量下降至88%，符合稳定性要求。' : 'The drug content decreased to 88% within 12 months, meeting stability requirements.', 16, y);
    y += 16;
  }
  if (exportOptions.includes('chart') || exportOptions.includes(t.chart) || exportOptions.includes('图表')) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${t.chart}：`, 14, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    if (chartImage) {
      doc.addImage(chartImage, 'PNG', 14, y, 180, 60);
      y += 70;
    }
  }
  if (exportOptions.includes('params') || exportOptions.includes(t.params) || exportOptions.includes('参数')) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${t.params}：`, 14, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    if (params) {
      const rows = Object.entries(params).map(([k, v]) => [k, String(v)]);
      (doc as any).autoTable({ head: [[t.params, t.params === '参数信息' ? '值' : 'Value']], body: rows, startY: y, styles: { font: 'helvetica', fontSize: 11, cellPadding: 2 }, headStyles: { fillColor: [25, 118, 210], textColor: 255, fontStyle: 'bold' }, alternateRowStyles: { fillColor: [245, 245, 245] }, margin: { left: 14, right: 14 } });
      y = (doc as any).lastAutoTable.finalY + 12;
    }
  }
  if (exportOptions.includes('ai') || exportOptions.includes(t.ai) || exportOptions.includes('AI建议')) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${t.ai}：`, 14, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    if (aiSuggestions && aiSuggestions.length) {
      aiSuggestions.forEach((sug, i) => {
        doc.setFont('helvetica', 'bold');
        doc.text(`${i + 1}. ${sug.title} [${sug.risk}]`, 16, y);
        doc.setFont('helvetica', 'normal');
        y += 6;
        doc.text(sug.desc, 18, y);
        y += 12;
      });
    }
  }
  // 页脚
  const pageCount = (doc as any).getNumberOfPages ? (doc as any).getNumberOfPages() : (doc.internal as any).getNumberOfPages?.() || 1;
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.setTextColor('#888');
    doc.text(`${projectName}  |  ${username || ''}  |  Page ${i}/${pageCount}  |  药物制剂稳定性研究助手`, 14, 290);
  }
  doc.save(`${projectName}_${t.report}_${new Date().toLocaleDateString()}.pdf`);
}

/**
 * 导出稳定性研究报告为Word
 * @param options ReportExportOptions
 */
export async function exportReportWord(options: ReportExportOptions): Promise<void> {
  const { projectName, exportOptions, chartImage, params, aiSuggestions = [], username, themeColor = '#1976d2', lang = 'zh', logoBase64, coverTitle, coverSubtitle, coverRemark } = options;
  const t = i18nText[lang];
  const children: (Paragraph | Table)[] = [];
  // 封面
  const coverChildren: (Paragraph | Table)[] = [];
  if (logoBase64) {
    coverChildren.push(new Paragraph({ children: [
      new ImageRun({
        data: Buffer.from(logoBase64.replace(/^data:image\/png;base64,/, ''), 'base64'),
        transformation: { width: 48, height: 48 },
        type: 'png',
      })
    ] }));
  }
  coverChildren.push(new Paragraph({ children: [new TextRun({ text: coverTitle || `${projectName} ${t.report}`, color: themeColor.replace('#', ''), bold: true, size: 48 })], spacing: { after: 320 }, heading: HeadingLevel.HEADING_1 }));
  if (coverSubtitle) {
    coverChildren.push(new Paragraph({ children: [new TextRun({ text: coverSubtitle, size: 32, color: themeColor.replace('#', '') })], spacing: { after: 120 } }));
  }
  coverChildren.push(new Paragraph({ text: `${t.exportedBy}：${username || ''}`, spacing: { after: 60 } }));
  coverChildren.push(new Paragraph({ text: `${t.exportDate}：${new Date().toLocaleString(lang === 'zh' ? 'zh-CN' : 'en-US')}`, spacing: { after: 60 } }));
  if (coverRemark) {
    coverChildren.push(new Paragraph({ children: [new TextRun({ text: coverRemark, color: '888888', italics: true })], spacing: { after: 120 } }));
  }
  children.push(...coverChildren);
  // 目录页
  const tocItems: { title: string; }[] = [];
  const allSections = [
    { key: 'raw', label: t.raw },
    { key: 'conclusion', label: t.conclusion },
    { key: 'chart', label: t.chart },
    { key: 'params', label: t.params },
    { key: 'ai', label: t.ai },
  ];
  allSections.forEach(sec => {
    if (exportOptions.includes(sec.key) || exportOptions.includes(sec.label) || exportOptions.includes(sec.label === t.raw ? '原始数据' : sec.label === t.conclusion ? '分析结论' : sec.label)) {
      tocItems.push({ title: sec.label });
    }
  });
  if (tocItems.length) {
    children.push(new Paragraph({ text: lang === 'zh' ? '目录' : 'Contents', heading: HeadingLevel.HEADING_2, spacing: { after: 120 } }));
    tocItems.forEach((item, idx) => {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${idx + 1}. ${item.title}`, style: 'Hyperlink', color: themeColor.replace('#', '') })
        ],
        spacing: { after: 60 }
      }));
    });
  }
  // 内容
  children.push(new Paragraph({ children: [new TextRun({ text: coverTitle || `${projectName} ${t.report}`, color: themeColor.replace('#', ''), bold: true, size: 32 })], heading: HeadingLevel.HEADING_2, spacing: { after: 200 } }));
  if (exportOptions.includes('raw') || exportOptions.includes(t.raw) || exportOptions.includes('原始数据')) {
    children.push(new Paragraph({ children: [new TextRun({ text: `${t.raw}：`, bold: true, size: 28 })], spacing: { after: 80 } }));
    children.push(new Table({
      rows: [
        new TableRow({ children: [new TableCell({ children: [new Paragraph('时间')] }), new TableCell({ children: [new Paragraph('0月, 1月, 3月, 6月, 12月')] })] }),
        new TableRow({ children: [new TableCell({ children: [new Paragraph('含量(%)')] }), new TableCell({ children: [new Paragraph('100, 98, 95, 92, 88')] })] }),
      ]
    }));
    children.push(new Paragraph({ children: [], spacing: { after: 120 } }));
  }
  if (exportOptions.includes('conclusion') || exportOptions.includes(t.conclusion) || exportOptions.includes('分析结论')) {
    children.push(new Paragraph({ children: [new TextRun({ text: `${t.conclusion}：`, bold: true, size: 28 })], spacing: { after: 80 } }));
    children.push(new Paragraph({ children: [new TextRun({ text: lang === 'zh' ? '本药物在12个月内含量下降至88%，符合稳定性要求。' : 'The drug content decreased to 88% within 12 months, meeting stability requirements.', size: 24 })] }));
    children.push(new Paragraph({ children: [], spacing: { after: 120 } }));
  }
  if (exportOptions.includes('chart') || exportOptions.includes(t.chart) || exportOptions.includes('图表')) {
    children.push(new Paragraph({ children: [new TextRun({ text: `${t.chart}：`, bold: true, size: 28 })], spacing: { after: 80 } }));
    if (chartImage) {
      const imageData = chartImage.replace(/^data:image\/png;base64,/, '');
      children.push(new Paragraph({ children: [
        new ImageRun({
          data: Buffer.from(imageData, 'base64'),
          transformation: { width: 480, height: 180 },
          type: 'png',
        })
      ] }));
    }
    children.push(new Paragraph({ children: [], spacing: { after: 120 } }));
  }
  if (exportOptions.includes('params') || exportOptions.includes(t.params) || exportOptions.includes('参数')) {
    children.push(new Paragraph({ children: [new TextRun({ text: `${t.params}：`, bold: true, size: 28 })], spacing: { after: 80 } }));
    if (params) {
      const rows = [
        new TableRow({ children: [new TableCell({ children: [new Paragraph(t.params)] }), new TableCell({ children: [new Paragraph(lang === 'zh' ? '值' : 'Value')] })] }),
        ...Object.entries(params).map(([k, v]) => new TableRow({ children: [new TableCell({ children: [new Paragraph(String(k))] }), new TableCell({ children: [new Paragraph(String(v))] })] }))
      ];
      children.push(new Table({ rows }));
    }
    children.push(new Paragraph({ children: [], spacing: { after: 120 } }));
  }
  if (exportOptions.includes('ai') || exportOptions.includes(t.ai) || exportOptions.includes('AI建议')) {
    children.push(new Paragraph({ children: [new TextRun({ text: `${t.ai}：`, bold: true, size: 28 })], spacing: { after: 80 } }));
    if (aiSuggestions && aiSuggestions.length) {
      aiSuggestions.forEach((sug, i) => {
        children.push(new Paragraph({ children: [new TextRun({ text: `${i + 1}. ${sug.title} [${sug.risk}]`, color: themeColor.replace('#', ''), bold: true, size: 24 })], heading: HeadingLevel.HEADING_3 }));
        children.push(new Paragraph({ children: [new TextRun({ text: sug.desc, size: 22 })] }));
        children.push(new Paragraph({ children: [], spacing: { after: 60 } }));
      });
    }
  }
  // 页脚（docx库暂不支持自动页脚，需手动添加）
  // 可在每页内容后添加页脚段落
  const doc = new Document({ sections: [{ children }] });
  const blob = await Packer.toBlob(doc);
  const filename = `${projectName}_${t.report}_${new Date().toLocaleDateString()}.docx`;
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
} 