# 项目数据管理功能文档

## 概述

本文档描述了药物稳定性研究助手系统中的项目数据管理功能，包括项目数据的保存、获取和管理。

## 功能特性

### 1. 项目数据保存
- **端点**: `POST /api/projects/{project_id}/save-data`
- **功能**: 保存项目的基本数据，包括药物信息、配方信息、包装储存条件等
- **支持的数据类型**:
  - 药物基本信息（名称、CAS号、分子式、SMILES等）
  - 配方信息（辅料列表及其功能）
  - 包装储存条件
  - 其他自定义数据

### 2. 项目数据获取
- **端点**: `GET /api/projects/{project_id}/data`
- **功能**: 获取指定项目的所有保存数据
- **返回信息**:
  - 项目基本信息（ID、名称）
  - 完整的项目数据

### 3. 数据结构
项目数据以JSON格式存储在数据库的`data`字段中，支持灵活的数据结构。

## API 接口详情

### 保存项目数据

```http
POST /api/projects/{project_id}/save-data
Content-Type: application/json

{
  "drug_name": "阿司匹林",
  "cas_number": "50-78-2",
  "molecular_formula": "C9H8O4",
  "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
  "category": "解热镇痛药",
  "description": "阿司匹林是一种常用的解热镇痛药",
  "formulation": [
    {
      "name": "微晶纤维素",
      "amount": "100mg",
      "function": "填充剂"
    },
    {
      "name": "硬脂酸镁",
      "amount": "2mg",
      "function": "润滑剂"
    }
  ],
  "packaging_storage": {
    "packaging": "铝塑泡罩包装",
    "storage": "密闭，在干燥处保存"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "项目数据保存成功",
  "project_id": 1,
  "saved_data": {
    "drug_name": "阿司匹林",
    "cas_number": "50-78-2",
    ...
  }
}
```

### 获取项目数据

```http
GET /api/projects/{project_id}/data
```

**响应示例**:
```json
{
  "success": true,
  "project_id": 1,
  "project_name": "API测试项目",
  "data": {
    "drug_name": "阿司匹林",
    "cas_number": "50-78-2",
    "molecular_formula": "C9H8O4",
    "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
    "formulation": [...],
    "packaging_storage": {...}
  }
}
```

## 数据库结构

### Projects表
- `id`: 项目唯一标识符
- `name`: 项目名称
- `description`: 项目描述
- `status`: 项目状态
- `data`: JSON格式的项目数据（新增字段）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 技术实现

### 后端组件
1. **API路由**: `app/api/project.py`
   - 项目数据保存路由
   - 项目数据获取路由

2. **数据模型**: `app/schemas.py`
   - `ProjectDataSaveRequest`: 数据保存请求模型
   - `ProjectUpdateRequest`: 项目更新请求模型（包含data字段）
   - `ProjectResponse`: 项目响应模型

3. **服务层**: `app/services/project_data_service.py`
   - 项目数据的业务逻辑处理

4. **数据库模型**: `app/models/project.py`
   - `ProjectORM`: 项目数据库模型

### 关键特性
- **灵活的数据结构**: 支持任意JSON数据格式
- **数据验证**: 使用Pydantic进行数据验证
- **错误处理**: 完善的错误处理和响应
- **类型安全**: 使用TypeScript类型定义

## 使用示例

### Python客户端示例
```python
import requests

# 保存项目数据
data = {
    "drug_name": "阿司匹林",
    "cas_number": "50-78-2",
    "molecular_formula": "C9H8O4"
}

response = requests.post(
    "http://localhost:8001/api/projects/1/save-data",
    json=data
)

# 获取项目数据
response = requests.get(
    "http://localhost:8001/api/projects/1/data"
)
project_data = response.json()
```

## 测试

项目包含完整的API测试脚本：`test_project_api.py`

运行测试：
```bash
cd backend
python test_project_api.py
```

测试覆盖：
- 项目创建
- 数据保存
- 数据获取
- 数据更新
- 数据验证

## 注意事项

1. **数据持久化**: 所有数据都保存在SQLite数据库中
2. **数据格式**: 数据以JSON格式存储，支持嵌套结构
3. **错误处理**: API提供详细的错误信息和状态码
4. **性能**: 适合中小型项目，大型项目可考虑优化数据库结构

## 未来改进

1. **数据版本控制**: 支持数据变更历史
2. **数据导入导出**: 支持批量数据操作
3. **数据验证增强**: 更严格的数据格式验证
4. **缓存优化**: 添加数据缓存机制
5. **权限控制**: 添加用户权限管理
