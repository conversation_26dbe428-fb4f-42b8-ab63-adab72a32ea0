#!/usr/bin/env python3
"""
用户工作流程测试
模拟用户从项目管理到数据输入的完整操作流程
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api"

def test_user_workflow():
    """测试完整的用户工作流程"""
    print("🚀 开始用户工作流程测试")
    print("=" * 70)
    
    try:
        # 场景1: 用户创建新项目
        print("\n📝 场景1: 用户在项目管理页面创建新项目")
        project_data = {
            "name": f"用户测试项目_{int(time.time())}",
            "description": "用户创建的测试项目",
            "status": "进行中"
        }
        
        response = requests.post(f"{BASE_URL}/projects", json=project_data, timeout=15)
        if response.status_code != 200:
            print(f"❌ 项目创建失败: {response.status_code}")
            return False
        
        project = response.json()
        project_id = project["id"]
        print(f"✅ 项目创建成功: {project['name']} (ID: {project_id})")
        
        # 场景2: 验证项目在列表中显示
        print("\n📋 场景2: 验证项目在项目管理列表中正确显示")
        response = requests.get(f"{BASE_URL}/projects", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取项目列表失败: {response.status_code}")
            return False
        
        projects = response.json()
        project_found = any(p["id"] == project_id for p in projects)
        if not project_found:
            print(f"❌ 新创建的项目未在列表中找到")
            return False
        
        print(f"✅ 项目在列表中正确显示")
        
        # 场景3: 检查项目初始数据状态（应该为空）
        print("\n🔍 场景3: 检查新项目的初始数据状态")
        response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                data = result.get("data", {})
                has_initial_data = bool(data.get("drug_name") or data.get("cas_number"))
                if has_initial_data:
                    print(f"⚠️ 新项目意外包含数据: {data}")
                else:
                    print(f"✅ 新项目数据状态正确（空数据）")
            else:
                print(f"✅ 新项目无数据（符合预期）")
        else:
            print(f"✅ 新项目无数据（符合预期）")
        
        # 场景4: 用户选择项目并进入数据输入页面（模拟前端行为）
        print("\n📥 场景4: 模拟用户选择项目并加载项目数据")
        # 这里模拟前端的 loadProjectData 调用
        response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
        print(f"✅ 项目数据加载请求完成（状态码: {response.status_code}）")
        
        # 场景5: 用户在数据输入页面填写并保存数据
        print("\n💾 场景5: 用户在数据输入页面保存项目数据")
        user_input_data = {
            "drug_name": "用户输入的阿司匹林",
            "cas_number": "50-78-2",
            "molecular_formula": "C9H8O4",
            "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
            "category": "解热镇痛药",
            "description": "用户通过界面输入的药物信息",
            "formulation": [
                {"name": "微晶纤维素", "amount": "100mg"},
                {"name": "硬脂酸镁", "amount": "2mg"}
            ],
            "packaging_storage": {
                "packaging": "铝塑泡罩包装",
                "storage": "密闭，在干燥处保存"
            },
            "production_process": "用户输入的生产工艺",
            "notes": "用户添加的备注信息"
        }
        
        response = requests.post(f"{BASE_URL}/projects/{project_id}/save-data", 
                               json=user_input_data, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ 数据保存失败: {response.status_code}")
            return False
        
        save_result = response.json()
        if not save_result.get("success"):
            print(f"❌ 数据保存失败: {save_result}")
            return False
        
        print(f"✅ 数据保存成功")
        
        # 场景6: 用户返回项目管理页面，验证项目数据状态更新
        print("\n🔄 场景6: 验证项目数据状态在项目管理页面正确显示")
        response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 数据获取失败: {response.status_code}")
            return False
        
        get_result = response.json()
        if not get_result.get("success"):
            print(f"❌ 数据获取失败: {get_result}")
            return False
        
        retrieved_data = get_result.get("data", {})
        has_data = bool(retrieved_data.get("drug_name") or retrieved_data.get("cas_number"))
        
        if not has_data:
            print(f"❌ 保存的数据未能正确获取")
            return False
        
        print(f"✅ 项目数据状态正确更新（有数据）")
        print(f"   药物名称: {retrieved_data.get('drug_name')}")
        print(f"   CAS号: {retrieved_data.get('cas_number')}")
        
        # 场景7: 用户再次选择项目，验证数据正确加载
        print("\n🔄 场景7: 用户再次选择项目，验证数据持久化")
        response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 数据重新加载失败: {response.status_code}")
            return False
        
        reload_result = response.json()
        if not reload_result.get("success"):
            print(f"❌ 数据重新加载失败: {reload_result}")
            return False
        
        reloaded_data = reload_result.get("data", {})
        
        # 验证关键数据字段
        data_integrity_check = (
            reloaded_data.get("drug_name") == user_input_data["drug_name"] and
            reloaded_data.get("cas_number") == user_input_data["cas_number"] and
            reloaded_data.get("molecular_formula") == user_input_data["molecular_formula"]
        )
        
        if not data_integrity_check:
            print(f"❌ 数据完整性验证失败")
            print(f"   原始: {user_input_data['drug_name']}")
            print(f"   重载: {reloaded_data.get('drug_name')}")
            return False
        
        print(f"✅ 数据持久化验证通过")
        print(f"   所有关键字段正确保存和加载")
        
        # 最终验证
        print("\n" + "=" * 70)
        print("🎉 用户工作流程测试完全通过！")
        print("✅ 项目创建 -> 数据输入 -> 数据保存 -> 数据加载 -> 数据持久化")
        print("✅ 跨页面数据传递和状态同步正常工作")
        return True
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_user_workflow()
    
    print("\n" + "=" * 70)
    if success:
        print("🎯 结论: 用户工作流程完全正常")
        print("   ✅ 用户可以正常创建项目")
        print("   ✅ 用户可以正常输入和保存数据")
        print("   ✅ 数据在不同页面间正确传递")
        print("   ✅ 数据状态正确显示和更新")
        print("   ✅ 数据持久化功能正常工作")
    else:
        print("🚨 结论: 用户工作流程存在问题")
        print("   需要进一步调试和修复")

if __name__ == "__main__":
    main()
