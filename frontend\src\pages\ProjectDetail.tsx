import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Descriptions, Tabs, Button, Table, Tag, Space, Spin, Empty, message } from 'antd';
import { ArrowLeftOutlined, EditOutlined, DeleteOutlined, ExportOutlined } from '@ant-design/icons';
import { getProject, Project, ProjectDetailStabilityData } from '../api'; // 导入API函数

const { TabPane } = Tabs;

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!id) {
        message.error("项目ID不存在");
        setLoading(false);
        return;
    }

    const fetchProject = async () => {
      setLoading(true);
      try {
        const response = await getProject(id);
        const projectData = response.data;
        // 为稳定性数据添加唯一的key
        if (projectData.stability_data) {
          projectData.stability_data = projectData.stability_data.map((item: ProjectDetailStabilityData, index: number) => ({
            ...item,
            id: item.id || index, // 使用后端ID，如果不存在则用索引作为key
          }));
        }
        setProject(projectData);
      } catch (error) {
        message.error('获取项目详情失败');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [id]);

  // 稳定性数据表格列
  const stabilityColumns = [
    {
      title: '时间点(月)',
      dataIndex: 'time_point',
      key: 'time_point',
    },
    {
      title: '温度(°C)',
      dataIndex: 'temperature',
      key: 'temperature',
    },
    {
      title: '湿度(%RH)',
      dataIndex: 'humidity',
      key: 'humidity',
    },
    {
      title: '测试项目',
      dataIndex: 'item',
      key: 'item',
    },
    {
      title: '测量值',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => value ? `${value.toFixed(1)}%` : '-',
    },
    {
      title: '状态',
      key: 'status',
      render: (text: any, record: ProjectDetailStabilityData) => {
        if (record.value == null) return <Tag>无数据</Tag>;
        return (
          <Tag color={record.value >= 95 ? 'green' : record.value >= 90 ? 'orange' : 'red'}>
            {record.value >= 95 ? '正常' : record.value >= 90 ? '警告' : '异常'}
          </Tag>
        )
      },
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!project) {
    return (
      <Empty
        description="未找到项目或加载失败"
        style={{ margin: '50px 0' }}
      />
    );
  }

  return (
    <div>
      <Button 
        icon={<ArrowLeftOutlined />} 
        style={{ marginBottom: 16 }}
        onClick={() => navigate('/projects')}
      >
        返回项目列表
      </Button>

      <Card
        title={project.name}
        extra={
          <Space>
            <Button icon={<EditOutlined />}>编辑</Button>
            <Button icon={<DeleteOutlined />} danger>删除</Button>
            <Button icon={<ExportOutlined />} type="primary">导出报告</Button>
          </Space>
        }
      >
        <Descriptions bordered>
          <Descriptions.Item label="项目状态"><Tag>{project.status}</Tag></Descriptions.Item>
          <Descriptions.Item label="创建日期">{project.created_at ? new Date(project.created_at).toLocaleDateString() : ''}</Descriptions.Item>
          <Descriptions.Item label="最后更新">{project.updated_at ? new Date(project.updated_at).toLocaleDateString() : ''}</Descriptions.Item>
          <Descriptions.Item label="药物名称">{project.drug_info?.drug_name || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="CAS号">{project.drug_info?.cas_number || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="项目描述" span={3}>
            {project.description}
          </Descriptions.Item>
        </Descriptions>

        <Tabs defaultActiveKey="1" style={{ marginTop: 24 }}>
          <TabPane tab="稳定性数据" key="1">
            <Table 
              columns={stabilityColumns} 
              dataSource={project.stability_data || []} 
              rowKey="id"
              pagination={false}
            />
          </TabPane>
          <TabPane tab="稳定性预测" key="2">
            <Empty description="暂无预测数据" />
          </TabPane>
          <TabPane tab="辅料相容性" key="3">
            <Empty description="暂无相容性数据" />
          </TabPane>
          <TabPane tab="AI建议" key="4">
            <Empty description="暂无AI建议" />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ProjectDetail; 