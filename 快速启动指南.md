# 药物稳定性研究助手 - 快速启动指南

## 🚀 优化后的启动流程

经过全面优化后，软件的安全性、性能和稳定性都得到了显著提升。请按照以下步骤启动：

---

## 📋 前置要求

### 1. 环境配置
```bash
# 确保已安装Python 3.8+和Node.js 16+
python --version
node --version
```

### 2. 安装新增依赖
```bash
# 后端新增依赖
cd backend
pip install redis bcrypt psutil

# 前端依赖（如有变化）
cd ../frontend
npm install
```

---

## 🔧 配置环境变量

### 1. 创建环境配置文件
```bash
# 复制环境变量模板
cp .env.example .env
```

### 2. 编辑.env文件
```bash
# 必须配置的API密钥（至少配置一个）
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
GROK_API_KEY=your_grok_api_key_here

# 数据库配置（可选，默认使用SQLite）
DATABASE_URL=sqlite:///./app.db

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_here
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 性能配置（可选）
ENABLE_CACHE=true
ENABLE_METRICS=true
```

---

## 🏃‍♂️ 启动步骤

### 方法一：使用现有启动脚本
```bash
# 双击运行
start_software.bat
```

### 方法二：手动启动
```bash
# 1. 启动后端
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 2. 启动前端（新终端）
cd frontend
npm start
```

---

## 🔍 验证启动状态

### 1. 检查后端健康状态
```bash
# 访问健康检查端点
curl http://localhost:8000/api/health
```

预期响应：
```json
{
  "status": "healthy",
  "timestamp": "2025-06-29T...",
  "checks": {
    "cpu": {"status": "healthy", "usage": 15.2},
    "memory": {"status": "healthy", "usage": 45.8},
    "database": {"status": "healthy", "connected": true},
    "cache": {"status": "healthy", "type": "memory"}
  }
}
```

### 2. 检查AI配置状态
```bash
curl http://localhost:8000/api/ai/status
```

预期响应：
```json
{
  "available_providers": ["deepseek", "openai"],
  "default_provider": "deepseek",
  "validation_results": {
    "openai": true,
    "deepseek": true,
    "grok": false
  }
}
```

### 3. 检查性能监控
```bash
curl http://localhost:8000/api/metrics
```

### 4. 访问前端应用
打开浏览器访问：http://localhost:3000

---

## ⚠️ 常见问题解决

### 问题1：API密钥未配置
**症状**：AI功能无法使用
**解决**：检查.env文件中的API密钥配置

### 问题2：端口被占用
**症状**：启动失败，提示端口已被使用
**解决**：
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 终止进程（替换PID）
taskkill /PID <PID> /F
```

### 问题3：依赖包缺失
**症状**：ImportError或ModuleNotFoundError
**解决**：
```bash
# 重新安装依赖
pip install -r requirements.txt
npm install
```

### 问题4：数据库初始化失败
**症状**：数据库相关错误
**解决**：
```bash
# 删除旧数据库文件
rm backend/app.db

# 重新启动应用，会自动初始化
```

---

## 📊 新增功能说明

### 1. 安全增强
- ✅ API密钥环境变量化
- ✅ 请求限流保护
- ✅ CORS安全策略
- ✅ 安全头配置

### 2. 性能监控
- ✅ 实时性能指标
- ✅ 系统资源监控
- ✅ API响应时间统计
- ✅ 错误率监控

### 3. 缓存系统
- ✅ 智能缓存机制
- ✅ 支持Redis和内存缓存
- ✅ 缓存命中率统计

### 4. 数据库优化
- ✅ 连接池管理
- ✅ SQLite WAL模式
- ✅ 查询性能优化

---

## 🔧 高级配置

### Redis缓存配置（可选）
```bash
# 安装Redis
# Windows: 下载Redis for Windows
# Linux: sudo apt-get install redis-server
# macOS: brew install redis

# 启动Redis
redis-server

# 更新.env配置
REDIS_URL=redis://localhost:6379/0
ENABLE_CACHE=true
```

### 生产环境配置
```bash
# 生产环境变量
APP_ENV=production
DEBUG=false
LOG_LEVEL=WARNING

# 安全配置
JWT_EXPIRE_MINUTES=60
RATE_LIMIT_PER_MINUTE=50
MAX_UPLOAD_SIZE=10
```

---

## 📈 性能优化建议

### 1. 系统资源
- CPU使用率保持在80%以下
- 内存使用率保持在85%以下
- 磁盘空间保持充足

### 2. 数据库优化
- 定期清理日志文件
- 监控查询性能
- 适时添加索引

### 3. 缓存策略
- 启用Redis缓存提升性能
- 合理设置缓存过期时间
- 监控缓存命中率

---

## 📞 技术支持

如遇到问题，请检查：
1. 📋 环境变量配置是否正确
2. 🔌 网络连接是否正常
3. 💾 磁盘空间是否充足
4. 🔑 API密钥是否有效

**优化完成时间**：2025年6月29日  
**版本**：v2.0 (优化版)  
**状态**：✅ 生产就绪
