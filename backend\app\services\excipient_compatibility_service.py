"""
辅料相容性服务
集成高级相容性评估引擎和AI功能
"""
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from ..config.database import get_db

from ..models.advanced_compatibility_engine import AdvancedCompatibilityEngine
from ..models.compatibility_engine import CompatibilityEngine
from ..models.knowledge_graph import KnowledgeGraph
from ..models.excipient import ExcipientInteraction
from .ai_client import AIClient

logger = logging.getLogger(__name__)


class ExcipientCompatibilityService:
    """辅料相容性服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.advanced_engine = AdvancedCompatibilityEngine()
        self.basic_engine = CompatibilityEngine()
        self.knowledge_graph = KnowledgeGraph()
        # The AI client is now instantiated where needed to pass API keys etc.
        # self.ai_service = AIClient() 
    
    async def assess_compatibility(
        self,
        drug_name: str,
        drug_structure: str,
        excipients: List[Dict[str, Any]],
        dosage_form: str = "tablet",
        process_conditions: Optional[Dict[str, Any]] = None,
        use_advanced: bool = True
    ) -> Dict[str, Any]:
        """
        评估药物-辅料相容性
        
        Args:
            drug_name: 药物名称
            drug_structure: 药物SMILES结构
            excipients: 辅料列表
            dosage_form: 剂型
            process_conditions: 工艺条件
            use_advanced: 是否使用高级评估引擎
            
        Returns:
            相容性评估结果
        """
        try:
            results = []
            
            # 对每个辅料进行评估
            for excipient in excipients:
                excipient_name = excipient.get('name', '')
                excipient_smiles = excipient.get('smiles', '')
                concentration = excipient.get('concentration', 0)
                
                # 1. 基础相容性评估 (Now uses DB)
                basic_result = self._assess_basic_compatibility(
                    drug_structure, excipient_smiles, excipient_name
                )
                
                # 2. 高级相容性评估（如果启用）
                advanced_result = None
                if use_advanced and excipient_smiles:
                    advanced_result = self.advanced_engine.assess_compatibility(
                        drug_smiles=drug_structure,
                        excipient_smiles=excipient_smiles,
                        conditions=process_conditions or {}
                    )
                
                # 3. 知识图谱查询
                knowledge_result = self._query_knowledge_base(
                    drug_name, excipient_name, dosage_form
                )
                
                # 4. 整合结果
                integrated_result = self._integrate_results(
                    excipient_name,
                    basic_result,
                    advanced_result,
                    knowledge_result,
                    concentration
                )
                
                results.append(integrated_result)
            
            # 5. AI增强分析
            ai_insights = None
            try:
                ai_context = {
                    "drug_name": drug_name,
                    "drug_structure": drug_structure,
                    "excipients": excipients,
                    "dosage_form": dosage_form,
                    "compatibility_results": results
                }
                
                ai_insights = await self.ai_service.get_compatibility_insights(
                    context=ai_context,
                    lang="zh"
                )
            except Exception as e:
                logger.warning(f"AI分析失败: {e}")
            
            # 6. 生成总体评估和建议
            overall_assessment = self._generate_overall_assessment(results)
            recommendations = self._generate_recommendations(
                results, drug_name, dosage_form
            )
            
            return {
                "drug": drug_name,
                "results": results,
                "overall_assessment": overall_assessment,
                "recommendations": recommendations,
                "ai_insights": ai_insights,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"相容性评估失败: {e}")
            raise
    
    def _assess_basic_compatibility(
        self,
        drug_smiles: str,
        excipient_smiles: str,
        excipient_name: str
    ) -> Dict[str, Any]:
        """基础相容性评估"""
        
        if not excipient_smiles:
            # 基于名称的数据库查询评估
            return self._db_based_assessment(excipient_name)
        
        # 使用基础引擎评估
        result = self.basic_engine.check_compatibility(drug_smiles, excipient_smiles)
        
        return {
            "method": "structure_based",
            "incompatible_groups": result.get('incompatible_groups', []),
            "risk_score": result.get('risk_score', 0),
            "reactions": result.get('potential_reactions', [])
        }
    
    def _db_based_assessment(self, excipient_name: str) -> Dict[str, Any]:
        """基于数据库查询的相容性评估"""
        interaction = self.db.query(ExcipientInteraction).filter(ExcipientInteraction.name == excipient_name).first()
        
        if not interaction:
            return {
                "method": "db_based",
                "known_issues": {},
                "risk_score": 0.1 # Default low risk for unknown
            }

        issue = {
            "risk_with": interaction.risk_with.split(",") if interaction.risk_with else [],
            "reaction": interaction.reaction,
            "risk_level": interaction.risk_level.lower()
        }

        return {
            "method": "db_based",
            "known_issues": issue,
            "risk_score": {"low": 0.3, "medium": 0.6, "high": 0.9}.get(
                issue.get('risk_level', 'low'), 0.3
            )
        }
    
    def _query_knowledge_base(
        self,
        drug_name: str,
        excipient_name: str,
        dosage_form: str
    ) -> Dict[str, Any]:
        """查询知识库"""
        
        # 查询相似案例
        similar_cases = self.knowledge_graph.query_similar_cases(
            drug_name=drug_name,
            dosage_form=dosage_form,
            issue=f"{excipient_name}相容性"
        )
        
        # 提取相关信息
        knowledge_result = {
            "similar_cases": similar_cases[:3],
            "reported_issues": [],
            "success_cases": []
        }
        
        for case in similar_cases:
            if case.get('outcome') == 'incompatible':
                knowledge_result['reported_issues'].append({
                    "drug": case.get('drug'),
                    "excipient": case.get('excipient'),
                    "issue": case.get('issue')
                })
            else:
                knowledge_result['success_cases'].append({
                    "drug": case.get('drug'),
                    "excipient": case.get('excipient'),
                    "conditions": case.get('conditions')
                })
        
        return knowledge_result
    
    def _integrate_results(
        self,
        excipient_name: str,
        basic_result: Dict,
        advanced_result: Optional[Dict],
        knowledge_result: Dict,
        concentration: float
    ) -> Dict[str, Any]:
        """整合多源评估结果"""
        
        # 初始化综合结果
        integrated = {
            "excipient": excipient_name,
            "concentration": concentration,
            "risk_level": "low",
            "risk_score": 0,
            "risk_types": [],
            "evidence": [],
            "mitigation_strategies": []
        }
        
        # 收集所有风险分数
        risk_scores = []
        
        # 1. 基础评估结果
        if basic_result:
            risk_scores.append(basic_result.get('risk_score', 0))
            if basic_result.get('incompatible_groups'):
                integrated['risk_types'].extend(basic_result['incompatible_groups'])
                integrated['evidence'].append(
                    f"结构分析发现不相容官能团: {', '.join(basic_result['incompatible_groups'])}"
                )
        
        # 2. 高级评估结果
        if advanced_result:
            # 提取风险分数
            if 'risk_assessment' in advanced_result:
                risk_scores.append(advanced_result['risk_assessment'].get('overall_risk', 0))
                
                # 添加反应风险
                for reaction in advanced_result['risk_assessment'].get('reaction_risks', []):
                    if reaction['risk_level'] in ['high', 'medium']:
                        integrated['risk_types'].append(reaction['reaction_type'])
                        integrated['evidence'].append(
                            f"{reaction['reaction_type']}: {reaction['description']}"
                        )
            
            # 添加缓解策略
            if 'mitigation_strategies' in advanced_result:
                integrated['mitigation_strategies'].extend(
                    advanced_result['mitigation_strategies']
                )
        
        # 3. 知识库结果
        if knowledge_result.get('reported_issues'):
            risk_scores.append(0.7)  # 有报道的不相容性
            for issue in knowledge_result['reported_issues']:
                integrated['evidence'].append(
                    f"文献报道: {issue['drug']}与{issue['excipient']}存在{issue.get('issue', '相容性问题')}"
                )
        
        # 计算综合风险分数
        if risk_scores:
            integrated['risk_score'] = max(risk_scores)  # 取最高风险
            
            # 确定风险等级
            if integrated['risk_score'] >= 0.7:
                integrated['risk_level'] = "high"
            elif integrated['risk_score'] >= 0.4:
                integrated['risk_level'] = "medium"
            else:
                integrated['risk_level'] = "low"
        
        # 浓度效应
        if concentration > 10:  # 高浓度辅料
            integrated['evidence'].append(f"辅料浓度较高({concentration}%)，需特别关注相互作用")
            if integrated['risk_level'] == "low":
                integrated['risk_level'] = "medium"
        
        return integrated
    
    def _generate_overall_assessment(
        self,
        results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成总体评估"""
        
        high_risk_count = sum(1 for r in results if r['risk_level'] == 'high')
        medium_risk_count = sum(1 for r in results if r['risk_level'] == 'medium')
        low_risk_count = sum(1 for r in results if r['risk_level'] == 'low')
        
        # 确定总体风险等级
        if high_risk_count > 0:
            overall_risk = "high"
            summary = f"发现{high_risk_count}个高风险相容性问题，需要重点关注"
        elif medium_risk_count > 2:
            overall_risk = "medium-high"
            summary = f"发现多个中等风险相容性问题({medium_risk_count}个)，建议进行相容性试验验证"
        elif medium_risk_count > 0:
            overall_risk = "medium"
            summary = f"发现{medium_risk_count}个中等风险相容性问题，建议关注"
        else:
            overall_risk = "low"
            summary = "未发现明显相容性风险，配方相对安全"
        
        # 关键风险因素
        key_risks = []
        for result in results:
            if result['risk_level'] in ['high', 'medium']:
                key_risks.extend(result['risk_types'])
        
        return {
            "overall_risk": overall_risk,
            "summary": summary,
            "risk_distribution": {
                "high": high_risk_count,
                "medium": medium_risk_count,
                "low": low_risk_count
            },
            "key_risk_factors": list(set(key_risks))
        }
    
    def _generate_recommendations(
        self,
        results: List[Dict[str, Any]],
        drug_name: str,
        dosage_form: str
    ) -> List[Dict[str, str]]:
        """生成建议"""
        
        recommendations = []
        
        # 1. 高风险辅料替代建议
        for result in results:
            if result['risk_level'] == 'high':
                recommendations.append({
                    "type": "critical",
                    "category": "formulation",
                    "content": f"建议替换{result['excipient']}，考虑使用相容性更好的辅料"
                })
                
                # 添加具体的缓解策略
                for strategy in result.get('mitigation_strategies', []):
                    recommendations.append({
                        "type": "action",
                        "category": "mitigation",
                        "content": strategy
                    })
        
        # 2. 相容性试验建议
        if any(r['risk_level'] in ['high', 'medium'] for r in results):
            recommendations.append({
                "type": "important",
                "category": "testing",
                "content": "建议进行药物-辅料相容性试验，包括DSC、FTIR、HPLC等分析"
            })
            
            recommendations.append({
                "type": "info",
                "category": "testing",
                "content": "相容性试验条件：40°C/75%RH，1个月；必要时进行应力试验"
            })
        
        # 3. 工艺优化建议
        risk_types = set()
        for result in results:
            risk_types.update(result['risk_types'])
        
        if 'hydrolysis' in risk_types:
            recommendations.append({
                "type": "warning",
                "category": "process",
                "content": "存在水解风险，建议采用干法制粒或直接压片工艺"
            })
        
        if 'oxidation' in risk_types:
            recommendations.append({
                "type": "warning",
                "category": "process",
                "content": "存在氧化风险，建议在惰性气体保护下生产，添加抗氧化剂"
            })
        
        # 4. 包装建议
        if risk_types:
            recommendations.append({
                "type": "info",
                "category": "packaging",
                "content": "建议使用高阻隔性包装材料，如铝塑泡罩或铝管包装"
            })
        
        # 5. 监测建议
        recommendations.append({
            "type": "info",
            "category": "monitoring",
            "content": "建议在稳定性研究中重点监测有关物质变化，特别是与辅料相互作用产生的降解产物"
        })
        
        return recommendations
    
    async def predict_compatibility_ml(
        self,
        drug_smiles: str,
        excipient_smiles: str,
        conditions: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """使用机器学习模型预测相容性"""
        
        try:
            # 调用高级引擎的ML预测功能
            ml_prediction = self.advanced_engine.predict_compatibility_ml(
                drug_smiles, excipient_smiles
            )
            
            # 增强预测结果
            if conditions:
                # 根据条件调整预测
                temperature = conditions.get('temperature', 25)
                humidity = conditions.get('humidity', 60)
                
                # 温度效应
                if temperature > 40:
                    ml_prediction['risk_score'] *= 1.2
                elif temperature < 10:
                    ml_prediction['risk_score'] *= 0.8
                
                # 湿度效应
                if humidity > 75:
                    ml_prediction['risk_score'] *= 1.1
            
            return ml_prediction
            
        except Exception as e:
            logger.error(f"ML预测失败: {e}")
            return {
                "method": "ml_prediction",
                "success": False,
                "error": str(e)
            } 