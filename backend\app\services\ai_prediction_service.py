"""
AI预测服务
集成各种AI预测和分析功能
"""
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
import asyncio

from .stability_prediction_service import StabilityPredictionService
from .excipient_compatibility_service import ExcipientCompatibilityService
from ..models.knowledge_graph import KnowledgeGraph
from app.services.ai_service import ai_client

logger = logging.getLogger(__name__)


class AIPredictionService:
    """AI预测服务"""
    
    def __init__(self):
        self.stability_service = StabilityPredictionService()
        self.compatibility_service = ExcipientCompatibilityService()
        self.knowledge_graph = KnowledgeGraph()
        self.ai_service = ai_client
    
    async def comprehensive_analysis(
        self,
        drug_name: str,
        drug_smiles: str,
        dosage_form: str,
        excipients: List[Dict[str, Any]],
        stability_data: Optional[List[Dict[str, Any]]] = None,
        target_shelf_life: Optional[int] = None,
        storage_zone: str = "Zone II",
        special_requirements: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        综合AI分析
        
        Args:
            drug_name: 药物名称
            drug_smiles: 药物SMILES结构
            dosage_form: 剂型
            excipients: 辅料列表
            stability_data: 历史稳定性数据
            target_shelf_life: 目标货架期
            storage_zone: 储存区域
            special_requirements: 特殊要求
            
        Returns:
            综合分析结果
        """
        try:
            # 并行执行多个分析任务
            tasks = []
            
            # 1. 药物结构分析
            tasks.append(self._analyze_drug_structure(drug_name, drug_smiles))
            
            # 2. 相容性评估
            tasks.append(self.compatibility_service.assess_compatibility(
                drug_name=drug_name,
                drug_structure=drug_smiles,
                excipients=excipients,
                dosage_form=dosage_form
            ))
            
            # 3. 稳定性预测
            tasks.append(self.stability_service.predict_stability(
                drug_name=drug_name,
                drug_smiles=drug_smiles,
                excipients=excipients,
                process=dosage_form,
                packaging="待定",
                environment=storage_zone,
                history_data=stability_data,
                prediction_months=target_shelf_life or 36
            ))
            
            # 4. 研究方案设计
            tasks.append(self.stability_service.design_stability_study(
                drug_name=drug_name,
                dosage_form=dosage_form,
                target_shelf_life=target_shelf_life or 24,
                storage_zone=storage_zone
            ))
            
            # 执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            structure_analysis = results[0] if not isinstance(results[0], Exception) else None
            compatibility_assessment = results[1] if not isinstance(results[1], Exception) else None
            stability_prediction = results[2] if not isinstance(results[2], Exception) else None
            study_design = results[3] if not isinstance(results[3], Exception) else None
            
            # 5. 查询相似案例
            similar_cases = self.knowledge_graph.query_similar_cases(
                drug_name=drug_name,
                dosage_form=dosage_form,
                issue="综合分析"
            )
            
            # 6. 生成综合建议
            comprehensive_recommendations = self._generate_comprehensive_recommendations(
                structure_analysis,
                compatibility_assessment,
                stability_prediction,
                study_design,
                similar_cases,
                special_requirements
            )
            
            # 7. 生成执行摘要
            executive_summary = self._generate_executive_summary(
                drug_name,
                dosage_form,
                compatibility_assessment,
                stability_prediction,
                comprehensive_recommendations
            )
            
            return {
                "drug_name": drug_name,
                "analysis_sections": {
                    "structure_analysis": structure_analysis,
                    "compatibility_assessment": compatibility_assessment,
                    "stability_prediction": stability_prediction,
                    "study_design": study_design,
                    "similar_cases": similar_cases[:3]
                },
                "comprehensive_recommendations": comprehensive_recommendations,
                "executive_summary": executive_summary,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            raise
    
    async def _analyze_drug_structure(
        self,
        drug_name: str,
        drug_smiles: str
    ) -> Dict[str, Any]:
        """分析药物结构"""
        
        try:
            # 使用知识图谱获取药物信息
            drug_info = self.knowledge_graph.get_drug_info(drug_name)
            
            # 结构特征分析
            structure_features = {
                "functional_groups": self._identify_functional_groups(drug_smiles),
                "stability_alerts": self._check_stability_alerts(drug_smiles),
                "physicochemical_properties": self._calculate_properties(drug_smiles)
            }
            
            # AI增强分析
            ai_analysis = await self.ai_service.analyze_structure(
                drug_name=drug_name,
                drug_smiles=drug_smiles,
                lang="zh"
            )
            
            return {
                "drug_name": drug_name,
                "drug_info": drug_info,
                "structure_features": structure_features,
                "ai_insights": ai_analysis,
                "degradation_pathways": self._predict_degradation_pathways(drug_smiles)
            }
            
        except Exception as e:
            logger.error(f"药物结构分析失败: {e}")
            return {"error": str(e)}
    
    def _identify_functional_groups(self, smiles: str) -> List[str]:
        """识别官能团"""
        
        functional_groups = []
        
        # 简化的官能团识别
        patterns = {
            "C(=O)O": "羧酸",
            "C(=O)N": "酰胺",
            "O[H]": "羟基",
            "N[H]": "胺基",
            "C=O": "羰基",
            "C=C": "双键",
            "c1ccccc1": "苯环",
            "S(=O)(=O)": "磺酰基",
            "P(=O)": "磷酰基"
        }
        
        for pattern, name in patterns.items():
            if pattern in smiles:
                functional_groups.append(name)
        
        return functional_groups
    
    def _check_stability_alerts(self, smiles: str) -> List[Dict[str, str]]:
        """检查稳定性警告"""
        
        alerts = []
        
        # 不稳定结构警告
        if "C(=O)Cl" in smiles:
            alerts.append({
                "type": "high",
                "structure": "酰氯",
                "issue": "极易水解"
            })
        
        if "OO" in smiles:
            alerts.append({
                "type": "high",
                "structure": "过氧化物",
                "issue": "易分解，具有爆炸性"
            })
        
        if "N=N" in smiles:
            alerts.append({
                "type": "medium",
                "structure": "偶氮基",
                "issue": "光敏感，易分解"
            })
        
        if "C=CC=C" in smiles:
            alerts.append({
                "type": "medium",
                "structure": "共轭双键",
                "issue": "易氧化，光敏感"
            })
        
        return alerts
    
    def _calculate_properties(self, smiles: str) -> Dict[str, Any]:
        """计算理化性质"""
        
        # 简化的性质计算
        properties = {
            "molecular_weight": len(smiles) * 12,  # 粗略估算
            "logP": smiles.count("C") - smiles.count("O") - smiles.count("N"),  # 粗略估算
            "hbd": smiles.count("O[H]") + smiles.count("N[H]"),  # 氢键供体
            "hba": smiles.count("O") + smiles.count("N"),  # 氢键受体
            "rotatable_bonds": smiles.count("CC") - smiles.count("c1ccccc1")  # 可旋转键
        }
        
        return properties
    
    def _predict_degradation_pathways(self, smiles: str) -> List[str]:
        """预测降解途径"""
        
        pathways = []
        
        if "C(=O)N" in smiles:
            pathways.append("水解（酰胺键断裂）")
        
        if "C(=O)O" in smiles and "O" in smiles:
            pathways.append("酯水解")
        
        if "C=C" in smiles or "c1ccccc1" in smiles:
            pathways.append("氧化")
        
        if "N" in smiles:
            pathways.append("N-氧化")
            pathways.append("去烷基化")
        
        if "S" in smiles:
            pathways.append("S-氧化")
        
        return pathways
    
    def _generate_comprehensive_recommendations(
        self,
        structure_analysis: Optional[Dict],
        compatibility_assessment: Optional[Dict],
        stability_prediction: Optional[Dict],
        study_design: Optional[Dict],
        similar_cases: List[Dict],
        special_requirements: Optional[Dict]
    ) -> List[Dict[str, Any]]:
        """生成综合建议"""
        
        recommendations = []
        
        # 1. 基于结构分析的建议
        if structure_analysis and 'stability_alerts' in structure_analysis:
            for alert in structure_analysis['stability_alerts']:
                if alert['type'] == 'high':
                    recommendations.append({
                        "priority": "critical",
                        "category": "formulation",
                        "title": "结构稳定性风险",
                        "content": f"药物含有{alert['structure']}，{alert['issue']}。建议特别关注配方设计和包装选择。",
                        "actions": [
                            "选择惰性辅料",
                            "考虑添加稳定剂",
                            "使用高阻隔包装"
                        ]
                    })
        
        # 2. 基于相容性评估的建议
        if compatibility_assessment and 'overall_assessment' in compatibility_assessment:
            risk_level = compatibility_assessment['overall_assessment'].get('overall_risk')
            if risk_level in ['high', 'medium-high']:
                recommendations.append({
                    "priority": "high",
                    "category": "compatibility",
                    "title": "辅料相容性风险",
                    "content": compatibility_assessment['overall_assessment'].get('summary'),
                    "actions": compatibility_assessment.get('recommendations', [])[:3]
                })
        
        # 3. 基于稳定性预测的建议
        if stability_prediction and 'prediction' in stability_prediction:
            t90 = stability_prediction['prediction'].get('long_term', {}).get('t90')
            if t90 and t90 < 24:
                recommendations.append({
                    "priority": "high",
                    "category": "stability",
                    "title": "货架期不足",
                    "content": f"预测货架期仅{t90}个月，低于目标要求",
                    "actions": [
                        "优化配方组成",
                        "改进制备工艺",
                        "升级包装材料",
                        "考虑添加抗氧化剂或稳定剂"
                    ]
                })
        
        # 4. 基于相似案例的建议
        if similar_cases:
            successful_cases = [c for c in similar_cases if c.get('outcome') == 'success']
            if successful_cases:
                recommendations.append({
                    "priority": "medium",
                    "category": "reference",
                    "title": "参考成功案例",
                    "content": f"发现{len(successful_cases)}个相似成功案例可供参考",
                    "actions": [f"参考{case.get('drug')}的配方设计" for case in successful_cases[:2]]
                })
        
        # 5. 研究设计建议
        if study_design:
            recommendations.append({
                "priority": "medium",
                "category": "study_design",
                "title": "稳定性研究方案",
                "content": "已生成符合ICH指南的稳定性研究方案",
                "actions": [
                    f"长期试验: {study_design['study_design']['long_term']['conditions']}",
                    f"加速试验: {study_design['study_design']['accelerated']['conditions']}",
                    "进行影响因素试验评估降解途径"
                ]
            })
        
        # 6. 特殊要求相关建议
        if special_requirements:
            if special_requirements.get('pediatric'):
                recommendations.append({
                    "priority": "high",
                    "category": "special",
                    "title": "儿科用药特殊要求",
                    "content": "需满足儿科用药的特殊要求",
                    "actions": [
                        "选择适合儿童的辅料",
                        "考虑口感改善",
                        "确保剂量准确性"
                    ]
                })
        
        # 按优先级排序
        priority_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        recommendations.sort(key=lambda x: priority_order.get(x['priority'], 3))
        
        return recommendations
    
    def _generate_executive_summary(
        self,
        drug_name: str,
        dosage_form: str,
        compatibility_assessment: Optional[Dict],
        stability_prediction: Optional[Dict],
        recommendations: List[Dict]
    ) -> Dict[str, Any]:
        """生成执行摘要"""
        
        # 总体评估
        overall_status = "favorable"
        key_findings = []
        critical_actions = []
        
        # 相容性评估总结
        if compatibility_assessment:
            risk = compatibility_assessment.get('overall_assessment', {}).get('overall_risk')
            if risk in ['high', 'medium-high']:
                overall_status = "challenging"
                key_findings.append(f"发现显著的辅料相容性风险（{risk}）")
        
        # 稳定性预测总结
        if stability_prediction:
            t90 = stability_prediction.get('prediction', {}).get('long_term', {}).get('t90')
            if t90:
                key_findings.append(f"预测货架期: {t90}个月")
                if t90 < 24:
                    overall_status = "challenging"
        
        # 关键行动项
        for rec in recommendations:
            if rec['priority'] in ['critical', 'high']:
                critical_actions.extend(rec.get('actions', [])[:2])
        
        # 时间线
        timeline = self._generate_timeline(dosage_form)
        
        # 总体评估描述
        assessment_text = {
            "favorable": f"{drug_name}的{dosage_form}开发前景良好，主要风险可控",
            "challenging": f"{drug_name}的{dosage_form}开发存在挑战，需要重点关注关键风险因素",
            "high_risk": f"{drug_name}的{dosage_form}开发风险较高，建议重新评估配方策略"
        }
        
        return {
            "overall_assessment": assessment_text.get(overall_status, "需要进一步评估"),
            "key_findings": key_findings[:5],  # 最多5个关键发现
            "critical_actions": list(set(critical_actions))[:5],  # 去重，最多5个
            "timeline": timeline,
            "risk_level": overall_status
        }
    
    def _generate_timeline(self, dosage_form: str) -> str:
        """生成开发时间线"""
        
        timelines = {
            "片剂": "预计开发周期18-24个月",
            "胶囊剂": "预计开发周期15-20个月",
            "注射剂": "预计开发周期24-36个月",
            "口服液体": "预计开发周期12-18个月"
        }
        
        return timelines.get(dosage_form, "预计开发周期18-24个月") 