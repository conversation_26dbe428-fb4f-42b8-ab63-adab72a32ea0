"""
项目数据服务
提供统一的项目数据访问接口
"""
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
import logging
from sqlalchemy.orm import Session
from app.models.project import ProjectORM
from app.models.drug import DrugORM
from app.models.excipient import ExcipientORM
from app.models.environment import EnvironmentORM
from app.models.stability import StabilityDataORM
from app.schemas import ProjectCreateRequest, ProjectUpdateRequest
import uuid

logger = logging.getLogger(__name__)

class ProjectDataService:
    """项目数据服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        
    def get_project(self, project_id: int) -> Optional[ProjectORM]:
        return self.db.query(ProjectORM).filter(ProjectORM.id == project_id).first()

    def list_projects(self, skip: int = 0, limit: int = 100) -> List[ProjectORM]:
        return self.db.query(ProjectORM).offset(skip).limit(limit).all()

    def create_project(self, project_data: ProjectCreateRequest) -> ProjectORM:
        now = datetime.now()
        db_project = ProjectORM(
            created_at=now,
            updated_at=now,
            **project_data.dict()
        )
        self.db.add(db_project)
        self.db.commit()
        self.db.refresh(db_project)
        return db_project

    def update_project(self, project_id: int, project_data: ProjectUpdateRequest) -> Optional[ProjectORM]:
        db_project = self.get_project(project_id)
        if not db_project:
            return None

        update_data = project_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_project, key, value)

        db_project.updated_at = datetime.now()
        self.db.commit()
        self.db.refresh(db_project)
        return db_project

    def delete_project(self, project_id: int) -> bool:
        db_project = self.get_project(project_id)
        if not db_project:
            return False

        self.db.delete(db_project)
        self.db.commit()
        return True

    def get_project_complete_data(self, project_id: int, db: Session) -> Optional[Dict[str, Any]]:
        """
        获取项目的完整数据，包括药物、辅料、环境等所有相关信息
        """
        try:
            # 查询项目
            project = db.query(ProjectORM).filter(ProjectORM.id == project_id).first()
            if not project:
                return None

            # 构建完整数据结构
            complete_data = {
                "project": {
                    "id": project.id,
                    "name": project.name,
                    "description": project.description,
                    "status": project.status,
                    "created_at": project.created_at.isoformat() if project.created_at else None,
                    "updated_at": project.updated_at.isoformat() if project.updated_at else None
                },
                "drug": {
                    "name": getattr(project, 'drug_name', None),
                    "cas": getattr(project, 'cas_number', None),
                    "formula": getattr(project, 'molecular_formula', None),
                    "smiles": getattr(project, 'smiles_structure', None),
                    "category": getattr(project, 'drug_class', None),
                    "description": getattr(project, 'drug_description', None)
                },
                "formulation": {
                    "dosage_form": project.dosage_form,
                    "strength": project.strength,
                    "excipients": project.excipients or [],
                    "packaging_materials": project.packaging_materials or []
                },
                "excipients": project.excipients or [],
                "environment": {
                    "storage_conditions": project.storage_conditions or [],
                    "production_process": getattr(project, 'production_process', None),
                    "packaging_material": getattr(project, 'packaging_material', None),
                    "storage_condition": getattr(project, 'storage_condition', None)
                },
                "stability_data": project.stability_data or [],
                "prediction_settings": project.prediction_settings or {}
            }

            return complete_data

        except Exception as e:
            logger.error(f"获取项目完整数据失败: {e}")
            return None

    def cache_project_data(self, project_id: int, data: Dict[str, Any]):
        """缓存项目数据"""
        if not hasattr(self, '_cache'):
            self._cache = {}
        self._cache[project_id] = {
            "data": data,
            "timestamp": datetime.now()
        }

    def get_cached_project_data(self, project_id: int) -> Optional[Dict[str, Any]]:
        """获取缓存的项目数据"""
        if not hasattr(self, '_cache'):
            self._cache = {}
        if project_id in self._cache:
            # 检查缓存是否过期（30分钟）
            cache_entry = self._cache[project_id]
            if (datetime.now() - cache_entry["timestamp"]).seconds < 1800:
                return cache_entry["data"]
        return None

    def clear_project_cache(self, project_id: int):
        """清除项目缓存"""
        if not hasattr(self, '_cache'):
            self._cache = {}
        if project_id in self._cache:
            del self._cache[project_id]

    def get_project_drug_info(self, project_id: int, db: Session) -> Optional[Dict[str, Any]]:
        """获取项目的药物信息"""
        try:
            project = db.query(ProjectORM).filter(ProjectORM.id == project_id).first()
            if not project:
                return None

            return {
                "name": getattr(project, 'drug_name', None),
                "cas": getattr(project, 'cas_number', None),
                "formula": getattr(project, 'molecular_formula', None),
                "smiles": getattr(project, 'smiles_structure', None),
                "category": getattr(project, 'drug_class', None),
                "description": getattr(project, 'drug_description', None)
            }
        except Exception as e:
            logger.error(f"获取项目药物信息失败: {e}")
            return None
    
    def get_project_excipients(self, project_id: int, db: Session) -> List[Dict[str, Any]]:
        """获取项目的辅料信息"""
        try:
            project = db.query(ProjectORM).filter(ProjectORM.id == project_id).first()
            if not project:
                return []

            return project.excipients or []
        except Exception as e:
            logger.error(f"获取项目辅料信息失败: {e}")
            return []

    def get_project_environment(self, project_id: int, db: Session) -> Dict[str, Any]:
        """获取项目的环境信息"""
        try:
            project = db.query(ProjectORM).filter(ProjectORM.id == project_id).first()
            if not project:
                return {}

            return {
                "storage_conditions": project.storage_conditions or [],
                "production_process": getattr(project, 'production_process', None),
                "packaging_material": getattr(project, 'packaging_material', None),
                "storage_condition": getattr(project, 'storage_condition', None)
            }
        except Exception as e:
            logger.error(f"获取项目环境信息失败: {e}")
            return {}

    def update_project_data(self, project_id: int, data_type: str, data: Dict[str, Any], db: Session) -> bool:
        """
        更新项目数据
        data_type: 'drug', 'excipients', 'environment', 'formulation'
        """
        try:
            project = db.query(ProjectORM).filter(ProjectORM.id == project_id).first()
            if not project:
                return False

            if data_type == "drug":
                # 更新或创建药物信息
                if project.drug_id:
                    drug = db.query(DrugORM).filter(DrugORM.id == project.drug_id).first()
                    if drug:
                        for key, value in data.items():
                            if hasattr(drug, key):
                                setattr(drug, key, value)
                else:
                    # 创建新的药物记录
                    drug = DrugORM(id=f"drug_{project_id}", **data)
                    db.add(drug)
                    project.drug_id = drug.id

            elif data_type == "excipients":
                # 更新辅料信息
                # 这里需要根据具体需求实现
                pass

            elif data_type == "environment":
                # 更新环境信息
                if hasattr(project, 'environment_id') and project.environment_id:
                    env = db.query(EnvironmentORM).filter(EnvironmentORM.id == project.environment_id).first()
                    if env:
                        for key, value in data.items():
                            if hasattr(env, key):
                                setattr(env, key, value)
                else:
                    # 创建新的环境记录
                    env = EnvironmentORM(id=f"env_{project_id}", **data)
                    db.add(env)
                    if hasattr(project, 'environment_id'):
                        project.environment_id = env.id

            elif data_type == "formulation":
                # 更新配方信息
                for key, value in data.items():
                    if hasattr(project, key):
                        setattr(project, key, value)

            db.commit()

            # 清除缓存
            self.clear_project_cache(project_id)

            return True

        except Exception as e:
            logger.error(f"更新项目数据失败: {e}")
            db.rollback()
            return False