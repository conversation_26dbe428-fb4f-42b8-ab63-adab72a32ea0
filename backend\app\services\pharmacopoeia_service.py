"""
药典信息集成服务
整合各国药典的公开信息，包括USP-NF, Ph. Eur., JP, ChP等
"""
import logging
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
import aiohttp
import asyncio
from datetime import datetime

from app.services.open_data_service import open_data_service

logger = logging.getLogger(__name__)

# 药典信息配置
PHARMACOPOEIA_SOURCES = {
    "usp": {
        "name": "United States Pharmacopeia",
        "abbreviation": "USP-NF",
        "public_access": [
            {
                "type": "index",
                "url": None,  # USP官方没有完全公开的API
                "description": "USP-NF在线版本需要订阅"
            }
        ]
    },
    "ph_eur": {
        "name": "European Pharmacopoeia",
        "abbreviation": "Ph. Eur.",
        "public_access": [
            {
                "type": "index",
                "url": "https://www.edqm.eu",
                "description": "EDQM提供部分公开信息"
            }
        ]
    },
    "jp": {
        "name": "Japanese Pharmacopoeia",
        "abbreviation": "JP",
        "public_access": [
            {
                "type": "pdf",
                "url": "https://www.mhlw.go.jp/content/11120000/000912390.pdf",
                "description": "JP18英文版PDF（部分章节）"
            }
        ]
    },
    "chp": {
        "name": "Chinese Pharmacopoeia",
        "abbreviation": "ChP",
        "public_access": [
            {
                "type": "index",
                "url": None,
                "description": "中国药典需要购买"
            }
        ]
    }
}

class PharmacopoeiaService:
    """药典信息服务类"""
    
    def __init__(self):
        self.cache_dir = Path("data/pharmacopoeia_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.knowledge_base = self._load_pharmacopoeia_knowledge()
    
    def _load_pharmacopoeia_knowledge(self) -> Dict[str, Any]:
        """加载预构建的药典知识库"""
        knowledge_file = Path("backend/data/pharmacopoeia_knowledge.json")
        if knowledge_file.exists():
            with open(knowledge_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return self._create_initial_knowledge()
    
    def _create_initial_knowledge(self) -> Dict[str, Any]:
        """创建初始药典知识库"""
        return {
            "common_excipients": {
                "乳糖": {
                    "usp": {
                        "monograph": "Lactose Monohydrate",
                        "cas": "64044-51-5",
                        "specifications": {
                            "identification": ["IR", "化学反应"],
                            "purity": ">99.0%",
                            "water_content": "4.5-5.5%",
                            "heavy_metals": "<5ppm",
                            "microbial_limits": "符合USP要求"
                        },
                        "storage": "密闭容器，常温储存"
                    },
                    "ph_eur": {
                        "monograph": "Lactosum monohydricum",
                        "specifications": {
                            "appearance": "白色或几乎白色的结晶性粉末",
                            "solubility": "水中微溶，乙醇中几乎不溶"
                        }
                    }
                },
                "微晶纤维素": {
                    "usp": {
                        "monograph": "Microcrystalline Cellulose",
                        "cas": "9004-34-6",
                        "specifications": {
                            "identification": ["IR", "显微镜检查"],
                            "ph": "5.0-7.5",
                            "loss_on_drying": "<7.0%",
                            "residue_on_ignition": "<0.1%"
                        },
                        "storage": "密闭容器"
                    }
                },
                "硬脂酸镁": {
                    "usp": {
                        "monograph": "Magnesium Stearate",
                        "cas": "557-04-0",
                        "specifications": {
                            "identification": ["IR", "镁盐反应"],
                            "content": "镁含量4.0-5.0%",
                            "loss_on_drying": "<6.0%",
                            "microbial_limits": "符合USP要求"
                        },
                        "storage": "密闭容器",
                        "incompatibilities": "强酸、强氧化剂、碱性物质"
                    }
                }
            },
            "stability_requirements": {
                "usp": {
                    "general_chapters": {
                        "<1150>": "Pharmaceutical Stability",
                        "<1151>": "Pharmaceutical Dosage Forms"
                    },
                    "storage_conditions": {
                        "room_temperature": "20-25°C",
                        "cool": "8-15°C", 
                        "cold": "2-8°C",
                        "freezer": "-25 to -10°C"
                    }
                },
                "ich": {
                    "guidelines": {
                        "Q1A": "Stability Testing of New Drug Substances",
                        "Q1B": "Photostability Testing",
                        "Q1E": "Evaluation of Stability Data"
                    }
                }
            }
        }
    
    def get_excipient_pharmacopoeia_info(self, excipient_name: str) -> Dict[str, Any]:
        """获取辅料的药典信息"""
        info = {}
        
        # 从知识库获取
        if excipient_name in self.knowledge_base.get("common_excipients", {}):
            info = self.knowledge_base["common_excipients"][excipient_name]
        
        # 补充通用信息
        info["general_info"] = {
            "pharmaceutical_grade": "需符合相应药典标准",
            "quality_control": "每批次需进行质量检验",
            "certificate_of_analysis": "供应商应提供COA"
        }
        
        return info
    
    def get_pharmacopoeia_requirements(self, 
                                     substance_type: str,
                                     pharmacopoeia: str = "usp") -> Dict[str, Any]:
        """获取特定物质类型的药典要求"""
        requirements = {
            "excipient": {
                "general_requirements": [
                    "符合药用级别标准",
                    "有完整的质量控制体系",
                    "批次间一致性",
                    "供应商资质认证"
                ],
                "testing_items": [
                    "性状",
                    "鉴别",
                    "纯度检查",
                    "含量测定",
                    "微生物限度",
                    "细菌内毒素（注射用）"
                ]
            },
            "drug_substance": {
                "general_requirements": [
                    "化学结构确证",
                    "理化性质表征",
                    "纯度和杂质谱",
                    "稳定性研究"
                ],
                "testing_items": [
                    "性状",
                    "鉴别（多种方法）",
                    "有关物质",
                    "残留溶剂",
                    "重金属",
                    "含量测定"
                ]
            }
        }
        
        return requirements.get(substance_type, {})
    
    async def search_pharmacopoeia_updates(self, substance_name: str) -> List[Dict[str, Any]]:
        """搜索药典更新信息（从公开渠道）"""
        updates = []
        
        # 这里可以集成各药典的公开更新通知
        # 例如USP的Pharmacopeial Forum等
        
        # 示例：返回模拟的更新信息
        updates.append({
            "source": "USP-NF",
            "date": "2024-01",
            "type": "修订",
            "summary": f"{substance_name}专论更新了微生物限度要求",
            "reference": "Pharmacopeial Forum Vol. 50(1)"
        })
        
        return updates
    
    def validate_excipient_specs(self, 
                                excipient_name: str,
                                test_results: Dict[str, Any]) -> Dict[str, Any]:
        """验证辅料检测结果是否符合药典标准"""
        validation_result = {
            "compliant": True,
            "non_compliances": [],
            "warnings": []
        }
        
        # 获取药典标准
        specs = self.get_excipient_pharmacopoeia_info(excipient_name)
        
        # 对比检测结果与标准
        # 这里可以实现具体的验证逻辑
        
        return validation_result
    
    def generate_pharmacopoeia_summary(self, excipient_list: List[str]) -> Dict[str, Any]:
        """生成配方中所有辅料的药典要求摘要"""
        summary = {
            "excipients": {},
            "common_requirements": [],
            "critical_parameters": [],
            "recommendations": []
        }
        
        for excipient in excipient_list:
            info = self.get_excipient_pharmacopoeia_info(excipient)
            summary["excipients"][excipient] = info
            
            # 提取关键参数
            if "usp" in info and "specifications" in info["usp"]:
                for param, value in info["usp"]["specifications"].items():
                    if param in ["water_content", "ph", "purity"]:
                        summary["critical_parameters"].append({
                            "excipient": excipient,
                            "parameter": param,
                            "specification": value
                        })
        
        # 生成通用要求
        summary["common_requirements"] = [
            "所有辅料应符合相应药典标准",
            "每批辅料需有合格的检验报告",
            "储存条件应满足药典要求",
            "供应商应通过GMP认证"
        ]
        
        # 生成建议
        summary["recommendations"] = [
            "建立辅料质量标准数据库",
            "定期审核供应商资质",
            "对关键辅料进行额外的入厂检验"
        ]
        
        return summary

# 创建全局实例
pharmacopoeia_service = PharmacopoeiaService() 