"""
AI客户端服务
支持多个AI提供商的统一接口
"""

import asyncio
import json
import importlib.util
from typing import Dict, List, Any, Optional
import logging

# 兼容Python 3.8的AsyncGenerator导入
try:
    from typing import AsyncGenerator
except ImportError:
    from typing_extensions import AsyncGenerator

# 安全导入aiohttp - 使用importlib更可靠地检测
aiohttp_spec = importlib.util.find_spec("aiohttp")
AIOHTTP_AVAILABLE = aiohttp_spec is not None

if AIOHTTP_AVAILABLE:
    import aiohttp
    # 只在调试模式下显示版本信息
    # print(f"aiohttp已安装，版本: {aiohttp.__version__}")
else:
    # 使用日志而不是直接打印
    logger = logging.getLogger(__name__)
    logger.warning("aiohttp未安装，AI功能将使用同步模式")
    import requests

from app.config.ai_config import AIConfig, AIProvider

class AIClient:
    """AI客户端"""
    
    def __init__(self, provider: AIProvider = AIConfig.DEFAULT_PROVIDER):
        self.provider = provider
        self.api_key = AIConfig.get_api_key(provider)
        self.endpoint = AIConfig.get_endpoint(provider)
        self.model_config = AIConfig.get_model_config(provider)
        
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            temperature: Optional[float] = None,
                            max_tokens: Optional[int] = None,
                            stream: bool = False) -> Dict[str, Any]:
        """
        聊天完成API调用
        
        参数:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式输出
            
        返回:
            AI响应结果
        """
        headers = self._get_headers()
        
        payload = {
            "model": self.model_config.get("chat", "gpt-3.5-turbo"),
            "messages": messages,
            "temperature": temperature or self.model_config.get("temperature", 0.7),
            "max_tokens": max_tokens or self.model_config.get("max_tokens", 4096),
            "stream": stream
        }
        
        # 根据不同提供商调整payload格式
        if self.provider == AIProvider.DEEPSEEK:
            payload["model"] = "deepseek-chat"
        elif self.provider == AIProvider.GROK:
            payload["model"] = "grok-beta"
        
        try:
            if AIOHTTP_AVAILABLE:
                return await self._async_request(headers, payload, stream)
            else:
                return self._sync_request(headers, payload)
        except Exception as e:
            print(f"AI API调用异常: {e}")
            return self._get_fallback_response(messages)
    
    async def _async_request(self, headers: Dict[str, str], payload: Dict[str, Any], stream: bool) -> Dict[str, Any]:
        """异步请求"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.endpoint}/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    if stream:
                        # 对于流式响应，收集所有数据并返回合并结果
                        stream_content = ""
                        async for chunk in self._handle_stream_response(response):
                            if chunk and chunk.get("content"):
                                stream_content += chunk.get("content", "")
                        
                        return {
                            "content": stream_content,
                            "role": "assistant",
                            "usage": {"total_tokens": 0},
                            "model": payload.get("model", "unknown")
                        }
                    else:
                        result = await response.json()
                        return self._format_response(result)
                else:
                    error_text = await response.text()
                    raise Exception(f"AI API调用失败: {response.status} - {error_text}")
    
    def _sync_request(self, headers: Dict[str, str], payload: Dict[str, Any]) -> Dict[str, Any]:
        """同步请求（备用方案）"""
        response = requests.post(
            f"{self.endpoint}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return self._format_response(result)
        else:
            raise Exception(f"AI API调用失败: {response.status_code} - {response.text}")
    
    async def analyze_stability(self, drug_name: str, stability_data: List[Dict], 
                              conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        稳定性分析
        """
        prompt = AIConfig.get_prompt(
            "stability_analysis",
            drug_name=drug_name,
            stability_data=json.dumps(stability_data, ensure_ascii=False, indent=2),
            conditions=json.dumps(conditions, ensure_ascii=False, indent=2)
        )
        
        messages = [
            {"role": "system", "content": "你是一名资深的药物制剂专家和药物化学专家。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        return {
            "analysis": response.get("content", ""),
            "provider": self.provider.value,
            "model": self.model_config.get("chat", "unknown"),
            "recommendations": self._extract_recommendations(response.get("content", "")),
            "risk_assessment": self._extract_risk_assessment(response.get("content", ""))
        }
    
    async def analyze_compatibility(self, drug_name: str, drug_structure: str,
                                  excipients: List[str], conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        相容性分析
        """
        prompt = AIConfig.get_prompt(
            "compatibility_analysis",
            drug_name=drug_name,
            drug_structure=drug_structure,
            excipients=", ".join(excipients),
            conditions=json.dumps(conditions, ensure_ascii=False, indent=2)
        )
        
        messages = [
            {"role": "system", "content": "你是一名药物制剂和药物化学专家。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        return {
            "analysis": response.get("content", ""),
            "provider": self.provider.value,
            "model": self.model_config.get("chat", "unknown"),
            "mechanisms": self._extract_mechanisms(response.get("content", "")),
            "recommendations": self._extract_recommendations(response.get("content", ""))
        }
    
    async def optimize_formulation(self, drug_name: str, dosage_form: str,
                                 formulation: List[Dict], target_specs: Dict) -> Dict[str, Any]:
        """
        处方优化
        """
        prompt = AIConfig.get_prompt(
            "formulation_optimization",
            drug_name=drug_name,
            dosage_form=dosage_form,
            formulation=json.dumps(formulation, ensure_ascii=False, indent=2),
            target_specs=json.dumps(target_specs, ensure_ascii=False, indent=2)
        )
        
        messages = [
            {"role": "system", "content": "你是一名药物制剂专家。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        return {
            "analysis": response.get("content", ""),
            "provider": self.provider.value,
            "model": self.model_config.get("chat", "unknown"),
            "optimization_suggestions": self._extract_optimization_suggestions(response.get("content", "")),
            "alternative_formulations": self._extract_alternatives(response.get("content", ""))
        }
    
    async def get_regulatory_guidance(self, product_type: str, development_stage: str,
                                    target_market: str, specific_question: str) -> Dict[str, Any]:
        """
        法规指导
        """
        prompt = AIConfig.get_prompt(
            "regulatory_guidance",
            product_type=product_type,
            development_stage=development_stage,
            target_market=target_market,
            specific_question=specific_question
        )
        
        messages = [
            {"role": "system", "content": "你是一名药品注册和法规专家。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        return {
            "guidance": response.get("content", ""),
            "provider": self.provider.value,
            "model": self.model_config.get("chat", "unknown"),
            "regulatory_requirements": self._extract_regulatory_requirements(response.get("content", "")),
            "timeline": self._extract_timeline(response.get("content", ""))
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 根据不同提供商调整请求头
        if self.provider == AIProvider.GROK:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        return headers
    
    def _format_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """格式化响应"""
        try:
            if "choices" in response and len(response["choices"]) > 0:
                choice = response["choices"][0]
                if "message" in choice:
                    return {
                        "content": choice["message"].get("content", ""),
                        "role": choice["message"].get("role", "assistant"),
                        "usage": response.get("usage", {}),
                        "model": response.get("model", "unknown")
                    }
            
            return {"content": "无法解析AI响应", "role": "assistant"}
            
        except Exception as e:
            print(f"响应格式化失败: {e}")
            return {"content": "响应格式化失败", "role": "assistant"}
    
    async def _handle_stream_response(self, response):
        """处理流式响应"""
        async for line in response.content:
            if line:
                try:
                    line_str = line.decode('utf-8').strip()
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            break
                        data = json.loads(data_str)
                        yield self._format_response(data)
                except Exception as e:
                    print(f"流式响应处理失败: {e}")
                    continue
    
    def _get_fallback_response(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """获取备用响应"""
        # 基于消息内容生成智能备用响应
        user_message = ""
        for msg in messages:
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        
        # 根据关键词生成专业建议
        fallback_content = self._generate_professional_fallback(user_message)
        
        return {
            "content": fallback_content,
            "role": "assistant",
            "usage": {"total_tokens": 0},
            "model": "fallback",
            "error": "AI服务不可用，使用专业知识库响应"
        }
    
    def _generate_professional_fallback(self, user_message: str) -> str:
        """生成专业备用响应"""
        if "稳定性" in user_message:
            return """基于药物稳定性的一般原则：
1. 建议按照ICH Q1A(R2)指导原则进行稳定性研究
2. 考虑温度、湿度、光照等环境因素的影响
3. 选择合适的包装材料和储存条件
4. 定期监测关键质量属性的变化
5. 建立科学的货架期预测模型"""
        
        elif "相容性" in user_message:
            return """药物-辅料相容性评估建议：
1. 进行强制降解试验验证相容性
2. 分析可能的化学反应机理
3. 考虑pH、温度、湿度等因素影响
4. 查阅相关文献和案例研究
5. 必要时更换辅料或调整处方"""
        
        elif "处方" in user_message or "配方" in user_message:
            return """处方优化建议：
1. 确保各辅料功能性和用量合理
2. 考虑药物与辅料的相容性
3. 优化工艺参数和质量控制
4. 进行处方筛选和验证试验
5. 符合相关法规和质量标准"""
        
        elif "法规" in user_message:
            return """法规指导建议：
1. 遵循ICH、FDA、NMPA等相关指导原则
2. 制定科学合理的研究计划
3. 确保数据质量和完整性
4. 及时关注法规更新和变化
5. 建议咨询专业法规顾问"""
        
        else:
            return """抱歉，AI服务暂时不可用。建议：
1. 查阅相关专业文献和指导原则
2. 咨询专业技术人员
3. 进行必要的实验验证
4. 稍后重试AI分析功能"""
    
    def _extract_recommendations(self, content: str) -> List[str]:
        """从内容中提取建议"""
        recommendations = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in ['建议', '推荐', '应该', '需要', '考虑']):
                if len(line) > 10:  # 过滤太短的行
                    recommendations.append(line)
        
        return recommendations[:5]  # 最多返回5条建议
    
    def _extract_risk_assessment(self, content: str) -> Dict[str, Any]:
        """从内容中提取风险评估"""
        risk_keywords = {
            "高风险": ["高风险", "严重", "危险", "不建议"],
            "中风险": ["中等风险", "注意", "谨慎", "监测"],
            "低风险": ["低风险", "安全", "可接受", "推荐"]
        }
        
        risk_level = "未知"
        for level, keywords in risk_keywords.items():
            if any(keyword in content for keyword in keywords):
                risk_level = level
                break
        
        return {
            "level": risk_level,
            "factors": self._extract_risk_factors(content)
        }
    
    def _extract_risk_factors(self, content: str) -> List[str]:
        """提取风险因素"""
        factors = []
        risk_terms = ["温度", "湿度", "pH", "光照", "氧化", "水解", "降解"]
        
        for term in risk_terms:
            if term in content:
                factors.append(term)
        
        return factors
    
    def _extract_mechanisms(self, content: str) -> List[str]:
        """提取反应机理"""
        mechanisms = []
        mechanism_terms = ["水解", "氧化", "Maillard反应", "聚合", "异构化", "脱羧", "环化"]
        
        for term in mechanism_terms:
            if term in content:
                mechanisms.append(term)
        
        return mechanisms
    
    def _extract_optimization_suggestions(self, content: str) -> List[Dict[str, str]]:
        """提取优化建议"""
        suggestions = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in ['优化', '改进', '调整', '替换']):
                if len(line) > 15:
                    suggestions.append({
                        "suggestion": line,
                        "category": "处方优化"
                    })
        
        return suggestions[:3]
    
    def _extract_alternatives(self, content: str) -> List[str]:
        """提取替代方案"""
        alternatives = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in ['替代', '备选', '可选', '其他']):
                if len(line) > 10:
                    alternatives.append(line)
        
        return alternatives[:3]
    
    def _extract_regulatory_requirements(self, content: str) -> List[str]:
        """提取法规要求"""
        requirements = []
        reg_terms = ["ICH", "FDA", "NMPA", "EMA", "试验", "标准", "指导原则"]
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(term in line for term in reg_terms):
                if len(line) > 15:
                    requirements.append(line)
        
        return requirements[:5]
    
    def _extract_timeline(self, content: str) -> Dict[str, str]:
        """提取时间节点"""
        timeline = {}
        time_terms = ["月", "年", "周", "天"]
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(term in line for term in time_terms) and any(keyword in line for keyword in ['阶段', '期间', '时间']):
                if len(line) > 10:
                    timeline[f"阶段{len(timeline)+1}"] = line
        
        return timeline

# 创建不同提供商的客户端实例
try:
    openai_client = AIClient(AIProvider.OPENAI)
    deepseek_client = AIClient(AIProvider.DEEPSEEK)
    grok_client = AIClient(AIProvider.GROK)
    
    # 默认客户端
    default_client = deepseek_client
    
    # 只在调试模式下显示初始化信息
    # print("AI客户端初始化成功")
except Exception as e:
    # 使用日志记录错误
    logger = logging.getLogger(__name__)
    logger.error(f"AI客户端初始化失败: {e}")
    # 创建备用客户端
    default_client = None 