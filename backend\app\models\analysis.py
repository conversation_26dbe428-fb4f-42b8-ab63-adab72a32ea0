from pydantic import BaseModel
from typing import Optional
from sqlalchemy import Column, String, DateTime
import datetime

# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base

class AISuggestion(BaseModel):
    title: str
    desc: str
    risk: str
    detail: str
    type: str
    mechanism: Optional[str] = None
    reference: Optional[str] = None

class AnalysisORM(Base):
    __tablename__ = 'analysis'
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow) 