import React, { useState } from 'react';
import { Modal, Button, Input, message } from 'antd';
import { useTranslation } from 'react-i18next';

const FeedbackModal: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [desc, setDesc] = useState('');
  const [contact, setContact] = useState('');
  const { t } = useTranslation();
  const handleSubmit = () => {
    message.success(t('Feedback submitted, thank you!'));
    setOpen(false); setDesc(''); setContact('');
  };
  return (
    <>
      <Button style={{ position: 'fixed', right: 32, bottom: 88, zIndex: 1000 }} onClick={() => setOpen(true)} aria-label={t('Feedback')}>{t('Feedback')}</Button>
      <Modal open={open} onCancel={() => setOpen(false)} onOk={handleSubmit} title={t('Feedback')}>
        <Input.TextArea rows={4} value={desc} onChange={e => setDesc(e.target.value)} placeholder={t('Describe your issue or suggestion')} aria-label={t('Description')} style={{ marginBottom: 12 }} />
        <Input value={contact} onChange={e => setContact(e.target.value)} placeholder={t('Contact (optional)')} aria-label={t('Contact')} />
      </Modal>
    </>
  );
};

export default FeedbackModal; 