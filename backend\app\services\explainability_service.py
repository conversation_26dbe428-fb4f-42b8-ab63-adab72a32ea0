"""
模型可解释性服务
使用SHAP提供机器学习模型的可解释性分析
"""
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union
import json
import base64
from io import BytesIO
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns

# 尝试导入SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except (ImportError, AttributeError) as e:
    SHAP_AVAILABLE = False
    logging.warning(f"SHAP导入失败，可解释性功能将不可用: {e}")
    # 创建一个模拟的shap模块
    class MockShap:
        class TreeExplainer:
            def __init__(self, *args, **kwargs):
                pass
        class LinearExplainer:
            def __init__(self, *args, **kwargs):
                pass
        class DeepExplainer:
            def __init__(self, *args, **kwargs):
                pass
        class Explainer:
            def __init__(self, *args, **kwargs):
                pass
    shap = MockShap()

logger = logging.getLogger(__name__)


class ExplainabilityService:
    """模型可解释性服务类"""
    
    def __init__(self):
        self.shap_available = SHAP_AVAILABLE
        self.explainer = None
        self.feature_names = None
        
    def initialize_explainer(self, model, model_type: str = "tree", 
                           feature_names: Optional[List[str]] = None):
        """初始化SHAP解释器"""
        if not self.shap_available:
            logger.error("SHAP未安装，无法初始化解释器")
            return False
            
        try:
            self.feature_names = feature_names
            
            if model_type == "tree":
                # 对于树模型（如RandomForest, XGBoost）
                self.explainer = shap.TreeExplainer(model)
            elif model_type == "linear":
                # 对于线性模型
                self.explainer = shap.LinearExplainer(model)
            elif model_type == "deep":
                # 对于深度学习模型
                self.explainer = shap.DeepExplainer(model)
            else:
                # 通用解释器（较慢但适用于任何模型）
                self.explainer = shap.Explainer(model)
                
            logger.info(f"成功初始化{model_type}类型的SHAP解释器")
            return True
            
        except Exception as e:
            logger.error(f"初始化SHAP解释器失败: {e}")
            return False
    
    def explain_prediction(self, X: Union[np.ndarray, pd.DataFrame], 
                         prediction_index: int = 0) -> Dict[str, Any]:
        """解释单个预测"""
        if not self.shap_available or self.explainer is None:
            return {"error": "SHAP解释器未初始化"}
            
        try:
            # 计算SHAP值
            if isinstance(X, pd.DataFrame):
                shap_values = self.explainer.shap_values(X.iloc[[prediction_index]])
            else:
                shap_values = self.explainer.shap_values(X[[prediction_index]])
            
            # 处理多输出情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
            
            # 获取特征值
            if isinstance(X, pd.DataFrame):
                feature_values = X.iloc[prediction_index].values
                feature_names = list(X.columns) if self.feature_names is None else self.feature_names
            else:
                feature_values = X[prediction_index]
                feature_names = self.feature_names or [f"Feature_{i}" for i in range(X.shape[1])]
            
            # 创建特征重要性字典
            feature_importance = []
            for i, (name, value, shap_val) in enumerate(zip(feature_names, feature_values, shap_values[0])):
                feature_importance.append({
                    "feature": name,
                    "value": float(value),
                    "shap_value": float(shap_val),
                    "impact": "positive" if shap_val > 0 else "negative",
                    "magnitude": abs(float(shap_val))
                })
            
            # 按影响程度排序
            feature_importance.sort(key=lambda x: x["magnitude"], reverse=True)
            
            # 生成解释
            explanation = {
                "base_value": float(self.explainer.expected_value),
                "prediction_value": float(self.explainer.expected_value + sum(shap_values[0])),
                "feature_importance": feature_importance[:10],  # 前10个最重要的特征
                "interpretation": self._generate_interpretation(feature_importance)
            }
            
            return explanation
            
        except Exception as e:
            logger.error(f"生成预测解释失败: {e}")
            return {"error": str(e)}
    
    def explain_dataset(self, X: Union[np.ndarray, pd.DataFrame], 
                       sample_size: int = 100) -> Dict[str, Any]:
        """解释整个数据集"""
        if not self.shap_available or self.explainer is None:
            return {"error": "SHAP解释器未初始化"}
            
        try:
            # 如果数据集太大，进行采样
            if len(X) > sample_size:
                if isinstance(X, pd.DataFrame):
                    X_sample = X.sample(n=sample_size, random_state=42)
                else:
                    indices = np.random.choice(len(X), sample_size, replace=False)
                    X_sample = X[indices]
            else:
                X_sample = X
            
            # 计算SHAP值
            shap_values = self.explainer.shap_values(X_sample)
            
            # 处理多输出情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
            
            # 获取特征名称
            if isinstance(X, pd.DataFrame):
                feature_names = list(X.columns) if self.feature_names is None else self.feature_names
            else:
                feature_names = self.feature_names or [f"Feature_{i}" for i in range(X.shape[1])]
            
            # 计算全局特征重要性
            global_importance = []
            mean_abs_shap = np.mean(np.abs(shap_values), axis=0)
            
            for i, (name, importance) in enumerate(zip(feature_names, mean_abs_shap)):
                global_importance.append({
                    "feature": name,
                    "importance": float(importance),
                    "rank": i + 1
                })
            
            # 按重要性排序
            global_importance.sort(key=lambda x: x["importance"], reverse=True)
            
            # 生成摘要
            summary = {
                "sample_size": len(X_sample),
                "global_feature_importance": global_importance[:10],
                "insights": self._generate_global_insights(global_importance)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成数据集解释失败: {e}")
            return {"error": str(e)}
    
    def generate_waterfall_plot(self, X: Union[np.ndarray, pd.DataFrame], 
                              prediction_index: int = 0) -> Optional[str]:
        """生成瀑布图（返回base64编码的图像）"""
        if not self.shap_available or self.explainer is None:
            return None
            
        try:
            # 计算SHAP值
            if isinstance(X, pd.DataFrame):
                shap_values = self.explainer(X.iloc[[prediction_index]])
            else:
                shap_values = self.explainer(X[[prediction_index]])
            
            # 创建瀑布图
            plt.figure(figsize=(10, 6))
            shap.plots.waterfall(shap_values[0], show=False)
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            plt.close()
            
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"生成瀑布图失败: {e}")
            return None
    
    def generate_summary_plot(self, X: Union[np.ndarray, pd.DataFrame], 
                            plot_type: str = "bar") -> Optional[str]:
        """生成摘要图（返回base64编码的图像）"""
        if not self.shap_available or self.explainer is None:
            return None
            
        try:
            # 计算SHAP值
            shap_values = self.explainer.shap_values(X)
            
            # 处理多输出情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
            
            # 创建摘要图
            plt.figure(figsize=(10, 6))
            
            if plot_type == "bar":
                shap.summary_plot(shap_values, X, plot_type="bar", show=False)
            else:
                shap.summary_plot(shap_values, X, show=False)
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            plt.close()
            
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"生成摘要图失败: {e}")
            return None
    
    def generate_force_plot(self, X: Union[np.ndarray, pd.DataFrame], 
                          prediction_index: int = 0) -> Optional[str]:
        """生成力图（返回HTML）"""
        if not self.shap_available or self.explainer is None:
            return None
            
        try:
            # 计算SHAP值
            if isinstance(X, pd.DataFrame):
                shap_values = self.explainer.shap_values(X.iloc[[prediction_index]])
                feature_values = X.iloc[prediction_index].values
            else:
                shap_values = self.explainer.shap_values(X[[prediction_index]])
                feature_values = X[prediction_index]
            
            # 处理多输出情况
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
            
            # 生成力图HTML
            force_plot = shap.force_plot(
                self.explainer.expected_value,
                shap_values[0],
                feature_values,
                feature_names=self.feature_names
            )
            
            # 获取HTML内容
            html_content = force_plot._repr_html_()
            
            return html_content
            
        except Exception as e:
            logger.error(f"生成力图失败: {e}")
            return None
    
    def _generate_interpretation(self, feature_importance: List[Dict[str, Any]]) -> str:
        """生成人类可读的解释"""
        interpretation = []
        
        # 获取前3个最重要的特征
        top_features = feature_importance[:3]
        
        for feature in top_features:
            impact = "增加" if feature["impact"] == "positive" else "降低"
            interpretation.append(
                f"{feature['feature']}（值为{feature['value']:.2f}）{impact}了预测值"
            )
        
        return "；".join(interpretation) + "。"
    
    def _generate_global_insights(self, global_importance: List[Dict[str, Any]]) -> List[str]:
        """生成全局洞察"""
        insights = []
        
        # 最重要的特征
        if global_importance:
            top_feature = global_importance[0]
            insights.append(f"最重要的特征是{top_feature['feature']}，对模型预测的平均影响为{top_feature['importance']:.3f}")
        
        # 特征分组
        high_impact = [f for f in global_importance if f["importance"] > 0.1]
        if len(high_impact) > 1:
            insights.append(f"共有{len(high_impact)}个特征对模型预测有显著影响（SHAP值>0.1）")
        
        # 特征集中度
        if len(global_importance) >= 5:
            top5_importance = sum(f["importance"] for f in global_importance[:5])
            total_importance = sum(f["importance"] for f in global_importance)
            concentration = top5_importance / total_importance * 100
            insights.append(f"前5个特征贡献了{concentration:.1f}%的预测影响")
        
        return insights
    
    def calculate_feature_interactions(self, X: Union[np.ndarray, pd.DataFrame], 
                                     feature1_idx: int, feature2_idx: int) -> Dict[str, Any]:
        """计算特征交互作用"""
        if not self.shap_available or self.explainer is None:
            return {"error": "SHAP解释器未初始化"}
            
        try:
            # 计算SHAP交互值
            shap_interaction_values = self.explainer.shap_interaction_values(X)
            
            # 获取特定特征对的交互值
            interaction_values = shap_interaction_values[:, feature1_idx, feature2_idx]
            
            # 统计信息
            interaction_stats = {
                "mean_interaction": float(np.mean(interaction_values)),
                "std_interaction": float(np.std(interaction_values)),
                "max_interaction": float(np.max(interaction_values)),
                "min_interaction": float(np.min(interaction_values))
            }
            
            return interaction_stats
            
        except Exception as e:
            logger.error(f"计算特征交互失败: {e}")
            return {"error": str(e)}


# 创建全局实例
explainability_service = ExplainabilityService() 