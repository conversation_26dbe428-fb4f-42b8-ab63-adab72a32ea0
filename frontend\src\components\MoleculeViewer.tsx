import React, { useEffect, useRef } from 'react';
import { Spin, Alert } from 'antd';

interface MoleculeViewerProps {
  smiles: string;
  width?: number;
  height?: number;
  options?: any;
  style?: React.CSSProperties;
}

/**
 * 分子结构查看器组件
 * 使用RDKit或SmilesDrawer渲染分子结构
 */
const MoleculeViewer: React.FC<MoleculeViewerProps> = ({ 
  smiles, 
  width = 300, 
  height = 200,
  options = {},
  style = {}
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    if (!smiles) {
      setError('未提供SMILES结构');
      setLoading(false);
      return;
    }

    const renderMolecule = async () => {
      try {
        setLoading(true);
        setError(null);

        // 尝试使用RDKitJS渲染分子
        if (window.RDKit) {
          const mol = window.RDKit.Mol.fromSmiles(smiles);
          if (mol) {
            if (canvasRef.current) {
              const canvas = canvasRef.current;
              mol.draw(canvas, options);
            }
          } else {
            setError('无法解析SMILES结构');
          }
        }
        // 备用方案：使用SmilesDrawer
        else if (window.SmilesDrawer) {
          const smilesDrawer = new window.SmilesDrawer.Drawer({
            width,
            height,
            ...options
          });

          smilesDrawer.draw(smiles, canvasRef.current, (err: any) => {
            if (err) {
              setError('渲染分子结构失败');
              console.error(err);
            }
          });
        }
        // 使用后端API渲染图像
        else {
          const response = await fetch('/api/visualize', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              smiles: smiles,
              width: width,
              height: height
            })
          });

          if (response.ok) {
            const data = await response.json();
            if (data.image) {
              const img = new Image();
              img.onload = () => {
                if (canvasRef.current) {
                  const ctx = canvasRef.current.getContext('2d');
                  ctx?.clearRect(0, 0, width, height);
                  ctx?.drawImage(img, 0, 0, width, height);
                }
              };
              img.src = data.image; // data.image 已经是 base64 格式
            } else {
              setError('后端未返回图像数据');
            }
          } else {
            const errorData = await response.json().catch(() => ({}));
            setError(`获取分子结构图像失败: ${errorData.detail || response.statusText}`);
          }
        }
      } catch (err) {
        console.error('渲染分子结构错误:', err);
        setError('渲染分子结构时发生错误');
      } finally {
        setLoading(false);
      }
    };

    renderMolecule();
  }, [smiles, width, height]);

  return (
    <div style={{ width, height, ...style }}>
      {loading && <Spin tip="加载分子结构..." />}
      {error && <Alert message={error} type="error" />}
      <canvas 
        ref={canvasRef} 
        width={width} 
        height={height}
        style={{ display: loading || error ? 'none' : 'block' }}
      />
    </div>
  );
};

// 为全局Window对象添加RDKit和SmilesDrawer类型定义
declare global {
  interface Window {
    RDKit?: any;
    SmilesDrawer?: any;
  }
}

export default MoleculeViewer;
