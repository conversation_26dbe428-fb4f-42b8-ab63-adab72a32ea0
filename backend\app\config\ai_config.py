"""
AI服务配置
支持多个AI提供商：OpenAI、DeepSeek、Grok
"""

import os
from enum import Enum
from typing import Dict, Any, Optional

class AIProvider(Enum):
    """AI提供商枚举"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    GROK = "grok"

class AIConfig:
    """AI配置类"""
    
    # API密钥配置 - 从环境变量读取，确保安全性
    API_KEYS = {
        AIProvider.OPENAI: os.getenv("OPENAI_API_KEY", ""),
        AIProvider.DEEPSEEK: os.getenv("DEEPSEEK_API_KEY", ""),
        AIProvider.GROK: os.getenv("GROK_API_KEY", "")
    }
    
    # API端点配置
    API_ENDPOINTS = {
        AIProvider.OPENAI: "https://api.openai.com/v1",
        AIProvider.DEEPSEEK: "https://api.deepseek.com/v1",
        AIProvider.GROK: "https://api.x.ai/v1"
    }
    
    # 模型配置
    MODELS = {
        AIProvider.OPENAI: {
            "chat": "gpt-4o-mini",
            "embedding": "text-embedding-3-small",
            "max_tokens": 4096,
            "temperature": 0.7
        },
        AIProvider.DEEPSEEK: {
            "chat": "deepseek-chat",
            "embedding": "deepseek-embedding",
            "max_tokens": 4096,
            "temperature": 0.7
        },
        AIProvider.GROK: {
            "chat": "grok-beta",
            "embedding": "grok-embedding",
            "max_tokens": 4096,
            "temperature": 0.7
        }
    }
    
    # 默认提供商
    DEFAULT_PROVIDER = AIProvider.DEEPSEEK
    
    # 专业提示词模板
    PROMPTS = {
        "stability_analysis": """
作为一名资深的药物制剂专家和药物化学专家，请基于以下稳定性数据进行专业分析：

药物信息：{drug_name}
稳定性数据：{stability_data}
环境条件：{conditions}

请从以下角度进行分析：
1. 降解动力学分析（零级、一级、二级反应）
2. 阿伦尼乌斯方程应用和活化能计算
3. ICH指导原则符合性评估
4. 货架期预测和置信区间
5. 储存条件优化建议
6. 包装材料选择建议
7. 质量控制策略

请提供科学、准确、可操作的专业建议。
""",
        
        "compatibility_analysis": """
作为一名药物制剂和药物化学专家，请分析以下药物-辅料相容性：

药物：{drug_name}
分子结构：{drug_structure}
辅料：{excipients}
环境条件：{conditions}

请从以下角度分析：
1. 化学反应机理（水解、氧化、Maillard反应等）
2. 官能团反应活性评估
3. 环境因素影响（温度、湿度、pH、光照）
4. 文献和案例分析
5. 风险等级评估
6. 缓解策略和预防措施
7. 替代辅料建议

请基于药物化学原理提供专业评估。
""",
        
        "formulation_optimization": """
作为药物制剂专家，请对以下处方进行优化分析：

药物：{drug_name}
剂型：{dosage_form}
处方组成：{formulation}
目标规格：{target_specs}

请从以下角度分析：
1. 处方合理性评估
2. 辅料功能性分析
3. 工艺适应性评估
4. 质量属性预测
5. 稳定性风险评估
6. 成本效益分析
7. 优化建议和替代方案

请提供符合制剂学原理的专业建议。
""",
        
        "regulatory_guidance": """
作为药品注册和法规专家，请提供以下方面的指导：

产品类型：{product_type}
开发阶段：{development_stage}
目标市场：{target_market}
具体问题：{specific_question}

请从以下角度提供指导：
1. 适用的法规要求（ICH、FDA、NMPA等）
2. 必需的研究项目和试验设计
3. 质量标准制定建议
4. 注册申报策略
5. 风险评估和控制
6. 时间节点规划
7. 常见问题和解决方案

请提供准确、实用的法规指导。
"""
    }
    
    @classmethod
    def get_api_key(cls, provider: AIProvider) -> str:
        """获取API密钥"""
        return cls.API_KEYS.get(provider, "")
    
    @classmethod
    def get_endpoint(cls, provider: AIProvider) -> str:
        """获取API端点"""
        return cls.API_ENDPOINTS.get(provider, "")
    
    @classmethod
    def get_model_config(cls, provider: AIProvider) -> Dict[str, Any]:
        """获取模型配置"""
        return cls.MODELS.get(provider, {})
    
    @classmethod
    def get_prompt(cls, prompt_type: str, **kwargs) -> str:
        """获取格式化的提示词"""
        template = cls.PROMPTS.get(prompt_type, "")
        return template.format(**kwargs)

# 环境变量覆盖（如果设置了环境变量，优先使用）
for provider in AIProvider:
    env_key = f"AI_{provider.value.upper()}_API_KEY"
    if os.getenv(env_key):
        AIConfig.API_KEYS[provider] = os.getenv(env_key) 