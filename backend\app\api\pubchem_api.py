"""
PubChem API端点
提供化合物查询、生物活性数据和相似性搜索等功能
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from app.api.auth import get_current_user
from app.services.pubchem_service import pubchem_service

router = APIRouter()


class CompoundSearchRequest(BaseModel):
    """化合物搜索请求"""
    name: str = Field(..., description="化合物名称")
    include_bioactivity: bool = Field(True, description="是否包含生物活性数据")
    include_patents: bool = Field(False, description="是否包含专利信息")


class SimilaritySearchRequest(BaseModel):
    """相似性搜索请求"""
    smiles: str = Field(..., description="查询化合物的SMILES")
    threshold: float = Field(0.8, ge=0.0, le=1.0, description="相似度阈值")
    max_results: int = Field(10, ge=1, le=50, description="最大返回结果数")


class CompoundResponse(BaseModel):
    """化合物信息响应"""
    cid: int
    name: str
    iupac_name: Optional[str]
    molecular_formula: str
    molecular_weight: float
    smiles: str
    inchi: Optional[str]
    inchi_key: Optional[str]
    synonyms: List[str]
    properties: Dict[str, Any]
    bioactivity_summary: Optional[Dict[str, Any]]
    patent_info: Optional[Dict[str, Any]]


@router.post("/search/compound", response_model=CompoundResponse)
async def search_compound(
    request: CompoundSearchRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    搜索化合物信息
    
    从PubChem数据库搜索化合物，返回详细信息包括：
    - 基本信息（名称、分子式、分子量等）
    - 结构信息（SMILES、InChI等）
    - 物化性质（LogP、TPSA等）
    - 生物活性摘要（可选）
    - 专利信息（可选）
    """
    try:
        async with pubchem_service as service:
            # 搜索化合物
            compound_info = await service.search_by_name(request.name)
            
            if not compound_info:
                raise HTTPException(
                    status_code=404,
                    detail=f"未找到化合物: {request.name}"
                )
            
            # 获取额外信息
            cid = compound_info.get("PubChemCID")
            
            if request.include_patents and cid:
                patent_info = await service.get_patent_info(cid)
                compound_info["PatentInfo"] = patent_info
            
            # 构建响应
            response = CompoundResponse(
                cid=cid,
                name=request.name,
                iupac_name=compound_info.get("IUPACName"),
                molecular_formula=compound_info.get("MolecularFormula", ""),
                molecular_weight=compound_info.get("MolecularWeight", 0),
                smiles=compound_info.get("CanonicalSMILES", ""),
                inchi=compound_info.get("InChI"),
                inchi_key=compound_info.get("InChIKey"),
                synonyms=compound_info.get("Synonyms", []),
                properties={
                    "xlogp": compound_info.get("XLogP"),
                    "tpsa": compound_info.get("TPSA"),
                    "h_bond_donor_count": compound_info.get("HBondDonorCount"),
                    "h_bond_acceptor_count": compound_info.get("HBondAcceptorCount"),
                    "rotatable_bond_count": compound_info.get("RotatableBondCount")
                },
                bioactivity_summary=compound_info.get("BioactivitySummary") if request.include_bioactivity else None,
                patent_info=compound_info.get("PatentInfo") if request.include_patents else None
            )
            
            return response
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compound/{cid}")
async def get_compound_by_cid(
    cid: int,
    current_user: dict = Depends(get_current_user)
):
    """
    通过CID获取化合物信息
    
    Args:
        cid: PubChem化合物ID
    
    Returns:
        化合物详细信息
    """
    try:
        async with pubchem_service as service:
            compound_info = await service.get_compound_by_cid(cid)
            
            if not compound_info:
                raise HTTPException(
                    status_code=404,
                    detail=f"未找到CID为{cid}的化合物"
                )
            
            return compound_info
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/similar")
async def search_similar_compounds(
    request: SimilaritySearchRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    搜索相似化合物
    
    使用Tanimoto相似度在PubChem数据库中搜索相似化合物
    
    Args:
        request: 相似性搜索参数
    
    Returns:
        相似化合物列表，按相似度排序
    """
    try:
        async with pubchem_service as service:
            similar_compounds = await service.search_similar_compounds(
                smiles=request.smiles,
                threshold=request.threshold,
                max_results=request.max_results
            )
            
            return {
                "query_smiles": request.smiles,
                "threshold": request.threshold,
                "total_found": len(similar_compounds),
                "compounds": similar_compounds
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compound/{cid}/bioactivity")
async def get_compound_bioactivity(
    cid: int,
    current_user: dict = Depends(get_current_user)
):
    """
    获取化合物的生物活性数据
    
    Args:
        cid: PubChem化合物ID
    
    Returns:
        生物活性测试摘要
    """
    try:
        async with pubchem_service as service:
            bioactivity = await service._get_bioactivity_summary(cid)
            
            if not bioactivity:
                return {
                    "cid": cid,
                    "message": "暂无生物活性数据"
                }
            
            return {
                "cid": cid,
                "bioactivity_summary": bioactivity
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compound/{cid}/patents")
async def get_compound_patents(
    cid: int,
    current_user: dict = Depends(get_current_user)
):
    """
    获取化合物的专利信息
    
    Args:
        cid: PubChem化合物ID
    
    Returns:
        相关专利列表
    """
    try:
        async with pubchem_service as service:
            patent_info = await service.get_patent_info(cid)
            
            return {
                "cid": cid,
                "patent_info": patent_info
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compound/{cid}/pharmacology")
async def get_compound_pharmacology(
    cid: int,
    current_user: dict = Depends(get_current_user)
):
    """
    获取化合物的药理学数据
    
    Args:
        cid: PubChem化合物ID
    
    Returns:
        药理学分类和作用机制
    """
    try:
        async with pubchem_service as service:
            pharmacology = await service.get_pharmacology_data(cid)
            
            return {
                "cid": cid,
                "pharmacology": pharmacology
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch/search")
async def batch_search_compounds(
    names: List[str] = Query(..., description="化合物名称列表"),
    current_user: dict = Depends(get_current_user)
):
    """
    批量搜索化合物
    
    Args:
        names: 化合物名称列表（最多20个）
    
    Returns:
        搜索结果列表
    """
    if len(names) > 20:
        raise HTTPException(
            status_code=400,
            detail="批量搜索最多支持20个化合物"
        )
    
    try:
        results = []
        async with pubchem_service as service:
            for name in names:
                compound_info = await service.search_by_name(name)
                results.append({
                    "query": name,
                    "found": compound_info is not None,
                    "compound": compound_info
                })
        
        return {
            "total_queries": len(names),
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compound/search/autocomplete")
async def autocomplete_compound_name(
    query: str = Query(..., min_length=2, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="返回结果数量"),
    current_user: dict = Depends(get_current_user)
):
    """
    化合物名称自动补全
    
    基于输入的部分名称返回可能的化合物列表
    
    Args:
        query: 搜索关键词（至少2个字符）
        limit: 返回结果数量限制
    
    Returns:
        匹配的化合物名称列表
    """
    try:
        # 这里可以实现更复杂的自动补全逻辑
        # 暂时返回简单的示例
        
        # 可以维护一个常用化合物名称列表或使用缓存
        common_compounds = [
            "Aspirin", "Ibuprofen", "Acetaminophen", "Caffeine",
            "Glucose", "Lactose", "Starch", "Cellulose",
            "Magnesium Stearate", "Silicon Dioxide"
        ]
        
        # 简单的模糊匹配
        matches = [
            name for name in common_compounds 
            if query.lower() in name.lower()
        ][:limit]
        
        return {
            "query": query,
            "suggestions": matches
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 