"""
批量数据导入服务
支持Excel/CSV文件的批量导入功能
"""
import logging
import pandas as pd
import json
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import tempfile
import traceback
from datetime import datetime
import asyncio
from io import BytesIO
from app.config.database import SessionLocal

logger = logging.getLogger(__name__)


class DataImportService:
    """批量数据导入服务类"""
    
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls', '.csv']
        self.import_templates = self._create_templates()
        self.validation_rules = self._create_validation_rules()
    
    def _create_templates(self) -> Dict[str, Dict[str, Any]]:
        """创建导入模板定义"""
        return {
            "excipients": {
                "name": "辅料信息导入模板",
                "required_columns": [
                    "name", "cas_number", "category", "function"
                ],
                "optional_columns": [
                    "synonyms", "appearance", "melting_point", "density",
                    "solubility_water", "solubility_organic", "hygroscopicity",
                    "storage_requirements", "incompatibilities", "typical_concentration"
                ],
                "column_mapping": {
                    "辅料名称": "name",
                    "CAS号": "cas_number",
                    "类别": "category",
                    "功能": "function",
                    "同义词": "synonyms",
                    "外观": "appearance",
                    "熔点": "melting_point",
                    "密度": "density",
                    "水中溶解度": "solubility_water",
                    "有机溶剂溶解度": "solubility_organic",
                    "吸湿性": "hygroscopicity",
                    "储存条件": "storage_requirements",
                    "配伍禁忌": "incompatibilities",
                    "典型用量": "typical_concentration"
                }
            },
            "drugs": {
                "name": "药物信息导入模板",
                "required_columns": [
                    "name", "cas_number", "smiles"
                ],
                "optional_columns": [
                    "category", "molecular_weight", "logp", "pka",
                    "solubility", "bcs_class", "indication"
                ],
                "column_mapping": {
                    "药物名称": "name",
                    "CAS号": "cas_number",
                    "SMILES": "smiles",
                    "类别": "category",
                    "分子量": "molecular_weight",
                    "LogP": "logp",
                    "pKa": "pka",
                    "溶解度": "solubility",
                    "BCS分类": "bcs_class",
                    "适应症": "indication"
                }
            },
            "stability_data": {
                "name": "稳定性数据导入模板",
                "required_columns": [
                    "drug_name", "test_condition", "time_point", "value"
                ],
                "optional_columns": [
                    "temperature", "humidity", "unit", "batch_no", "test_method"
                ],
                "column_mapping": {
                    "药物名称": "drug_name",
                    "试验条件": "test_condition",
                    "时间点": "time_point",
                    "检测值": "value",
                    "温度": "temperature",
                    "湿度": "humidity",
                    "单位": "unit",
                    "批号": "batch_no",
                    "检测方法": "test_method"
                }
            }
        }
    
    def _create_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """创建验证规则"""
        return {
            "cas_number": {
                "type": "regex",
                "pattern": r"^\d{2,7}-\d{2}-\d$",
                "message": "CAS号格式不正确，应为：XXXXX-XX-X"
            },
            "smiles": {
                "type": "custom",
                "validator": "validate_smiles",
                "message": "SMILES字符串无效"
            },
            "temperature": {
                "type": "range",
                "min": -273,
                "max": 200,
                "message": "温度值应在-273到200之间"
            },
            "humidity": {
                "type": "range",
                "min": 0,
                "max": 100,
                "message": "湿度值应在0到100之间"
            },
            "molecular_weight": {
                "type": "range",
                "min": 0,
                "max": 5000,
                "message": "分子量应在0到5000之间"
            }
        }
    
    async def import_data(
        self,
        file_content: bytes,
        file_name: str,
        data_type: str,
        validate_only: bool = False,
        update_existing: bool = False
    ) -> Dict[str, Any]:
        """
        导入数据
        
        Args:
            file_content: 文件内容
            file_name: 文件名
            data_type: 数据类型（excipients/drugs/stability_data）
            validate_only: 仅验证不导入
            update_existing: 是否更新已存在的记录
            
        Returns:
            导入结果
        """
        try:
            # 检查文件格式
            file_ext = Path(file_name).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "success": False,
                    "error": f"不支持的文件格式: {file_ext}",
                    "supported_formats": self.supported_formats
                }
            
            # 检查数据类型
            if data_type not in self.import_templates:
                return {
                    "success": False,
                    "error": f"不支持的数据类型: {data_type}",
                    "supported_types": list(self.import_templates.keys())
                }
            
            # 读取文件
            df = self._read_file(file_content, file_ext)
            if df is None:
                return {
                    "success": False,
                    "error": "无法读取文件内容"
                }
            
            # 验证数据
            validation_result = await self._validate_data(df, data_type)
            
            if validate_only:
                return {
                    "success": validation_result["valid"],
                    "validation": validation_result,
                    "preview": self._generate_preview(df, validation_result)
                }
            
            # 如果验证失败，返回错误
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "validation": validation_result,
                    "error": "数据验证失败，请检查错误信息"
                }
            
            # 执行导入
            import_result = await self._perform_import(
                df, data_type, validation_result, update_existing
            )
            
            return {
                "success": import_result["success"],
                "import_result": import_result,
                "validation": validation_result,
                "summary": self._generate_summary(import_result)
            }
            
        except Exception as e:
            logger.error(f"数据导入失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def _read_file(self, file_content: bytes, file_ext: str) -> Optional[pd.DataFrame]:
        """读取文件内容"""
        try:
            if file_ext == '.csv':
                # 尝试不同的编码
                for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                    try:
                        return pd.read_csv(BytesIO(file_content), encoding=encoding)
                    except:
                        continue
                return None
            else:
                # Excel文件
                return pd.read_excel(BytesIO(file_content))
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return None
    
    async def _validate_data(self, df: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """验证数据"""
        template = self.import_templates[data_type]
        errors = []
        warnings = []
        
        # 标准化列名
        df = self._standardize_columns(df, template)
        
        # 检查必需列
        missing_columns = []
        for col in template["required_columns"]:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            errors.append({
                "type": "missing_columns",
                "message": f"缺少必需列: {', '.join(missing_columns)}",
                "columns": missing_columns
            })
        
        # 检查数据有效性
        for idx, row in df.iterrows():
            row_errors = await self._validate_row(row, int(idx) + 2, data_type)  # +2 因为Excel从1开始，且有表头
            errors.extend(row_errors)
        
        # 检查重复数据
        if data_type == "excipients" and "name" in df.columns:
            duplicates = df[df.duplicated(subset=["name"], keep=False)]
            if not duplicates.empty:
                warnings.append({
                    "type": "duplicates",
                    "message": f"发现{len(duplicates)}条重复的辅料名称",
                    "rows": duplicates.index.tolist()
                })
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "total_rows": len(df),
            "valid_rows": len(df) - len(set(e.get("row", -1) for e in errors if e.get("row")))
        }
    
    def _standardize_columns(self, df: pd.DataFrame, template: Dict) -> pd.DataFrame:
        """标准化列名"""
        mapping = template["column_mapping"]
        
        # 创建反向映射
        reverse_mapping = {v: k for k, v in mapping.items()}
        
        # 重命名列
        new_columns = {}
        for col in df.columns:
            if col in mapping:
                new_columns[col] = mapping[col]
            elif col in reverse_mapping:
                new_columns[col] = col  # 已经是标准名称
            else:
                new_columns[col] = col.lower().replace(' ', '_')
        
        df = df.rename(columns=new_columns)
        return df
    
    async def _validate_row(self, row: pd.Series, row_num: int, data_type: str) -> List[Dict]:
        """验证单行数据"""
        errors = []
        
        # 特定字段验证
        for field, value in row.items():
            if pd.isna(value):
                continue
                
            # CAS号验证
            if field == "cas_number" and field in self.validation_rules:
                rule = self.validation_rules[field]
                import re
                if not re.match(rule["pattern"], str(value)):
                    errors.append({
                        "row": row_num,
                        "column": field,
                        "value": value,
                        "message": rule["message"]
                    })
            
            # 数值范围验证
            elif field in ["temperature", "humidity", "molecular_weight"] and field in self.validation_rules:
                rule = self.validation_rules[field]
                try:
                    num_value = float(value)
                    if num_value < rule["min"] or num_value > rule["max"]:
                        errors.append({
                            "row": row_num,
                            "column": field,
                            "value": value,
                            "message": rule["message"]
                        })
                except:
                    errors.append({
                        "row": row_num,
                        "column": field,
                        "value": value,
                        "message": f"{field}必须是数值"
                    })
            
            # SMILES验证
            elif field == "smiles":
                # 这里应该调用structure_service进行验证
                # 暂时跳过
                pass
        
        return errors
    
    async def _perform_import(
        self,
        df: pd.DataFrame,
        data_type: str,
        validation_result: Dict,
        update_existing: bool
    ) -> Dict[str, Any]:
        """执行实际的数据导入"""
        imported = 0
        updated = 0
        failed = 0
        errors = []
        
        # 根据数据类型调用相应的导入方法
        if data_type == "excipients":
            result = await self._import_excipients(df, update_existing)
        elif data_type == "drugs":
            result = await self._import_drugs(df, update_existing)
        elif data_type == "stability_data":
            result = await self._import_stability_data(df, update_existing)
        else:
            return {
                "success": False,
                "error": f"未实现的导入类型: {data_type}"
            }
        
        return result
    
    async def _import_excipients(self, df: pd.DataFrame, update_existing: bool) -> Dict[str, Any]:
        """导入辅料数据"""
        from app.services.excipient_data_service import ExcipientDataService  # 延迟导入
        db = SessionLocal()
        excipient_service = ExcipientDataService(db)
        imported = 0
        updated = 0
        failed = 0
        errors = []
        for idx, row in df.iterrows():
            try:
                excipient_data = {
                    "name": row.get("name"),
                    "cas_number": row.get("cas_number"),
                    "category": row.get("category"),
                    "function": row.get("function"),
                    "synonyms": row.get("synonyms", ""),
                    "appearance": row.get("appearance", ""),
                    "melting_point": row.get("melting_point", ""),
                    "density": row.get("density", ""),
                    "solubility_water": row.get("solubility_water", ""),
                    "solubility_organic": row.get("solubility_organic", ""),
                    "hygroscopicity": row.get("hygroscopicity", ""),
                    "storage_requirements": row.get("storage_requirements", ""),
                    "incompatibilities": row.get("incompatibilities", ""),
                    "typical_concentration": row.get("typical_concentration", "")
                }
                existing = excipient_service.get_excipient_info(excipient_data["name"])
                if existing and not update_existing:
                    errors.append({
                        "row": int(idx) + 2,
                        "error": f"辅料 {excipient_data['name']} 已存在"
                    })
                    failed += 1
                    continue
                if existing and update_existing:
                    # 更新现有辅料
                    for key, value in excipient_data.items():
                        setattr(existing, key, value)
                    db.commit()
                    db.refresh(existing)
                    updated += 1
                else:
                    # 创建新辅料
                    from types import SimpleNamespace
                    class Dummy:
                        def dict(self):
                            return excipient_data
                    new_excipient = excipient_service.create_excipient(Dummy())
                    if new_excipient:
                        imported += 1
                    else:
                        failed += 1
                        errors.append({
                            "row": int(idx) + 2,
                            "error": f"创建辅料 {excipient_data['name']} 失败"
                        })
            except Exception as e:
                failed += 1
                errors.append({
                    "row": int(idx) + 2,
                    "error": str(e)
                })
        db.close()
        return {
            "success": failed == 0,
            "imported": imported,
            "updated": updated,
            "failed": failed,
            "errors": errors
        }
    
    async def _import_drugs(self, df: pd.DataFrame, update_existing: bool) -> Dict[str, Any]:
        """
        导入药物数据
        """
        from app.models.drug import DrugORM
        db = SessionLocal()
        imported = 0
        updated = 0
        failed = 0
        errors = []
        for idx, row in df.iterrows():
            try:
                # 验证SMILES
                smiles = row.get("smiles", "")
                # 可调用结构验证服务
                # ...existing code...
                # 实现药物数据的实际存储
                name = row.get("name")
                cas = row.get("cas_number")
                existing = db.query(DrugORM).filter(DrugORM.name == name).first()
                if existing and not update_existing:
                    errors.append({"row": int(idx) + 2, "error": f"药物 {name} 已存在"})
                    failed += 1
                    continue
                if existing and update_existing:
                    for key in ["cas", "smiles", "formula", "molecular_weight", "logp", "pka", "solubility"]:
                        if key in row:
                            setattr(existing, key, row.get(key))
                    db.commit()
                    db.refresh(existing)
                    updated += 1
                else:
                    drug = DrugORM(
                        name=name,
                        cas=cas,
                        smiles=row.get("smiles"),
                        formula=row.get("formula"),
                        molecular_weight=row.get("molecular_weight"),
                        logp=row.get("logp"),
                        pka=row.get("pka"),
                        solubility=row.get("solubility"),
                        structure_image_url=row.get("structure_image_url")
                    )
                    db.add(drug)
                    db.commit()
                    db.refresh(drug)
                    imported += 1
            except Exception as e:
                failed += 1
                errors.append({"row": int(idx) + 2, "error": str(e)})
        db.close()
        return {
            "success": failed == 0,
            "imported": imported,
            "updated": updated,
            "failed": failed,
            "errors": errors
        }

    async def _import_stability_data(self, df: pd.DataFrame, update_existing: bool) -> Dict[str, Any]:
        """
        导入稳定性数据
        """
        from app.models.stability import StabilityDataORM
        import uuid
        db = SessionLocal()
        imported = 0
        updated = 0
        failed = 0
        errors = []
        for idx, row in df.iterrows():
            try:
                # 校验必需字段
                if pd.isna(row.get("drug_name")) or pd.isna(row.get("time_point")) or pd.isna(row.get("value")):
                    errors.append({"row": int(idx) + 2, "error": "缺少必需字段"})
                    failed += 1
                    continue
                # 查找是否已存在（可根据 drug_id, time_point, item, batch 唯一）
                drug_name = row.get("drug_name")
                time_point = row.get("time_point")
                value = row.get("value")
                item = row.get("item", "含量")
                batch = row.get("batch_no", "")
                # 这里假设 drug_id 可通过名称查找
                from app.models.drug import DrugORM
                drug = db.query(DrugORM).filter(DrugORM.name == drug_name).first()
                if not drug:
                    errors.append({"row": int(idx) + 2, "error": f"找不到药物: {drug_name}"})
                    failed += 1
                    continue
                existing = db.query(StabilityDataORM).filter(
                    StabilityDataORM.drug_id == drug.id,
                    StabilityDataORM.time_point == time_point,
                    StabilityDataORM.item == item,
                    StabilityDataORM.batch == batch
                ).first()
                if existing and not update_existing:
                    errors.append({"row": int(idx) + 2, "error": "该时间点数据已存在"})
                    failed += 1
                    continue
                if existing and update_existing:
                    existing.value = value
                    existing.updated_at = datetime.now()
                    db.commit()
                    db.refresh(existing)
                    updated += 1
                else:
                    stability = StabilityDataORM(
                        id=str(uuid.uuid4()),
                        project_id=row.get("project_id", ""),
                        drug_id=drug.id,
                        time_point=time_point,
                        value=value,
                        item=item,
                        unit=row.get("unit", "%"),
                        batch=batch,
                        method=row.get("test_method", "")
                    )
                    db.add(stability)
                    db.commit()
                    db.refresh(stability)
                    imported += 1
            except Exception as e:
                failed += 1
                errors.append({"row": int(idx) + 2, "error": str(e)})
        db.close()
        return {
            "success": failed == 0,
            "imported": imported,
            "updated": updated,
            "failed": failed,
            "errors": errors
        }
    
    def _generate_preview(self, df: pd.DataFrame, validation_result: Dict) -> Dict[str, Any]:
        """生成预览数据"""
        # 获取前10行数据
        preview_df = df.head(10)
        
        # 标记错误行
        error_rows = set(e.get("row", -1) - 2 for e in validation_result["errors"] if e.get("row"))
        
        return {
            "data": preview_df.to_dict('records'),
            "columns": list(preview_df.columns),
            "total_rows": len(df),
            "preview_rows": len(preview_df),
            "error_rows": list(error_rows)
        }
    
    def _generate_summary(self, import_result: Dict) -> Dict[str, Any]:
        """生成导入摘要"""
        return {
            "total": import_result.get("imported", 0) + import_result.get("updated", 0) + import_result.get("failed", 0),
            "success": import_result.get("imported", 0) + import_result.get("updated", 0),
            "imported": import_result.get("imported", 0),
            "updated": import_result.get("updated", 0),
            "failed": import_result.get("failed", 0),
            "timestamp": datetime.now().isoformat()
        }
    
    def generate_template(self, data_type: str) -> bytes:
        """生成导入模板Excel文件"""
        if data_type not in self.import_templates:
            raise ValueError(f"不支持的数据类型: {data_type}")
        
        template = self.import_templates[data_type]
        
        # 创建示例数据
        # 移除_create_sample_data等所有示例/模拟数据生成逻辑，所有数据导入和校验必须通过真实数据和后端逻辑实现。
        
        # 创建DataFrame
        columns = template["required_columns"] + template["optional_columns"]
        df = pd.DataFrame(columns=columns)
        
        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 写入数据
            df.to_excel(writer, sheet_name='数据', index=False)
            
            # 写入说明
            instructions = self._create_instructions(data_type)
            instructions_df = pd.DataFrame(instructions)
            instructions_df.to_excel(writer, sheet_name='说明', index=False)
            
        output.seek(0)
        return output.getvalue()
    
    def _create_instructions(self, data_type: str) -> List[Dict]:
        """创建导入说明"""
        template = self.import_templates[data_type]
        
        instructions = [
            {"项目": "数据类型", "说明": template["name"]},
            {"项目": "必填字段", "说明": ", ".join(template["required_columns"])},
            {"项目": "选填字段", "说明": ", ".join(template["optional_columns"])},
            {"项目": "注意事项", "说明": "请确保CAS号格式正确，数值字段不要包含单位"}
        ]
        
        return instructions


# 创建全局实例
data_import_service = DataImportService()