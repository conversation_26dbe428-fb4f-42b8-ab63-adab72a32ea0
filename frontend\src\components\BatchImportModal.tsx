import React, { useRef, useState, useEffect } from 'react';
import { Modal, Upload, Button, Progress, Alert, Table, Typography } from 'antd';
import { InboxOutlined, DownloadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import { apiFetch } from '../api';

const { Dragger } = Upload;
const { Text, Link } = Typography;

interface BatchImportModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (data: any[]) => void;
  templateUrl: string;
  entityName: string;
  templateFields?: string[];
  i18n?: { [k: string]: string };
}

// 导入历史
function getImportHistory(entity: string) {
  try {
    return JSON.parse(localStorage.getItem(`import_history_${entity}`) || '[]');
  } catch {
    return [];
  }
}
function addImportHistory(entity: string, record: any) {
  const history = getImportHistory(entity);
  history.unshift(record);
  localStorage.setItem(`import_history_${entity}`, JSON.stringify(history.slice(0, 10)));
}
function clearImportHistory(entity: string) {
  localStorage.removeItem(`import_history_${entity}`);
}

const columns = [
  { title: '行', dataIndex: 'row', key: 'row' },
  { title: '字段', dataIndex: 'field', key: 'field' },
  { title: '错误', dataIndex: 'error', key: 'error' },
];

const BatchImportModal: React.FC<BatchImportModalProps> = ({ open, onClose, onSuccess, templateUrl, entityName, templateFields, i18n }) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<{ success: boolean; data: any[]; errors: any[] } | null>(null);
  const [errorReportUrl, setErrorReportUrl] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [importHistory, setImportHistory] = useState<any[]>(() => getImportHistory(entityName));

  useEffect(() => {
    apiFetch<any[]>('/api/import/history').then(res => setImportHistory(res || []));
  }, []);

  const handleUpload = async (file: File) => {
    setUploading(true);
    setProgress(30);
    try {
      const res = await apiFetch<{ success: boolean; data: any[]; errors: any[] }>(
        '/api/import', {
          method: 'POST',
          body: JSON.stringify({ file: file }),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      setProgress(100);
      if (res && res.success) {
        setResult(res);
        onSuccess(res.data);
      } else if (res) {
        setResult(res);
      } else {
        setResult(null);
      }
      // 新增：写入导入历史
      const now = new Date();
      addImportHistory(entityName, {
        time: now.toLocaleString(),
        entity: entityName,
        file: file.name,
        result: res && res.errors && res.data ? (res.errors.length > 0 ? (res.data.length > 0 ? '部分成功' : '失败') : '成功') : '未知',
        errorCount: res && res.errors ? res.errors.length : 0,
        dataCount: res && res.data ? res.data.length : 0,
      });
      setImportHistory(getImportHistory(entityName));
    } catch (error) {
      console.error('导入失败', error);
      setUploading(false);
      setProgress(0);
      setResult(null);
      setErrorReportUrl(null);
    }
  };

  const beforeUpload = (file: File) => {
    setFileList([{
      uid: `${Date.now()}-${file.name}`,
      name: file.name,
      status: 'uploading',
      percent: 0,
      originFileObj: file as any,
    }]);
    handleUpload(file);
    return false;
  };

  const handleRemove = () => {
    setFileList([]);
    setResult(null);
    setProgress(0);
    setErrorReportUrl(null);
  };

  // 模板自定义下载
  const handleTemplateDownload = () => {
    if (templateFields && templateFields.length > 0) {
      const csv = templateFields.join(',') + '\n';
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${entityName}_import_template.csv`;
      a.click();
      URL.revokeObjectURL(url);
    } else {
      window.open(templateUrl, '_blank');
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      title={`批量导入${entityName}`}
      footer={null}
      width={600}
      style={{ fontFamily: 'SimSun, serif' }}
      styles={{ body: { borderRadius: 12 } }}
    >
      <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', gap: 16 }}>
        <Dragger
          name="file"
          multiple={false}
          fileList={fileList}
          beforeUpload={beforeUpload}
          onRemove={handleRemove}
          accept=".xlsx,.xls,.csv"
          showUploadList={{ showRemoveIcon: true }}
          style={{ borderRadius: 8, background: '#f6f8fa', border: '1px dashed #1976d2', width: 340 }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ color: '#1976d2', fontSize: 32 }} />
          </p>
          <p style={{ color: '#1976d2', fontWeight: 500 }}>拖拽或点击上传Excel/CSV文件</p>
        </Dragger>
        <div>
          <Button type="link" onClick={handleTemplateDownload} style={{ marginLeft: 8 }} icon={<DownloadOutlined />}>
            {i18n?.downloadTemplate || '下载模板'}
          </Button>
        </div>
      </div>
      {uploading && <Progress percent={progress} status="active" style={{ marginBottom: 12 }} />}
      {result && (
        <div style={{ marginBottom: 12 }}>
          {result.errors.length === 0 ? (
            <Alert message="导入成功" type="success" showIcon style={{ borderRadius: 8, fontFamily: 'SimSun' }} />
          ) : (
            <Alert
              message={result.data.length > 0 ? '部分成功，部分数据有错误' : '导入失败'}
              type={result.data.length > 0 ? 'warning' : 'error'}
              showIcon
              style={{ borderRadius: 8, fontFamily: 'SimSun' }}
            />
          )}
        </div>
      )}
      {result && result.errors.length > 0 && (
        <div style={{ marginBottom: 12 }}>
          <Table
            columns={columns}
            dataSource={result.errors}
            rowKey={r => `${r.row}-${r.field}`}
            pagination={false}
            size="small"
            bordered
            style={{ fontFamily: 'SimSun', borderRadius: 8 }}
          />
          {errorReportUrl && (
            <Button type="link" href={errorReportUrl} download="错误报告.csv" style={{ marginTop: 8 }}>
              导出错误报告
            </Button>
          )}
        </div>
      )}
      {/* 导入历史（模拟） */}
      <div style={{ marginTop: 16, background: '#f6f8fa', borderRadius: 8, padding: 12 }}>
        <Text strong>{i18n?.importHistory || '最近导入记录：'}</Text>
        {importHistory.length === 0 ? (
          <div style={{ fontSize: 13, color: '#888' }}>{i18n?.noHistory || '暂无记录'}</div>
        ) : (
          <>
            {importHistory.map((h, idx) => (
              <div key={idx} style={{ fontSize: 13, color: '#888' }}>
                {h.time} {h.entity} {h.file}（{h.result}，{h.dataCount}条，{h.errorCount > 0 ? `错误${h.errorCount}条` : '无错误'}）
              </div>
            ))}
            <Button size="small" type="link" onClick={() => { clearImportHistory(entityName); setImportHistory([]); }} style={{ color: '#d32f2f' }}>{i18n?.clearHistory || '清空历史'}</Button>
          </>
        )}
      </div>
    </Modal>
  );
};

export default BatchImportModal; 