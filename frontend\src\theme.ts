// 主题色配置与切换工具
export type Theme = 'blue' | 'gray' | 'dark';

export const themeColors: Record<Theme, {
  primary: string;
  primaryHover: string;
  primaryActive: string;
  background: string;
  contentBackground: string;
  text: string;
  textSecondary: string;
  borderColor: string;
  borderLight: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
}> = {
  blue: {
    primary: '#1890ff',
    primaryHover: '#40a9ff',
    primaryActive: '#096dd9',
    background: '#f0f2f5',
    contentBackground: '#ffffff',
    text: '#262626',
    textSecondary: '#8c8c8c',
    borderColor: '#d9d9d9',
    borderLight: '#f0f0f0',
    successColor: '#52c41a',
    warningColor: '#faad14',
    errorColor: '#ff4d4f'
  },
  gray: {
    primary: '#595959',
    primaryHover: '#737373',
    primaryActive: '#434343',
    background: '#f5f5f5',
    contentBackground: '#ffffff',
    text: '#262626',
    textSecondary: '#8c8c8c',
    borderColor: '#d9d9d9',
    borderLight: '#f0f0f0',
    successColor: '#52c41a',
    warningColor: '#faad14',
    errorColor: '#ff4d4f'
  },
  dark: {
    primary: '#177ddc',
    primaryHover: '#3c9ae8',
    primaryActive: '#0958d9',
    background: '#141414',
    contentBackground: '#1f1f1f',
    text: '#ffffff',
    textSecondary: '#a6a6a6',
    borderColor: '#434343',
    borderLight: '#303030',
    successColor: '#73d13d',
    warningColor: '#ffc53d',
    errorColor: '#ff7875'
  },
};

const THEME_KEY = 'app_theme';

export function setTheme(theme: Theme) {
  const colors = themeColors[theme];
  const root = document.documentElement;

  // 设置所有CSS变量
  root.style.setProperty('--primary-color', colors.primary);
  root.style.setProperty('--primary-hover', colors.primaryHover);
  root.style.setProperty('--primary-active', colors.primaryActive);
  root.style.setProperty('--background-color', colors.background);
  root.style.setProperty('--content-background', colors.contentBackground);
  root.style.setProperty('--text-color', colors.text);
  root.style.setProperty('--text-secondary', colors.textSecondary);
  root.style.setProperty('--border-color', colors.borderColor);
  root.style.setProperty('--border-light', colors.borderLight);
  root.style.setProperty('--success-color', colors.successColor);
  root.style.setProperty('--warning-color', colors.warningColor);
  root.style.setProperty('--error-color', colors.errorColor);

  localStorage.setItem(THEME_KEY, theme);
}

export function getTheme(): Theme {
  const t = localStorage.getItem(THEME_KEY);
  if (t && ['blue', 'gray', 'dark'].includes(t)) return t as Theme;
  return 'blue';
}

export function applyTheme() {
  setTheme(getTheme());
} 