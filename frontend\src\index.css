:root {
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  --background-color: #f0f2f5;
  --content-background: #ffffff;
  --text-color: #262626;
  --text-secondary: #8c8c8c;
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --border-radius: 6px;
  --border-radius-lg: 8px;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-color);
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.5715;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 全局样式优化 */
* {
  box-sizing: border-box;
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background: var(--background-color) !important;
}

.ant-layout-content {
  background: var(--background-color) !important;
  padding: 24px !important;
  min-height: calc(100vh - 64px) !important;
}

.ant-btn-primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: var(--shadow-light) !important;
  border-radius: var(--border-radius) !important;
  font-weight: 500 !important;
  height: 36px !important;
  padding: 4px 16px !important;
  font-size: 14px !important;
}

.ant-btn-primary:hover {
  background: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium) !important;
}

.ant-btn-primary:active {
  background: var(--primary-active) !important;
  border-color: var(--primary-active) !important;
  transform: translateY(0);
}

.ant-btn {
  border-radius: var(--border-radius) !important;
  font-weight: 500 !important;
  height: 36px !important;
  padding: 4px 16px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light) !important;
}

.ant-card {
  background: var(--content-background) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  border: 1px solid var(--border-light) !important;
  margin-bottom: 16px !important;
}

.ant-card-head {
  border-bottom: 1px solid var(--border-light) !important;
  padding: 16px 24px !important;
}

.ant-card-head-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
}

.ant-card-body {
  padding: 24px !important;
}

.ant-table {
  font-size: 14px !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  border-bottom: 1px solid var(--border-light) !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  font-size: 14px !important;
  padding: 16px !important;
}

.ant-table-tbody > tr > td {
  padding: 16px !important;
  font-size: 14px !important;
  border-bottom: 1px solid var(--border-light) !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

.ant-input {
  border-radius: var(--border-radius) !important;
  border: 1px solid var(--border-color) !important;
  font-size: 14px !important;
  height: 36px !important;
  padding: 4px 12px !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.ant-select {
  font-size: 14px !important;
}

.ant-select-selector {
  border-radius: var(--border-radius) !important;
  border: 1px solid var(--border-color) !important;
  height: 36px !important;
}

.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.ant-modal-content {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-medium) !important;
}

.ant-modal-header {
  border-bottom: 1px solid var(--border-light) !important;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
}

.ant-modal-title {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5 {
  color: var(--text-color) !important;
  font-weight: 600 !important;
}

.ant-typography {
  color: var(--text-color) !important;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-hover);
}

/* 自定义工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

/* 表格空状态优化 */
.ant-empty {
  padding: 40px 20px !important;
}

.ant-empty-description {
  color: var(--text-secondary) !important;
  font-size: 14px !important;
}

.ant-table-placeholder {
  padding: 60px 20px !important;
}

.ant-table-placeholder .ant-empty-description {
  font-size: 16px !important;
  color: var(--text-secondary) !important;
}

/* 菜单样式优化 */
.ant-menu {
  border-right: none !important;
  background: var(--content-background) !important;
}

.ant-menu-item {
  margin: 4px 8px !important;
  border-radius: var(--border-radius) !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important;
}

.ant-menu-item-selected {
  background: rgba(24, 144, 255, 0.1) !important;
  color: var(--primary-color) !important;
}

.ant-menu-item:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  color: var(--primary-color) !important;
}

.ant-menu-submenu-title {
  margin: 4px 8px !important;
  border-radius: var(--border-radius) !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.ant-menu-submenu-title:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  color: var(--primary-color) !important;
}

/* 分页器样式优化 */
.ant-pagination {
  text-align: center !important;
  margin-top: 16px !important;
}

.ant-pagination-item {
  border-radius: var(--border-radius) !important;
  border: 1px solid var(--border-color) !important;
}

.ant-pagination-item-active {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active a {
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 16px !important;
  }

  .ant-card-body {
    padding: 16px !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 13px !important;
  }

  .ant-menu-item,
  .ant-menu-submenu-title {
    height: 36px !important;
    line-height: 36px !important;
    font-size: 13px !important;
  }
}
