import React, { useState } from 'react';
import {
  Card,
  Upload,
  Button,
  Select,
  Space,
  Alert,
  Table,
  Tag,
  Steps,
  Result,
  Progress,
  Descriptions,
  Typography,
  message,
  Modal,
  Row,
  Col,
  Divider,
  Statistic
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { UploadProps } from 'antd/lib/upload';
import axios from 'axios';

const { Step } = Steps;
const { Option } = Select;
const { Title, Text } = Typography;
const { Dragger } = Upload;

interface BatchImporterProps {
  onImportComplete?: (result: any) => void;
}

interface ImportResult {
  success: boolean;
  validation?: {
    valid: boolean;
    errors: any[];
    warnings: any[];
    total_rows: number;
    valid_rows: number;
  };
  preview?: {
    data: any[];
    columns: string[];
    total_rows: number;
    error_rows: number[];
  };
  summary?: {
    total: number;
    success: number;
    imported: number;
    updated: number;
    failed: number;
  };
}

export const BatchImporter: React.FC<BatchImporterProps> = ({ onImportComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [dataType, setDataType] = useState<string>('excipients');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [validateOnly, setValidateOnly] = useState(true);

  const dataTypeOptions = [
    { value: 'excipients', label: '辅料信息', icon: <FileExcelOutlined /> },
    { value: 'drugs', label: '药物信息', icon: <FileExcelOutlined /> },
    { value: 'stability_data', label: '稳定性数据', icon: <FileExcelOutlined /> }
  ];

  // 下载模板
  const downloadTemplate = async () => {
    try {
      const response = await axios.get(`/api/data-import/template/${dataType}`, {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data as BlobPart]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${dataType}_import_template.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      message.success('模板下载成功');
    } catch (error) {
      message.error('模板下载失败');
    }
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file) => {
      setSelectedFile(file);
      return false; // 阻止自动上传
    },
    onRemove: () => {
      setSelectedFile(null);
      setImportResult(null);
    }
  };

  // 验证数据
  const validateData = async () => {
    if (!selectedFile) {
      message.error('请先选择文件');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await axios.post<ImportResult>('/api/data-import/import', formData, {
        params: {
          data_type: dataType,
          validate_only: true,
          update_existing: false
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setImportResult(response.data);
      if (response.data.validation?.valid) {
        message.success('数据验证通过');
        setCurrentStep(2);
      } else {
        message.warning('数据验证发现问题，请查看详情');
        setCurrentStep(2);
      }
    } catch (error) {
      message.error('数据验证失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行导入
  const performImport = async () => {
    if (!selectedFile) {
      message.error('请先选择文件');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await axios.post<ImportResult>('/api/data-import/import', formData, {
        params: {
          data_type: dataType,
          validate_only: false,
          update_existing: true
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setImportResult(response.data);
      if (response.data.success) {
        message.success('数据导入成功');
        setCurrentStep(3);
        onImportComplete?.(response.data);
      } else {
        message.error('数据导入失败');
      }
    } catch (error) {
      message.error('数据导入失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderDataTypeSelection();
      case 1:
        return renderFileUpload();
      case 2:
        return renderValidationResult();
      case 3:
        return renderImportResult();
      default:
        return null;
    }
  };

  // 选择数据类型
  const renderDataTypeSelection = () => (
    <div style={{ textAlign: 'center', padding: '40px 0' }}>
      <Title level={4}>选择要导入的数据类型</Title>
      <Select
        value={dataType}
        onChange={setDataType}
        style={{ width: 300, marginBottom: 24 }}
        size="large"
      >
        {dataTypeOptions.map(option => (
          <Option key={option.value} value={option.value}>
            <Space>
              {option.icon}
              {option.label}
            </Space>
          </Option>
        ))}
      </Select>
      
      <div>
        <Button type="link" onClick={downloadTemplate}>
          <DownloadOutlined /> 下载导入模板
        </Button>
      </div>
      
      <div style={{ marginTop: 24 }}>
        <Button type="primary" size="large" onClick={() => setCurrentStep(1)}>
          下一步
        </Button>
      </div>
    </div>
  );

  // 文件上传
  const renderFileUpload = () => (
    <div style={{ padding: '20px 0' }}>
      <Dragger {...uploadProps} style={{ marginBottom: 24 }}>
        <p className="ant-upload-drag-icon">
          <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式文件
        </p>
      </Dragger>

      {selectedFile && (
        <Alert
          message={`已选择文件: ${selectedFile.name}`}
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={16}>
        <Col span={12}>
          <Button block onClick={() => setCurrentStep(0)}>
            上一步
          </Button>
        </Col>
        <Col span={12}>
          <Button 
            type="primary" 
            block 
            onClick={validateData}
            disabled={!selectedFile}
            loading={loading}
          >
            验证数据
          </Button>
        </Col>
      </Row>
    </div>
  );

  // 验证结果
  const renderValidationResult = () => {
    if (!importResult) return null;

    const { validation, preview } = importResult;
    const hasErrors = validation && validation.errors.length > 0;
    const hasWarnings = validation && validation.warnings.length > 0;

    return (
      <div>
        {validation && (
          <>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总行数"
                    value={validation.total_rows}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="有效行数"
                    value={validation.valid_rows}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="错误数"
                    value={validation.errors.length}
                    valueStyle={{ color: hasErrors ? '#f5222d' : '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="警告数"
                    value={validation.warnings.length}
                    valueStyle={{ color: hasWarnings ? '#faad14' : '#52c41a' }}
                  />
                </Card>
              </Col>
            </Row>

            {hasErrors && (
              <Alert
                message="发现数据错误"
                description="请修正以下错误后重新上传"
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            {validation.errors.length > 0 && (
              <Card title="错误详情" style={{ marginBottom: 16 }}>
                <Table
                  dataSource={validation.errors}
                  columns={[
                    {
                      title: '行号',
                      dataIndex: 'row',
                      width: 80
                    },
                    {
                      title: '列名',
                      dataIndex: 'column',
                      width: 120
                    },
                    {
                      title: '错误值',
                      dataIndex: 'value',
                      ellipsis: true
                    },
                    {
                      title: '错误信息',
                      dataIndex: 'message',
                      ellipsis: true
                    }
                  ]}
                  size="small"
                  pagination={{ pageSize: 5 }}
                />
              </Card>
            )}

            {validation.warnings.length > 0 && (
              <Card title="警告信息" style={{ marginBottom: 16 }}>
                {validation.warnings.map((warning: any, index: number) => (
                  <Alert
                    key={index}
                    message={warning.message}
                    type="warning"
                    showIcon
                    style={{ marginBottom: 8 }}
                  />
                ))}
              </Card>
            )}
          </>
        )}

        <Row gutter={16}>
          <Col span={8}>
            <Button block onClick={() => setCurrentStep(1)}>
              重新上传
            </Button>
          </Col>
          <Col span={8}>
            <Button block onClick={() => setCurrentStep(0)}>
              返回首页
            </Button>
          </Col>
          <Col span={8}>
            <Button 
              type="primary" 
              block 
              onClick={performImport}
              disabled={hasErrors}
              loading={loading}
            >
              {hasErrors ? '请先修正错误' : '执行导入'}
            </Button>
          </Col>
        </Row>
      </div>
    );
  };

  // 导入结果
  const renderImportResult = () => {
    if (!importResult || !importResult.summary) return null;

    const { summary } = importResult;
    const success = summary.failed === 0;

    return (
      <Result
        status={success ? 'success' : 'warning'}
        title={success ? '数据导入成功' : '数据导入部分成功'}
        subTitle={
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="总记录数">{summary.total}</Descriptions.Item>
              <Descriptions.Item label="成功数">{summary.success}</Descriptions.Item>
              <Descriptions.Item label="新增数">{summary.imported}</Descriptions.Item>
              <Descriptions.Item label="更新数">{summary.updated}</Descriptions.Item>
              <Descriptions.Item label="失败数">{summary.failed}</Descriptions.Item>
            </Descriptions>
          </Space>
        }
        extra={[
          <Button key="new" onClick={() => {
            setCurrentStep(0);
            setSelectedFile(null);
            setImportResult(null);
          }}>
            导入新数据
          </Button>,
          <Button key="close" type="primary" onClick={() => window.location.reload()}>
            完成
          </Button>
        ]}
      />
    );
  };

  return (
    <Card title="批量数据导入" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Steps current={currentStep} style={{ marginBottom: 32 }}>
        <Step title="选择类型" />
        <Step title="上传文件" />
        <Step title="验证数据" />
        <Step title="导入结果" />
      </Steps>
      
      {renderStepContent()}
    </Card>
  );
}; 