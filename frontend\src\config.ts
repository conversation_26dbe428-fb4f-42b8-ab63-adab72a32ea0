// API URLs
export const API_BASE_URL = '/api';

// API Endpoints
export const ENDPOINTS = {
  // Project endpoints
  PROJECTS: `${API_BASE_URL}/projects`,
  PROJECT_DETAIL: (id: string) => `${API_BASE_URL}/projects/${id}`,
  
  // Stability prediction endpoints
  STABILITY_PREDICT: `${API_BASE_URL}/stability/predict`,
  
  // Excipient analysis endpoints
  EXCIPIENT_COMPATIBILITY: `${API_BASE_URL}/compatibility/assess`,
  EXCIPIENT_LIST: `${API_BASE_URL}/excipients/common`,
  
  // Formulation analysis endpoints
  FORMULATION_ANALYZE: `${API_BASE_URL}/formulation/analyze`,
  
  // User management endpoints
  USERS: `${API_BASE_URL}/users`,
  USER_DETAIL: (id: number) => `${API_BASE_URL}/users/${id}`,
  LOGIN: `${API_BASE_URL}/login`,
  LOGOUT: `${API_BASE_URL}/logout`,
};

// Application settings
export const APP_SETTINGS = {
  PAGE_SIZE: 10,
  DATE_FORMAT: 'YYYY-MM-DD',
  TIME_FORMAT: 'HH:mm:ss',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
};

// Storage keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USERNAME: 'username',
  PROJECTS: 'projects',
  CURRENT_PROJECT: 'currentProject',
  INPUT_DATA: 'inputData',
  ANALYSIS_RESULT: 'analysisResult',
};

// Default values
export const DEFAULT_VALUES = {
  TEMPERATURE: 25,
  HUMIDITY: 60,
  PH: 7,
  PREDICTION_MONTHS: 36,
};

// Theme settings
export const THEME_SETTINGS = {
  PRIMARY_COLOR: '#1890ff',
  SUCCESS_COLOR: '#52c41a',
  WARNING_COLOR: '#faad14',
  ERROR_COLOR: '#f5222d',
};

export default {
  API_BASE_URL,
  ENDPOINTS,
  APP_SETTINGS,
  STORAGE_KEYS,
  DEFAULT_VALUES,
  THEME_SETTINGS,
}; 