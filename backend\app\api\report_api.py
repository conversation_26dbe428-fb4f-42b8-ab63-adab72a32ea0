"""
报告生成API端点
提供PDF报告生成和下载功能
"""
import logging
import json
from pathlib import Path
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import base64
from datetime import datetime
from functools import lru_cache
import uuid
from sqlalchemy.orm import Session

from app.api.auth import get_current_user
from app.services.report_generation_service import ReportGenerationService
from app.config.database import get_db

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/report", tags=["Reports"])


class ReportGenerationRequest(BaseModel):
    """报告生成请求"""
    analysis_data: Dict[str, Any] = Field(..., description="分析数据")
    language: str = Field("zh", description="报告语言（zh/en）")
    include_raw_data: bool = Field(False, description="是否包含原始数据")


class ReportTemplateRequest(BaseModel):
    """报告模板请求"""
    template_type: str = Field(..., description="模板类型")
    customizations: Optional[Dict[str, Any]] = Field(None, description="自定义设置")


class ReportResponse(BaseModel):
    """报告响应"""
    success: bool
    filename: Optional[str]
    pdf_data: Optional[str]  # Base64编码的PDF数据
    size: Optional[int]
    generated_at: Optional[str]
    error: Optional[str]


def load_system_config():
    config_path = Path("config/system_config.json")
    if not config_path.exists():
        raise RuntimeError("system_config.json not found")
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def get_report_service(db: Session = Depends(get_db)) -> ReportGenerationService:
    config = load_system_config()
    return ReportGenerationService(db=db, config=config)


def get_report_templates():
    template_path = Path("app/config/report_templates.json")
    if not template_path.exists():
        return []
    with open(template_path, 'r', encoding='utf-8') as f:
        return json.load(f)


@router.post("/generate", status_code=202)
async def generate_report(
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    service: ReportGenerationService = Depends(get_report_service),
    current_user: dict = Depends(get_current_user)
):
    """
    Starts the generation of a PDF report in the background.
    """
    try:
        if not request.analysis_data:
            raise HTTPException(status_code=400, detail="分析数据不能为空")
        
        task_id = str(uuid.uuid4())
        analysis_data = request.analysis_data.copy()
        analysis_data['generated_by'] = current_user.get('username', 'System')
        analysis_data['generation_date'] = datetime.now().isoformat()
        analysis_data['task_id'] = task_id

        background_tasks.add_task(
            service.generate_and_save_stability_report,
            analysis_data=analysis_data,
            language=request.language,
            include_raw_data=request.include_raw_data
        )
        
        return {
            "message": "报告生成任务已开始。",
            "task_id": task_id,
            "status_url": f"/api/report/status/{task_id}"
        }
    except Exception as e:
        logger.error(f"Failed to start report generation task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"无法启动报告生成任务: {e}")


@router.get("/download/{report_id}")
async def download_report(
    report_id: str,
    service: ReportGenerationService = Depends(get_report_service)
):
    report_path = service.get_report_by_id(report_id)
    if not report_path or not report_path.exists():
        raise HTTPException(status_code=404, detail="报告文件未找到")
    
    return FileResponse(
        path=report_path,
        filename=report_path.name,
        media_type="application/pdf"
    )


@router.post("/generate-from-analysis/{analysis_id}")
async def generate_report_from_analysis(
    analysis_id: int,
    background_tasks: BackgroundTasks,
    language: str = Query("zh", description="报告语言"),
    include_raw_data: bool = Query(False, description="是否包含原始数据"),
    service: ReportGenerationService = Depends(get_report_service),
    current_user: dict = Depends(get_current_user)
):
    analysis_data = service.get_analysis_data(analysis_id)
    if not analysis_data:
        raise HTTPException(status_code=404, detail="分析数据未找到")
    
    req = ReportGenerationRequest(
        analysis_data=analysis_data,
        language=language,
        include_raw_data=include_raw_data
    )
    return await generate_report(req, background_tasks, service, current_user)


@router.get("/templates")
async def list_report_templates(templates: List[Dict] = Depends(get_report_templates)):
    return {
        "templates": templates,
        "total": len(templates)
    }


@router.get("/history")
async def get_report_history(
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    service: ReportGenerationService = Depends(get_report_service)
):
    history = service.get_report_history(limit=limit, offset=offset)
    return history


@router.post("/batch-generate")
async def batch_generate_reports(
    analyses: List[Dict[str, Any]],
    template: str = "stability_standard",
    language: str = "zh",
    current_user: dict = Depends(get_current_user)
):
    """
    批量生成报告
    
    为多个分析结果批量生成报告
    """
    if len(analyses) > 10:
        raise HTTPException(
            status_code=400,
            detail="批量生成最多支持10个报告"
        )
    
    results = []
    for analysis in analyses:
        try:
            result = await report_generation_service.generate_stability_report(
                analysis_data=analysis,
                language=language,
                include_raw_data=False
            )
            results.append({
                "drug_name": analysis.get('drug_name', 'Unknown'),
                "status": "success" if result['success'] else "failed",
                "filename": result.get('filename'),
                "error": result.get('error')
            })
        except Exception as e:
            results.append({
                "drug_name": analysis.get('drug_name', 'Unknown'),
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "total": len(analyses),
        "success": sum(1 for r in results if r['status'] == 'success'),
        "failed": sum(1 for r in results if r['status'] == 'failed'),
        "results": results
    }


@router.post("/preview")
async def preview_report(
    request: ReportGenerationRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    预览报告内容（不生成PDF）
    
    返回报告的结构化内容，用于前端预览
    """
    try:
        # 提取报告内容结构
        preview_data = {
            "title": "药物稳定性研究报告",
            "drug_name": request.analysis_data.get('drug_name', 'N/A'),
            "sections": []
        }
        
        # 执行摘要
        if 'prediction' in request.analysis_data:
            prediction = request.analysis_data['prediction']
            summary = {
                "title": "执行摘要",
                "content": []
            }
            
            if 'long_term' in prediction:
                t90 = prediction['long_term'].get('t90')
                if t90:
                    summary["content"].append(
                        f"预测长期稳定性（25°C/60%RH）：{t90}个月"
                    )
            
            preview_data["sections"].append(summary)
        
        # 药物信息
        if 'structure_analysis' in request.analysis_data:
            drug_info = {
                "title": "药物信息",
                "content": []
            }
            
            properties = request.analysis_data['structure_analysis'].get('properties', {})
            if properties:
                drug_info["content"].extend([
                    f"分子量: {properties.get('molecular_weight', 'N/A')} g/mol",
                    f"LogP: {properties.get('logp', 'N/A')}",
                    f"TPSA: {properties.get('tpsa', 'N/A')} Ų"
                ])
            
            preview_data["sections"].append(drug_info)
        
        return preview_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 