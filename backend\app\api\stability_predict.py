from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

from app.services.stability_prediction_service import StabilityPredictionService
from app.config.database import get_db
from pydantic import BaseModel

router = APIRouter()

# --- Pydantic Models ---

class StabilityDataBase(BaseModel):
    project_id: int
    time_point: float
    temperature: float
    humidity: float
    value: float
    item: str

class StabilityDataCreate(StabilityDataBase):
    pass

class StabilityData(BaseModel):
    id: int
    project_id: int
    time_point: float
    temperature: float
    humidity: float
    value: float
    item: str

    class Config:
        orm_mode = True

class StabilityPredictionRequest(BaseModel):
    project_id: int
    prediction_months: int = 36
    confidence_level: float = 0.95
    model_selection: str = "auto"
    
class StabilityPredictionResponse(BaseModel):
    fit_result: Dict[str, Any]
    shelf_life_info: Dict[str, Any]
    plot_base64: Optional[str]

# --- Dependency ---

def get_stability_service(db: Session = Depends(get_db)) -> StabilityPredictionService:
    return StabilityPredictionService(db)

# --- API Routes ---

@router.post("/stability-data/", response_model=StabilityData, status_code=201, tags=["stability-data"])
def create_stability_data_entry(
    data: StabilityDataCreate,
    service: StabilityPredictionService = Depends(get_stability_service)
):
    """
    创建一个新的稳定性数据点。
    """
    return service.create_data_entry(data=data)

@router.get("/stability-data/", response_model=List[StabilityData], tags=["stability-data"])
def list_stability_data(
    project_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    service: StabilityPredictionService = Depends(get_stability_service)
):
    """
    获取指定项目的稳定性数据列表。
    """
    return service.get_data_entries(project_id=project_id, skip=skip, limit=limit)

@router.post("/stability/predict", response_model=StabilityPredictionResponse, tags=["stability-prediction"])
def predict_stability(
    request: StabilityPredictionRequest,
    service: StabilityPredictionService = Depends(get_stability_service)
):
    """
    对指定项目的数据进行稳定性预测。
    """
    try:
        prediction_result = service.run_prediction(
            project_id=request.project_id,
            prediction_months=request.prediction_months,
            confidence_level=request.confidence_level,
            model_selection=request.model_selection
        )
        if not prediction_result:
            raise HTTPException(status_code=400, detail="预测失败，数据不足或模型不适用。")
        return prediction_result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # Log the exception for debugging
        # logger.error(f"Internal server error during prediction: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误")

@router.get("/stability/models", response_model=List[Dict[str, str]], tags=["stability-prediction"])
def get_available_models(service: StabilityPredictionService = Depends(get_stability_service)):
    """
    获取可用的动力学模型列表。
    """
    return service.get_available_models() 