import React, { useContext, useState } from 'react';
import { Table, Button, Input, Space, Modal, Form } from 'antd';
import ConfirmButton from '../components/ConfirmButton';
import { RoleContext, ProjectContext } from '../App';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';

export interface Project {
  id: string;
  name: string;
  status: string;
  created?: string;
  created_at?: string;
  updated_at?: string;
  [key: string]: any;
}

const ProjectsPage: React.FC = () => {
  const role = useContext(RoleContext);
  const { projects, addProject, editProject, deleteProject } = useContext(ProjectContext);
  const [modalOpen, setModalOpen] = useState(false);
  const [editItem, setEditItem] = useState<Project | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const handleNew = () => { setEditItem(null); form.resetFields(); setModalOpen(true); };
  const handleEdit = (item: Project) => { setEditItem(item); form.setFieldsValue(item); setModalOpen(true); };
  const handleDelete = (id: string) => { deleteProject(id); message.success('已删除'); };
  const handleOk = () => {
    form.validateFields().then((values: { name: string }) => {
      const now = new Date().toISOString();
      if (editItem) {
        editProject({
          ...(editItem as Project),
          ...values,
          updated_at: now,
          created_at: editItem.created_at || now,
        });
        message.success('编辑成功');
      } else {
        const newProject: Project = {
          ...values,
          id: Date.now().toString(),
          status: '新建',
          created: now.slice(0, 10),
          created_at: now,
          updated_at: now,
        };
        addProject(newProject);
        message.success('新建成功');
        navigate('/data-input');
      }
      setModalOpen(false);
    });
  };

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Input.Search placeholder="搜索项目" style={{ width: 240 }} />
        <Button type="primary" onClick={handleNew}>新建项目</Button>
      </div>
      <Table columns={[
        { title: '项目名', dataIndex: 'name', key: 'name', sorter: (a, b) => a.name.localeCompare(b.name) },
        { title: '状态', dataIndex: 'status', key: 'status', filters: [
          { text: '已完成', value: '已完成' },
          { text: '分析中', value: '分析中' },
          { text: '风险', value: '风险' },
          { text: '新建', value: '新建' },
        ],
        onFilter: (value, record) => record.status === value },
        { title: '创建时间', dataIndex: 'created', key: 'created', sorter: (a, b) => (a.created || a.created_at || '').localeCompare(b.created || b.created_at || ''), render: (_, project) => project?.created || project?.created_at || '' },
        { title: '操作', key: 'action', render: (_, record) => <Space><Button size="small" onClick={() => handleEdit(record)}>编辑</Button>{role === 'admin' && <ConfirmButton danger onConfirm={() => handleDelete(record.id)}>删除</ConfirmButton>}</Space> },
      ]} dataSource={projects} rowKey="id" />
      <Modal open={modalOpen} onCancel={() => setModalOpen(false)} onOk={handleOk} title={editItem ? '编辑项目' : '新建项目'}>
        <Form form={form} layout="vertical">
          <Form.Item name="name" label="项目名" rules={[{ required: true, message: '请输入项目名' }]}> <Input /> </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectsPage;