import React, { useState, useEffect, useContext } from 'react';
import { Card, Button, Collapse, Typography, Space, Tag, Alert } from 'antd';
import { BugOutlined, CopyOutlined, ReloadOutlined } from '@ant-design/icons';
import { ProjectContext } from '../App';
import api from '../api';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

interface DebugInfo {
  timestamp: string;
  userAgent: string;
  url: string;
  localStorage: any;
  sessionStorage: any;
  projectContext: any;
  apiBaseUrl: string;
  networkStatus: string;
  consoleErrors: any[];
  apiTests: any[];
}

const DebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const { currentProject, inputData } = useContext(ProjectContext);

  const collectDebugInfo = async () => {
    setLoading(true);
    try {
      // 收集基础信息
      const info: DebugInfo = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        localStorage: {},
        sessionStorage: {},
        projectContext: {
          currentProject,
          inputData,
          hasProject: !!currentProject,
          projectId: currentProject?.id
        },
        apiBaseUrl: process.env.REACT_APP_API_URL || 'http://localhost:8000',
        networkStatus: navigator.onLine ? 'online' : 'offline',
        consoleErrors: [],
        apiTests: []
      };

      // 收集localStorage
      try {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            info.localStorage[key] = localStorage.getItem(key);
          }
        }
      } catch (e) {
        info.localStorage = { error: 'Cannot access localStorage' };
      }

      // 收集sessionStorage
      try {
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key) {
            info.sessionStorage[key] = sessionStorage.getItem(key);
          }
        }
      } catch (e) {
        info.sessionStorage = { error: 'Cannot access sessionStorage' };
      }

      // 测试关键API
      const apiTests = [
        { name: '健康检查', url: '/api/health' },
        { name: '项目列表', url: '/api/projects/' },
        { name: '辅料数据库', url: '/api/excipients-database' },
        { name: '药物搜索', url: '/api/drug-info/search?name=aspirin' }
      ];

      for (const test of apiTests) {
        try {
          const startTime = Date.now();
          const response = await api.get(test.url);
          const endTime = Date.now();
          
          info.apiTests.push({
            name: test.name,
            url: test.url,
            status: 'success',
            statusCode: response.status,
            responseTime: endTime - startTime,
            data: response.data
          });
        } catch (error: any) {
          info.apiTests.push({
            name: test.name,
            url: test.url,
            status: 'error',
            error: error.message,
            statusCode: error.response?.status,
            data: error.response?.data
          });
        }
      }

      setDebugInfo(info);
    } catch (error) {
      console.error('收集调试信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (debugInfo) {
      const text = JSON.stringify(debugInfo, null, 2);
      navigator.clipboard.writeText(text).then(() => {
        alert('调试信息已复制到剪贴板');
      });
    }
  };

  const renderApiTests = () => {
    if (!debugInfo?.apiTests.length) return null;

    return (
      <div>
        {debugInfo.apiTests.map((test, index) => (
          <div key={index} style={{ marginBottom: 8 }}>
            <Space>
              <Tag color={test.status === 'success' ? 'green' : 'red'}>
                {test.name}
              </Tag>
              {test.status === 'success' ? (
                <Text type="success">
                  {test.statusCode} ({test.responseTime}ms)
                </Text>
              ) : (
                <Text type="danger">
                  {test.statusCode || 'Network Error'}: {test.error}
                </Text>
              )}
            </Space>
          </div>
        ))}
      </div>
    );
  };

  if (!visible) {
    return (
      <Button
        icon={<BugOutlined />}
        onClick={() => setVisible(true)}
        style={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 1000,
          backgroundColor: '#ff4d4f',
          borderColor: '#ff4d4f',
          color: 'white'
        }}
      >
        调试面板
      </Button>
    );
  }

  return (
    <Card
      title="前端调试面板"
      style={{
        position: 'fixed',
        bottom: 20,
        right: 20,
        width: 500,
        maxHeight: 600,
        overflow: 'auto',
        zIndex: 1000,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
      }}
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={collectDebugInfo}
            loading={loading}
            size="small"
          >
            刷新
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={copyToClipboard}
            disabled={!debugInfo}
            size="small"
          >
            复制
          </Button>
          <Button
            onClick={() => setVisible(false)}
            size="small"
          >
            关闭
          </Button>
        </Space>
      }
    >
      {!debugInfo ? (
        <Alert
          message="点击刷新按钮收集调试信息"
          type="info"
          showIcon
        />
      ) : (
        <Collapse size="small">
          <Panel header="基础信息" key="basic">
            <div>
              <Text strong>时间:</Text> {debugInfo.timestamp}<br/>
              <Text strong>URL:</Text> {debugInfo.url}<br/>
              <Text strong>网络状态:</Text> {debugInfo.networkStatus}<br/>
              <Text strong>API地址:</Text> {debugInfo.apiBaseUrl}
            </div>
          </Panel>

          <Panel header="项目上下文" key="project">
            <div>
              <Text strong>当前项目:</Text> {debugInfo.projectContext.hasProject ? '已选择' : '未选择'}<br/>
              {debugInfo.projectContext.currentProject && (
                <>
                  <Text strong>项目ID:</Text> {debugInfo.projectContext.projectId}<br/>
                  <Text strong>项目名称:</Text> {debugInfo.projectContext.currentProject.name}<br/>
                </>
              )}
              <Text strong>输入数据:</Text> {debugInfo.projectContext.inputData ? '有数据' : '无数据'}
            </div>
          </Panel>

          <Panel header="API测试结果" key="api">
            {renderApiTests()}
          </Panel>

          <Panel header="本地存储" key="storage">
            <div>
              <Text strong>localStorage:</Text>
              <Paragraph>
                <pre style={{ fontSize: '12px', maxHeight: '100px', overflow: 'auto' }}>
                  {JSON.stringify(debugInfo.localStorage, null, 2)}
                </pre>
              </Paragraph>
              <Text strong>sessionStorage:</Text>
              <Paragraph>
                <pre style={{ fontSize: '12px', maxHeight: '100px', overflow: 'auto' }}>
                  {JSON.stringify(debugInfo.sessionStorage, null, 2)}
                </pre>
              </Paragraph>
            </div>
          </Panel>
        </Collapse>
      )}
    </Card>
  );
};

export default DebugPanel;
