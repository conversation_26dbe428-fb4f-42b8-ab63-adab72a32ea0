from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional

from ..services.excipient_compatibility_service import ExcipientCompatibilityService
from ..services.database import get_db
from pydantic import BaseModel

router = APIRouter()

class CompatibilityRequest(BaseModel):
    drug_name: str
    drug_structure: str
    excipients: List[Dict[str, Any]]
    dosage_form: str = "tablet"
    process_conditions: Optional[Dict[str, Any]] = None
    use_advanced: bool = True

@router.post("/assess", summary="评估药物与辅料的相容性")
async def assess_excipient_compatibility(
    request: CompatibilityRequest,
    db: Session = Depends(get_db)
):
    """
    评估给定药物和一系列辅料之间的化学相容性。

    - **drug_name**: 药物的名称。
    - **drug_structure**: 药物的SMILES化学结构式。
    - **excipients**: 辅料列表，每个辅料应包含 'name' 和可选的 'smiles'。
    - **dosage_form**: 剂型。
    - **process_conditions**: 工艺条件。
    - **use_advanced**: 是否使用高级评估引擎。
    """
    try:
        service = ExcipientCompatibilityService(db=db)
        results = await service.assess_compatibility(
            drug_name=request.drug_name,
            drug_structure=request.drug_structure,
            excipients=request.excipients,
            dosage_form=request.dosage_form,
            process_conditions=request.process_conditions,
            use_advanced=request.use_advanced
        )
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 