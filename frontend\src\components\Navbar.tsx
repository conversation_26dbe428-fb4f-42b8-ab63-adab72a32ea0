import React from 'react';
import { Menu, Dropdown, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const Navbar: React.FC = () => {
  const navigate = useNavigate();
  const username = localStorage.getItem('token') ? '用户' : '未登录';
  const handleMenuClick = (e: any) => {
    navigate(e.key);
  };
  const menu = (
    <Menu onClick={handleMenuClick} mode="horizontal" style={{ fontFamily: 'SimSun, serif', background: '#2b3a55', color: '#fff', borderBottom: 'none' }}>
      <Menu.Item key="/">首页</Menu.Item>
      <Menu.Item key="/input">数据输入</Menu.Item>
      <Menu.Item key="/projects">项目管理</Menu.Item>
      <Menu.Item key="/user">用户中心</Menu.Item>
    </Menu>
  );
  return (
    <div style={{ background: '#2b3a55', padding: '0 32px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: 56 }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <img src="/logo192.png" alt="logo" style={{ height: 36, marginRight: 16 }} />
        {menu}
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button size="small" style={{ marginRight: 16 }}>中/EN</Button>
        <Dropdown
          menu={{
            items: [
              {
                key: 'logout',
                label: '退出登录'
              }
            ]
          }}
          placement="bottomRight"
        >
          <Button size="small">{username}</Button>
        </Dropdown>
      </div>
    </div>
  );
};

export default Navbar; 