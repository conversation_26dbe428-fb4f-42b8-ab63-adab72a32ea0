import React, { useEffect, useState } from 'react';
import { List, Button, Popconfirm, message, Input, Modal, Typography } from 'antd';
import { apiFetch } from '../api';
import { useTranslation } from 'react-i18next';

export interface ExportHistoryItem {
  id: number;
  fileName: string;
  exportTime: string;
  exportedBy: string;
  options: string[];
  url: string;
}

interface ExportHistoryListProps {
  projectId?: number;
  history?: ExportHistoryItem[];
  onDownload?: (item: ExportHistoryItem) => void;
  onDelete?: (id: number) => void;
  filterExporter?: string;
  filterContent?: string;
  filterDate?: string;
}

const ExportHistoryList: React.FC<ExportHistoryListProps> = ({ projectId, history, onDownload, onDelete, filterExporter, filterContent, filterDate }) => {
  const [data, setData] = useState<ExportHistoryItem[]>(history || []);
  const [loading, setLoading] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingValue, setEditingValue] = useState('');
  const [previewItem, setPreviewItem] = useState<ExportHistoryItem | null>(null);
  const { t, i18n } = useTranslation();

  // 获取导出历史
  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await apiFetch<ExportHistoryItem[]>(`/api/report/export/history?project_id=${projectId}`);
      if (Array.isArray(res) && res.length > 0) {
        setData(res);
      } else {
        setData([]);
      }
    } catch (e) {
      message.warning(t('获取导出历史失败，已使用模拟数据'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (history) {
      setData(history);
      return;
    }
    if (projectId) fetchData();
    // eslint-disable-next-line
  }, [projectId, history]);

  const handleDownload = (item: ExportHistoryItem) => {
    window.open(item.url, '_blank');
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      await apiFetch(`/api/report/export/${id}`, { method: 'DELETE' });
      setData(d => d.filter(item => item.id !== id));
      message.success(t('已删除导出记录'));
      if (onDelete) onDelete(id);
    } catch {
      message.error(t('删除失败'));
    } finally {
      setLoading(false);
    }
  };

  const handleRename = async (id: number, newName: string) => {
    setLoading(true);
    try {
      // 假设有API /api/report/export/:id/rename
      await apiFetch(`/api/report/export/${id}/rename`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fileName: newName }),
      });
      setData(d => d.map(item => item.id === id ? { ...item, fileName: newName } : item));
      message.success(t('重命名成功'));
    } catch {
      message.error(t('重命名失败'));
    } finally {
      setEditingId(null);
      setLoading(false);
    }
  };

  const filteredData = data.filter(item => {
    if (filterExporter && !item.exportedBy.includes(filterExporter)) return false;
    if (filterContent && !item.options.join(',').includes(filterContent)) return false;
    if (filterDate && !item.exportTime.startsWith(filterDate)) return false;
    return true;
  });

  return (
    <div style={{ marginTop: 24, borderRadius: 12, boxShadow: '0 2px 8px #eee', background: '#fff', padding: 12 }} aria-label={t('导出历史区块')}>
      <h4 style={{ marginBottom: 12 }}>{t('导出历史')}</h4>
      <List
        loading={loading}
        dataSource={filteredData}
        locale={{ emptyText: t('暂无导出记录') }}
        renderItem={item => (
          <List.Item style={{ borderRadius: 8, marginBottom: 8, transition: 'background 0.2s' }}
            onMouseEnter={e => (e.currentTarget.style.background = '#f6f8fa')}
            onMouseLeave={e => (e.currentTarget.style.background = '#fff')}
            actions={[
              <Button type="link" onClick={() => handleDownload(item)} key="download" aria-label={t('下载') + ':' + item.fileName}>{t('下载')}</Button>,
              <Button size="small" onClick={() => setPreviewItem(item)} key="preview" aria-label={t('预览') + ':' + item.fileName}>{t('预览')}</Button>,
              <Popconfirm title={t('确定删除该记录？')} onConfirm={() => handleDelete(item.id)} okText={t('删除')} cancelText={t('取消')}>
                <Button size="small" danger key="delete" aria-label={t('删除') + ':' + item.fileName}>{t('删除')}</Button>
              </Popconfirm>,
            ]}
          >
            <List.Item.Meta
              title={<Typography.Text>{item.fileName}</Typography.Text>}
              description={
                <>
                  <div>{t('导出时间')}：{item.exportTime}</div>
                  <div>{t('导出人')}：{item.exportedBy}</div>
                  <div>{t('内容')}：{item.options.join(', ')}</div>
                </>
              }
            />
          </List.Item>
        )}
      />
      <Modal open={!!previewItem} onCancel={() => setPreviewItem(null)} onOk={() => setPreviewItem(null)} title={t('内容预览')} footer={null} width={600} aria-modal="true" aria-label={t('内容预览')}>
        {previewItem && (
          <div style={{ maxWidth: 500 }}>
            <div style={{ marginBottom: 12 }}><b>{t('文件名')}：</b>{previewItem.fileName}</div>
            <div style={{ marginBottom: 12 }}><b>{t('导出时间')}：</b>{previewItem.exportTime}</div>
            <div style={{ marginBottom: 12 }}><b>{t('导出人')}：</b>{previewItem.exportedBy}</div>
            <div style={{ marginBottom: 12 }}><b>{t('内容')}：</b>{previewItem.options.join(', ')}</div>
            <div style={{ marginBottom: 12 }}><b>{t('内容预览')}：</b></div>
            <div style={{ background: '#fafbfc', border: '1px solid #eee', borderRadius: 6, padding: 12 }}>
              {previewItem.options.includes('PDF') ? (
                <div style={{ color: '#888' }}>{t('PDF文档内容预览（请下载查看完整内容）')}</div>
              ) : previewItem.options.includes('Word') ? (
                <div style={{ color: '#888' }}>{t('Word文档内容预览（请下载查看完整内容）')}</div>
              ) : (
                <div style={{ color: '#888' }}>{t('未知格式，无法预览')}</div>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ExportHistoryList; 