import React, { useState, useContext } from 'react';
import { Layout, Menu, Typography } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  FormOutlined,
  ExperimentOutlined,
  SolutionOutlined,
  TeamOutlined,
  SettingOutlined,
  GlobalOutlined,
  RobotOutlined,
  BulbOutlined,
  ToolOutlined,
  AreaChartOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { RoleContext } from '../App';
import '../styles/Sidebar.css';

const { Sider } = Layout;
const { Title } = Typography;

const Sidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const role = useContext(RoleContext);

  const getSelectedKeys = () => {
    const path = location.pathname.split('/')[1];
    return [path || 'dashboard'];
  };

  const getOpenKeys = () => {
    const path = location.pathname.split('/')[1];
    if (['stability-prediction', 'excipient-analysis', 'formulation-analysis'].includes(path)) {
      return ['analysis-group'];
    }
    if (['pubchem-search', 'ai-analysis'].includes(path)) {
      return ['tools-group'];
    }
    if (['user-management', 'system-settings'].includes(path)) {
      return ['system-group'];
    }
    return [];
  };

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={value => setCollapsed(value)}
      theme="light"
      width={200}
      collapsedWidth={80}
      style={{
        borderRight: '1px solid var(--border-light)',
        background: 'var(--content-background)',
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.06)'
      }}
    >
      <div className={`sidebar-header ${collapsed ? 'collapsed' : 'expanded'}`}>
        <RobotOutlined className={`sidebar-logo ${collapsed ? 'collapsed' : 'expanded'}`} />
        {!collapsed && (
          <Title level={4} className="sidebar-title" title="药物稳定性研究助手">
            稳定性助手
          </Title>
        )}
      </div>
      <Menu
        className={`sidebar-menu ${collapsed ? 'collapsed' : ''}`}
        mode="inline"
        defaultOpenKeys={getOpenKeys()}
        selectedKeys={getSelectedKeys()}
        items={[
          {
            key: 'dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/">仪表盘</Link>,
          },
          {
            key: 'projects',
            icon: <ProjectOutlined />,
            label: <Link to="/projects">项目管理</Link>,
          },
          {
            key: 'data-input',
            icon: <FormOutlined />,
            label: <Link to="/data-input">数据输入</Link>,
          },
          {
            type: 'divider',
          },
          {
            key: 'analysis-group',
            icon: <AreaChartOutlined />,
            label: '分析模块',
            children: [
              {
                key: 'stability-prediction',
                icon: <ExperimentOutlined />,
                label: <Link to="/stability-prediction">稳定性预测</Link>,
              },
              {
                key: 'excipient-analysis',
                icon: <SolutionOutlined />,
                label: <Link to="/excipient-analysis">原辅料相容性分析</Link>,
              },
              {
                key: 'formulation-analysis',
                icon: <BulbOutlined />,
                label: <Link to="/formulation-analysis">配方分析</Link>,
              },
            ],
          },
          {
            key: 'tools-group',
            icon: <ToolOutlined />,
            label: '智能工具',
            children: [
              {
                key: 'pubchem-search',
                icon: <GlobalOutlined />,
                label: <Link to="/pubchem-search">数据库搜索</Link>,
              },
              {
                key: 'ai-analysis',
                icon: <RobotOutlined />,
                label: <Link to="/ai-analysis">AI智能分析</Link>,
              },
            ],
          },
          ...(role === 'admin' ? [
            {
              type: 'divider' as const,
            },
            {
              key: 'system-group',
              icon: <ApartmentOutlined />,
              label: '系统管理',
              children: [
                {
                  key: 'user-management',
                  icon: <TeamOutlined />,
                  label: <Link to="/user-management">用户管理</Link>,
                },
                {
                  key: 'system-settings',
                  icon: <SettingOutlined />,
                  label: <Link to="/system-settings">系统设置</Link>,
                },
              ],
            },
          ] : []),
        ]}
      />
    </Sider>
  );
};

export default Sidebar; 