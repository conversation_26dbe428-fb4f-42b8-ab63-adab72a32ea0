from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
import datetime
from .compatibility_assess import assess_compatibility, CompatibilityRequest
from ..models.drug import DrugORM
from ..models.excipient import ExcipientORM, ExcipientCompatibilityORM
from ..models.stability import StabilityPredictionORM
from app.services import get_db
from app.services.ai_service import generate_ai_suggestions_real
import uuid
import numpy as np
from .ai_enhanced import AIAnalysisRequest

router = APIRouter()

class FormulationAnalysisRequest(BaseModel):
    """综合配方分析请求"""
    # 药物信息
    drug_name: str
    drug_structure: Optional[str] = None  # SMILES
    drug_id: Optional[str] = None
    
    # 辅料信息
    excipients: List[Dict[str, Any]]
    
    # 环境条件
    temperature: Optional[float] = 25.0  # °C
    humidity: Optional[float] = 60.0  # %RH
    ph: Optional[float] = 7.0
    packaging: Optional[str] = None
    
    # 稳定性数据
    history_data: List[Dict[str, Any]] = []
    
    # 预测配置
    prediction_timepoints: List[float] = [1, 3, 6, 9, 12, 18, 24, 36]  # months
    model_selection: Optional[str] = "auto"
    confidence_level: float = 0.95

class FormulationRiskFactor(BaseModel):
    """配方风险因素"""
    factor: str
    risk_level: str  # 高/中/低
    description: str
    impact_score: float  # 0-10
    mitigation: str

class FormulationRecommendation(BaseModel):
    """配方建议"""
    title: str
    description: str
    priority: str  # 高/中/低
    rationale: str
    implementation: str

class FormulationAnalysisResponse(BaseModel):
    """综合配方分析响应"""
    # 基本信息
    drug_name: str
    analysis_date: str
    
    # 相容性分析结果
    compatibility_results: Dict[str, Any]
    
    # 稳定性预测结果
    stability_prediction: Dict[str, Any]
    
    # 综合风险评估
    overall_risk_level: str  # 高/中/低
    risk_factors: List[FormulationRiskFactor]
    
    # 建议
    recommendations: List[FormulationRecommendation]
    
    # AI建议
    ai_suggestions: Dict[str, Any]

class ExcipientFormulationInfo(BaseModel):
    id: Optional[str] = None
    name: str
    category: Optional[str] = None
    function: Optional[str] = None
    amount: Optional[float] = None
    compatibility_risk: Optional[str] = None
    
class FormulationInput(BaseModel):
    drug_name: str
    drug_structure: Optional[str] = None
    drug_properties: Optional[Dict[str, Any]] = None
    excipients: List[ExcipientFormulationInfo]
    process: Optional[str] = None
    packaging: Optional[str] = None
    target_dosage_form: Optional[str] = None
    target_release_profile: Optional[str] = None
    stability_data: Optional[List[Dict[str, Any]]] = None
    compatibility_data: Optional[List[Dict[str, Any]]] = None

class ExcipientEvaluation(BaseModel):
    name: str
    function: str
    amount: float
    amount_evaluation: str
    compatibility: str
    functionality: str
    recommendations: List[str]

class FormulationAnalysisResult(BaseModel):
    drug: str
    dosage_form: str
    excipient_evaluations: List[ExcipientEvaluation]
    overall_risk_score: float
    stability_prediction: Dict[str, Any]
    recommendations: List[str]
    formulation_gaps: List[str]
    regulatory_considerations: List[str]

def calculate_overall_risk(compatibility_results, stability_prediction):
    """计算整体风险等级"""
    # 提取相容性风险
    compatibility_risks = []
    for result in compatibility_results.get("results", []):
        if result.get("risk_level") == "高":
            compatibility_risks.append(10)
        elif result.get("risk_level") == "中":
            compatibility_risks.append(5)
        elif result.get("risk_level") == "低":
            compatibility_risks.append(1)
    
    # 提取稳定性风险
    stability_risk = 0
    t90 = stability_prediction.get("prediction", {}).get("long_term", {}).get("t90", 24)
    if t90 < 12:
        stability_risk = 10  # 高风险
    elif t90 < 24:
        stability_risk = 5   # 中风险
    else:
        stability_risk = 1   # 低风险
    
    # 计算平均风险分数
    avg_compatibility_risk = sum(compatibility_risks) / len(compatibility_risks) if compatibility_risks else 0
    overall_risk_score = (avg_compatibility_risk * 0.6) + (stability_risk * 0.4)  # 相容性权重0.6，稳定性权重0.4
    
    # 确定风险等级
    if overall_risk_score >= 7:
        return "高"
    elif overall_risk_score >= 3:
        return "中"
    else:
        return "低"

def generate_risk_factors(compatibility_results, stability_prediction):
    """生成风险因素列表"""
    risk_factors = []
    
    # 从相容性结果提取风险因素
    for result in compatibility_results.get("results", []):
        if result.get("risk_level") in ["高", "中"]:
            risk_factors.append(FormulationRiskFactor(
                factor=f"辅料相容性: {result.get('excipient')}",
                risk_level=result.get("risk_level"),
                description=f"{result.get('risk_type')}风险",
                impact_score=result.get("risk_score", 5.0),
                mitigation=result.get("suggestion", "需进一步评估")
            ))
    
    # 从稳定性预测提取风险因素
    t90 = stability_prediction.get("prediction", {}).get("long_term", {}).get("t90", 24)
    if t90 < 24:
        risk_level = "高" if t90 < 12 else "中"
        risk_factors.append(FormulationRiskFactor(
            factor="稳定性预测",
            risk_level=risk_level,
            description=f"预测货架期为{t90}个月，低于目标24个月",
            impact_score=10 if t90 < 12 else 5,
            mitigation="考虑改进配方或包装，或调整储存条件"
        ))
    
    # 从敏感性分析提取风险因素
    for factor in stability_prediction.get("sensitivity", []):
        if factor.get("impact", 0) > 0.3:  # 影响因子大于0.3的因素
            risk_factors.append(FormulationRiskFactor(
                factor=f"敏感因素: {factor.get('factor')}",
                risk_level="中",
                description=f"{factor.get('factor')}对稳定性影响显著",
                impact_score=factor.get("impact", 0) * 10,
                mitigation=f"优化{factor.get('factor')}条件"
            ))
    
    # 按影响分数排序
    risk_factors.sort(key=lambda x: x.impact_score, reverse=True)
    return risk_factors

def generate_recommendations(risk_factors, compatibility_results, stability_prediction):
    """生成建议列表"""
    recommendations = []
    
    # 基于风险因素生成建议
    for i, factor in enumerate(risk_factors):
        if factor.risk_level == "高":
            priority = "高"
        elif factor.risk_level == "中":
            priority = "中"
        else:
            priority = "低"
        
        recommendations.append(FormulationRecommendation(
            title=f"解决{factor.factor}风险",
            description=factor.mitigation,
            priority=priority,
            rationale=factor.description,
            implementation=f"根据{factor.factor}的具体情况，实施相应的风险缓解措施"
        ))
    
    # 添加通用建议
    if not any(r.title.startswith("优化包装") for r in recommendations):
        packaging = compatibility_results.get("environment", {}).get("packaging")
        if packaging and "PVC" in packaging:
            recommendations.append(FormulationRecommendation(
                title="优化包装材料",
                description=f"从{packaging}升级为铝塑包装",
                priority="中",
                rationale="提高水汽和氧气屏障性能",
                implementation="评估铝塑复合材料或PVDC涂层材料作为替代包装"
            ))
    
    # 基于稳定性模型添加建议
    model_type = stability_prediction.get("model_info", {}).get("type")
    if model_type == "first-order":
        recommendations.append(FormulationRecommendation(
            title="应对一级动力学降解",
            description="添加适当的稳定剂或抗氧化剂",
            priority="中",
            rationale="药物呈现一级动力学降解特征，可能与氧化或水解相关",
            implementation="考虑添加BHT、抗坏血酸或EDTA等稳定剂"
        ))
    
    # 确保建议不重复且数量适中
    unique_recommendations = []
    titles = set()
    for rec in recommendations:
        if rec.title not in titles:
            titles.add(rec.title)
            unique_recommendations.append(rec)
    
    # 按优先级排序
    priority_map = {"高": 0, "中": 1, "低": 2}
    unique_recommendations.sort(key=lambda x: priority_map.get(x.priority, 3))
    
    return unique_recommendations[:5]  # 最多返回5条建议

@router.post("/analyze", response_model=FormulationAnalysisResult)
async def analyze_formulation(input_data: FormulationInput):
    """
    分析药物配方的综合性能
    """
    try:
        # 验证基本输入
        if not input_data.drug_name:
            raise HTTPException(status_code=400, detail="请提供药物名称")
        
        if not input_data.excipients or len(input_data.excipients) == 0:
            raise HTTPException(status_code=400, detail="请提供至少一种辅料")
        
        # 确定目标剂型
        dosage_form = input_data.target_dosage_form or "片剂"
        
        # 辅料评估预设值
        excipient_standard_ranges = {
            "微晶纤维素": {"function": "填充剂", "range": [20, 90], "unit": "%"},
            "乳糖": {"function": "填充剂", "range": [20, 80], "unit": "%"},
            "硬脂酸镁": {"function": "润滑剂", "range": [0.25, 2], "unit": "%"},
            "聚维酮K30": {"function": "黏合剂", "range": [2, 10], "unit": "%"},
            "羟丙甲纤维素": {"function": "崩解剂", "range": [2, 5], "unit": "%"},
            "交联聚维酮": {"function": "崩解剂", "range": [2, 8], "unit": "%"},
            "二氧化硅": {"function": "流动性改善剂", "range": [0.2, 2], "unit": "%"},
            "淀粉": {"function": "填充剂/崩解剂", "range": [5, 20], "unit": "%"},
            "羟丙纤维素": {"function": "黏合剂", "range": [2, 8], "unit": "%"},
            "甘露醇": {"function": "填充剂", "range": [10, 90], "unit": "%"},
            "山梨醇": {"function": "填充剂", "range": [10, 90], "unit": "%"},
            "羧甲基纤维素钠": {"function": "崩解剂", "range": [2, 6], "unit": "%"},
            "十二烷基硫酸钠": {"function": "表面活性剂", "range": [0.5, 2], "unit": "%"},
            "聚山梨酯80": {"function": "表面活性剂", "range": [0.1, 1], "unit": "%"},
            "氢氧化铝": {"function": "抗酸剂", "range": [10, 40], "unit": "%"},
            "柠檬酸": {"function": "酸度调节剂", "range": [0.5, 5], "unit": "%"},
            "甘油": {"function": "增塑剂", "range": [5, 15], "unit": "%"},
            "氢氧化钠": {"function": "酸度调节剂", "range": [0.1, 2], "unit": "%"},
            "苯甲酸钠": {"function": "防腐剂", "range": [0.1, 0.5], "unit": "%"},
            "尼泊金甲酯": {"function": "防腐剂", "range": [0.1, 0.3], "unit": "%"},
        }
        
        # 辅料与药物的相容性预设
        compatibility_presets = {
            "微晶纤维素": "良好",
            "乳糖": "中等",
            "硬脂酸镁": "中等",
            "聚维酮K30": "良好",
            "羟丙甲纤维素": "良好",
            "交联聚维酮": "良好",
            "二氧化硅": "良好",
            "淀粉": "良好",
            "羟丙纤维素": "良好",
            "甘露醇": "良好",
            "山梨醇": "良好",
            "羧甲基纤维素钠": "中等",
            "十二烷基硫酸钠": "较差",
            "聚山梨酯80": "中等",
            "氢氧化铝": "较差",
            "柠檬酸": "中等",
            "甘油": "良好",
            "氢氧化钠": "较差",
            "苯甲酸钠": "中等",
            "尼泊金甲酯": "中等",
        }
        
        # 分析每个辅料
        excipient_evaluations = []
        overall_risk = 0
        total_excipient_amount = sum([e.amount or 0 for e in input_data.excipients])
        
        total_excipient_amount = max(total_excipient_amount, 0.1)  # 避免除以零
        
        for excipient in input_data.excipients:
            name = excipient.name
            amount = excipient.amount or 0
            
            # 计算百分比
            percentage = (amount / total_excipient_amount) * 100 if total_excipient_amount > 0 else 0
            
            # 获取辅料标准范围
            standard = excipient_standard_ranges.get(name, {"function": excipient.function or "未知", "range": [0, 100], "unit": "%"})
            
            # 评估剂量是否在标准范围内
            amount_evaluation = "适宜"
            if percentage < standard["range"][0]:
                amount_evaluation = "偏低"
            elif percentage > standard["range"][1]:
                amount_evaluation = "偏高"
            
            # 获取相容性评估
            compatibility = excipient.compatibility_risk or compatibility_presets.get(name, "未知")
            if compatibility in ["较差", "high"]:
                compatibility = "较差"
                risk_score = 0.8
            elif compatibility in ["中等", "medium"]:
                compatibility = "中等"
                risk_score = 0.4
            else:
                compatibility = "良好"
                risk_score = 0.1
            
            # 功能性评估
            functionality = "良好"
            if amount_evaluation != "适宜":
                functionality = "可能不足" if amount_evaluation == "偏低" else "可能过量"
            
            # 针对该辅料的建议
            recommendations = []
            if amount_evaluation == "偏低":
                recommendations.append(f"考虑增加{name}的用量至{standard['range'][0]}-{standard['range'][1]}{standard['unit']}范围")
            elif amount_evaluation == "偏高":
                recommendations.append(f"考虑减少{name}的用量至{standard['range'][0]}-{standard['range'][1]}{standard['unit']}范围")
            
            if compatibility == "较差":
                recommendations.append(f"该辅料与药物相容性较差，考虑替换为其他类似功能的辅料")
            elif compatibility == "中等":
                recommendations.append(f"定期监测该辅料与药物的相容性，必要时考虑调整")
            
            # 添加到评估结果
            excipient_evaluations.append(ExcipientEvaluation(
                name=name,
                function=excipient.function or standard["function"],
                amount=percentage,
                amount_evaluation=amount_evaluation,
                compatibility=compatibility,
                functionality=functionality,
                recommendations=recommendations
            ))
            
            # 累计风险分数
            overall_risk += risk_score * (percentage / 100)
        
        # 归一化整体风险分数到0-10
        overall_risk = min(10, max(0, overall_risk * 10))
        
        # 模拟稳定性预测
        service = StabilityPredictionService(db)
        stability_prediction = service.run_prediction(
            drug_smiles=input_data.drug_structure,
            conditions={
                "temperature": input_data.temperature,
                "humidity": input_data.humidity,
                "ph": input_data.ph
            },
            history_data=input_data.history_data,
            prediction_timepoints=input_data.prediction_timepoints
        )
        
        # 综合建议
        formulation_recommendations = []
        
        # 检查是否缺少关键辅料
        existing_functions = [e.function for e in input_data.excipients if e.function]
        
        missing_functions = []
        if "填充剂" not in existing_functions and dosage_form in ["片剂", "胶囊"]:
            missing_functions.append("填充剂")
        if "润滑剂" not in existing_functions and dosage_form in ["片剂"]:
            missing_functions.append("润滑剂")
        if "崩解剂" not in existing_functions and dosage_form in ["片剂"]:
            missing_functions.append("崩解剂")
        
        if missing_functions:
            formulation_recommendations.append(f"配方中缺少必要的{'、'.join(missing_functions)}，建议添加相应辅料")
        
        # 相容性问题评估
        compatibility_issues = [e for e in excipient_evaluations if e.compatibility in ["较差", "中等"]]
        if compatibility_issues:
            formulation_recommendations.append(f"有{len(compatibility_issues)}个辅料与药物相容性存在风险，建议进一步评估")
        
        # 剂量问题评估
        dose_issues = [e for e in excipient_evaluations if e.amount_evaluation != "适宜"]
        if dose_issues:
            formulation_recommendations.append(f"有{len(dose_issues)}个辅料的用量可能需要调整，参考各辅料具体建议")
        
        # 根据整体风险评分提供建议
        if overall_risk > 7:
            formulation_recommendations.append("配方整体风险较高，建议重新设计或进行大幅调整")
        elif overall_risk > 4:
            formulation_recommendations.append("配方存在中等风险，建议根据具体问题进行针对性调整")
        else:
            formulation_recommendations.append("配方整体风险较低，可以进行中试生产并评估实际性能")
        
        # 配方缺口分析
        formulation_gaps = []
        if missing_functions:
            formulation_gaps.append(f"功能性缺口: 缺少{'、'.join(missing_functions)}")
        
        if dosage_form == "控释片" and "控释材料" not in existing_functions:
            formulation_gaps.append("功能性缺口: 缺少控释材料，无法实现目标释放特性")
        
        # 法规考虑
        regulatory_considerations = [
            "所有辅料应符合药典标准或有相应的安全性数据",
            f"该配方{input_data.target_dosage_form or '剂型'}的生产应符合GMP相关要求",
            "建议进行完整的稳定性研究以确定合适的有效期"
        ]
        
        # 构建最终分析结果
        result = FormulationAnalysisResult(
            drug=input_data.drug_name,
            dosage_form=dosage_form,
            excipient_evaluations=excipient_evaluations,
            overall_risk_score=round(overall_risk, 1),
            stability_prediction=stability_prediction,
            recommendations=formulation_recommendations,
            formulation_gaps=formulation_gaps,
            regulatory_considerations=regulatory_considerations
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配方分析失败: {str(e)}")

@router.post("/optimize", response_model=FormulationAnalysisResult)
async def optimize_formulation(input_data: FormulationInput):
    """
    优化药物配方
    """
    # 目前复用分析逻辑
    return await analyze_formulation(input_data)

@router.post("/analyze_comprehensive", response_model=FormulationAnalysisResponse, summary="执行综合配方分析")
async def analyze_formulation_comprehensive(request: FormulationAnalysisRequest, db: Session = Depends(get_db)):
    """
    执行全面的配方分析，包括相容性、稳定性和AI建议。
    """
    try:
        # 1. 辅料相容性分析
        compat_request = CompatibilityRequest(
            drug_smiles=request.drug_structure,
            excipients=[{"smiles": exc.get("smiles", ""), "name": exc.get("name", "")} for exc in request.excipients]
        )
        compatibility_results = assess_compatibility(compat_request)

        # 2. 稳定性预测
        service = StabilityPredictionService(db)
        stability_prediction = service.run_prediction(
            drug_smiles=request.drug_structure,
            conditions={
                "temperature": request.temperature,
                "humidity": request.humidity,
                "ph": request.ph
            },
            history_data=request.history_data,
            prediction_timepoints=request.prediction_timepoints
        )

        # 3. 整合数据并调用AI服务
        ai_context = {
            "analysis_type": "general_formulation",
            "drug_name": request.drug_name,
            "drug_structure": request.drug_structure,
            "excipients": request.excipients,
            "conditions": {
                "temperature": request.temperature,
                "humidity": request.humidity,
                "ph": request.ph
            },
            "compatibility_results": compatibility_results,
            "stability_prediction": stability_prediction
        }
        ai_suggestions = await generate_ai_suggestions_real(ai_context)
        if "error" in ai_suggestions:
            # 如果AI调用失败，可以记录日志但继续执行，返回不含AI建议的结果
            print(f"AI suggestion error: {ai_suggestions['error']}")
            ai_suggestions = {"suggestions": [], "info": "AI service unavailable"}

        # 4. 生成报告
        overall_risk = calculate_overall_risk(compatibility_results, stability_prediction)
        risk_factors = generate_risk_factors(compatibility_results, stability_prediction)
        recommendations = generate_recommendations(risk_factors, compatibility_results, stability_prediction)

        return FormulationAnalysisResponse(
            drug_name=request.drug_name,
            analysis_date=datetime.datetime.now().isoformat(),
            compatibility_results=compatibility_results,
            stability_prediction=stability_prediction,
            overall_risk_level=overall_risk,
            risk_factors=risk_factors,
            recommendations=recommendations,
            ai_suggestions=ai_suggestions,
        )

    except Exception as e:
        print(f"综合配方分析错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"配方分析失败: {str(e)}")

@router.post("/{project_id}/ai-suggestions", summary="获取AI剂型优化建议")
async def get_ai_formulation_suggestions(
    project_id: int,
    analysis_request: AIAnalysisRequest,
    db: Session = Depends(get_db)
):
    """
    根据项目ID和当前剂型数据，获取AI提供的优化建议。
    """
    # 准备用于AI分析的数据
    # 注意：这里的 `analysis_request.dict()` 需要根据实际模型调整
    # 我们假设它包含了 drug_name, conditions, stability_data, compatibility_data 等
    ai_data = analysis_request.dict()
    ai_data["project_id"] = project_id
    
    # 确定分析类型
    # 这里的逻辑可以根据前端发送的请求内容来决定
    # 例如，如果请求中包含stability_data，则为稳定性分析
    if "stability_data" in ai_data and ai_data["stability_data"]:
        ai_data["analysis_type"] = "stability"
    elif "compatibility_data" in ai_data and ai_data["compatibility_data"]:
        ai_data["analysis_type"] = "compatibility"
    else:
        ai_data["analysis_type"] = "general_formulation"


    # 直接调用异步函数
    suggestions = await generate_ai_suggestions_real(ai_data)
    
    if "error" in suggestions:
        raise HTTPException(status_code=500, detail=suggestions["error"])
        
    return suggestions