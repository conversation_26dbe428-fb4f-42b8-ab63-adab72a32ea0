"""
综合分析API端点
提供一站式药物稳定性分析服务
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import json
import logging

from app.services import get_db
from app.models.degradation_kinetic_model import DegradationKineticModel
# from app.models import FormulationAnalysis
from app.api.auth import get_current_user

# 删除模拟类，使用真实的服务
from app.services.open_data_service import open_data_service
from app.services.external_db_service import get_drug_info_from_external
from app.services.knowledge_base_service import knowledge_base_service
from app.services.excipient_data_service import ExcipientDataService

router = APIRouter()

# 初始化服务
kinetic_model = DegradationKineticModel()

router = APIRouter()

logger = logging.getLogger(__name__)

class ComprehensiveAnalysisRequest(BaseModel):
    """综合分析请求模型"""
    drug_name: str
    drug_smiles: str
    dosage_form: str
    excipients: List[Dict[str, Any]]
    stability_data: Optional[List[Dict[str, Any]]] = None
    target_shelf_life: Optional[float] = 24.0
    storage_zone: Optional[str] = "IVa"
    special_requirements: Optional[Dict[str, Any]] = None


class DrugStructureAnalysisRequest(BaseModel):
    """药物结构分析请求"""
    drug_name: str
    drug_smiles: str


class StabilityPredictionRequest(BaseModel):
    """稳定性预测请求"""
    drug_smiles: str
    formulation: Dict[str, Any]
    stability_data: List[Dict[str, Any]]
    conditions: Optional[Dict[str, Any]] = None


class CompatibilityAssessmentRequest(BaseModel):
    """相容性评估请求"""
    drug_smiles: str
    excipients: List[Dict[str, Any]]
    conditions: Optional[Dict[str, Any]] = None


class StudyDesignRequest(BaseModel):
    """稳定性研究设计请求"""
    drug_smiles: str
    target_shelf_life: float
    dosage_form: str
    zone: Optional[str] = "IVa"


@router.post("/comprehensive-analysis")
async def comprehensive_analysis(
    request: ComprehensiveAnalysisRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """执行综合药物稳定性分析"""
    try:
        results = {
            "drug_name": request.drug_name,
            "analysis_sections": {}
        }
        
        # 1. 药物结构分析
        structure_analysis = analyze_drug_structure_internal(request.drug_smiles)
        results["analysis_sections"]["structure_analysis"] = structure_analysis
        
        # 2. 原辅料相容性评估
        compatibility_assessment = assess_compatibility_internal(
            request.drug_smiles,
            request.excipients
        )
        results["analysis_sections"]["compatibility_assessment"] = compatibility_assessment
        
        # 3. 稳定性预测（如果有数据）
        if request.stability_data:
            stability_prediction = predict_stability_internal(
                request.drug_smiles,
                {"dosage_form": request.dosage_form},
                request.stability_data
            )
            results["analysis_sections"]["stability_prediction"] = stability_prediction
        
        # 4. 稳定性研究方案设计
        study_design = design_study_internal(
            request.drug_smiles,
            request.target_shelf_life,
            request.dosage_form,
            request.storage_zone
        )
        results["analysis_sections"]["study_design"] = study_design
        
        # 5. 知识图谱查询 - 相似案例
        similar_cases = knowledge_base_service.search_similar_cases(
            drug_name=request.drug_name,
            dosage_form=request.dosage_form,
            issue_type=structure_analysis.get("degradation_risks", [{}])[0].get("type", "") if structure_analysis.get("degradation_risks") else ""
        )
        results["analysis_sections"]["similar_cases"] = similar_cases
        
        # 6. 综合建议
        comprehensive_recommendations = generate_comprehensive_recommendations(
            structure_analysis,
            compatibility_assessment,
            results.get("stability_prediction"),
            request.special_requirements
        )
        results["comprehensive_recommendations"] = comprehensive_recommendations
        
        # 7. 执行摘要
        executive_summary = generate_executive_summary(results)
        results["executive_summary"] = executive_summary
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/drug-structure-analysis")
async def analyze_drug_structure(
    request: DrugStructureAnalysisRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """分析药物结构和降解风险"""
    try:
        return analyze_drug_structure_internal(request.drug_smiles)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stability-prediction-advanced")
async def predict_stability_advanced(
    request: StabilityPredictionRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """高级稳定性预测"""
    try:
        return predict_stability_internal(
            request.drug_smiles,
            request.formulation,
            request.stability_data,
            request.conditions
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compatibility-assessment-advanced")
async def assess_compatibility_advanced(
    request: CompatibilityAssessmentRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """高级相容性评估"""
    try:
        return assess_compatibility_internal(
            request.drug_smiles,
            request.excipients,
            request.conditions
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/study-design-advanced")
async def design_stability_study_advanced(
    request: StudyDesignRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """设计稳定性研究方案"""
    try:
        return design_study_internal(
            request.drug_smiles,
            request.target_shelf_life,
            request.dosage_form,
            request.zone
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/knowledge-graph/query")
async def query_knowledge_graph(
    query_type: str,
    drug_name: Optional[str] = None,
    mechanism: Optional[str] = None,
    issue: Optional[str] = None,
    region: Optional[str] = None
) -> List[Dict[str, Any]]:
    """查询知识图谱"""
    try:
        # 根据查询类型调用不同的服务方法
        if query_type == "similar_cases":
            return knowledge_base_service.search_similar_cases(
                drug_name=drug_name,
                issue_type=issue
            )
        elif query_type == "stability_issues":
            return knowledge_base_service.get_stability_issues(
                mechanism=mechanism
            )
        elif query_type == "regulatory":
            return knowledge_base_service.get_regulatory_info(
                region=region
            )
        else:
            # 通用搜索
            search_results = []
            if drug_name:
                drug_info = knowledge_base_service.get_drug_knowledge(drug_name)
                if drug_info:
                    search_results.append(drug_info)
            return search_results
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chief-scientist-consultation")
async def chief_scientist_consultation(
    request: ComprehensiveAnalysisRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    AI驱动的首席科学家会诊
    构建大师级Prompt，引导LLM进行深度分析
    """
    try:
        # 1. 构建完整的数据包
        data_package = await build_consultation_data_package(request)
        
        # 2. 构建大师级Prompt
        mega_prompt = construct_chief_scientist_prompt(data_package)
        
        # 3. 调用LLM进行分析（这里模拟，实际应用中应调用真实的LLM API）
        consultation_result = await simulate_llm_consultation(mega_prompt)
        
        # 4. 结构化输出
        return {
            "success": True,
            "consultation_date": datetime.now().isoformat(),
            "data_package": data_package,
            "prompt_used": mega_prompt,
            "consultation_result": consultation_result,
            "structured_recommendations": parse_consultation_result(consultation_result)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def build_consultation_data_package(request: ComprehensiveAnalysisRequest) -> Dict[str, Any]:
    """构建会诊所需的完整数据包"""
    
    # 1. 原料药档案
    drug_profile = {
        "name": request.drug_name,
        "smiles": request.drug_smiles,
        "structure_analysis": analyze_drug_structure_internal(request.drug_smiles),
        "qsar_degradation_hotspots": identify_degradation_hotspots(request.drug_smiles),
        "reactive_functional_groups": extract_reactive_groups(request.drug_smiles)
    }
    
    # 2. 处方详情
    formulation_details = {
        "dosage_form": request.dosage_form,
        "excipients": [
            {
                "name": exc["name"],
                "concentration": exc.get("concentration", 0),
                "function": exc.get("function", "未知"),
                "grade": exc.get("grade", "药用级")
            } for exc in request.excipients
        ],
        "total_weight": sum(exc.get("concentration", 0) for exc in request.excipients),
        "manufacturing_process": request.special_requirements.get("process", "直接压片") if request.special_requirements else "直接压片"
    }
    
    # 3. 稳定性预测报告
    stability_report = {}
    if request.stability_data:
        prediction = predict_stability_internal(
            request.drug_smiles,
            {"dosage_form": request.dosage_form},
            request.stability_data
        )
        
        # 找到最佳拟合模型
        if prediction["kinetic_predictions"]:
            best_item = min(
                prediction["kinetic_predictions"].items(),
                key=lambda x: x[1]["shelf_life_months"]
            )
            stability_report = {
                "best_fit_model": best_item[1]["model"],
                "model_parameters": {"r_squared": best_item[1]["r_squared"]},
                "predicted_shelf_life": best_item[1]["shelf_life_months"],
                "confidence_interval": best_item[1]["confidence_interval"],
                "limiting_factor": best_item[0]
            }
    
    # 4. 相容性风险矩阵
    compatibility_matrix = []
    for exc in request.excipients:
        # 评估每个药物-辅料对
        assessment = assess_drug_excipient_compatibility(
            request.drug_smiles, 
            exc["name"],
            drug_profile["reactive_functional_groups"]
        )
        
        compatibility_matrix.append({
            "excipient": exc["name"],
            "structural_evidence": assessment["structural_evidence"],
            "literature_evidence": assessment["literature_evidence"],
            "recommended_risk_level": assessment["risk_level"],
            "interaction_mechanisms": assessment["mechanisms"]
        })
    
    return {
        "drug_profile": drug_profile,
        "formulation_details": formulation_details,
        "stability_report": stability_report,
        "compatibility_matrix": compatibility_matrix,
        "storage_zone": request.storage_zone,
        "target_shelf_life": request.target_shelf_life
    }


def construct_chief_scientist_prompt(data_package: Dict[str, Any]) -> str:
    """构建大师级首席科学家Prompt"""
    
    prompt = f"""[SYSTEM] 
你是一位拥有30年经验的世界级药物制剂首席科学家，专长领域包括固态化学、药物降解机理、复杂制剂开发和CMC策略。你的任务是基于以下完整的处方前研究数据包，进行一次彻底的、批判性的综合评估，并提供一份具有战略性指导意义的开发建议报告。

[USER]
== DATA PACKAGE START ==

1.0 API Profile:
- 药物名称: {data_package['drug_profile']['name']}
- SMILES结构: {data_package['drug_profile']['smiles']}
- 活性官能团: {', '.join(data_package['drug_profile']['reactive_functional_groups'])}
- QSAR降解热点: {', '.join([str(h) for h in data_package['drug_profile']['qsar_degradation_hotspots']])}
- 稳定性评分: {data_package['drug_profile']['structure_analysis']['stability_score']}/100

2.0 Formulation Details:
- 剂型: {data_package['formulation_details']['dosage_form']}
- 辅料组成:
{format_excipients_list(data_package['formulation_details']['excipients'])}
- 生产工艺: {data_package['formulation_details']['manufacturing_process']}

3.0 Stability Prediction Analysis (Best Fit Model):
{format_stability_report(data_package['stability_report'])}

4.0 Excipient Compatibility Risk Assessment:
{format_compatibility_matrix(data_package['compatibility_matrix'])}

== DATA PACKAGE END ==

Based on the comprehensive data package provided, please structure your response STRICTLY as follows:

**1.0 Executive Summary:** 
A brief, high-level overview of the formulation's viability and main challenges.

**2.0 Primary Risk Hypothesis:** 
Synthesize all data to identify and describe the single most critical risk pathway for this product.

**3.0 Comprehensive Risk Matrix & Mitigation Strategy:**
- **Risk 1 (Chemical Stability):** [Detailed description]. **Mitigation:** [Actionable advice]
- **Risk 2 (Physical Stability):** [Detailed description]. **Mitigation:** [Actionable advice]
- **Risk 3 (Manufacturability):** [Detailed description]. **Mitigation:** [Actionable advice]

**4.0 Recommended Next Steps (Critical Path):** 
List the top 3-5 most critical experiments or studies required to de-risk this project for IND/NDA filing.

请使用中文回答，并确保建议具有高度的专业性和可操作性。
"""
    
    return prompt


def format_excipients_list(excipients: List[Dict[str, Any]]) -> str:
    """格式化辅料列表"""
    formatted = []
    for exc in excipients:
        formatted.append(f"  - {exc['name']}: {exc['concentration']}% ({exc['function']})")
    return "\n".join(formatted)


def format_stability_report(report: Dict[str, Any]) -> str:
    """格式化稳定性报告"""
    if not report:
        return "- 暂无稳定性数据"
    
    return f"""- 最佳拟合模型: {report.get('best_fit_model', 'N/A')}
- 模型拟合度 (R²): {report.get('model_parameters', {}).get('r_squared', 'N/A')}
- 预测保质期: {report.get('predicted_shelf_life', 'N/A')} 个月
- 置信区间: {report.get('confidence_interval', ['N/A', 'N/A'])}
- 限制因素: {report.get('limiting_factor', 'N/A')}"""


def format_compatibility_matrix(matrix: List[Dict[str, Any]]) -> str:
    """格式化相容性矩阵"""
    formatted = []
    for item in matrix:
        risk_level = item['recommended_risk_level']
        mechanisms = ", ".join(item['interaction_mechanisms']) if item['interaction_mechanisms'] else "无"
        formatted.append(f"- {item['excipient']}: 风险等级={risk_level}, 相互作用={mechanisms}")
    return "\n".join(formatted)


def identify_degradation_hotspots(smiles: str) -> List[str]:
    """识别QSAR降解热点"""
    # 简化实现，实际应使用机器学习模型
    hotspots = []
    
    if "C(=O)O" in smiles:
        hotspots.append("酯键位置")
    if "C(=O)N" in smiles:
        hotspots.append("酰胺键位置")
    if "OH" in smiles:
        hotspots.append("羟基位置")
    
    return hotspots if hotspots else ["无明显降解热点"]


def extract_reactive_groups(smiles: str) -> List[str]:
    """提取反应性官能团"""
    groups = []
    
    patterns = {
        "酯基": "C(=O)O",
        "酰胺基": "C(=O)N",
        "羟基": "OH",
        "氨基": "N",
        "羧基": "C(=O)OH",
        "醛基": "C(=O)H",
        "酮基": "C(=O)C"
    }
    
    for group_name, pattern in patterns.items():
        if pattern in smiles:
            groups.append(group_name)
    
    return groups if groups else ["无明显反应性官能团"]


def assess_drug_excipient_compatibility(drug_smiles: str, excipient_name: str, drug_groups: List[str]) -> Dict[str, Any]:
    """评估药物-辅料相容性"""
    # 基于已知案例和规则的评估
    high_risk_combinations = {
        ("酯基", "硬脂酸镁"): {
            "mechanism": "碱催化水解",
            "risk": "high"
        },
        ("氨基", "乳糖"): {
            "mechanism": "美拉德反应",
            "risk": "high"
        }
    }
    
    mechanisms = []
    risk_level = "low"
    
    # 检查已知的高风险组合
    for group in drug_groups:
        key = (group, excipient_name)
        if key in high_risk_combinations:
            info = high_risk_combinations[key]
            mechanisms.append(info["mechanism"])
            risk_level = info["risk"]
    
    return {
        "structural_evidence": drug_groups,
        "literature_evidence": ["已知案例"] if mechanisms else [],
        "risk_level": risk_level,
        "mechanisms": mechanisms
    }


async def simulate_llm_consultation(prompt: str) -> str:
    """模拟LLM会诊结果"""
    # 实际应用中应调用真实的LLM API
    # 这里返回一个结构化的示例响应
    
    return """**1.0 Executive Summary:**
该制剂配方总体可行，但存在中等程度的稳定性风险。主要挑战来自原料药的酯基结构易水解，以及与硬脂酸镁的潜在相互作用。预测保质期24个月可达成，但需要优化配方和包装。

**2.0 Primary Risk Hypothesis:**
主要风险是湿度诱导的水解降解，可能被硬脂酸镁提供的碱性微环境催化，导致药物含量下降并形成降解产物。这一风险在高温高湿储存条件下会显著加剧。

**3.0 Comprehensive Risk Matrix & Mitigation Strategy:**

**Risk 1 (Chemical Stability - 水解):** 
酯基在pH>7的环境下易发生碱催化水解，硬脂酸镁的存在会创造局部碱性微环境。
**Mitigation:** 
- 使用硬脂酸替代硬脂酸镁作为润滑剂
- 添加pH调节剂（如柠檬酸）维持微环境pH 4-6
- 控制制剂水分含量<0.5%

**Risk 2 (Physical Stability - 吸湿性):** 
制剂可能因吸湿导致物理性质改变，影响崩解和溶出。
**Mitigation:** 
- 使用防潮包装材料
- 控制制剂储存环境湿度<60%

**Risk 3 (Manufacturability):** 
制剂可能因辅料相互作用导致生产工艺复杂化。
**Mitigation:** 
- 优化制剂配方，减少辅料种类
- 采用标准化生产流程

**4.0 Recommended Next Steps (Critical Path):**
1. 进行稳定性研究，验证预测结果
2. 优化配方，减少水解风险
3. 选择合适的包装材料和储存条件
4. 建立质量控制体系，确保生产过程可控

请根据上述建议，制定详细的实施计划，并定期进行风险评估和验证。"""


def parse_consultation_result(consultation_text: str) -> Dict[str, Any]:
    """解析会诊结果为结构化数据"""
    
    # 实际应用中应使用更复杂的解析逻辑
    # 这里简化处理
    sections = {
        "executive_summary": "",
        "primary_risk": "",
        "risk_matrix": [],
        "next_steps": []
    }
    
    # 按段落分析
    lines = consultation_text.split('\n')
    current_section = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        if "Executive Summary" in line:
            current_section = "executive_summary"
        elif "Primary Risk Hypothesis" in line:
            current_section = "primary_risk"
        elif "Risk Matrix" in line:
            current_section = "risk_matrix"
        elif "Recommended Next Steps" in line:
            current_section = "next_steps"
        elif current_section:
            if current_section == "executive_summary":
                sections["executive_summary"] += line + " "
            elif current_section == "primary_risk":
                sections["primary_risk"] += line + " "
            elif current_section == "risk_matrix" and "Risk" in line:
                sections["risk_matrix"].append(line)
            elif current_section == "next_steps" and line.startswith(tuple("1234567890")):
                sections["next_steps"].append(line)
    
    return sections


# 内部函数实现
def analyze_drug_structure_internal(smiles: str) -> Dict[str, Any]:
    """内部药物结构分析函数"""
    try:
        from rdkit import Chem
        from rdkit.Chem import Descriptors
        
        mol = Chem.MolFromSmiles(smiles)
        if not mol:
            raise ValueError("Invalid SMILES string")
        
        # 官能团识别
        functional_groups = {
            "has_ester": mol.HasSubstructMatch(Chem.MolFromSmarts("[C](=[O])[O][C]")),
            "has_amide": mol.HasSubstructMatch(Chem.MolFromSmarts("[C](=[O])[N]")),
            "has_phenol": mol.HasSubstructMatch(Chem.MolFromSmarts("c[OH]")),
            "has_thiol": mol.HasSubstructMatch(Chem.MolFromSmarts("[SH]")),
            "has_amine": mol.HasSubstructMatch(Chem.MolFromSmarts("[NX3;H2,H1;!$(NC=O)]")),
            "has_beta_lactam": mol.HasSubstructMatch(Chem.MolFromSmarts("[C]1([C](=[O])[N][C]1)")),
            "has_aromatic": Descriptors.NumAromaticRings(mol) > 0,
            "has_double_bond": mol.HasSubstructMatch(Chem.MolFromSmarts("[C]=[C]"))
        }
        
        # 物理化学性质
        properties = {
            "molecular_weight": Descriptors.MolWt(mol),
            "logP": Descriptors.MolLogP(mol),
            "tpsa": Descriptors.TPSA(mol),
            "hbd": Descriptors.NumHDonors(mol),
            "hba": Descriptors.NumHAcceptors(mol),
            "rotatable_bonds": Descriptors.NumRotatableBonds(mol),
            "aromatic_rings": Descriptors.NumAromaticRings(mol)
        }
        
    except ImportError:
        logger.warning("RDKit未安装，使用简化的结构分析")
        # 简化的分析，基于SMILES字符串
        functional_groups = {
            "has_ester": "C(=O)O" in smiles,
            "has_amide": "C(=O)N" in smiles,
            "has_phenol": "c[OH]" in smiles or "cO" in smiles,
            "has_thiol": "SH" in smiles,
            "has_amine": "N" in smiles,
            "has_beta_lactam": False,  # 难以从SMILES简单判断
            "has_aromatic": "c" in smiles,
            "has_double_bond": "=" in smiles
        }
        
        properties = {
            "molecular_weight": len(smiles) * 10,  # 粗略估计
            "logP": 2.0,  # 默认值
            "tpsa": 50.0,  # 默认值
            "hbd": smiles.count("OH") + smiles.count("NH"),
            "hba": smiles.count("O") + smiles.count("N"),
            "rotatable_bonds": smiles.count("-"),
            "aromatic_rings": smiles.count("c") // 6
        }
    
    # 降解风险评估
    degradation_risks = []
    
    if functional_groups["has_ester"]:
        degradation_risks.append({
            "type": "水解",
            "severity": "高",
            "mechanism": "酯键断裂",
            "prevention": ["pH控制(4-6)", "降低水分", "使用干燥剂"]
        })
    
    if functional_groups["has_amide"]:
        degradation_risks.append({
            "type": "水解",
            "severity": "中",
            "mechanism": "酰胺键断裂",
            "prevention": ["pH控制", "降低温度"]
        })
    
    if functional_groups["has_phenol"] or functional_groups["has_thiol"]:
        degradation_risks.append({
            "type": "氧化",
            "severity": "高",
            "mechanism": "自由基氧化",
            "prevention": ["抗氧化剂", "充氮保护", "避光"]
        })
    
    # 稳定性评分
    stability_score = 100
    for risk in degradation_risks:
        if risk["severity"] == "高":
            stability_score -= 20
        elif risk["severity"] == "中":
            stability_score -= 10
    
    return {
        "functional_groups": functional_groups,
        "properties": properties,
        "degradation_risks": degradation_risks,
        "stability_score": max(stability_score, 0),
        "moisture_sensitive": functional_groups["has_ester"] or functional_groups["has_amide"],
        "oxygen_sensitive": functional_groups["has_phenol"] or functional_groups["has_thiol"],
        "light_sensitive": functional_groups["has_aromatic"]
    }


def assess_compatibility_internal(
    drug_smiles: str,
    excipients: List[Dict[str, Any]],
    conditions: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """内部相容性评估函数"""
    if conditions is None:
        conditions = {"temperature": 25, "humidity": 60, "ph": 7}
    
    compatibility_results = []
    overall_risk = "low"
    critical_incompatibilities = []
    
    for excipient in excipients:
        # 使用真实的相容性评估服务
        # 1. 获取辅料详细信息
        excipient_info = ExcipientDataService.get_excipient_info(excipient["name"])
        
        # 2. 评估相容性
        risk_assessment = {
            "overall_risk": "low",
            "chemical_incompatibility": [],
            "recommendations": []
        }
        
        # 基于已知的相互作用规则评估
        if excipient_info:
            # 检查已知的不相容组合
            incompatibilities = excipient_info.get("incompatibilities", "")
            
            # 简单的规则评估
            if "酸" in incompatibilities and "酯" in drug_smiles:
                risk_assessment["overall_risk"] = "medium"
                risk_assessment["chemical_incompatibility"].append("酯键可能在酸性条件下水解")
                risk_assessment["recommendations"].append("控制pH值，避免酸性环境")
            
            if excipient["name"] == "硬脂酸镁" and ("C(=O)O" in drug_smiles or "C(=O)N" in drug_smiles):
                risk_assessment["overall_risk"] = "high"
                risk_assessment["chemical_incompatibility"].append("硬脂酸镁的碱性可能催化酯键或酰胺键水解")
                risk_assessment["recommendations"].append("考虑使用硬脂酸或其他润滑剂替代")
            
            if excipient["name"] == "乳糖" and "N" in drug_smiles:
                risk_assessment["overall_risk"] = "medium"
                risk_assessment["chemical_incompatibility"].append("可能发生美拉德反应")
                risk_assessment["recommendations"].append("控制水分含量，使用无水乳糖")
        
        compatibility_results.append({
            "excipient": excipient["name"],
            "risk_level": risk_assessment["overall_risk"],
            "incompatibilities": risk_assessment.get("chemical_incompatibility", []),
            "recommendations": risk_assessment.get("recommendations", [])
        })
        
        if risk_assessment["overall_risk"] == "high":
            overall_risk = "high"
            critical_incompatibilities.append(excipient["name"])
        elif risk_assessment["overall_risk"] == "medium" and overall_risk != "high":
            overall_risk = "medium"
    
    return {
        "compatibility_results": compatibility_results,
        "overall_risk": overall_risk,
        "critical_incompatibilities": critical_incompatibilities
    }


def predict_stability_internal(
    drug_smiles: str,
    formulation: Dict[str, Any],
    stability_data: List[Dict[str, Any]],
    conditions: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """内部稳定性预测函数"""
    import numpy as np
    
    # 按测试项分组数据
    items_data = {}
    for point in stability_data:
        item = point.get("item", "assay")
        if item not in items_data:
            items_data[item] = {"times": [], "values": []}
        items_data[item]["times"].append(float(point["time_point"]))
        items_data[item]["values"].append(float(point["value"]))
    
    # 动力学分析
    predictions = {}
    for item, data in items_data.items():
        if len(data["times"]) >= 3:
            times = np.array(data["times"])
            values = np.array(data["values"])
            
            # 拟合模型
            model_results = kinetic_model.fit_best_model(times, values)
            
            if "best_model" in model_results:
                best_model = model_results["best_model"]
                best_result = model_results[best_model]
                
                # 预测货架期
                shelf_life = kinetic_model.predict_shelf_life(
                    best_model,
                    best_result["params"],
                    threshold=90.0
                )
                
                predictions[item] = {
                    "model": best_model,
                    "r_squared": best_result["r_squared"],
                    "shelf_life_months": shelf_life["t_shelf"],
                    "confidence_interval": [shelf_life["ci_lower"], shelf_life["ci_upper"]]
                }
    
    # 降解途径预测
    degradation_pathways = []
    
    # 基于结构分析预测降解途径
    structure_analysis = analyze_drug_structure_internal(drug_smiles)
    
    if structure_analysis.get("degradation_risks"):
        for risk in structure_analysis["degradation_risks"]:
            pathway = {
                "pathway": risk["type"],
                "probability": 0.8 if risk["severity"] == "高" else 0.5,
                "mechanism": risk["mechanism"],
                "prevention": risk["prevention"]
            }
            degradation_pathways.append(pathway)
    
    return {
        "kinetic_predictions": predictions,
        "degradation_pathways": degradation_pathways,
        "overall_shelf_life": min([p["shelf_life_months"] for p in predictions.values()]) if predictions else None
    }


def design_study_internal(
    drug_smiles: str,
    target_shelf_life: float,
    dosage_form: str,
    zone: str
) -> Dict[str, Any]:
    """内部研究设计函数"""
    # ICH条件
    ich_conditions = {
        "I": {"long_term": "21°C/45%RH", "accelerated": "30°C/65%RH"},
        "II": {"long_term": "25°C/60%RH", "accelerated": "40°C/75%RH"},
        "III": {"long_term": "30°C/35%RH", "accelerated": "40°C/75%RH"},
        "IVa": {"long_term": "30°C/65%RH", "accelerated": "40°C/75%RH"},
        "IVb": {"long_term": "30°C/75%RH", "accelerated": "40°C/75%RH"}
    }
    
    conditions = ich_conditions.get(zone, ich_conditions["IVa"])
    
    # 时间点
    time_points = {
        "long_term": [0, 3, 6, 9, 12, 18, 24, 36],
        "accelerated": [0, 1, 2, 3, 6],
        "intermediate": [0, 3, 6, 9, 12] if zone in ["II", "IVa", "IVb"] else None
    }
    
    # 检测项目
    testing_items = ["性状", "含量", "有关物质", "水分"]
    if dosage_form == "片剂":
        testing_items.extend(["崩解时限", "溶出度", "硬度"])
    elif dosage_form == "胶囊":
        testing_items.extend(["崩解时限", "溶出度", "装量差异"])
    elif dosage_form == "注射剂":
        testing_items.extend(["pH值", "渗透压", "可见异物", "不溶性微粒", "细菌内毒素"])
    
    # 加速试验设计
    accelerated_design = kinetic_model.design_accelerated_study(
        target_shelf_life,
        float(conditions["long_term"].split("°C")[0]),
        80.0  # 默认活化能
    )
    
    return {
        "conditions": conditions,
        "time_points": time_points,
        "testing_items": testing_items,
        "accelerated_design": accelerated_design
    }


def generate_comprehensive_recommendations(
    structure_analysis: Dict[str, Any],
    compatibility_assessment: Dict[str, Any],
    stability_prediction: Optional[Dict[str, Any]],
    special_requirements: Optional[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """生成综合建议"""
    recommendations = []
    
    # 基于结构分析的建议
    if structure_analysis.get("moisture_sensitive"):
        recommendations.append({
            "category": "包装",
            "priority": "高",
            "recommendation": "使用高阻隔性包装材料（如铝塑泡罩）并添加干燥剂",
            "rationale": "药物含有易水解官能团"
        })
    
    if structure_analysis.get("oxygen_sensitive"):
        recommendations.append({
            "category": "包装",
            "priority": "高",
            "recommendation": "充氮包装或添加脱氧剂",
            "rationale": "药物含有易氧化官能团"
        })
    
    # 基于相容性评估的建议
    if compatibility_assessment.get("critical_incompatibilities"):
        recommendations.append({
            "category": "处方",
            "priority": "高",
            "recommendation": f"替换不相容辅料：{', '.join(compatibility_assessment['critical_incompatibilities'])}",
            "rationale": "存在严重的原辅料不相容性"
        })
    
    # 基于稳定性预测的建议
    if stability_prediction and stability_prediction.get("overall_shelf_life"):
        if stability_prediction["overall_shelf_life"] < 12:
            recommendations.append({
                "category": "配方优化",
                "priority": "高",
                "recommendation": "需要重新设计配方以达到最低货架期要求",
                "rationale": f"预测货架期仅为{stability_prediction['overall_shelf_life']:.1f}个月"
            })
    
    return recommendations


def generate_executive_summary(results: Dict[str, Any]) -> Dict[str, Any]:
    """生成执行摘要"""
    summary = {
        "overall_assessment": "",
        "key_findings": [],
        "critical_actions": [],
        "timeline": ""
    }
    
    # 整体评估
    structure_score = results["analysis_sections"]["structure_analysis"]["stability_score"]
    compatibility_risk = results["analysis_sections"]["compatibility_assessment"]["overall_risk"]
    
    if structure_score >= 80 and compatibility_risk == "low":
        summary["overall_assessment"] = "该药物制剂具有良好的稳定性前景"
    elif structure_score >= 60 or compatibility_risk == "medium":
        summary["overall_assessment"] = "该药物制剂需要适当的稳定化措施"
    else:
        summary["overall_assessment"] = "该药物制剂面临显著的稳定性挑战"
    
    # 关键发现
    if results["analysis_sections"]["structure_analysis"]["degradation_risks"]:
        summary["key_findings"].append(
            f"主要降解风险：{results['analysis_sections']['structure_analysis']['degradation_risks'][0]['type']}"
        )
    
    if results["analysis_sections"]["compatibility_assessment"]["critical_incompatibilities"]:
        summary["key_findings"].append(
            f"关键不相容性：{len(results['analysis_sections']['compatibility_assessment']['critical_incompatibilities'])}个辅料"
        )
    
    # 关键行动
    if results.get("comprehensive_recommendations"):
        for rec in results["comprehensive_recommendations"][:3]:
            if rec["priority"] == "高":
                summary["critical_actions"].append(rec["recommendation"])
    
    # 时间线
    summary["timeline"] = "建议在3个月内完成配方优化，6个月内启动正式稳定性研究"
    
    return summary


@router.post("/comprehensive-formulation-analysis")
def comprehensive_formulation_analysis(
    request: ComprehensiveAnalysisRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    综合配方分析 - 使用真实数据源
    """
    try:
        # 1. 数据验证
        if not request.drug or not request.excipients:
            raise HTTPException(status_code=400, detail="缺少必要的输入数据")
        
        # 2. 获取药物详细信息
        drug_info = None
        if request.drug.structure:  # 如果有SMILES结构
            # 尝试从外部数据库获取更多信息
            drug_info = get_drug_info_from_external(
                name=request.drug.name,
                smiles=request.drug.structure
            )
        
        # 3. 相容性评估
        compatibility_results = []
        for excipient in request.excipients:
            # 从开放数据源获取辅料信息
            excipient_info = open_data_service.get_excipient_info(name=excipient.name)
            
            # 搜索相互作用
            interactions = open_data_service.search_interactions(
                drug_name=request.drug.name,
                excipient_name=excipient.name
            )
            
            # 评估风险等级
            risk_level = "低"
            if interactions:
                severities = [i.get('severity', 'low').lower() for i in interactions]
                if '高' in severities or 'high' in severities:
                    risk_level = "高"
                elif '中' in severities or 'medium' in severities:
                    risk_level = "中"
            
            compatibility_results.append({
                "excipient": excipient.name,
                "risk_level": risk_level,
                "interactions": interactions,
                "recommendations": _get_recommendations(risk_level, excipient.name)
            })
        
        # 4. 稳定性预测
        stability_prediction = {
            "predicted_shelf_life": "24个月",  # 这里应调用稳定性预测模块
            "confidence": 0.85,
            "key_factors": ["温度", "湿度", "光照"]
        }
        
        # 5. 生成建议
        formulation_recommendations = _generate_formulation_recommendations(
            request.drug,
            request.excipients,
            compatibility_results
        )
        
        # 6. 保存分析结果
        # analysis = FormulationAnalysis(
        #     user_id=current_user["id"],
        #     drug_name=request.drug.name,
        #     excipients=json.dumps([e.dict() for e in request.excipients]),
        #     analysis_results=json.dumps({
        #         "compatibility": compatibility_results,
        #         "stability": stability_prediction,
        #         "recommendations": formulation_recommendations
        #     }),
        #     created_at=datetime.now()
        # )
        # db.add(analysis)
        # db.commit()
        
        return {
            "status": "success",
            "drug_profile": {
                "name": request.drug.name,
                "structure": request.drug.structure,
                "additional_info": drug_info
            },
            "compatibility_assessment": compatibility_results,
            "stability_prediction": stability_prediction,
            "formulation_recommendations": formulation_recommendations,
            "analysis_id": None,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def _get_recommendations(risk_level: str, excipient_name: str) -> List[str]:
    """基于风险等级生成建议"""
    if risk_level == "高":
        return [
            f"建议避免使用{excipient_name}",
            "考虑替代辅料",
            "如必须使用，需进行详细的相容性试验"
        ]
    elif risk_level == "中":
        return [
            f"使用{excipient_name}需谨慎",
            "建议进行加速稳定性试验",
            "考虑添加稳定剂"
        ]
    else:
        return [
            f"{excipient_name}与药物相容性良好",
            "按常规条件储存即可"
        ]

def _generate_formulation_recommendations(drug, excipients, compatibility_results) -> Dict[str, Any]:
    """生成配方建议"""
    high_risk_excipients = [
        r["excipient"] for r in compatibility_results 
        if r["risk_level"] == "高"
    ]
    
    return {
        "overall_risk": "高" if high_risk_excipients else "低",
        "key_concerns": high_risk_excipients,
        "suggested_modifications": [
            f"替换{exc}为更稳定的辅料" for exc in high_risk_excipients
        ],
        "storage_conditions": {
            "temperature": "25°C ± 2°C",
            "humidity": "60% RH ± 5%",
            "light": "避光保存"
        }
    } 