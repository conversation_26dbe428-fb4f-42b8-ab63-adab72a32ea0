from fastapi import APIRouter, HTTPException, UploadFile, File, Body, Request, Depends
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import requests
import re
import xml.etree.ElementTree as ET
import hashlib
import random
import time
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.drug import DrugORM
from app.models.environment import EnvironmentORM
from app.services import get_db

router = APIRouter()

class IdentifyRequest(BaseModel):
    name: Optional[str] = None
    formula: Optional[str] = None
    cas: Optional[str] = None
    smiles: Optional[str] = None

class IdentifyResponse(BaseModel):
    name: str
    formula: str
    cas: str
    smiles: str
    structure_image_url: str

class IdentifySourceResult(BaseModel):
    source: str  # 数据来源
    confidence: float  # 置信度
    detail: Dict[str, Any]  # 详细信息（如IdentifyResponse结构）
    used_en_name: Optional[str] = None  # 若发生自动翻译，记录用到的英文名

class FeedbackRequest(BaseModel):
    content: str
    related: Optional[Any] = None

class Drug(BaseModel):
    id: str
    name: str
    formula: str = None
    cas: str = None
    smiles: str = None
    structure_image_url: str = None

class DrugCreateRequest(BaseModel):
    name: str
    formula: str = None
    cas: str = None
    smiles: str = None
    structure_image_url: str = None

class Environment(BaseModel):
    id: str
    temperature: str = None
    humidity: str = None
    packaging: str = None
    batch: str = None
    sample_code: str = None

class EnvironmentCreateRequest(BaseModel):
    temperature: str = None
    humidity: str = None
    packaging: str = None
    batch: str = None
    sample_code: str = None

# 本地药品字典，优先本地识别
local_drug_dict = {
    "右美托咪定": {
        "name": "右美托咪定",
        "formula": "C13H16N2",
        "cas": "113775-47-6",
        "smiles": "C1=CC=C(C=C1)C(CN2C=NC=N2)C",
        "structure_image_url": "https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid=5311066&t=l"
    },
    "对乙酰氨基酚": {
        "name": "对乙酰氨基酚",
        "formula": "C8H9NO2",
        "cas": "103-90-2",
        "smiles": "CC(=O)NC1=CC=C(C=C1)O",
        "structure_image_url": "https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid=1983&t=l"
    },
    "布洛芬": {
        "name": "布洛芬",
        "formula": "C13H18O2",
        "cas": "15687-27-1",
        "smiles": "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O",
        "structure_image_url": "https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid=3672&t=l"
    },
    "阿司匹林": {
        "name": "阿司匹林",
        "formula": "C9H8O4",
        "cas": "50-78-2",
        "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
        "structure_image_url": "https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid=2244&t=l"
    },
    # 可继续补充更多常见药物...
}

# 百度翻译API配置（请替换为你自己的appid和密钥）
BAIDU_APPID = 'your_baidu_appid'
BAIDU_SECRET = 'your_baidu_secret'

# 自动翻译工具函数
def translate_to_en(text: str) -> str:
    if not text or re.match(r'^[A-Za-z0-9\-\s]+$', text):
        return text  # 已是英文或数字
    url = 'https://fanyi-api.baidu.com/api/trans/vip/translate'
    salt = str(random.randint(32768, 65536))
    sign = hashlib.md5((BAIDU_APPID + text + salt + BAIDU_SECRET).encode('utf-8')).hexdigest()
    params = {
        'q': text,
        'from': 'zh',
        'to': 'en',
        'appid': BAIDU_APPID,
        'salt': salt,
        'sign': sign
    }
    try:
        r = requests.get(url, params=params, timeout=5)
        result = r.json()
        if 'trans_result' in result:
            return result['trans_result'][0]['dst']
    except Exception as e:
        print('DEBUG translate error:', e)
    return text

# 预留开放中文药物数据库API接口
def try_open_cn_drugdb(query: str) -> Optional[IdentifyResponse]:
    # 示例：如有免费API可在此实现
    # url = f'https://api.drugdb.cn/free/search?q={query}'
    # try:
    #     r = requests.get(url, timeout=5)
    #     if r.status_code == 200:
    #         data = r.json()
    #         if data and 'name' in data:
    #             return IdentifyResponse(
    #                 name=data['name'],
    #                 formula=data.get('formula', ''),
    #                 cas=data.get('cas', ''),
    #                 smiles=data.get('smiles', ''),
    #                 structure_image_url=data.get('structure_image_url', '')
    #             )
    # except Exception as e:
    #     print('DEBUG open_cn_drugdb error:', e)
    return None

# =====================
# 多源识别工具函数提升到文件级作用域
# =====================
def try_pubchem(info):
    try:
        def get_pubchem_props(url):
            try:
                r = requests.get(url, timeout=5)
                print('DEBUG PubChem url:', url, 'status:', r.status_code)
                if r.status_code != 200:
                    return None
                props = r.json().get('PropertyTable', {}).get('Properties', [])
                if not props:
                    return None
                return props[0]
            except Exception as e:
                print('DEBUG PubChem error:', e)
                return None
        # 优先顺序：SMILES > CAS > formula > name
        if info.smiles:
            url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{info.smiles}/property/Title,MolecularFormula,CAS,CanonicalSMILES/JSON'
            props = get_pubchem_props(url)
            if props:
                return IdentifyResponse(name=props.get('Title', ''), formula=props.get('MolecularFormula', ''), cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', info.smiles), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{info.smiles}/PNG')
        if info.cas:
            url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cas/{info.cas}/property/Title,MolecularFormula,CanonicalSMILES/JSON'
            props = get_pubchem_props(url)
            if props:
                return IdentifyResponse(name=props.get('Title', ''), formula=props.get('MolecularFormula', ''), cas=info.cas, smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cas/{info.cas}/PNG')
        if info.formula:
            url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/formula/{info.formula}/property/Title,CAS,CanonicalSMILES/JSON'
            props = get_pubchem_props(url)
            if props:
                return IdentifyResponse(name=props.get('Title', ''), formula=info.formula, cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/formula/{info.formula}/PNG')
        if info.name:
            url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{info.name}/property/Title,MolecularFormula,CAS,CanonicalSMILES/JSON'
            props = get_pubchem_props(url)
            if props:
                return IdentifyResponse(name=props.get('Title', info.name), formula=props.get('MolecularFormula', ''), cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{info.name}/PNG')
        return None
    except Exception as e:
        print('DEBUG PubChem outer error:', e)
        return None

def try_chembl(info):
    try:
        query = info.name or info.cas or info.smiles or info.formula
        if not query:
            return None
        url = f'https://www.ebi.ac.uk/chembl/api/data/molecule/search/{query}.json'
        try:
            r = requests.get(url, timeout=5)
            print('DEBUG Chembl url:', url, 'status:', r.status_code)
            if r.status_code != 200:
                return None
            molecules = r.json().get('molecules', [])
            if not molecules:
                return None
            mol = molecules[0]
            return IdentifyResponse(
                name=mol.get('pref_name', query),
                formula=mol.get('molecule_properties', {}).get('full_molformula', ''),
                cas=mol.get('molecule_properties', {}).get('cas', ''),
                smiles=mol.get('molecule_structures', {}).get('canonical_smiles', ''),
                structure_image_url=f'https://www.ebi.ac.uk/chembl/api/data/image/{mol.get("molecule_chembl_id", "")}'
            )
        except Exception as e:
            print('DEBUG Chembl error:', e)
            return None
    except Exception as e:
        print('DEBUG Chembl outer error:', e)
        return None

def try_drugbank(info):
    try:
        if info.name:
            url = f'https://go.drugbank.com/unearth/q?searcher=drugs&query={info.name}'
            print('DEBUG DrugBank url:', url)
            r = requests.get(url, timeout=5)
            if r.status_code == 200 and 'drugbank.com/drugs/' in r.text:
                import re
                m = re.search(r'/drugs/(DB\d+)', r.text)
                if m:
                    dbid = m.group(1)
                    struct_url = f'https://go.drugbank.com/structures/{dbid}/image.svg'
                    return IdentifyResponse(name=info.name, formula='', cas='', smiles='', structure_image_url=struct_url)
        return None
    except Exception as e:
        print('DEBUG DrugBank error:', e)
        return None

def try_kegg(info):
    try:
        if info.name:
            url = f'http://rest.kegg.jp/find/drug/{info.name}'
            print('DEBUG KEGG url:', url)
            r = requests.get(url, timeout=5)
            if r.status_code == 200 and r.text:
                first_line = r.text.split('\n')[0]
                if first_line:
                    kegg_id = first_line.split('\t')[0]
                    detail_url = f'http://rest.kegg.jp/get/{kegg_id}'
                    detail = requests.get(detail_url, timeout=5).text
                    formula = ''
                    cas = ''
                    for line in detail.split('\n'):
                        if line.startswith('FORMULA'):
                            formula = line.split('FORMULA')[1].strip()
                        if line.startswith('CAS'): cas = line.split('CAS')[1].strip()
                    struct_url = f'https://www.kegg.jp/Fig/drug/{kegg_id}.gif'
                    return IdentifyResponse(name=info.name, formula=formula, cas=cas, smiles='', structure_image_url=struct_url)
        return None
    except Exception as e:
        print('DEBUG KEGG error:', e)
        return None

def try_chinese_drug_db(info):
    cn_dict = [
        {'name': '右美托咪定', 'en': 'dexmedetomidine', 'alias': ['Dexmedetomidine'], 'formula': 'C13H16N2', 'cas': '113775-47-6', 'smiles': 'C1=CC=C(C=C1)C(CN2C=NC=N2)C3=CC=CC=C3', 'pubchem': '5311066'},
        {'name': '对乙酰氨基酚', 'en': 'paracetamol', 'alias': ['acetaminophen', '扑热息痛'], 'formula': 'C8H9NO2', 'cas': '103-90-2', 'smiles': 'CC(=O)NC1=CC=C(O)C=C1', 'pubchem': '1983'},
        {'name': '布洛芬', 'en': 'ibuprofen', 'alias': ['Ibuprofen'], 'formula': 'C13H18O2', 'cas': '15687-27-1', 'smiles': 'CC(C)CC1=CC=C(C=C1)C(C)C(=O)O', 'pubchem': '3672'},
        {'name': '阿司匹林', 'en': 'aspirin', 'alias': ['Aspirin', '乙酰水杨酸'], 'formula': 'C9H8O4', 'cas': '50-78-2', 'smiles': 'CC(=O)OC1=CC=CC=C1C(=O)O', 'pubchem': '2244'},
    ]
    q_name = (info.name or '').lower()
    q_cas = (info.cas or '').lower()
    q_smiles = (info.smiles or '').lower()
    q_formula = (info.formula or '').lower()
    for v in cn_dict:
        all_names = [v['name'].lower(), v['en'].lower()] + [a.lower() for a in v.get('alias', [])]
        if q_name and any(q_name == n or q_name in n for n in all_names):
            struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
            return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
        if q_cas and q_cas == v['cas'].lower():
            struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
            return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
        if q_smiles and q_smiles == v['smiles'].lower():
            struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
            return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
        if q_formula and q_formula == v['formula'].lower():
            struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
            return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
    return None

def try_local_dict(info):
    for key, value in local_drug_dict.items():
        if info.name and (info.name == key or info.name.lower() == value["name"].lower()):
            return IdentifyResponse(**value)
        if info.cas and info.cas == value["cas"]:
            return IdentifyResponse(**value)
        if info.smiles and info.smiles == value["smiles"]:
            return IdentifyResponse(**value)
    return None

@router.post('/identify', response_model=IdentifyResponse)
async def identify_drug(
    info: IdentifyRequest = Body(...)
):
    print("DEBUG info:", info)
    # 先联网数据库（PubChem/ChEMBL/DrugBank/KEGG）
    def try_pubchem():
        try:
            def get_pubchem_props(url):
                try:
                    r = requests.get(url, timeout=5)
                    print('DEBUG PubChem url:', url, 'status:', r.status_code)
                    if r.status_code != 200:
                        return None
                    props = r.json().get('PropertyTable', {}).get('Properties', [])
                    if not props:
                        return None
                    return props[0]
                except Exception as e:
                    print('DEBUG PubChem error:', e)
                    return None
            # 优先顺序：SMILES > CAS > formula > name
            if info.smiles:
                url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{info.smiles}/property/Title,MolecularFormula,CAS,CanonicalSMILES/JSON'
                props = get_pubchem_props(url)
                if props:
                    return IdentifyResponse(name=props.get('Title', ''), formula=props.get('MolecularFormula', ''), cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', info.smiles), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{info.smiles}/PNG')
            if info.cas:
                url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cas/{info.cas}/property/Title,MolecularFormula,CanonicalSMILES/JSON'
                props = get_pubchem_props(url)
                if props:
                    return IdentifyResponse(name=props.get('Title', ''), formula=props.get('MolecularFormula', ''), cas=info.cas, smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cas/{info.cas}/PNG')
            if info.formula:
                url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/formula/{info.formula}/property/Title,CAS,CanonicalSMILES/JSON'
                props = get_pubchem_props(url)
                if props:
                    return IdentifyResponse(name=props.get('Title', ''), formula=info.formula, cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/formula/{info.formula}/PNG')
            if info.name:
                url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{info.name}/property/Title,MolecularFormula,CAS,CanonicalSMILES/JSON'
                props = get_pubchem_props(url)
                if props:
                    return IdentifyResponse(name=props.get('Title', info.name), formula=props.get('MolecularFormula', ''), cas=props.get('CAS', ''), smiles=props.get('CanonicalSMILES', ''), structure_image_url=f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{info.name}/PNG')
            return None
        except Exception as e:
            print('DEBUG PubChem outer error:', e)
            return None
    def try_chembl():
        try:
            query = info.name or info.cas or info.smiles or info.formula
            if not query:
                return None
            url = f'https://www.ebi.ac.uk/chembl/api/data/molecule/search/{query}.json'
            try:
                r = requests.get(url, timeout=5)
                print('DEBUG Chembl url:', url, 'status:', r.status_code)
                if r.status_code != 200:
                    return None
                molecules = r.json().get('molecules', [])
                if not molecules:
                    return None
                mol = molecules[0]
                return IdentifyResponse(
                    name=mol.get('pref_name', query),
                    formula=mol.get('molecule_properties', {}).get('full_molformula', ''),
                    cas=mol.get('molecule_properties', {}).get('cas', ''),
                    smiles=mol.get('molecule_structures', {}).get('canonical_smiles', ''),
                    structure_image_url=f'https://www.ebi.ac.uk/chembl/api/data/image/{mol.get("molecule_chembl_id", "")}'
                )
            except Exception as e:
                print('DEBUG Chembl error:', e)
                return None
        except Exception as e:
            print('DEBUG Chembl outer error:', e)
            return None
    def try_drugbank():
        try:
            # DrugBank REST API 需注册账号，部分数据可用公开接口
            # 这里只做简单GET示例，实际可用官方API或爬虫
            if info.name:
                url = f'https://go.drugbank.com/unearth/q?searcher=drugs&query={info.name}'
                print('DEBUG DrugBank url:', url)
                r = requests.get(url, timeout=5)
                if r.status_code == 200 and 'drugbank.com/drugs/' in r.text:
                    # 简单提取DrugBank ID
                    import re
                    m = re.search(r'/drugs/(DB\d+)', r.text)
                    if m:
                        dbid = m.group(1)
                        # DrugBank结构图片
                        struct_url = f'https://go.drugbank.com/structures/{dbid}/image.svg'
                        return IdentifyResponse(name=info.name, formula='', cas='', smiles='', structure_image_url=struct_url)
            return None
        except Exception as e:
            print('DEBUG DrugBank error:', e)
            return None
    def try_kegg():
        try:
            # KEGG REST API
            if info.name:
                url = f'http://rest.kegg.jp/find/drug/{info.name}'
                print('DEBUG KEGG url:', url)
                r = requests.get(url, timeout=5)
                if r.status_code == 200 and r.text:
                    # 取第一个KEGG ID
                    first_line = r.text.split('\n')[0]
                    if first_line:
                        kegg_id = first_line.split('\t')[0]
                        # 获取详细信息
                        detail_url = f'http://rest.kegg.jp/get/{kegg_id}'
                        detail = requests.get(detail_url, timeout=5).text
                        # 简单提取分子式、CAS
                        formula = ''
                        cas = ''
                        for line in detail.split('\n'):
                            if line.startswith('FORMULA'):
                                formula = line.split('FORMULA')[1].strip()
                            if line.startswith('CAS'): cas = line.split('CAS')[1].strip()
                        struct_url = f'https://www.kegg.jp/Fig/drug/{kegg_id}.gif'
                        return IdentifyResponse(name=info.name, formula=formula, cas=cas, smiles='', structure_image_url=struct_url)
            return None
        except Exception as e:
            print('DEBUG KEGG error:', e)
            return None
    def try_chinese_drug_db():
        cn_dict = [
            {'name': '右美托咪定', 'en': 'dexmedetomidine', 'alias': ['Dexmedetomidine'], 'formula': 'C13H16N2', 'cas': '113775-47-6', 'smiles': 'C1=CC=C(C=C1)C(CN2C=NC=N2)C3=CC=CC=C3', 'pubchem': '5311066'},
            {'name': '对乙酰氨基酚', 'en': 'paracetamol', 'alias': ['acetaminophen', '扑热息痛'], 'formula': 'C8H9NO2', 'cas': '103-90-2', 'smiles': 'CC(=O)NC1=CC=C(O)C=C1', 'pubchem': '1983'},
            {'name': '布洛芬', 'en': 'ibuprofen', 'alias': ['Ibuprofen'], 'formula': 'C13H18O2', 'cas': '15687-27-1', 'smiles': 'CC(C)CC1=CC=C(C=C1)C(C)C(=O)O', 'pubchem': '3672'},
            {'name': '阿司匹林', 'en': 'aspirin', 'alias': ['Aspirin', '乙酰水杨酸'], 'formula': 'C9H8O4', 'cas': '50-78-2', 'smiles': 'CC(=O)OC1=CC=CC=C1C(=O)O', 'pubchem': '2244'},
        ]
        q_name = (info.name or '').lower()
        q_cas = (info.cas or '').lower()
        q_smiles = (info.smiles or '').lower()
        q_formula = (info.formula or '').lower()
        for v in cn_dict:
            all_names = [v['name'].lower(), v['en'].lower()] + [a.lower() for a in v.get('alias', [])]
            if q_name and any(q_name == n or q_name in n for n in all_names):
                struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
                return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
            if q_cas and q_cas == v['cas'].lower():
                struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
                return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
            if q_smiles and q_smiles == v['smiles'].lower():
                struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
                return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
            if q_formula and q_formula == v['formula'].lower():
                struct_url = f'https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{v["pubchem"]}/PNG' if v.get('pubchem') else ''
                return IdentifyResponse(name=v['en'], formula=v['formula'], cas=v['cas'], smiles=v['smiles'], structure_image_url=struct_url)
        return None
    # 最后才查本地字典
    def try_local_dict():
        for key, value in local_drug_dict.items():
            if info.name and (info.name == key or info.name.lower() == value["name"].lower()):
                return IdentifyResponse(**value)
            if info.cas and info.cas == value["cas"]:
                return IdentifyResponse(**value)
            if info.smiles and info.smiles == value["smiles"]:
                return IdentifyResponse(**value)
        return None
    # 新优先级：PubChem > ChEMBL > DrugBank > KEGG > 本地别名 > 本地字典
    result = try_pubchem() or try_chembl() or try_drugbank() or try_kegg() or try_chinese_drug_db() or try_local_dict()
    if result:
        print('DEBUG identify result:', result)
        return result
    raise HTTPException(status_code=404, detail='未查到该药品信息，请检查名称、CAS、SMILES或尝试其他字段/别名。')

@router.post('/identify/image', response_model=IdentifyResponse)
async def identify_drug_image(file: UploadFile = File(...)):
    # 结构图识别，优先图片
    return IdentifyResponse(name='图片识别药物', formula='未知', cas='未知', smiles='未知', structure_image_url='https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?cid=2244&t=l')

@router.post('/identify/v2', response_model=List[IdentifySourceResult])
async def identify_drug_v2(
    info: IdentifyRequest = Body(...)
):
    print('DEBUG info:', info)
    results = []
    # 1. 自动翻译
    en_name = None
    used_en_name = None
    if info.name:
        en_name = translate_to_en(info.name)
        if en_name and en_name != info.name:
            used_en_name = en_name
        print(f'DEBUG translate: {info.name} -> {en_name}')
    # 2. 多源检索
    # PubChem
    pubchem_info = None
    if en_name and en_name != info.name:
        tmp = IdentifyRequest(**{**info.dict(), 'name': en_name})
        pubchem_info = try_pubchem(tmp)
    if not pubchem_info:
        pubchem_info = try_pubchem(info)
    if pubchem_info:
        results.append(IdentifySourceResult(source='PubChem', confidence=0.95, detail=pubchem_info.dict(), used_en_name=used_en_name))
    # ChEMBL
    chembl_info = None
    if en_name and en_name != info.name:
        tmp = IdentifyRequest(**{**info.dict(), 'name': en_name})
        chembl_info = try_chembl(tmp)
    if not chembl_info:
        chembl_info = try_chembl(info)
    if chembl_info:
        results.append(IdentifySourceResult(source='ChEMBL', confidence=0.9, detail=chembl_info.dict(), used_en_name=used_en_name))
    # DrugBank
    drugbank_info = None
    if en_name and en_name != info.name:
        tmp = IdentifyRequest(**{**info.dict(), 'name': en_name})
        drugbank_info = try_drugbank(tmp)
    if not drugbank_info:
        drugbank_info = try_drugbank(info)
    if drugbank_info:
        results.append(IdentifySourceResult(source='DrugBank', confidence=0.85, detail=drugbank_info.dict(), used_en_name=used_en_name))
    # KEGG
    kegg_info = None
    if en_name and en_name != info.name:
        tmp = IdentifyRequest(**{**info.dict(), 'name': en_name})
        kegg_info = try_kegg(tmp)
    if not kegg_info:
        kegg_info = try_kegg(info)
    if kegg_info:
        results.append(IdentifySourceResult(source='KEGG', confidence=0.8, detail=kegg_info.dict(), used_en_name=used_en_name))
    # 开放中文药物数据库
    cn_api_info = try_open_cn_drugdb(info.name or en_name or '')
    if cn_api_info:
        results.append(IdentifySourceResult(source='OpenCNDrugDB', confidence=0.8, detail=cn_api_info.dict(), used_en_name=used_en_name))
    # 本地别名/药典
    cn_dict_info = try_chinese_drug_db(info)
    if cn_dict_info:
        results.append(IdentifySourceResult(source='LocalAlias', confidence=0.7, detail=cn_dict_info.dict(), used_en_name=used_en_name))
    # 本地字典
    local_info = try_local_dict(info)
    if local_info:
        results.append(IdentifySourceResult(source='LocalDict', confidence=0.6, detail=local_info.dict(), used_en_name=used_en_name))
    # 结果融合与排序
    if not results:
        raise HTTPException(status_code=404, detail='未查到该药品信息，请检查名称、CAS、SMILES或尝试其他字段/别名。')
    # 按置信度排序
    results.sort(key=lambda x: -x.confidence)
    print('DEBUG identify v2 results:', results)
    return results

@router.post('/feedback')
async def feedback_api(feedback: FeedbackRequest):
    log_line = f"{datetime.now().isoformat()}\t{feedback.content}\t{str(feedback.related)}\n"
    with open('feedback.log', 'a', encoding='utf-8') as f:
        f.write(log_line)
    return {'msg': 'ok'}

@router.get('/identify/suggest')
def identify_suggest(query: str = '', type: str = 'drug'):
    # 本地字典、别名、历史、常用药物/辅料/包材
    names = set()
    # 药品
    if type == 'drug':
        for k in local_drug_dict.keys():
            if query.lower() in k.lower():
                names.add(k)
        # 别名/常用药物
        for v in [
            '布洛芬', '对乙酰氨基酚', '阿司匹林', '右美托咪定', '扑热息痛', '氯化钠', '葡萄糖', '阿莫西林', '头孢拉定', '盐酸二甲双胍'
        ]:
            if query.lower() in v.lower():
                names.add(v)
    # 辅料
    elif type == 'excipient':
        for v in ['乳糖', '淀粉', '微晶纤维素', '羟丙基甲基纤维素', '聚乙烯吡咯烷酮', '羧甲基淀粉钠', '硬脂酸镁', '滑石粉']:
            if query.lower() in v.lower():
                names.add(v)
    # 包材
    elif type == 'packaging':
        for v in ['铝箔', '泡罩', '瓶装', '塑料袋', '玻璃瓶', '铝塑板']:
            if query.lower() in v.lower():
                names.add(v)
    # TODO: 可扩展远程API、历史项目、药典等
    # 示例：
    # - 查询历史项目数据库
    # - 查询药典API
    # - 查询第三方药物知识库
    # 具体实现可根据实际API文档补充
    return list(names)

@router.post('/drug/create', response_model=Drug)
def create_drug(data: DrugCreateRequest, db: Session = Depends(get_db)):
    import uuid
    did = str(uuid.uuid4())
    drug = DrugORM(id=did, name=data.name, formula=data.formula, cas=data.cas, smiles=data.smiles, structure_image_url=data.structure_image_url)
    db.add(drug)
    db.commit()
    db.refresh(drug)
    return Drug(
        id=drug.id,
        name=drug.name,
        formula=drug.formula,
        cas=drug.cas,
        smiles=drug.smiles,
        structure_image_url=drug.structure_image_url
    )

@router.get('/drug/list', response_model=List[Drug])
def list_drugs(db: Session = Depends(get_db)):
    drugs = db.query(DrugORM).all()
    return [Drug(
        id=d.id,
        name=d.name,
        formula=d.formula,
        cas=d.cas,
        smiles=d.smiles,
        structure_image_url=d.structure_image_url
    ) for d in drugs]

@router.delete('/drug/delete/{drug_id}')
def delete_drug(drug_id: str, db: Session = Depends(get_db)):
    drug = db.query(DrugORM).filter(DrugORM.id == drug_id).first()
    if not drug:
        raise HTTPException(status_code=404, detail='药物不存在')
    db.delete(drug)
    db.commit()
    return {'msg': 'deleted'}

@router.post('/environment/create', response_model=Environment)
def create_environment(data: EnvironmentCreateRequest, db: Session = Depends(get_db)):
    import uuid
    eid = str(uuid.uuid4())
    env = EnvironmentORM(id=eid, temperature=data.temperature, humidity=data.humidity, packaging=data.packaging, batch=data.batch, sample_code=data.sample_code)
    db.add(env)
    db.commit()
    db.refresh(env)
    return Environment(
        id=env.id,
        temperature=env.temperature,
        humidity=env.humidity,
        packaging=env.packaging,
        batch=env.batch,
        sample_code=env.sample_code
    )

@router.get('/environment/list', response_model=List[Environment])
def list_environments(db: Session = Depends(get_db)):
    envs = db.query(EnvironmentORM).all()
    return [Environment(
        id=e.id,
        temperature=e.temperature,
        humidity=e.humidity,
        packaging=e.packaging,
        batch=e.batch,
        sample_code=e.sample_code
    ) for e in envs]

@router.delete('/environment/delete/{environment_id}')
def delete_environment(environment_id: str, db: Session = Depends(get_db)):
    env = db.query(EnvironmentORM).filter(EnvironmentORM.id == environment_id).first()
    if not env:
        raise HTTPException(status_code=404, detail='环境参数不存在')
    db.delete(env)
    db.commit()
    return {'msg': 'deleted'}