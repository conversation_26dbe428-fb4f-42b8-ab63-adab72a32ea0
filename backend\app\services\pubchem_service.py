"""
PubChem数据服务
提供化合物信息查询、生物活性数据和相似化合物搜索
"""
import logging
import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
import json
from datetime import datetime, timedelta
import os
from pathlib import Path

logger = logging.getLogger(__name__)


class PubChemService:
    """PubChem API服务类"""
    
    def __init__(self):
        self.base_url = "https://pubchem.ncbi.nlm.nih.gov/rest/pug"
        self.base_url_view = "https://pubchem.ncbi.nlm.nih.gov/rest/pug_view"
        self.cache_dir = Path("cache/pubchem")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_duration = timedelta(days=30)
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        return self.session
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"
    
    def _is_cache_valid(self, cache_path: Path) -> bool:
        """检查缓存是否有效"""
        if not cache_path.exists():
            return False
        
        # 检查缓存时间
        mtime = datetime.fromtimestamp(cache_path.stat().st_mtime)
        return datetime.now() - mtime < self.cache_duration
    
    async def search_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """通过名称搜索化合物"""
        cache_key = f"name_{name.lower().replace(' ', '_')}"
        cache_path = self._get_cache_path(cache_key)
        
        # 检查缓存
        if self._is_cache_valid(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        try:
            session = await self._get_session()
            
            # 搜索化合物
            search_url = f"{self.base_url}/compound/name/{name}/cids/JSON"
            async with session.get(search_url) as resp:
                if resp.status != 200:
                    logger.warning(f"PubChem搜索失败: {name}")
                    return None
                
                data = await resp.json()
                cids = data.get("IdentifierList", {}).get("CID", [])
                
                if not cids:
                    return None
                
                # 获取第一个化合物的详细信息
                cid = cids[0]
                compound_info = await self.get_compound_by_cid(cid)
                
                # 缓存结果
                if compound_info:
                    with open(cache_path, 'w', encoding='utf-8') as f:
                        json.dump(compound_info, f, ensure_ascii=False, indent=2)
                
                return compound_info
                
        except Exception as e:
            logger.error(f"PubChem搜索错误: {e}")
            return None
    
    async def get_compound_by_cid(self, cid: int) -> Optional[Dict[str, Any]]:
        """通过CID获取化合物详细信息"""
        cache_key = f"cid_{cid}"
        cache_path = self._get_cache_path(cache_key)
        
        # 检查缓存
        if self._is_cache_valid(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        try:
            session = await self._get_session()
            
            # 获取基本信息
            props_url = f"{self.base_url}/compound/cid/{cid}/property/MolecularFormula,MolecularWeight,CanonicalSMILES,InChI,InChIKey,IUPACName,XLogP,TPSA,HBondDonorCount,HBondAcceptorCount,RotatableBondCount/JSON"
            
            async with session.get(props_url) as resp:
                if resp.status != 200:
                    return None
                
                data = await resp.json()
                properties = data.get("PropertyTable", {}).get("Properties", [])
                
                if not properties:
                    return None
                
                compound_info = properties[0]
                compound_info["PubChemCID"] = cid
                
                # 获取同义词
                synonyms = await self._get_synonyms(cid)
                if synonyms:
                    compound_info["Synonyms"] = synonyms[:10]  # 最多10个同义词
                
                # 获取生物活性摘要
                bioactivity = await self._get_bioactivity_summary(cid)
                if bioactivity:
                    compound_info["BioactivitySummary"] = bioactivity
                
                # 缓存结果
                with open(cache_path, 'w', encoding='utf-8') as f:
                    json.dump(compound_info, f, ensure_ascii=False, indent=2)
                
                return compound_info
                
        except Exception as e:
            logger.error(f"获取化合物信息错误 (CID: {cid}): {e}")
            return None
    
    async def _get_synonyms(self, cid: int) -> List[str]:
        """获取化合物同义词"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/compound/cid/{cid}/synonyms/JSON"
            
            async with session.get(url) as resp:
                if resp.status != 200:
                    return []
                
                data = await resp.json()
                synonyms = data.get("InformationList", {}).get("Information", [])
                
                if synonyms:
                    return synonyms[0].get("Synonym", [])
                
                return []
                
        except Exception as e:
            logger.error(f"获取同义词错误: {e}")
            return []
    
    async def _get_bioactivity_summary(self, cid: int) -> Dict[str, Any]:
        """获取生物活性摘要"""
        try:
            session = await self._get_session()
            
            # 获取生物测定数据统计
            url = f"{self.base_url}/compound/cid/{cid}/assaysummary/JSON"
            
            async with session.get(url) as resp:
                if resp.status != 200:
                    return {}
                
                data = await resp.json()
                
                # 解析活性数据
                summary = {
                    "total_assays": 0,
                    "active_assays": 0,
                    "inactive_assays": 0,
                    "activity_types": []
                }
                
                assay_summary = data.get("AssaySummary", {})
                if assay_summary:
                    summary["total_assays"] = assay_summary.get("TotalAssays", 0)
                    summary["active_assays"] = assay_summary.get("ActiveAssays", 0)
                    summary["inactive_assays"] = assay_summary.get("InactiveAssays", 0)
                
                return summary
                
        except Exception as e:
            logger.error(f"获取生物活性摘要错误: {e}")
            return {}
    
    async def search_similar_compounds(
        self, 
        smiles: str, 
        threshold: float = 0.8,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索相似化合物"""
        try:
            session = await self._get_session()
            
            # 使用Tanimoto相似度搜索
            # 注意：PubChem的相似度阈值是整数（80表示80%）
            threshold_int = int(threshold * 100)
            
            # 首先获取查询化合物的CID
            search_url = f"{self.base_url}/compound/smiles/{smiles}/cids/JSON"
            async with session.get(search_url) as resp:
                if resp.status != 200:
                    logger.warning(f"SMILES搜索失败: {smiles}")
                    return []
                
                data = await resp.json()
                cids = data.get("IdentifierList", {}).get("CID", [])
                
                if not cids:
                    return []
                
                query_cid = cids[0]
            
            # 搜索相似化合物
            similar_url = f"{self.base_url}/compound/fastsimilarity_2d/cid/{query_cid}/cids/JSON?Threshold={threshold_int}&MaxRecords={max_results}"
            
            async with session.get(similar_url) as resp:
                if resp.status != 200:
                    return []
                
                data = await resp.json()
                similar_cids = data.get("IdentifierList", {}).get("CID", [])
                
                # 获取相似化合物的详细信息
                similar_compounds = []
                for cid in similar_cids[:max_results]:
                    if cid == query_cid:  # 跳过查询化合物本身
                        continue
                    
                    compound_info = await self.get_compound_by_cid(cid)
                    if compound_info:
                        # 计算相似度（这里简化处理，实际相似度需要专门计算）
                        compound_info["similarity"] = threshold
                        similar_compounds.append(compound_info)
                
                return similar_compounds
                
        except Exception as e:
            logger.error(f"搜索相似化合物错误: {e}")
            return []
    
    async def get_drug_interactions(self, cid: int) -> List[Dict[str, Any]]:
        """获取药物相互作用信息"""
        try:
            session = await self._get_session()
            
            # 使用PubChem的药物相互作用数据
            # 注意：这个功能在PubChem中比较有限，主要通过文献关联
            url = f"{self.base_url_view}/data/compound/{cid}/JSON"
            
            async with session.get(url) as resp:
                if resp.status != 200:
                    return []
                
                data = await resp.json()
                
                # 解析相互作用信息
                interactions = []
                
                # 这里需要解析复杂的PubChem数据结构
                # 实际实现时需要根据具体的数据格式调整
                
                return interactions
                
        except Exception as e:
            logger.error(f"获取药物相互作用错误: {e}")
            return []
    
    async def get_patent_info(self, cid: int) -> Dict[str, Any]:
        """获取专利信息"""
        try:
            session = await self._get_session()
            
            # PubChem提供专利信息
            url = f"{self.base_url}/compound/cid/{cid}/xrefs/PatentID/JSON"
            
            async with session.get(url) as resp:
                if resp.status != 200:
                    return {}
                
                data = await resp.json()
                
                # 解析专利信息
                patent_info = {
                    "total_patents": 0,
                    "patent_ids": []
                }
                
                patents = data.get("InformationList", {}).get("Information", [])
                if patents:
                    patent_ids = patents[0].get("PatentID", [])
                    patent_info["total_patents"] = len(patent_ids)
                    patent_info["patent_ids"] = patent_ids[:20]  # 最多20个专利
                
                return patent_info
                
        except Exception as e:
            logger.error(f"获取专利信息错误: {e}")
            return {}
    
    async def get_pharmacology_data(self, cid: int) -> Dict[str, Any]:
        """获取药理学数据"""
        try:
            # 使用PubChem的药理学分类数据
            session = await self._get_session()
            
            # 获取药理学分类
            url = f"{self.base_url_view}/data/compound/{cid}/JSON?heading=Drug+and+Medication+Information"
            
            async with session.get(url) as resp:
                if resp.status != 200:
                    return {}
                
                data = await resp.json()
                
                # 解析药理学信息
                pharmacology = {
                    "therapeutic_uses": [],
                    "mechanism_of_action": "",
                    "pharmacological_class": []
                }
                
                # 这里需要根据PubChem的实际数据结构解析
                # 实际实现时需要详细处理
                
                return pharmacology
                
        except Exception as e:
            logger.error(f"获取药理学数据错误: {e}")
            return {}


# 创建全局实例
pubchem_service = PubChemService()


# 便捷函数
async def search_pubchem(name: str) -> Optional[Dict[str, Any]]:
    """搜索PubChem化合物"""
    async with pubchem_service as service:
        return await service.search_by_name(name)


async def get_similar_compounds(smiles: str, threshold: float = 0.8) -> List[Dict[str, Any]]:
    """获取相似化合物"""
    async with pubchem_service as service:
        return await service.search_similar_compounds(smiles, threshold) 