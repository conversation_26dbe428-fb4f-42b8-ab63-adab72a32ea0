#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
后端主入口文件
"""
import os
import sys
import importlib.util

# 确保项目根目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 检查虚拟环境中的aiohttp
venv_path = os.path.join(project_root, '.venv')
if os.path.exists(venv_path):
    venv_site_packages = os.path.join(venv_path, 'Lib', 'site-packages')
    if venv_site_packages not in sys.path:
        sys.path.insert(0, venv_site_packages)
        print(f"已添加虚拟环境路径: {venv_site_packages}")

# 检查aiohttp是否可用
aiohttp_spec = importlib.util.find_spec("aiohttp")
if aiohttp_spec:
    import aiohttp
    # 只在调试模式下显示版本信息
    # print(f"✓ aiohttp已安装，版本: {aiohttp.__version__}")
else:
    # 移除警告输出，因为系统已经正确安装了aiohttp
    pass
    # 尝试安装aiohttp的代码也可以移除，因为应该通过requirements.txt管理依赖
    # try:
    #     import pip
    #     print("正在尝试自动安装aiohttp...")
    #     pip.main(['install', 'aiohttp'])
    #     print("aiohttp安装完成，请重新启动服务")
    # except Exception as e:
    #     print(f"自动安装aiohttp失败: {e}")

# 从app包导入FastAPI应用
try:
    from app.main import app
    print("✓ 应用成功导入")
except ImportError as e:
    print(f"导入应用失败: {e}")
    raise

# 当直接运行此文件时
if __name__ == "__main__":
    import uvicorn
    
    print("===== 启动药物稳定性助手后端服务 =====")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python解释器: {sys.executable}")
    
    # 启动服务 - 统一使用8000端口
    uvicorn.run(
        "app.main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        reload_dirs=[current_dir]
    ) 