from fastapi import APIRouter
from typing import Optional
import importlib
from . import system
import logging
from . import excipient_compatibility
# 临时注释掉批量导入，改为按需导入
# from . import (
#     ai_stability, stability_predict, structure_api, pubchem_api,
#     formulation_analysis, compatibility_assess, identify, packaging,
#     project, excipient, drug_info, data_import_api,
#     report_api, comprehensive_analysis, ai_enhanced, analysis, auth,
#     excipient_management
# )

logger = logging.getLogger(__name__)

# 创建主API路由
api_router = APIRouter()
api_router.include_router(excipient_compatibility.router, prefix="/excipient-compatibility", tags=["Excipient Compatibility"])

# 尝试导入各个路由模块
modules_to_import = [
    ('project', 'project', ''),  # project.py已经有prefix="/projects"
    ('auth', 'auth', ''),        # auth.py已经有prefix="/auth"
    ('export', 'export', ''),
    ('analysis', 'analysis', ''),
    ('identify', 'identify', ''),
    ('ai_stability', 'ai-stability', ''),
    ('ai_compatibility', 'ai-compatibility', ''),
    ('stability_predict', 'stability-prediction', ''),
    ('excipient', 'excipient', ''),  # excipient.py已经有prefix="/excipient"
    ('formulation_analysis', 'formulation', ''),
    ('compatibility_assess', 'compatibility', ''),
    ('comprehensive_analysis', 'comprehensive', ''),
    ('ai_enhanced', 'ai-enhanced', ''),
    ('drug_info', 'drug-info', ''),
    ('packaging', 'packaging', ''),
    ('system', 'system', ''),       # system.py已经有prefix="/system"
    ('excipient_management', 'excipient-management', ''),
    ('structure_api', 'structure', ''),
    ('data_import_api', 'data-import', ''),
    ('pubchem_api', 'pubchem', ''),
    ('report_api', 'report', '')
]

# 重新启用自动加载关键模块
for module_name, tag, prefix in modules_to_import:
    try:
        # 使用importlib.import_module进行相对导入
        module = importlib.import_module(f'.{module_name}', package='app.api')
        if hasattr(module, 'router'):
            api_router.include_router(module.router, prefix=prefix, tags=[tag])
            print(f"✓ 已加载 {module_name} 路由 (前缀: {prefix or '无'})")
    except ImportError as e:
        logger.warning(f"无法导入 {module_name} 路由 - {e}")
    except Exception as e:
        logger.error(f"加载 {module_name} 路由时出错 - {e}")

# 添加基本的健康检查路由
@api_router.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

# auth路由已在上面的自动加载循环中处理

api_router.include_router(system.router, prefix="/system", tags=["system"])

# 添加缺失的路由映射，以匹配前端调用的路径
try:
    # 导入必要的模块
    from . import excipient, compatibility_assess
    
    # 添加辅料搜索路由（前端调用 /api/excipients/search）
    @api_router.get("/excipients/search")
    async def search_excipients_forward(query: str = None):
        """转发到excipient模块的搜索功能"""
        from . import excipient
        return await excipient.search_excipients_api(query)
    
    # 添加辅料数据库路由（前端调用 /api/excipients-database）
    @api_router.get("/excipients-database")
    async def get_excipients_database():
        """获取辅料数据库列表"""
        try:
            return {
                "categories": {
                    "填充剂": ["微晶纤维素", "乳糖", "甘露醇", "淀粉", "磷酸氢钙", "硫酸钙"],
                    "崩解剂": ["羧甲基纤维素钠", "交联聚维酮", "羟丙纤维素", "淀粉", "交联羧甲基纤维素钠"],
                    "润滑剂": ["硬脂酸镁", "硬脂酸", "滑石粉", "聚乙二醇", "硬脂酸钙"],
                    "黏合剂": ["聚维酮K30", "羟丙甲纤维素", "聚乙二醇", "明胶", "阿拉伯胶"],
                    "表面活性剂": ["十二烷基硫酸钠", "聚山梨酯80", "泊洛沙姆", "聚山梨酯20"],
                    "防腐剂": ["苯甲酸钠", "尼泊金甲酯", "山梨酸钾", "苯扎氯铵"],
                    "抗氧化剂": ["维生素E", "BHT", "抗坏血酸", "亚硫酸钠", "柠檬酸"]
                },
                "properties": {
                    "excipient_compatibility_matrix": []
                }
            }
        except Exception as e:
            return {
                "categories": {},
                "properties": {}
            }
    
    # 添加相容性案例库路由（前端调用 /api/compatibility/cases）
    @api_router.get("/compatibility/cases")
    async def get_compatibility_cases():
        """获取相容性案例库"""
        return {
            "cases": [
                {
                    "id": "1",
                    "drug": "阿司匹林",
                    "excipient": "乳糖",
                    "result": "不相容",
                    "mechanism": "酰胺化反应",
                    "reference": "J Pharm Sci. 2005;94(7):1396-417"
                },
                {
                    "id": "2",
                    "drug": "对乙酰氨基酚",
                    "excipient": "聚维酮",
                    "result": "相容",
                    "mechanism": "无明显相互作用",
                    "reference": "Drug Dev Ind Pharm. 2003;29(10):1119-28"
                }
            ]
        }
    
    # 添加辅料分类路由
    @api_router.get("/excipient/categories")
    async def get_excipient_categories():
        """获取辅料分类"""
        return {
            "填充剂": [
                "微晶纤维素", "乳糖一水合物", "无水乳糖", "甘露醇", "山梨醇", "木糖醇",
                "玉米淀粉", "预胶化淀粉", "磷酸氢钙二水合物", "无水磷酸氢钙", "硫酸钙二水合物",
                "碳酸钙", "硅酸钙", "蔗糖", "葡萄糖", "果糖", "麦芽糖醇", "异麦芽酮糖醇"
            ],
            "崩解剂": [
                "羧甲基纤维素钠", "交联聚维酮", "交联羧甲基纤维素钠", "羟丙纤维素",
                "低取代羟丙纤维素", "淀粉", "预胶化淀粉", "羧甲基淀粉钠", "交联聚乙烯吡咯烷酮",
                "藻酸钠", "琼脂", "果胶", "甲基纤维素"
            ],
            "润滑剂": [
                "硬脂酸镁", "硬脂酸", "硬脂酸钙", "硬脂酸锌", "滑石粉", "聚乙二醇4000",
                "聚乙二醇6000", "聚乙二醇8000", "氢化植物油", "棕榈酸", "月桂酸",
                "硬脂富马酸钠", "苯甲酸钠", "十二烷基硫酸钠"
            ],
            "黏合剂": [
                "聚维酮K30", "聚维酮K90", "羟丙甲纤维素", "羟丙纤维素", "甲基纤维素",
                "聚乙二醇", "明胶", "阿拉伯胶", "淀粉浆", "糊精", "羧甲基纤维素钠",
                "海藻酸钠", "壳聚糖", "聚乙烯醇", "聚丙烯酸", "丙烯酸树脂"
            ],
            "包衣材料": [
                "羟丙甲纤维素", "羟丙纤维素", "乙基纤维素", "聚乙烯醇", "聚维酮",
                "丙烯酸树脂", "虫胶", "明胶", "蔗糖", "滑石粉", "二氧化钛",
                "氧化铁", "柠檬黄", "胭脂红", "亮蓝"
            ],
            "表面活性剂": [
                "十二烷基硫酸钠", "聚山梨酯20", "聚山梨酯40", "聚山梨酯60", "聚山梨酯80",
                "泊洛沙姆188", "泊洛沙姆407", "聚氧乙烯蓖麻油", "卵磷脂", "胆酸钠",
                "脱氧胆酸钠", "十六烷基三甲基溴化铵", "苯扎氯铵"
            ],
            "防腐剂": [
                "苯甲酸", "苯甲酸钠", "山梨酸", "山梨酸钾", "尼泊金甲酯", "尼泊金乙酯",
                "尼泊金丙酯", "尼泊金丁酯", "苯扎氯铵", "氯化苄烷铵", "硫柳汞",
                "苯酚", "间甲酚", "氯仿", "乙醇"
            ],
            "抗氧化剂": [
                "维生素E", "维生素C", "抗坏血酸", "抗坏血酸钠", "BHT", "BHA",
                "没食子酸丙酯", "亚硫酸钠", "亚硫酸氢钠", "焦亚硫酸钠", "柠檬酸",
                "柠檬酸钠", "EDTA二钠", "硫代硫酸钠", "半胱氨酸"
            ],
            "pH调节剂": [
                "柠檬酸", "柠檬酸钠", "磷酸", "磷酸钠", "磷酸氢二钠", "磷酸二氢钠",
                "乳酸", "乳酸钠", "醋酸", "醋酸钠", "氢氧化钠", "氢氧化钾",
                "碳酸钠", "碳酸氢钠", "氨水", "盐酸", "硫酸"
            ],
            "甜味剂": [
                "蔗糖", "果糖", "葡萄糖", "乳糖", "甘露醇", "山梨醇", "木糖醇",
                "麦芽糖醇", "异麦芽酮糖醇", "糖精钠", "阿斯巴甜", "安赛蜜",
                "三氯蔗糖", "甜菊糖苷", "纽甜"
            ],
            "着色剂": [
                "二氧化钛", "氧化铁红", "氧化铁黄", "氧化铁黑", "柠檬黄",
                "日落黄", "胭脂红", "苋菜红", "亮蓝", "靛蓝", "叶绿素铜钠",
                "β-胡萝卜素", "辣椒红", "红曲红", "栀子黄"
            ]
        }

    # 添加辅料详情路由
    @api_router.get("/excipient/details")
    async def get_excipient_details():
        """获取辅料详情"""
        return [
            # 填充剂
            {"id": 1, "name": "微晶纤维素", "category": "填充剂", "function": "填充", "description": "最常用的片剂填充剂，流动性好，可压性强"},
            {"id": 2, "name": "乳糖一水合物", "category": "填充剂", "function": "填充", "description": "传统填充剂，甜味，水溶性好"},
            {"id": 3, "name": "甘露醇", "category": "填充剂", "function": "填充", "description": "甜味填充剂，适用于咀嚼片"},
            {"id": 4, "name": "玉米淀粉", "category": "填充剂", "function": "填充", "description": "天然填充剂，兼具崩解功能"},
            {"id": 5, "name": "磷酸氢钙二水合物", "category": "填充剂", "function": "填充", "description": "无机填充剂，硬度高"},

            # 崩解剂
            {"id": 6, "name": "羧甲基纤维素钠", "category": "崩解剂", "function": "崩解", "description": "常用崩解剂，遇水膨胀"},
            {"id": 7, "name": "交联聚维酮", "category": "崩解剂", "function": "崩解", "description": "超级崩解剂，崩解速度快"},
            {"id": 8, "name": "交联羧甲基纤维素钠", "category": "崩解剂", "function": "崩解", "description": "超级崩解剂，吸水膨胀"},
            {"id": 9, "name": "低取代羟丙纤维素", "category": "崩解剂", "function": "崩解", "description": "新型崩解剂，机理独特"},

            # 润滑剂
            {"id": 10, "name": "硬脂酸镁", "category": "润滑剂", "function": "润滑", "description": "最常用润滑剂，用量0.5-1%"},
            {"id": 11, "name": "硬脂酸", "category": "润滑剂", "function": "润滑", "description": "润滑剂，也可作助流剂"},
            {"id": 12, "name": "滑石粉", "category": "润滑剂", "function": "润滑", "description": "传统润滑剂，助流效果好"},
            {"id": 13, "name": "聚乙二醇6000", "category": "润滑剂", "function": "润滑", "description": "水溶性润滑剂"},

            # 黏合剂
            {"id": 14, "name": "聚维酮K30", "category": "黏合剂", "function": "黏合", "description": "常用黏合剂，粘接力强"},
            {"id": 15, "name": "羟丙甲纤维素", "category": "黏合剂", "function": "黏合", "description": "纤维素醚类黏合剂"},
            {"id": 16, "name": "羟丙纤维素", "category": "黏合剂", "function": "黏合", "description": "热塑性黏合剂"},
            {"id": 17, "name": "明胶", "category": "黏合剂", "function": "黏合", "description": "天然黏合剂，用于胶囊"},

            # 包衣材料
            {"id": 18, "name": "羟丙甲纤维素", "category": "包衣材料", "function": "包衣", "description": "薄膜包衣材料，成膜性好"},
            {"id": 19, "name": "乙基纤维素", "category": "包衣材料", "function": "包衣", "description": "肠溶包衣材料"},
            {"id": 20, "name": "丙烯酸树脂", "category": "包衣材料", "function": "包衣", "description": "pH敏感包衣材料"},

            # 表面活性剂
            {"id": 21, "name": "聚山梨酯80", "category": "表面活性剂", "function": "增溶", "description": "非离子表面活性剂"},
            {"id": 22, "name": "十二烷基硫酸钠", "category": "表面活性剂", "function": "增溶", "description": "阴离子表面活性剂"},
            {"id": 23, "name": "泊洛沙姆188", "category": "表面活性剂", "function": "增溶", "description": "嵌段共聚物表面活性剂"},

            # 防腐剂
            {"id": 24, "name": "苯甲酸钠", "category": "防腐剂", "function": "防腐", "description": "常用防腐剂，pH<4.5有效"},
            {"id": 25, "name": "尼泊金甲酯", "category": "防腐剂", "function": "防腐", "description": "对羟基苯甲酸酯类防腐剂"},
            {"id": 26, "name": "山梨酸钾", "category": "防腐剂", "function": "防腐", "description": "广谱防腐剂，安全性好"},

            # 抗氧化剂
            {"id": 27, "name": "维生素E", "category": "抗氧化剂", "function": "抗氧化", "description": "脂溶性抗氧化剂"},
            {"id": 28, "name": "抗坏血酸", "category": "抗氧化剂", "function": "抗氧化", "description": "水溶性抗氧化剂"},
            {"id": 29, "name": "BHT", "category": "抗氧化剂", "function": "抗氧化", "description": "合成抗氧化剂，效果强"},
            {"id": 30, "name": "柠檬酸", "category": "抗氧化剂", "function": "抗氧化", "description": "金属螯合剂，协同抗氧化"},

            # pH调节剂
            {"id": 31, "name": "柠檬酸", "category": "pH调节剂", "function": "调节pH", "description": "有机酸，调节pH至酸性"},
            {"id": 32, "name": "磷酸氢二钠", "category": "pH调节剂", "function": "调节pH", "description": "磷酸盐缓冲系统"},
            {"id": 33, "name": "碳酸氢钠", "category": "pH调节剂", "function": "调节pH", "description": "碱性pH调节剂"},

            # 甜味剂
            {"id": 34, "name": "蔗糖", "category": "甜味剂", "function": "矫味", "description": "天然甜味剂"},
            {"id": 35, "name": "糖精钠", "category": "甜味剂", "function": "矫味", "description": "人工甜味剂，甜度高"},
            {"id": 36, "name": "阿斯巴甜", "category": "甜味剂", "function": "矫味", "description": "氨基酸类甜味剂"},

            # 着色剂
            {"id": 37, "name": "二氧化钛", "category": "着色剂", "function": "着色", "description": "白色着色剂，遮光性好"},
            {"id": 38, "name": "氧化铁红", "category": "着色剂", "function": "着色", "description": "红色无机着色剂"},
            {"id": 39, "name": "柠檬黄", "category": "着色剂", "function": "着色", "description": "黄色有机着色剂"},
            {"id": 40, "name": "亮蓝", "category": "着色剂", "function": "着色", "description": "蓝色有机着色剂"}
        ]

    # 添加包装材料路由
    @api_router.get("/excipient/packaging-materials")
    async def get_packaging_materials():
        """获取包装材料（详细阻隔性能参数）"""
        return [
            # 口服固体制剂包材 - PVC系列
            {
                "id": 1, "name": "PVC泡罩（透明）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "PVC", "specification": "250μm厚度", "color": "透明",
                "description": "聚氯乙烯泡罩，成本低，透明度好",
                "barrier_properties": {
                    "moisture_permeability": "15-25 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "150-300 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-10°C至+60°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "无"
                },
                "advantages": ["成本低", "透明度好", "易成型"],
                "disadvantages": ["阻湿性一般", "无光保护"],
                "suitable_drugs": ["对湿度不敏感的固体制剂"]
            },
            {
                "id": 2, "name": "PVC/PVDC泡罩（60g PVDC）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "PVC/PVDC", "specification": "PVC 250μm + PVDC 60g/m²", "color": "透明",
                "description": "PVC基材涂布60g/m² PVDC，阻湿性能提升",
                "barrier_properties": {
                    "moisture_permeability": "2-5 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "5-15 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (可见光)",
                    "temperature_resistance": "-10°C至+60°C",
                    "chemical_resistance": "优良",
                    "uv_protection": "轻微"
                },
                "advantages": ["良好阻湿性", "中等阻氧性", "成本适中"],
                "disadvantages": ["光保护有限"],
                "suitable_drugs": ["湿敏感药物", "一般稳定性药物"]
            },
            {
                "id": 3, "name": "PVC/PVDC泡罩（90g PVDC）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "PVC/PVDC", "specification": "PVC 250μm + PVDC 90g/m²", "color": "透明",
                "description": "PVC基材涂布90g/m² PVDC，高阻隔性能",
                "barrier_properties": {
                    "moisture_permeability": "0.5-2 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "1-5 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "80-85% (可见光)",
                    "temperature_resistance": "-10°C至+60°C",
                    "chemical_resistance": "优良",
                    "uv_protection": "中等"
                },
                "advantages": ["高阻湿性", "高阻氧性", "化学稳定"],
                "disadvantages": ["成本较高"],
                "suitable_drugs": ["高湿敏感药物", "氧敏感药物"]
            },
            {
                "id": 4, "name": "铝塑泡罩（冷成型）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "铝箔复合", "specification": "铝箔20μm + 尼龙25μm + PVC250μm", "color": "银色",
                "description": "冷成型铝塑泡罩，最高阻隔性能",
                "barrier_properties": {
                    "moisture_permeability": "<0.1 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "<0.1 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "0% (完全避光)",
                    "temperature_resistance": "-20°C至+80°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "完全"
                },
                "advantages": ["最高阻隔性", "完全避光", "化学惰性"],
                "disadvantages": ["成本高", "不透明"],
                "suitable_drugs": ["高敏感药物", "光敏感药物", "生物制品"]
            },

            # 瓶装包材系列
            {
                "id": 5, "name": "HDPE瓶（白色）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊/颗粒",
                "material_type": "HDPE", "specification": "壁厚1.5mm", "color": "白色",
                "description": "高密度聚乙烯瓶，白色，良好化学稳定性",
                "barrier_properties": {
                    "moisture_permeability": "0.5-1.5 g/100in²/day (38°C, 90%RH)",
                    "oxygen_permeability": "100-300 cm³/100in²/day (23°C, 0%RH)",
                    "light_transmission": "5-15% (可见光)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "优良",
                    "uv_protection": "良好"
                },
                "advantages": ["化学稳定", "成本低", "轻质"],
                "disadvantages": ["阻氧性一般"],
                "suitable_drugs": ["大包装固体制剂", "稳定性好的药物"]
            },
            {
                "id": 6, "name": "玻璃瓶（棕色I类）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊/颗粒",
                "material_type": "硼硅酸盐玻璃", "specification": "USP I类，壁厚2mm", "color": "棕色",
                "description": "I类硼硅酸盐玻璃瓶，棕色，最高化学稳定性",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "10-15% (可见光), <1% (UV)",
                    "temperature_resistance": "-40°C至+500°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "优异"
                },
                "advantages": ["完全阻隔", "化学惰性", "可高温灭菌"],
                "disadvantages": ["重量大", "易碎", "成本高"],
                "suitable_drugs": ["光敏感药物", "高价值药物", "注射用药物"]
            },
            {
                "id": 7, "name": "玻璃瓶（透明I类）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊/颗粒",
                "material_type": "硼硅酸盐玻璃", "specification": "USP I类，壁厚2mm", "color": "透明",
                "description": "I类硼硅酸盐玻璃瓶，透明，可观察内容物",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "90-95% (可见光), 85% (UV)",
                    "temperature_resistance": "-40°C至+500°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["完全阻隔", "化学惰性", "透明观察"],
                "disadvantages": ["无光保护", "重量大", "易碎"],
                "suitable_drugs": ["对光不敏感的高价值药物"]
            },
            {
                "id": 13, "name": "PVC/PVDC泡罩（30g PVDC）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "PVC/PVDC", "specification": "PVC 250μm + PVDC 30g/m²", "color": "透明",
                "description": "PVC基材涂布30g/m² PVDC，基础阻湿改善型包装",
                "barrier_properties": {
                    "moisture_permeability": "8-15 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "50-100 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "88-92% (可见光)",
                    "temperature_resistance": "-10°C至+60°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "轻微"
                },
                "advantages": ["成本适中", "基础阻湿改善", "易加工"],
                "disadvantages": ["阻隔性能有限", "光保护不足"],
                "suitable_drugs": ["轻度湿敏感药物", "成本敏感产品"],
                "reference": "Pharmaceutical Packaging Technology, 2019; ASTM F1249-13"
            },
            {
                "id": 14, "name": "PP泡罩（聚丙烯）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "PP", "specification": "厚度300-400μm", "color": "透明/白色",
                "description": "聚丙烯泡罩，优异的化学稳定性和耐温性",
                "barrier_properties": {
                    "moisture_permeability": "3-8 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "1500-3000 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (可见光)",
                    "temperature_resistance": "-20°C至+121°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "有限"
                },
                "advantages": ["优异耐温性", "化学稳定", "可高温灭菌", "环保可回收"],
                "disadvantages": ["氧气阻隔性差", "成本较高"],
                "suitable_drugs": ["需高温灭菌的药物", "化学稳定性要求高的药物"],
                "reference": "Journal of Pharmaceutical Sciences, 2020; USP <661>"
            },
            {
                "id": 15, "name": "Aclar薄膜（PCTFE）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊/API",
                "material_type": "PCTFE", "specification": "厚度25-88μm", "color": "透明",
                "description": "聚三氟氯乙烯薄膜，超高阻隔性能，Honeywell Aclar品牌",
                "barrier_properties": {
                    "moisture_permeability": "0.02-0.1 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "0.5-2 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-200°C至+200°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "中等"
                },
                "advantages": ["超高阻湿性", "优异阻氧性", "化学惰性", "宽温度范围"],
                "disadvantages": ["成本极高", "加工难度大", "环保问题"],
                "suitable_drugs": ["高敏感API", "生物制品", "诊断试剂"],
                "reference": "Pharmaceutical Technology, 2021; Honeywell Aclar Technical Data"
            },
            {
                "id": 16, "name": "COC泡罩（环烯烃共聚物）", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "COC", "specification": "厚度200-300μm", "color": "透明",
                "description": "环烯烃共聚物泡罩，低吸湿性，优异透明度",
                "barrier_properties": {
                    "moisture_permeability": "0.1-0.5 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "10-50 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "92-95% (可见光)",
                    "temperature_resistance": "-40°C至+140°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["极低吸湿性", "优异透明度", "化学惰性", "低萃取物"],
                "disadvantages": ["成本高", "氧气阻隔一般"],
                "suitable_drugs": ["生物制品", "高纯度API", "诊断试剂"],
                "reference": "Pharmaceutical Packaging Technology, 2020; TOPAS COC Technical Data"
            },
            {
                "id": 17, "name": "EVOH阻隔薄膜", "category": "口服固体制剂", "dosage_form": "片剂/胶囊",
                "material_type": "EVOH", "specification": "厚度15-50μm", "color": "透明",
                "description": "乙烯-乙烯醇共聚物，优异的氧气阻隔性能",
                "barrier_properties": {
                    "moisture_permeability": "15-25 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "0.1-1 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-40°C至+100°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "无"
                },
                "advantages": ["优异阻氧性", "透明度好", "成本适中"],
                "disadvantages": ["湿度敏感", "阻湿性一般"],
                "suitable_drugs": ["氧敏感药物", "维生素类", "抗氧化剂"],
                "reference": "Packaging Technology and Science, 2019; Kuraray EVOH Technical Guide"
            },

            # 口服液体制剂包材扩充
            {
                "id": 18, "name": "PET瓶（热固化）", "category": "口服液体制剂", "dosage_form": "糖浆/溶液",
                "material_type": "PET", "specification": "壁厚1-2mm，热固化处理", "color": "透明/琥珀色",
                "description": "聚对苯二甲酸乙二醇酯瓶，热固化处理提升阻隔性",
                "barrier_properties": {
                    "moisture_permeability": "1-3 g/100in²/day (38°C, 90%RH)",
                    "oxygen_permeability": "5-15 cm³/100in²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (透明), 15-25% (琥珀色)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "中等（琥珀色）"
                },
                "advantages": ["轻质", "透明度好", "可回收", "成本低"],
                "disadvantages": ["阻隔性有限", "温度敏感"],
                "suitable_drugs": ["一般液体制剂", "短期储存产品"],
                "reference": "Pharmaceutical Technology, 2020; ASTM D3985-17"
            },
            {
                "id": 19, "name": "玻璃瓶（II类钠钙玻璃）", "category": "口服液体制剂", "dosage_form": "糖浆/溶液",
                "material_type": "钠钙玻璃", "specification": "USP II类，壁厚2-3mm", "color": "透明/琥珀色",
                "description": "II类钠钙玻璃瓶，经表面处理提升化学稳定性",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "90-95% (透明), 10-20% (琥珀色)",
                    "temperature_resistance": "-40°C至+300°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "优异（琥珀色）"
                },
                "advantages": ["完全阻隔", "成本适中", "可高温灭菌"],
                "disadvantages": ["化学稳定性不如I类", "重量大"],
                "suitable_drugs": ["一般液体制剂", "成本敏感产品"],
                "reference": "USP <660>; Pharmaceutical Packaging Handbook, 2019"
            },

            # 注射剂包材系列
            {
                "id": 20, "name": "玻璃西林瓶（I类透明）", "category": "注射剂", "dosage_form": "注射液/冻干粉",
                "material_type": "硼硅酸盐玻璃", "specification": "USP I类，2-100ml", "color": "透明",
                "description": "I类硼硅酸盐玻璃西林瓶，可重复密封，适用于多剂量注射剂",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-40°C至+500°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["完全密封", "可重复使用", "化学惰性", "可高温灭菌"],
                "disadvantages": ["无光保护", "重量大", "成本高"],
                "suitable_drugs": ["多剂量注射液", "冻干粉针", "对光不敏感的注射剂"],
                "reference": "USP <660>; European Pharmacopoeia 10.0"
            },
            {
                "id": 21, "name": "COP预充式注射器", "category": "注射剂", "dosage_form": "注射液",
                "material_type": "环烯烃聚合物", "specification": "1-10ml，带安全装置", "color": "透明",
                "description": "环烯烃聚合物预充式注射器，超低萃取物，生物相容性优异",
                "barrier_properties": {
                    "moisture_permeability": "0.05-0.2 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "5-20 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "92-95% (可见光)",
                    "temperature_resistance": "-80°C至+121°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["超低萃取物", "优异透明度", "生物相容性", "可预灌装"],
                "disadvantages": ["成本极高", "加工复杂"],
                "suitable_drugs": ["生物制品", "单克隆抗体", "疫苗", "高价值注射剂"],
                "reference": "Pharmaceutical Technology, 2021; Daikyo COP Technical Data"
            },
            {
                "id": 22, "name": "多层共挤输液袋", "category": "注射剂", "dosage_form": "大容量注射液",
                "material_type": "PE/EVOH/PE", "specification": "50-3000ml，三层共挤", "color": "透明",
                "description": "聚乙烯/EVOH/聚乙烯三层共挤输液袋，优异阻氧性",
                "barrier_properties": {
                    "moisture_permeability": "2-5 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "0.1-0.5 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (可见光)",
                    "temperature_resistance": "-40°C至+121°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "无"
                },
                "advantages": ["优异阻氧性", "柔韧性好", "可高温灭菌", "成本适中"],
                "disadvantages": ["层间剥离风险", "加工复杂"],
                "suitable_drugs": ["氧敏感输液", "营养液", "血液制品"],
                "reference": "Pharmaceutical Packaging Technology, 2020; ASTM F1927-14"
            },
            {
                "id": 8, "name": "玻璃安瓿（透明I类）", "category": "注射剂", "dosage_form": "注射液",
                "material_type": "硼硅酸盐玻璃", "specification": "USP I类，1-20ml", "color": "透明",
                "description": "I类硼硅酸盐玻璃安瓿，透明，单次使用",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-40°C至+500°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["完全密封", "化学惰性", "可高温灭菌"],
                "disadvantages": ["无光保护", "易碎", "单次使用"],
                "suitable_drugs": ["注射液", "对光不敏感的药物"]
            },
            {
                "id": 9, "name": "玻璃安瓿（棕色I类）", "category": "注射剂", "dosage_form": "注射液",
                "material_type": "硼硅酸盐玻璃", "specification": "USP I类，1-20ml", "color": "棕色",
                "description": "I类硼硅酸盐玻璃安瓿，棕色，避光保护",
                "barrier_properties": {
                    "moisture_permeability": "0 g/m²/day (完全阻隔)",
                    "oxygen_permeability": "0 cm³/m²/day (完全阻隔)",
                    "light_transmission": "10-15% (可见光), <1% (UV)",
                    "temperature_resistance": "-40°C至+500°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "优异"
                },
                "advantages": ["完全密封", "避光保护", "化学惰性"],
                "disadvantages": ["不透明", "易碎", "成本较高"],
                "suitable_drugs": ["光敏感注射液", "维生素类注射液"]
            },
            {
                "id": 10, "name": "COC预充式注射器", "category": "注射剂", "dosage_form": "注射液",
                "material_type": "环烯烃共聚物", "specification": "1-3ml，带针头保护", "color": "透明",
                "description": "COC材质预充式注射器，低蛋白吸附",
                "barrier_properties": {
                    "moisture_permeability": "0.1-0.5 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "10-50 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "90-95% (可见光)",
                    "temperature_resistance": "-40°C至+121°C",
                    "chemical_resistance": "优良",
                    "uv_protection": "无"
                },
                "advantages": ["低蛋白吸附", "便于使用", "精确剂量"],
                "disadvantages": ["成本高", "无光保护"],
                "suitable_drugs": ["生物制品", "蛋白质药物", "单克隆抗体"]
            },

            # 外用制剂包材
            {
                "id": 23, "name": "层压铝管（ABL）", "category": "外用制剂", "dosage_form": "软膏/凝胶",
                "material_type": "PE/AL/PE", "specification": "铝箔12μm + PE内外层", "color": "银色/白色",
                "description": "铝塑复合层压管，优异阻隔性能，可印刷",
                "barrier_properties": {
                    "moisture_permeability": "<0.01 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "<0.01 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "0% (完全避光)",
                    "temperature_resistance": "-20°C至+80°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "完全"
                },
                "advantages": ["超高阻隔性", "完全避光", "可印刷", "成本适中"],
                "disadvantages": ["不可回收", "层间剥离风险"],
                "suitable_drugs": ["高敏感外用药", "维A酸类", "抗生素软膏"],
                "reference": "Pharmaceutical Technology, 2020; Montebello ABL Technical Guide"
            },
            {
                "id": 24, "name": "LDPE软管", "category": "外用制剂", "dosage_form": "凝胶/乳液",
                "material_type": "LDPE", "specification": "壁厚0.8-1.2mm", "color": "透明/白色/彩色",
                "description": "低密度聚乙烯软管，柔韧性好，挤压性能优异",
                "barrier_properties": {
                    "moisture_permeability": "5-15 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "500-1500 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "10-90% (取决于颜色和添加剂)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "有限"
                },
                "advantages": ["柔韧性好", "挤压性能优", "成本低", "可着色"],
                "disadvantages": ["阻隔性一般", "易渗透"],
                "suitable_drugs": ["一般外用制剂", "大包装产品", "成本敏感产品"],
                "reference": "Pharmaceutical Packaging Handbook, 2019; ASTM D1434-82"
            },
            {
                "id": 25, "name": "PP喷雾瓶", "category": "外用制剂", "dosage_form": "喷雾剂/洗剂",
                "material_type": "PP", "specification": "30-500ml，带泵头", "color": "透明/白色/彩色",
                "description": "聚丙烯喷雾瓶，化学稳定性优异，可高温灭菌",
                "barrier_properties": {
                    "moisture_permeability": "2-6 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "1000-2500 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (透明), 10-30% (有色)",
                    "temperature_resistance": "-20°C至+121°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "中等（有色）"
                },
                "advantages": ["化学稳定", "可高温灭菌", "精确喷雾", "可回收"],
                "disadvantages": ["氧气阻隔差", "成本较高"],
                "suitable_drugs": ["外用喷雾剂", "消毒剂", "需灭菌的外用液"],
                "reference": "Journal of Pharmaceutical Sciences, 2019; USP <661>"
            },
            {
                "id": 26, "name": "医用级硅胶管", "category": "外用制剂", "dosage_form": "特殊软膏",
                "material_type": "医用硅胶", "specification": "USP VI级硅胶", "color": "透明/白色",
                "description": "医用级硅胶管，生物相容性优异，适用于特殊用途",
                "barrier_properties": {
                    "moisture_permeability": "20-50 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "8000-15000 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (可见光)",
                    "temperature_resistance": "-60°C至+200°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "无"
                },
                "advantages": ["生物相容性优", "宽温度范围", "化学惰性", "柔韧性极佳"],
                "disadvantages": ["阻隔性差", "成本极高", "易吸附"],
                "suitable_drugs": ["医疗器械润滑剂", "特殊用途软膏", "生物相容性要求高的产品"],
                "reference": "USP <88>; FDA Guidance on Medical Device Materials"
            },
            {
                "id": 11, "name": "铝管（内涂层）", "category": "外用制剂", "dosage_form": "软膏/凝胶",
                "material_type": "铝合金", "specification": "内涂环氧酚醛树脂", "color": "银色",
                "description": "铝管内涂环氧酚醛树脂，阻隔性优异",
                "barrier_properties": {
                    "moisture_permeability": "<0.1 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "<0.1 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "0% (完全避光)",
                    "temperature_resistance": "-20°C至+80°C",
                    "chemical_resistance": "优良",
                    "uv_protection": "完全"
                },
                "advantages": ["完全阻隔", "避光保护", "可挤压"],
                "disadvantages": ["成本较高", "可能有金属离子迁移"],
                "suitable_drugs": ["光敏感外用药", "含活性成分的软膏"]
            },
            {
                "id": 12, "name": "PE塑料管", "category": "外用制剂", "dosage_form": "软膏/凝胶",
                "material_type": "聚乙烯", "specification": "壁厚1mm", "color": "白色/透明",
                "description": "聚乙烯塑料管，成本低，化学稳定",
                "barrier_properties": {
                    "moisture_permeability": "2-8 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "200-800 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "10-90% (取决于颜色)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "有限"
                },
                "advantages": ["成本低", "化学稳定", "可挤压"],
                "disadvantages": ["阻隔性一般", "光保护有限"],
                "suitable_drugs": ["稳定性好的外用药", "大包装产品"]
            },

            # 吸入制剂专业包材
            {
                "id": 27, "name": "铝制MDI容器（内涂层）", "category": "吸入制剂", "dosage_form": "定量吸入剂",
                "material_type": "铝合金", "specification": "内涂聚合物，10-30ml", "color": "银色",
                "description": "铝制定量吸入器容器，内涂聚合物防腐蚀，耐高压",
                "barrier_properties": {
                    "moisture_permeability": "<0.01 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "<0.01 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "0% (完全避光)",
                    "temperature_resistance": "-20°C至+50°C",
                    "chemical_resistance": "优异",
                    "uv_protection": "完全",
                    "pressure_resistance": "可承受15-20 bar压力"
                },
                "advantages": ["完全阻隔", "耐高压", "避光保护", "可回收"],
                "disadvantages": ["重量大", "成本高", "需特殊阀门"],
                "suitable_drugs": ["HFA推进剂制剂", "压力敏感药物", "光敏感吸入剂"],
                "reference": "Pharmaceutical Technology, 2021; ISPE Baseline Guide Vol.5"
            },
            {
                "id": 28, "name": "HPMC胶囊（DPI用）", "category": "吸入制剂", "dosage_form": "干粉吸入剂",
                "material_type": "HPMC", "specification": "Size 3-0，低湿度敏感", "color": "透明/有色",
                "description": "羟丙甲纤维素胶囊，专用于干粉吸入器，低湿度敏感性",
                "barrier_properties": {
                    "moisture_permeability": "高透湿性（设计特性）",
                    "oxygen_permeability": "高透氧性（设计特性）",
                    "light_transmission": "85-95% (透明), 10-50% (有色)",
                    "temperature_resistance": "-10°C至+60°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "中等（有色）"
                },
                "advantages": ["低湿度敏感", "易穿刺", "植物来源", "宗教友好"],
                "disadvantages": ["成本高", "机械强度低"],
                "suitable_drugs": ["干粉吸入剂", "植物来源药物", "宗教敏感市场"],
                "reference": "Pharmaceutical Research, 2020; Capsugel HPMC Technical Data"
            },

            # 眼用制剂专业包材
            {
                "id": 29, "name": "LDPE滴眼剂瓶（多剂量）", "category": "眼用制剂", "dosage_form": "滴眼液",
                "material_type": "LDPE", "specification": "5-15ml，带控制滴头", "color": "透明/琥珀色",
                "description": "低密度聚乙烯滴眼剂瓶，柔韧性好，挤压控制精确",
                "barrier_properties": {
                    "moisture_permeability": "8-20 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "300-800 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (透明), 20-30% (琥珀色)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "中等（琥珀色）"
                },
                "advantages": ["挤压性能好", "滴量控制精确", "成本低", "患者友好"],
                "disadvantages": ["阻隔性一般", "易渗透"],
                "suitable_drugs": ["多剂量滴眼液", "含防腐剂制剂", "短期使用产品"],
                "reference": "USP <1>; European Pharmacopoeia 10.0"
            },
            {
                "id": 30, "name": "单剂量LDPE容器", "category": "眼用制剂", "dosage_form": "滴眼液",
                "material_type": "LDPE", "specification": "0.3-0.5ml，单次使用", "color": "透明",
                "description": "单剂量LDPE容器，无防腐剂制剂专用，一次性使用",
                "barrier_properties": {
                    "moisture_permeability": "10-25 g/m²/day (38°C, 90%RH)",
                    "oxygen_permeability": "400-1000 cm³/m²/day (23°C, 0%RH)",
                    "light_transmission": "85-90% (可见光)",
                    "temperature_resistance": "-40°C至+80°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "无"
                },
                "advantages": ["无防腐剂", "单次使用", "安全性高", "便携"],
                "disadvantages": ["成本高", "包装废料多", "阻隔性一般"],
                "suitable_drugs": ["无防腐剂滴眼液", "敏感患者用药", "高端眼科制剂"],
                "reference": "Pharmaceutical Technology, 2019; FDA Guidance on Ophthalmic Products"
            },

            # 特殊功能包材
            {
                "id": 31, "name": "活性包装薄膜", "category": "特殊包装", "dosage_form": "片剂/胶囊",
                "material_type": "功能性聚合物", "specification": "含干燥剂/脱氧剂", "color": "透明/白色",
                "description": "含活性成分的包装薄膜，主动控制包装内环境",
                "barrier_properties": {
                    "moisture_permeability": "可调节（主动控制）",
                    "oxygen_permeability": "可调节（主动吸收）",
                    "light_transmission": "80-90% (可见光)",
                    "temperature_resistance": "-20°C至+60°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "可定制"
                },
                "advantages": ["主动环境控制", "延长保质期", "减少添加剂", "智能包装"],
                "disadvantages": ["成本极高", "技术复杂", "法规要求高"],
                "suitable_drugs": ["高敏感药物", "生物制品", "长期储存产品"],
                "reference": "Packaging Technology and Science, 2021; Active Packaging Research"
            },
            {
                "id": 32, "name": "智能标签包装", "category": "特殊包装", "dosage_form": "通用",
                "material_type": "复合材料", "specification": "集成传感器", "color": "多色",
                "description": "集成温湿度传感器的智能包装，实时监控储存条件",
                "barrier_properties": {
                    "moisture_permeability": "根据基材而定",
                    "oxygen_permeability": "根据基材而定",
                    "light_transmission": "根据基材而定",
                    "temperature_resistance": "-20°C至+60°C",
                    "chemical_resistance": "良好",
                    "uv_protection": "可定制",
                    "monitoring_capability": "温度、湿度、时间、开启次数"
                },
                "advantages": ["实时监控", "数据记录", "防伪功能", "患者依从性"],
                "disadvantages": ["成本极高", "技术复杂", "电池寿命"],
                "suitable_drugs": ["高价值药物", "冷链产品", "临床试验用药"],
                "reference": "Pharmaceutical Technology, 2022; Smart Packaging Innovation"
            }
        ]

    # 添加储存条件路由
    @api_router.get("/excipient/storage-conditions")
    async def get_storage_conditions():
        """获取储存条件（参考ICH指南）"""
        return [
            # ICH标准储存条件
            {"id": 1, "name": "长期试验条件", "temperature": "25±2°C", "humidity": "60±5%RH", "description": "ICH Zone II 长期稳定性试验条件", "ich_zone": "Zone II", "test_type": "长期试验", "duration": "12个月"},
            {"id": 2, "name": "中间试验条件", "temperature": "30±2°C", "humidity": "65±5%RH", "description": "ICH Zone II 中间试验条件", "ich_zone": "Zone II", "test_type": "中间试验", "duration": "6个月"},
            {"id": 3, "name": "加速试验条件", "temperature": "40±2°C", "humidity": "75±5%RH", "description": "ICH 加速稳定性试验条件", "ich_zone": "通用", "test_type": "加速试验", "duration": "6个月"},

            # ICH Zone I (温带气候)
            {"id": 4, "name": "Zone I 长期", "temperature": "21±2°C", "humidity": "45±5%RH", "description": "温带气候长期储存条件", "ich_zone": "Zone I", "test_type": "长期试验", "duration": "12个月"},
            {"id": 5, "name": "Zone I 中间", "temperature": "25±2°C", "humidity": "60±5%RH", "description": "温带气候中间试验条件", "ich_zone": "Zone I", "test_type": "中间试验", "duration": "6个月"},

            # ICH Zone III (炎热干燥气候)
            {"id": 6, "name": "Zone III 长期", "temperature": "30±2°C", "humidity": "35±5%RH", "description": "炎热干燥气候长期储存条件", "ich_zone": "Zone III", "test_type": "长期试验", "duration": "12个月"},

            # ICH Zone IV (炎热潮湿气候)
            {"id": 7, "name": "Zone IV 长期", "temperature": "30±2°C", "humidity": "75±5%RH", "description": "炎热潮湿气候长期储存条件", "ich_zone": "Zone IV", "test_type": "长期试验", "duration": "12个月"},
            {"id": 8, "name": "Zone IV 中间", "temperature": "30±2°C", "humidity": "65±5%RH", "description": "炎热潮湿气候中间试验条件", "ich_zone": "Zone IV", "test_type": "中间试验", "duration": "6个月"},

            # 冷藏储存条件
            {"id": 9, "name": "冷藏储存", "temperature": "5±3°C", "humidity": "不控制", "description": "冷藏储存条件，适用于生物制品", "ich_zone": "通用", "test_type": "冷藏", "duration": "长期"},
            {"id": 10, "name": "冷藏加速", "temperature": "25±2°C", "humidity": "60±5%RH", "description": "冷藏产品加速试验条件", "ich_zone": "通用", "test_type": "加速试验", "duration": "6个月"},

            # 冷冻储存条件
            {"id": 11, "name": "冷冻储存", "temperature": "-20±5°C", "humidity": "不控制", "description": "冷冻储存条件，适用于特殊生物制品", "ich_zone": "通用", "test_type": "冷冻", "duration": "长期"},
            {"id": 12, "name": "超低温储存", "temperature": "-70±10°C", "humidity": "不控制", "description": "超低温储存条件，适用于疫苗等", "ich_zone": "通用", "test_type": "超低温", "duration": "长期"},

            # 特殊储存条件
            {"id": 13, "name": "避光储存", "temperature": "25±2°C", "humidity": "60±5%RH", "description": "避光储存，适用于光敏感药物", "ich_zone": "Zone II", "test_type": "避光", "duration": "长期", "special": "避光"},
            {"id": 14, "name": "干燥储存", "temperature": "25±2°C", "humidity": "≤30%RH", "description": "干燥储存条件，适用于湿敏感药物", "ich_zone": "Zone II", "test_type": "干燥", "duration": "长期", "special": "干燥剂"},
            {"id": 15, "name": "密闭储存", "temperature": "25±2°C", "humidity": "60±5%RH", "description": "密闭储存，防止挥发和污染", "ich_zone": "Zone II", "test_type": "密闭", "duration": "长期", "special": "密闭容器"},

            # 开放稳定性试验条件
            {"id": 16, "name": "开放试验", "temperature": "40±2°C", "humidity": "75±5%RH", "description": "开放稳定性试验条件", "ich_zone": "通用", "test_type": "开放试验", "duration": "6个月", "special": "开放容器"},

            # 循环试验条件
            {"id": 17, "name": "温度循环", "temperature": "-10°C至+40°C", "humidity": "变化", "description": "温度循环试验条件", "ich_zone": "通用", "test_type": "循环试验", "duration": "6个月", "special": "温度循环"},
            {"id": 18, "name": "冻融循环", "temperature": "-20°C至+5°C", "humidity": "不控制", "description": "冻融循环试验条件", "ich_zone": "通用", "test_type": "冻融循环", "duration": "3个月", "special": "冻融循环"},

            # 运输储存条件
            {"id": 19, "name": "运输条件", "temperature": "15-25°C", "humidity": "≤60%RH", "description": "常温运输储存条件", "ich_zone": "通用", "test_type": "运输", "duration": "短期"},
            {"id": 20, "name": "冷链运输", "temperature": "2-8°C", "humidity": "不控制", "description": "冷链运输储存条件", "ich_zone": "通用", "test_type": "冷链运输", "duration": "短期"},

            # 患者使用条件
            {"id": 21, "name": "患者使用", "temperature": "15-30°C", "humidity": "≤75%RH", "description": "患者使用期间储存条件", "ich_zone": "通用", "test_type": "使用期", "duration": "开启后使用期限"}
        ]

    # 添加生产工艺路由
    @api_router.get("/excipient/production-processes")
    async def get_production_processes():
        """获取生产工艺"""
        return [
            {"id": 1, "name": "湿法制粒", "category": "制粒工艺", "description": "使用粘合剂溶液制粒"},
            {"id": 2, "name": "干法制粒", "category": "制粒工艺", "description": "直接压制制粒"},
            {"id": 3, "name": "流化床制粒", "category": "制粒工艺", "description": "流化床内制粒"},
            {"id": 4, "name": "直接压片", "category": "压片工艺", "description": "直接压制成片"},
            {"id": 5, "name": "包衣工艺", "category": "包衣工艺", "description": "片剂表面包衣"},
            {"id": 6, "name": "胶囊填充", "category": "胶囊工艺", "description": "胶囊剂填充工艺"}
        ]

    # 添加按剂型分类的包材路由
    @api_router.get("/excipient/packaging-by-dosage-form")
    async def get_packaging_by_dosage_form(dosage_form: Optional[str] = None):
        """按剂型获取包装材料"""
        all_materials = await get_packaging_materials()

        if dosage_form:
            # 筛选特定剂型的包材
            filtered_materials = [
                material for material in all_materials
                if dosage_form.lower() in material.get("dosage_form", "").lower() or
                   dosage_form.lower() in material.get("category", "").lower()
            ]
            return filtered_materials

        # 按剂型分组返回
        grouped_materials = {}
        for material in all_materials:
            category = material.get("category", "其他")
            if category not in grouped_materials:
                grouped_materials[category] = []
            grouped_materials[category].append(material)

        return grouped_materials

    # 添加ICH储存条件分类路由
    @api_router.get("/excipient/storage-by-type")
    async def get_storage_by_type(test_type: Optional[str] = None, ich_zone: Optional[str] = None):
        """按试验类型或ICH区域获取储存条件"""
        all_conditions = await get_storage_conditions()

        filtered_conditions = all_conditions

        if test_type:
            filtered_conditions = [
                condition for condition in filtered_conditions
                if test_type.lower() in condition.get("test_type", "").lower()
            ]

        if ich_zone:
            filtered_conditions = [
                condition for condition in filtered_conditions
                if ich_zone.lower() in condition.get("ich_zone", "").lower()
            ]

        return filtered_conditions

    # 项目管理相关API

    # 模拟项目数据存储（实际应用中应使用数据库）
    projects_storage = [
        {
            "id": 1,
            "name": "阿司匹林稳定性研究",
            "description": "阿司匹林片剂的稳定性研究项目",
            "status": "进行中",
            "created_at": "2024-01-15",
            "updated_at": "2024-01-15",
            "data": {}
        },
        {
            "id": 2,
            "name": "布洛芬胶囊相容性分析",
            "description": "布洛芬胶囊与包装材料的相容性分析",
            "status": "已完成",
            "created_at": "2024-01-10",
            "updated_at": "2024-01-14",
            "data": {}
        }
    ]

    # 注释掉前端兼容的项目路由，使用真正的项目API
    # @api_router.get("/projects")
    # async def get_projects():
    #     """获取所有项目"""
    #     return {
    #         "success": True,
    #         "data": projects_storage,
    #         "message": "获取项目列表成功"
    #     }

    # @api_router.post("/projects")
    # async def create_project(project_data: dict):
    #     """创建新项目"""
    #     try:
    #         from datetime import datetime

    #         # 生成新的项目ID
    #         new_id = max([p["id"] for p in projects_storage], default=0) + 1

    #         new_project = {
    #             "id": new_id,
    #             "name": project_data.get("name", f"新项目 {new_id}"),
    #             "description": project_data.get("description", ""),
    #             "status": "进行中",
    #             "created_at": datetime.now().strftime("%Y-%m-%d"),
    #             "updated_at": datetime.now().strftime("%Y-%m-%d"),
    #             "data": {}
    #         }

    #         projects_storage.append(new_project)

    #         return {
    #             "success": True,
    #             "data": new_project,
    #             "message": "项目创建成功"
    #         }
    #     except Exception as e:
    #         return {
    #             "success": False,
    #             "error": str(e),
    #             "message": "创建项目失败"
    #         }

    # @api_router.get("/projects/{project_id}")
    # async def get_project_by_id(project_id: int):
    #     """根据ID获取项目"""
    #     project = next((p for p in projects_storage if p["id"] == project_id), None)

    #     if not project:
    #         return {
    #             "success": False,
    #             "error": f"项目ID {project_id} 不存在",
    #             "message": "项目不存在"
    #         }

    #     return {
    #         "success": True,
    #         "data": project,
    #         "message": "获取项目成功"
    #     }

    # 注释掉前端兼容的项目数据保存路由，使用真正的项目API
    # @api_router.post("/projects/{project_id}/save-data")
    # async def save_project_data(project_id: int, data: dict):
    #     """保存项目数据"""
    #     try:
    #         # 首先检查项目是否存在
    #         project = next((p for p in projects_storage if p["id"] == project_id), None)

    #         if not project:
    #             return {
    #                 "success": False,
    #                 "error": f"项目ID {project_id} 不存在",
    #                 "message": "项目不存在"
    #             }

    #         # 验证数据结构
    #         required_fields = ["drug_name"]
    #         for field in required_fields:
    #             if field not in data:
    #                 return {
    #                     "success": False,
    #                     "error": f"缺少必需字段: {field}",
    #                     "message": "数据验证失败"
    #                 }

    #         # 处理包装材料数据，提取阻隔性能参数
    #         if "packaging" in data and data["packaging"]:
    #             # 获取包装材料详细信息
    #             packaging_materials = await get_packaging_materials()
    #             selected_packaging = next((p for p in packaging_materials if p["id"] == data["packaging"]), None)

    #             if selected_packaging:
    #                 # 提取阻隔性能参数用于后续分析
    #                 data["packaging_details"] = {
    #                     "material_info": selected_packaging,
    #                     "barrier_parameters": selected_packaging.get("barrier_properties", {}),
    #                     "material_type": selected_packaging.get("material_type"),
    #                     "specification": selected_packaging.get("specification"),
    #                     "advantages": selected_packaging.get("advantages", []),
    #                     "disadvantages": selected_packaging.get("disadvantages", [])
    #                 }

    #         # 处理储存条件数据
    #         if "storage_condition" in data and data["storage_condition"]:
    #             storage_conditions = await get_storage_conditions()
    #             selected_storage = next((s for s in storage_conditions if s["id"] == data["storage_condition"]), None)

    #             if selected_storage:
    #                 data["storage_details"] = {
    #                     "condition_info": selected_storage,
    #                     "temperature": selected_storage.get("temperature"),
    #                     "humidity": selected_storage.get("humidity"),
    #                     "ich_zone": selected_storage.get("ich_zone"),
    #                     "test_type": selected_storage.get("test_type")
    #                 }

    #         # 添加保存时间戳
    #         from datetime import datetime
    #         data["saved_at"] = datetime.now().isoformat()
    #         data["project_id"] = project_id

    #         # 保存数据到项目中
    #         project["data"] = data
    #         project["updated_at"] = datetime.now().strftime("%Y-%m-%d")

    #         # 如果项目状态是"未开始"，更新为"进行中"
    #         if project.get("status") == "未开始":
    #             project["status"] = "进行中"

    #         return {
    #             "success": True,
    #             "message": "项目数据保存成功",
    #             "data": {
    #                 "project_id": project_id,
    #                 "project_name": project["name"],
    #                 "saved_fields": list(data.keys()),
    #                 "saved_at": data["saved_at"],
    #                 "project_status": project["status"]
    #             }
    #         }

    #     except Exception as e:
    #         import traceback
    #         error_detail = traceback.format_exc()
    #         print(f"保存数据错误: {error_detail}")

    #         # 返回HTTP 500错误而不是200状态码
    #         from fastapi import HTTPException
    #         raise HTTPException(
    #             status_code=500,
    #             detail=f"保存数据失败: {str(e)}"
    #         )

    # 注释掉前端兼容的项目数据获取路由，使用真正的项目API
    # @api_router.get("/projects/{project_id}/data")
    # async def get_project_data(project_id: int):
    #     """获取项目数据"""
    #     try:
    #         # 查找项目
    #         project = next((p for p in projects_storage if p["id"] == project_id), None)

    #         if not project:
    #             return {
    #                 "success": False,
    #                 "error": f"项目ID {project_id} 不存在",
    #                 "message": "项目不存在"
    #             }

    #         return {
    #             "success": True,
    #             "data": project.get("data", {}),
    #             "project_info": {
    #                 "id": project["id"],
    #                 "name": project["name"],
    #                 "status": project["status"],
    #                 "updated_at": project["updated_at"]
    #             },
    #             "message": "项目数据获取成功"
    #         }

    #     except Exception as e:
    #         return {
    #             "success": False,
    #             "error": str(e),
    #             "message": "获取项目数据时发生错误"
    #         }

    # 添加药物信息搜索路由
    @api_router.get("/drug-info/search")
    async def search_drug_info(
        name: Optional[str] = None,
        cas: Optional[str] = None,
        smiles: Optional[str] = None
    ):
        """搜索药物信息"""
        try:
            from ..services.external_db_service import get_drug_info_from_external

            # 调用外部数据库搜索
            drug_info = await get_drug_info_from_external(name=name, cas=cas, smiles=smiles)

            if drug_info:
                return {
                    "success": True,
                    "data": drug_info,
                    "source": "external_database",
                    "message": "成功获取药物信息"
                }
            else:
                # 返回基本信息，表示未找到完整信息
                return {
                    "success": False,
                    "data": {
                        "name": name or "未知药物",
                        "description": "未找到详细信息，请手动补充"
                    },
                    "source": "manual_input",
                    "message": "未找到完整药物信息"
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "搜索药物信息时发生错误"
            }

    # 添加稳定性模型列表路由（已在stability_predict.py中定义，这里只是确保路径正确）
    # 路由已经在stability_predict.py中定义为 @router.get("/stability/models")

    print("✓ 已添加前端兼容路由映射")

except Exception as e:
    logger.warning(f"添加兼容路由时出错 - {e}")
