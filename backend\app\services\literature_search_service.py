"""
文献搜索服务
集成PubMed等开放学术资源进行真实的文献检索
"""
import logging
import requests
from typing import List, Dict, Any, Optional
import xml.etree.ElementTree as ET
from datetime import datetime
import asyncio
import aiohttp

logger = logging.getLogger(__name__)

# PubMed API配置（免费，无需API密钥）
PUBMED_BASE_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
PUBMED_SEARCH_URL = f"{PUBMED_BASE_URL}/esearch.fcgi"
PUBMED_FETCH_URL = f"{PUBMED_BASE_URL}/efetch.fcgi"

class LiteratureSearchService:
    """文献搜索服务类"""
    
    def __init__(self):
        self.session = None
    
    async def search_pubmed(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        搜索PubMed文献
        PubMed是免费的，不需要API密钥
        """
        try:
            # 构建搜索查询
            search_params = {
                "db": "pubmed",
                "term": query,
                "retmax": max_results,
                "retmode": "xml",
                "sort": "relevance"
            }
            
            async with aiohttp.ClientSession() as session:
                # 执行搜索
                async with session.get(PUBMED_SEARCH_URL, params=search_params) as response:
                    if response.status != 200:
                        logger.error(f"PubMed搜索失败: {response.status}")
                        return []
                    
                    search_xml = await response.text()
                    root = ET.fromstring(search_xml)
                    
                    # 提取PMID列表
                    id_list = root.find('.//IdList')
                    if id_list is None:
                        return []
                    
                    pmids = [id_elem.text for id_elem in id_list.findall('Id')]
                    
                    if not pmids:
                        return []
                    
                    # 获取文献详情
                    fetch_params = {
                        "db": "pubmed",
                        "id": ",".join(pmids),
                        "retmode": "xml",
                        "rettype": "abstract"
                    }
                    
                    async with session.get(PUBMED_FETCH_URL, params=fetch_params) as fetch_response:
                        if fetch_response.status != 200:
                            logger.error(f"PubMed获取详情失败: {fetch_response.status}")
                            return []
                        
                        fetch_xml = await fetch_response.text()
                        return self._parse_pubmed_results(fetch_xml)
                        
        except Exception as e:
            logger.error(f"PubMed搜索错误: {e}")
            return []
    
    def _parse_pubmed_results(self, xml_data: str) -> List[Dict[str, Any]]:
        """解析PubMed返回的XML数据"""
        results = []
        
        try:
            root = ET.fromstring(xml_data)
            articles = root.findall('.//PubmedArticle')
            
            for article in articles:
                try:
                    # 提取文章信息
                    medline = article.find('.//MedlineCitation')
                    if medline is None:
                        continue
                    
                    # PMID
                    pmid_elem = medline.find('.//PMID')
                    pmid = pmid_elem.text if pmid_elem is not None else ""
                    
                    # 标题
                    title_elem = medline.find('.//ArticleTitle')
                    title = title_elem.text if title_elem is not None else ""
                    
                    # 摘要
                    abstract_elem = medline.find('.//AbstractText')
                    abstract = abstract_elem.text if abstract_elem is not None else ""
                    
                    # 作者
                    authors = []
                    author_list = medline.find('.//AuthorList')
                    if author_list is not None:
                        for author in author_list.findall('.//Author'):
                            last_name = author.find('.//LastName')
                            fore_name = author.find('.//ForeName')
                            if last_name is not None and fore_name is not None:
                                authors.append(f"{last_name.text} {fore_name.text}")
                    
                    # 期刊
                    journal_elem = medline.find('.//Journal/Title')
                    journal = journal_elem.text if journal_elem is not None else ""
                    
                    # 发表年份
                    year_elem = medline.find('.//PubDate/Year')
                    year = year_elem.text if year_elem is not None else ""
                    
                    results.append({
                        "pmid": pmid,
                        "title": title,
                        "abstract": abstract,
                        "authors": ", ".join(authors[:3]) + (" et al." if len(authors) > 3 else ""),
                        "journal": journal,
                        "year": year,
                        "source": "PubMed",
                        "url": f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"
                    })
                    
                except Exception as e:
                    logger.error(f"解析单篇文章错误: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"解析PubMed结果错误: {e}")
        
        return results
    
    async def search_drug_excipient_interactions(self, 
                                                drug_name: str, 
                                                excipient_name: str) -> List[Dict[str, Any]]:
        """
        搜索特定药物-辅料相互作用的文献
        """
        # 构建专业的搜索查询
        queries = [
            f'"{drug_name}" AND "{excipient_name}" AND (interaction OR compatibility OR stability)',
            f'"{drug_name}" AND "{excipient_name}" AND (degradation OR incompatibility)',
            f'pharmaceutical excipient "{excipient_name}" drug interaction'
        ]
        
        all_results = []
        seen_pmids = set()
        
        for query in queries:
            results = await self.search_pubmed(query, max_results=5)
            
            # 去重
            for result in results:
                if result['pmid'] not in seen_pmids:
                    seen_pmids.add(result['pmid'])
                    
                    # 评估相关性
                    relevance_score = self._calculate_relevance(
                        result, drug_name, excipient_name
                    )
                    result['relevance_score'] = relevance_score
                    
                    # 提取关键发现
                    result['findings'] = self._extract_key_findings(
                        result['abstract'], drug_name, excipient_name
                    )
                    
                    all_results.append(result)
        
        # 按相关性排序
        all_results.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return all_results[:10]  # 返回最相关的10篇
    
    def _calculate_relevance(self, article: Dict[str, Any], 
                           drug_name: str, excipient_name: str) -> float:
        """计算文献相关性评分"""
        score = 0.0
        
        # 标题和摘要中的关键词匹配
        text = (article.get('title', '') + ' ' + article.get('abstract', '')).lower()
        drug_lower = drug_name.lower()
        excipient_lower = excipient_name.lower()
        
        # 药物名称出现
        if drug_lower in text:
            score += 0.3
        
        # 辅料名称出现
        if excipient_lower in text:
            score += 0.3
        
        # 相互作用相关关键词
        interaction_keywords = [
            'interaction', 'compatibility', 'incompatibility', 
            'stability', 'degradation', 'reaction', 'complex'
        ]
        
        for keyword in interaction_keywords:
            if keyword in text:
                score += 0.1
        
        # 年份新近度（最近5年加分）
        try:
            year = int(article.get('year', '0'))
            if year >= datetime.now().year - 5:
                score += 0.2
        except:
            pass
        
        return min(score, 1.0)  # 最高分1.0
    
    def _extract_key_findings(self, abstract: str, 
                            drug_name: str, excipient_name: str) -> str:
        """从摘要中提取关键发现"""
        if not abstract:
            return "无摘要信息"
        
        # 查找包含药物和辅料名称的句子
        sentences = abstract.split('. ')
        relevant_sentences = []
        
        drug_lower = drug_name.lower()
        excipient_lower = excipient_name.lower()
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            # 优先选择同时包含药物和辅料的句子
            if drug_lower in sentence_lower or excipient_lower in sentence_lower:
                # 检查是否包含结果相关的关键词
                result_keywords = [
                    'showed', 'demonstrated', 'found', 'indicated',
                    'resulted', 'caused', 'led to', 'revealed'
                ]
                
                if any(keyword in sentence_lower for keyword in result_keywords):
                    relevant_sentences.append(sentence)
        
        if relevant_sentences:
            # 返回最相关的2-3个句子
            return '. '.join(relevant_sentences[:3]) + '.'
        else:
            # 如果没有找到特定句子，返回摘要的前200个字符
            return abstract[:200] + '...' if len(abstract) > 200 else abstract
    
    async def search_general_excipient_info(self, excipient_name: str) -> List[Dict[str, Any]]:
        """
        搜索辅料的一般信息
        """
        query = f'pharmaceutical excipient "{excipient_name}" (properties OR safety OR function)'
        
        results = await self.search_pubmed(query, max_results=5)
        
        # 补充化学信息搜索
        if "酸" in excipient_name or "acid" in excipient_name.lower():
            chemistry_query = f'"{excipient_name}" chemical properties pharmaceutical'
            chemistry_results = await self.search_pubmed(chemistry_query, max_results=3)
            results.extend(chemistry_results)
        
        return results

# 创建全局实例
literature_service = LiteratureSearchService() 