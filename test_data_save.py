#!/usr/bin/env python3
"""
测试数据保存功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_project_data_save():
    """测试项目数据保存"""
    print("🔍 测试项目数据保存...")
    
    # 首先获取项目列表
    try:
        response = requests.get(f"{BASE_URL}/projects", timeout=10)
        if response.status_code == 200:
            projects = response.json()
            if not projects:
                print("❌ 没有找到项目，请先创建项目")
                return False
            
            project_id = projects[0]["id"]
            print(f"✅ 找到项目 ID: {project_id}")
            
            # 测试数据保存
            test_data = {
                "drug_name": "测试药物",
                "cas_number": "123-45-6",
                "molecular_formula": "C9H8O4",
                "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
                "category": "测试类别",
                "description": "这是一个测试药物",
                "formulation": [
                    {"name": "微晶纤维素", "amount": "100mg"},
                    {"name": "硬脂酸镁", "amount": "2mg"}
                ],
                "packaging_storage": {
                    "packaging": "铝塑泡罩包装",
                    "storage": "密闭，在干燥处保存"
                },
                "production_process": "测试生产工艺",
                "notes": "测试备注"
            }
            
            print("📤 发送保存请求...")
            response = requests.post(
                f"{BASE_URL}/projects/{project_id}/save-data",
                json=test_data,
                timeout=15
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print("✅ 数据保存成功")
                    
                    # 验证数据获取
                    print("🔍 验证数据获取...")
                    get_response = requests.get(
                        f"{BASE_URL}/projects/{project_id}/data",
                        timeout=10
                    )
                    
                    if get_response.status_code == 200:
                        get_result = get_response.json()
                        if get_result.get("success"):
                            saved_data = get_result.get("data", {})
                            print(f"✅ 数据获取成功: {saved_data.get('drug_name', 'Unknown')}")
                            
                            # 验证关键字段
                            if saved_data.get("drug_name") == test_data["drug_name"]:
                                print("✅ 数据完整性验证通过")
                                return True
                            else:
                                print("❌ 数据完整性验证失败")
                                return False
                        else:
                            print("❌ 数据获取失败")
                            return False
                    else:
                        print(f"❌ 数据获取请求失败: {get_response.status_code}")
                        return False
                else:
                    print(f"❌ 保存失败: {result}")
                    return False
            else:
                print(f"❌ 保存请求失败: {response.status_code}")
                return False
        else:
            print(f"❌ 获取项目列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始数据保存测试")
    print("=" * 50)
    
    success = test_project_data_save()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据保存测试通过！")
    else:
        print("💥 数据保存测试失败！")

if __name__ == "__main__":
    main()
