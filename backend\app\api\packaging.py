"""
包装材料查询API
"""
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.services.packaging_service import packaging_service

router = APIRouter()

class PackagingMaterialResponse(BaseModel):
    """包装材料响应模型"""
    name: str
    category: str
    dosage_forms: List[str]
    material_type: str
    barrier_properties: Dict[str, str]
    compatibility: List[str]
    regulatory_status: str
    description: str
    advantages: List[str]
    disadvantages: List[str]
    typical_applications: List[str]

class PackagingRecommendationRequest(BaseModel):
    """包装推荐请求模型"""
    dosage_form: str
    drug_properties: Dict[str, Any]

@router.get("/packaging/dosage-forms")
async def get_dosage_forms():
    """获取所有剂型"""
    forms = packaging_service.get_all_dosage_forms()
    return {"dosage_forms": forms}

@router.get("/packaging/categories")
async def get_packaging_categories():
    """获取所有包装类别"""
    categories = packaging_service.get_all_categories()
    return {"categories": categories}

@router.get("/packaging/by-dosage-form", response_model=List[PackagingMaterialResponse])
async def get_packaging_by_dosage_form(
    dosage_form: str = Query(..., description="剂型")
):
    """根据剂型获取适用的包装材料"""
    materials = packaging_service.get_packaging_by_dosage_form(dosage_form)
    
    if not materials:
        raise HTTPException(status_code=404, detail=f"未找到适用于{dosage_form}的包装材料")
    
    return [PackagingMaterialResponse(
        name=material.name,
        category=material.category,
        dosage_forms=material.dosage_forms,
        material_type=material.material_type,
        barrier_properties=material.barrier_properties,
        compatibility=material.compatibility,
        regulatory_status=material.regulatory_status,
        description=material.description,
        advantages=material.advantages,
        disadvantages=material.disadvantages,
        typical_applications=material.typical_applications
    ) for material in materials]

@router.get("/packaging/by-category", response_model=List[PackagingMaterialResponse])
async def get_packaging_by_category(
    category: str = Query(..., description="包装类别")
):
    """根据包装类别获取包装材料"""
    materials = packaging_service.get_packaging_by_category(category)
    
    if not materials:
        raise HTTPException(status_code=404, detail=f"未找到{category}类别的包装材料")
    
    return [PackagingMaterialResponse(
        name=material.name,
        category=material.category,
        dosage_forms=material.dosage_forms,
        material_type=material.material_type,
        barrier_properties=material.barrier_properties,
        compatibility=material.compatibility,
        regulatory_status=material.regulatory_status,
        description=material.description,
        advantages=material.advantages,
        disadvantages=material.disadvantages,
        typical_applications=material.typical_applications
    ) for material in materials]

@router.get("/packaging/search", response_model=List[PackagingMaterialResponse])
async def search_packaging(
    query: str = Query(..., description="搜索关键词")
):
    """搜索包装材料"""
    materials = packaging_service.search_packaging(query)
    
    if not materials:
        raise HTTPException(status_code=404, detail="未找到匹配的包装材料")
    
    return [PackagingMaterialResponse(
        name=material.name,
        category=material.category,
        dosage_forms=material.dosage_forms,
        material_type=material.material_type,
        barrier_properties=material.barrier_properties,
        compatibility=material.compatibility,
        regulatory_status=material.regulatory_status,
        description=material.description,
        advantages=material.advantages,
        disadvantages=material.disadvantages,
        typical_applications=material.typical_applications
    ) for material in materials]

@router.post("/packaging/recommendations")
async def get_packaging_recommendations(request: PackagingRecommendationRequest):
    """根据剂型和药物性质推荐包装材料"""
    recommendations = packaging_service.get_packaging_recommendations(
        request.dosage_form, 
        request.drug_properties
    )
    
    if not recommendations:
        raise HTTPException(
            status_code=404, 
            detail=f"未找到适用于{request.dosage_form}的包装材料推荐"
        )
    
    result = []
    for rec in recommendations:
        material = rec['material']
        result.append({
            'material': {
                'name': material.name,
                'category': material.category,
                'dosage_forms': material.dosage_forms,
                'material_type': material.material_type,
                'barrier_properties': material.barrier_properties,
                'compatibility': material.compatibility,
                'regulatory_status': material.regulatory_status,
                'description': material.description,
                'advantages': material.advantages,
                'disadvantages': material.disadvantages,
                'typical_applications': material.typical_applications
            },
            'score': rec['score'],
            'reasons': rec['reasons']
        })
    
    return {"recommendations": result}

@router.get("/packaging/database")
async def get_packaging_database():
    """获取完整的包装材料数据库"""
    return packaging_service.export_packaging_database()

# 兼容性端点
@router.get("/api/packaging/materials")
async def get_packaging_materials_legacy(dosage_form: str = Query(None)):
    """兼容旧版API的包装材料查询"""
    try:
        if dosage_form:
            materials = packaging_service.get_packaging_by_dosage_form(dosage_form)
        else:
            materials = list(packaging_service.packaging_database.values())
        
        return {
            "success": True,
            "data": [
                {
                    "name": material.name,
                    "category": material.category,
                    "description": material.description,
                    "advantages": material.advantages,
                    "typical_applications": material.typical_applications
                }
                for material in materials
            ]
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e)
        } 