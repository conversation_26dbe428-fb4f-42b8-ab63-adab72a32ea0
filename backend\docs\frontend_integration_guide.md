# 前端集成指南

## 概述

本指南描述了如何在前端应用中集成项目数据管理功能。

## API 集成

### 1. 保存项目数据

```typescript
// 类型定义
interface ProjectData {
  drug_name?: string;
  cas_number?: string;
  molecular_formula?: string;
  smiles?: string;
  category?: string;
  description?: string;
  formulation?: Array<{
    name: string;
    amount: string;
    function: string;
  }>;
  packaging_storage?: {
    packaging: string;
    storage: string;
  };
  [key: string]: any; // 支持其他自定义字段
}

// API调用函数
async function saveProjectData(projectId: number, data: ProjectData) {
  try {
    const response = await fetch(`/api/projects/${projectId}/save-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('保存项目数据失败:', error);
    throw error;
  }
}
```

### 2. 获取项目数据

```typescript
// 响应类型定义
interface ProjectDataResponse {
  success: boolean;
  project_id: number;
  project_name: string;
  data: ProjectData;
}

// API调用函数
async function getProjectData(projectId: number): Promise<ProjectDataResponse> {
  try {
    const response = await fetch(`/api/projects/${projectId}/data`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('获取项目数据失败:', error);
    throw error;
  }
}
```

## React 组件示例

### 项目数据表单组件

```tsx
import React, { useState, useEffect } from 'react';

interface ProjectDataFormProps {
  projectId: number;
  onSave?: (data: ProjectData) => void;
}

const ProjectDataForm: React.FC<ProjectDataFormProps> = ({ projectId, onSave }) => {
  const [formData, setFormData] = useState<ProjectData>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载现有数据
  useEffect(() => {
    loadProjectData();
  }, [projectId]);

  const loadProjectData = async () => {
    try {
      setLoading(true);
      const response = await getProjectData(projectId);
      setFormData(response.data || {});
    } catch (err) {
      setError('加载项目数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      
      const result = await saveProjectData(projectId, formData);
      
      if (result.success) {
        onSave?.(formData);
        alert('数据保存成功！');
      }
    } catch (err) {
      setError('保存数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) return <div>加载中...</div>;

  return (
    <form onSubmit={handleSubmit} className="project-data-form">
      {error && <div className="error-message">{error}</div>}
      
      {/* 药物基本信息 */}
      <section>
        <h3>药物基本信息</h3>
        
        <div className="form-group">
          <label>药物名称:</label>
          <input
            type="text"
            value={formData.drug_name || ''}
            onChange={(e) => handleInputChange('drug_name', e.target.value)}
            placeholder="请输入药物名称"
          />
        </div>

        <div className="form-group">
          <label>CAS号:</label>
          <input
            type="text"
            value={formData.cas_number || ''}
            onChange={(e) => handleInputChange('cas_number', e.target.value)}
            placeholder="请输入CAS号"
          />
        </div>

        <div className="form-group">
          <label>分子式:</label>
          <input
            type="text"
            value={formData.molecular_formula || ''}
            onChange={(e) => handleInputChange('molecular_formula', e.target.value)}
            placeholder="请输入分子式"
          />
        </div>

        <div className="form-group">
          <label>SMILES:</label>
          <input
            type="text"
            value={formData.smiles || ''}
            onChange={(e) => handleInputChange('smiles', e.target.value)}
            placeholder="请输入SMILES结构"
          />
        </div>
      </section>

      {/* 配方信息 */}
      <section>
        <h3>配方信息</h3>
        <FormulationEditor
          formulation={formData.formulation || []}
          onChange={(formulation) => handleInputChange('formulation', formulation)}
        />
      </section>

      {/* 包装储存 */}
      <section>
        <h3>包装储存</h3>
        <div className="form-group">
          <label>包装方式:</label>
          <input
            type="text"
            value={formData.packaging_storage?.packaging || ''}
            onChange={(e) => handleInputChange('packaging_storage', {
              ...formData.packaging_storage,
              packaging: e.target.value
            })}
            placeholder="请输入包装方式"
          />
        </div>

        <div className="form-group">
          <label>储存条件:</label>
          <input
            type="text"
            value={formData.packaging_storage?.storage || ''}
            onChange={(e) => handleInputChange('packaging_storage', {
              ...formData.packaging_storage,
              storage: e.target.value
            })}
            placeholder="请输入储存条件"
          />
        </div>
      </section>

      <button type="submit" disabled={loading}>
        {loading ? '保存中...' : '保存数据'}
      </button>
    </form>
  );
};
```

### 配方编辑器组件

```tsx
interface FormulationItem {
  name: string;
  amount: string;
  function: string;
}

interface FormulationEditorProps {
  formulation: FormulationItem[];
  onChange: (formulation: FormulationItem[]) => void;
}

const FormulationEditor: React.FC<FormulationEditorProps> = ({ formulation, onChange }) => {
  const addItem = () => {
    onChange([...formulation, { name: '', amount: '', function: '' }]);
  };

  const removeItem = (index: number) => {
    onChange(formulation.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof FormulationItem, value: string) => {
    const updated = formulation.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    );
    onChange(updated);
  };

  return (
    <div className="formulation-editor">
      {formulation.map((item, index) => (
        <div key={index} className="formulation-item">
          <input
            type="text"
            placeholder="辅料名称"
            value={item.name}
            onChange={(e) => updateItem(index, 'name', e.target.value)}
          />
          <input
            type="text"
            placeholder="用量"
            value={item.amount}
            onChange={(e) => updateItem(index, 'amount', e.target.value)}
          />
          <input
            type="text"
            placeholder="功能"
            value={item.function}
            onChange={(e) => updateItem(index, 'function', e.target.value)}
          />
          <button type="button" onClick={() => removeItem(index)}>删除</button>
        </div>
      ))}
      <button type="button" onClick={addItem}>添加辅料</button>
    </div>
  );
};
```

## 状态管理

### 使用 Redux Toolkit

```typescript
// store/projectDataSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const saveProjectDataAsync = createAsyncThunk(
  'projectData/save',
  async ({ projectId, data }: { projectId: number; data: ProjectData }) => {
    const response = await saveProjectData(projectId, data);
    return response;
  }
);

export const loadProjectDataAsync = createAsyncThunk(
  'projectData/load',
  async (projectId: number) => {
    const response = await getProjectData(projectId);
    return response;
  }
);

const projectDataSlice = createSlice({
  name: 'projectData',
  initialState: {
    data: {} as ProjectData,
    loading: false,
    error: null as string | null,
  },
  reducers: {
    updateData: (state, action) => {
      state.data = { ...state.data, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(saveProjectDataAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveProjectDataAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.saved_data;
      })
      .addCase(saveProjectDataAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '保存失败';
      })
      .addCase(loadProjectDataAsync.fulfilled, (state, action) => {
        state.data = action.payload.data;
      });
  },
});

export const { updateData, clearError } = projectDataSlice.actions;
export default projectDataSlice.reducer;
```

## 错误处理

```typescript
// utils/errorHandler.ts
export const handleApiError = (error: any) => {
  if (error.response) {
    // 服务器响应错误
    const status = error.response.status;
    const message = error.response.data?.detail || '服务器错误';
    
    switch (status) {
      case 404:
        return '项目不存在';
      case 422:
        return '数据格式错误';
      case 500:
        return '服务器内部错误';
      default:
        return message;
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置';
  } else {
    // 其他错误
    return error.message || '未知错误';
  }
};
```

## 最佳实践

1. **数据验证**: 在前端进行基本的数据验证
2. **错误处理**: 提供友好的错误提示
3. **加载状态**: 显示加载指示器
4. **数据缓存**: 合理使用缓存减少API调用
5. **类型安全**: 使用TypeScript确保类型安全
6. **用户体验**: 提供自动保存和数据恢复功能

## 注意事项

1. **API兼容性**: 确保前端代码与后端API版本兼容
2. **数据同步**: 处理多用户同时编辑的情况
3. **性能优化**: 大量数据时考虑分页或虚拟滚动
4. **安全性**: 验证用户权限和数据访问控制
