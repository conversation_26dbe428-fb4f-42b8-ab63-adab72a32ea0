"""
兼容性占位：ExcipientDataService
如需实现真实功能，请补充具体逻辑。
"""

from sqlalchemy.orm import Session
from typing import List, Optional

from ..models.excipient import Excipient as ExcipientORM
# 延迟导入或类型注解字符串，避免循环依赖
# from ..api.excipient import ExcipientCreate

class ExcipientDataService:
    def __init__(self, db: Session):
        self.db = db

    def get_excipient(self, excipient_id: int) -> Optional[ExcipientORM]:
        return self.db.query(ExcipientORM).filter(ExcipientORM.id == excipient_id).first()

    def get_excipient_by_name(self, name: str) -> Optional[ExcipientORM]:
        return self.db.query(ExcipientORM).filter(ExcipientORM.name == name).first()

    def get_excipients(self, skip: int = 0, limit: int = 100) -> List[ExcipientORM]:
        return self.db.query(ExcipientORM).offset(skip).limit(limit).all()

    def create_excipient(self, excipient_data) -> ExcipientORM:
        # excipient_data: ExcipientCreate
        db_excipient = ExcipientORM(**excipient_data.dict())
        self.db.add(db_excipient)
        self.db.commit()
        self.db.refresh(db_excipient)
        return db_excipient

    def update_excipient(self, excipient_id: int, excipient_data) -> Optional[ExcipientORM]:
        db_excipient = self.get_excipient(excipient_id)
        if not db_excipient:
            return None
        for key, value in excipient_data.dict().items():
            setattr(db_excipient, key, value)
        self.db.commit()
        self.db.refresh(db_excipient)
        return db_excipient

    def delete_excipient(self, excipient_id: int) -> bool:
        db_excipient = self.get_excipient(excipient_id)
        if not db_excipient:
            return False
        self.db.delete(db_excipient)
        self.db.commit()
        return True

    def get_all_excipients(self) -> list:
        return self.db.query(ExcipientORM).all()

    def get_excipient_info(self, name: str) -> Optional[ExcipientORM]:
        return self.db.query(ExcipientORM).filter(ExcipientORM.name == name).first() 