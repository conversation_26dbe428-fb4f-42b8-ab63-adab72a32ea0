"""Initial database schema

Revision ID: 2bd689981315
Revises: 
Create Date: 2025-06-17 12:18:04.328615

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2bd689981315'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('analysis',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_analysis_id'), 'analysis', ['id'], unique=False)
    op.create_table('drugs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('synonyms', sa.JSON(), nullable=True),
    sa.Column('formula', sa.String(), nullable=True),
    sa.Column('cas', sa.String(), nullable=True),
    sa.Column('smiles', sa.String(), nullable=True),
    sa.Column('inchi', sa.String(), nullable=True),
    sa.Column('inchi_key', sa.String(), nullable=True),
    sa.Column('structure_image_url', sa.String(), nullable=True),
    sa.Column('molecular_weight', sa.Float(), nullable=True),
    sa.Column('logp', sa.Float(), nullable=True),
    sa.Column('pka', sa.JSON(), nullable=True),
    sa.Column('solubility', sa.String(), nullable=True),
    sa.Column('melting_point', sa.Float(), nullable=True),
    sa.Column('hydrogen_bond_donors', sa.Integer(), nullable=True),
    sa.Column('hydrogen_bond_acceptors', sa.Integer(), nullable=True),
    sa.Column('rotatable_bonds', sa.Integer(), nullable=True),
    sa.Column('aromatic_rings', sa.Integer(), nullable=True),
    sa.Column('heavy_atoms', sa.Integer(), nullable=True),
    sa.Column('tpsa', sa.Float(), nullable=True),
    sa.Column('hygroscopicity', sa.String(), nullable=True),
    sa.Column('photosensitivity', sa.Boolean(), nullable=True),
    sa.Column('thermal_sensitivity', sa.Boolean(), nullable=True),
    sa.Column('oxidation_sensitivity', sa.Boolean(), nullable=True),
    sa.Column('hydrolysis_sensitivity', sa.Boolean(), nullable=True),
    sa.Column('degradation_pathways', sa.JSON(), nullable=True),
    sa.Column('has_ester', sa.Boolean(), nullable=True),
    sa.Column('has_amide', sa.Boolean(), nullable=True),
    sa.Column('has_carboxylic_acid', sa.Boolean(), nullable=True),
    sa.Column('has_amine', sa.Boolean(), nullable=True),
    sa.Column('has_phenol', sa.Boolean(), nullable=True),
    sa.Column('has_aldehyde', sa.Boolean(), nullable=True),
    sa.Column('has_ketone', sa.Boolean(), nullable=True),
    sa.Column('has_thiol', sa.Boolean(), nullable=True),
    sa.Column('has_sulfide', sa.Boolean(), nullable=True),
    sa.Column('has_nitro', sa.Boolean(), nullable=True),
    sa.Column('therapeutic_category', sa.String(), nullable=True),
    sa.Column('data_source', sa.String(), nullable=True),
    sa.Column('pharmacophore', sa.JSON(), nullable=True),
    sa.Column('references', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_drugs_cas'), 'drugs', ['cas'], unique=True)
    op.create_index(op.f('ix_drugs_id'), 'drugs', ['id'], unique=False)
    op.create_index(op.f('ix_drugs_name'), 'drugs', ['name'], unique=True)
    op.create_table('environments',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('temperature', sa.Float(), nullable=True),
    sa.Column('humidity', sa.Float(), nullable=True),
    sa.Column('packaging', sa.String(), nullable=True),
    sa.Column('batch', sa.String(), nullable=True),
    sa.Column('sample_code', sa.String(), nullable=True),
    sa.Column('light_condition', sa.String(), nullable=True),
    sa.Column('light_intensity', sa.Float(), nullable=True),
    sa.Column('uv_exposure', sa.Boolean(), nullable=True),
    sa.Column('pressure', sa.Float(), nullable=True),
    sa.Column('oxygen_level', sa.Float(), nullable=True),
    sa.Column('carbon_dioxide_level', sa.Float(), nullable=True),
    sa.Column('climatic_zone', sa.String(), nullable=True),
    sa.Column('ich_condition', sa.String(), nullable=True),
    sa.Column('packaging_type', sa.String(), nullable=True),
    sa.Column('packaging_material', sa.String(), nullable=True),
    sa.Column('packaging_details', sa.JSON(), nullable=True),
    sa.Column('moisture_barrier', sa.Float(), nullable=True),
    sa.Column('oxygen_barrier', sa.Float(), nullable=True),
    sa.Column('light_barrier', sa.Float(), nullable=True),
    sa.Column('closure_type', sa.String(), nullable=True),
    sa.Column('desiccant', sa.Boolean(), nullable=True),
    sa.Column('desiccant_type', sa.String(), nullable=True),
    sa.Column('test_duration', sa.Integer(), nullable=True),
    sa.Column('cycling', sa.Boolean(), nullable=True),
    sa.Column('cycling_details', sa.JSON(), nullable=True),
    sa.Column('study_type', sa.String(), nullable=True),
    sa.Column('time_point', sa.Float(), nullable=True),
    sa.Column('storage_orientation', sa.String(), nullable=True),
    sa.Column('stress_condition', sa.String(), nullable=True),
    sa.Column('stress_level', sa.Float(), nullable=True),
    sa.Column('stress_duration', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_environments_id'), 'environments', ['id'], unique=False)
    op.create_table('excipient_interactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('risk_with', sa.String(), nullable=False),
    sa.Column('reaction', sa.String(), nullable=False),
    sa.Column('risk_level', sa.String(), nullable=False),
    sa.Column('source', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_excipient_interactions_id'), 'excipient_interactions', ['id'], unique=False)
    op.create_index(op.f('ix_excipient_interactions_name'), 'excipient_interactions', ['name'], unique=False)
    op.create_table('excipients',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('function', sa.String(), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('risk_level', sa.String(), nullable=True),
    sa.Column('remark', sa.String(), nullable=True),
    sa.Column('chemical_name', sa.String(), nullable=True),
    sa.Column('cas', sa.String(), nullable=True),
    sa.Column('smiles', sa.String(), nullable=True),
    sa.Column('inchi', sa.String(), nullable=True),
    sa.Column('structure_image_url', sa.String(), nullable=True),
    sa.Column('molecular_weight', sa.Float(), nullable=True),
    sa.Column('ph', sa.Float(), nullable=True),
    sa.Column('solubility', sa.String(), nullable=True),
    sa.Column('hygroscopicity', sa.String(), nullable=True),
    sa.Column('melting_point', sa.Float(), nullable=True),
    sa.Column('thermal_stability', sa.String(), nullable=True),
    sa.Column('light_stability', sa.String(), nullable=True),
    sa.Column('moisture_sensitivity', sa.Boolean(), nullable=True),
    sa.Column('oxygen_sensitivity', sa.Boolean(), nullable=True),
    sa.Column('impurities', sa.JSON(), nullable=True),
    sa.Column('known_incompatibilities', sa.JSON(), nullable=True),
    sa.Column('compatibility_data', sa.JSON(), nullable=True),
    sa.Column('has_reducing_sugar', sa.Boolean(), nullable=True),
    sa.Column('has_aldehyde', sa.Boolean(), nullable=True),
    sa.Column('has_peroxide', sa.Boolean(), nullable=True),
    sa.Column('has_metal_ion', sa.Boolean(), nullable=True),
    sa.Column('has_acidic_group', sa.Boolean(), nullable=True),
    sa.Column('has_basic_group', sa.Boolean(), nullable=True),
    sa.Column('supplier', sa.String(), nullable=True),
    sa.Column('grade', sa.String(), nullable=True),
    sa.Column('specifications', sa.JSON(), nullable=True),
    sa.Column('regulatory_status', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_excipients_id'), 'excipients', ['id'], unique=False)
    op.create_index(op.f('ix_excipients_name'), 'excipients', ['name'], unique=True)
    op.create_table('packaging_materials',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('material', sa.String(), nullable=True),
    sa.Column('moisture_vapor_transmission_rate', sa.Float(), nullable=True),
    sa.Column('oxygen_transmission_rate', sa.Float(), nullable=True),
    sa.Column('light_transmission', sa.JSON(), nullable=True),
    sa.Column('thickness', sa.Float(), nullable=True),
    sa.Column('tensile_strength', sa.Float(), nullable=True),
    sa.Column('elongation', sa.Float(), nullable=True),
    sa.Column('chemical_resistance', sa.JSON(), nullable=True),
    sa.Column('extractables_profile', sa.JSON(), nullable=True),
    sa.Column('leachables_risk', sa.String(), nullable=True),
    sa.Column('known_incompatibilities', sa.JSON(), nullable=True),
    sa.Column('supplier', sa.String(), nullable=True),
    sa.Column('grade', sa.String(), nullable=True),
    sa.Column('specifications', sa.JSON(), nullable=True),
    sa.Column('regulatory_status', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_packaging_materials_id'), 'packaging_materials', ['id'], unique=False)
    op.create_table('stability_conditions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('humidity', sa.Float(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=False),
    sa.Column('ich_type', sa.String(), nullable=True),
    sa.Column('climatic_zone', sa.String(), nullable=True),
    sa.Column('time_points', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('reference', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stability_conditions_id'), 'stability_conditions', ['id'], unique=False)
    op.create_table('stability_predictions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('project_id', sa.String(), nullable=False),
    sa.Column('drug_id', sa.String(), nullable=False),
    sa.Column('model_type', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('r_squared', sa.Float(), nullable=True),
    sa.Column('prediction_conditions', sa.JSON(), nullable=True),
    sa.Column('prediction_results', sa.JSON(), nullable=True),
    sa.Column('shelf_life', sa.Float(), nullable=True),
    sa.Column('confidence_interval', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stability_predictions_id'), 'stability_predictions', ['id'], unique=False)
    op.create_table('stability_protocols',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('project_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('version', sa.String(), nullable=True),
    sa.Column('conditions', sa.JSON(), nullable=True),
    sa.Column('time_points', sa.JSON(), nullable=True),
    sa.Column('test_items', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('approved_by', sa.String(), nullable=True),
    sa.Column('approved_date', sa.DateTime(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stability_protocols_id'), 'stability_protocols', ['id'], unique=False)
    op.create_table('stability_studies',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('project_id', sa.String(), nullable=False),
    sa.Column('study_type', sa.String(), nullable=False),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('temperature', sa.Float(), nullable=True),
    sa.Column('humidity', sa.Float(), nullable=True),
    sa.Column('packaging', sa.String(), nullable=True),
    sa.Column('time_points', sa.JSON(), nullable=True),
    sa.Column('test_items', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stability_studies_id'), 'stability_studies', ['id'], unique=False)
    op.create_table('degradation_products',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('drug_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('structure', sa.String(), nullable=True),
    sa.Column('formation_condition', sa.String(), nullable=True),
    sa.Column('degradation_pathway', sa.String(), nullable=True),
    sa.Column('detection_method', sa.String(), nullable=True),
    sa.Column('relative_retention_time', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['drug_id'], ['drugs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_degradation_products_id'), 'degradation_products', ['id'], unique=False)
    op.create_table('excipient_batches',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('excipient_id', sa.String(), nullable=False),
    sa.Column('batch_number', sa.String(), nullable=False),
    sa.Column('supplier', sa.String(), nullable=True),
    sa.Column('manufacturing_date', sa.DateTime(), nullable=True),
    sa.Column('expiry_date', sa.DateTime(), nullable=True),
    sa.Column('ph', sa.Float(), nullable=True),
    sa.Column('moisture_content', sa.Float(), nullable=True),
    sa.Column('particle_size', sa.JSON(), nullable=True),
    sa.Column('specific_surface_area', sa.Float(), nullable=True),
    sa.Column('bulk_density', sa.Float(), nullable=True),
    sa.Column('impurity_profile', sa.JSON(), nullable=True),
    sa.Column('heavy_metals', sa.Float(), nullable=True),
    sa.Column('residual_solvents', sa.JSON(), nullable=True),
    sa.Column('stability_data', sa.JSON(), nullable=True),
    sa.Column('coa_url', sa.String(), nullable=True),
    sa.Column('msds_url', sa.String(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['excipient_id'], ['excipients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_excipient_batches_id'), 'excipient_batches', ['id'], unique=False)
    op.create_table('excipient_compatibilities',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('excipient_id', sa.String(), nullable=False),
    sa.Column('drug_id', sa.String(), nullable=False),
    sa.Column('compatibility_level', sa.String(), nullable=False),
    sa.Column('risk_level', sa.String(), nullable=True),
    sa.Column('risk_type', sa.String(), nullable=True),
    sa.Column('temperature', sa.Float(), nullable=True),
    sa.Column('humidity', sa.Float(), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('ph', sa.Float(), nullable=True),
    sa.Column('observations', sa.Text(), nullable=True),
    sa.Column('analytical_results', sa.JSON(), nullable=True),
    sa.Column('degradation_products', sa.JSON(), nullable=True),
    sa.Column('evidence', sa.JSON(), nullable=True),
    sa.Column('recommendations', sa.Text(), nullable=True),
    sa.Column('references', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['drug_id'], ['drugs.id'], ),
    sa.ForeignKeyConstraint(['excipient_id'], ['excipients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_excipient_compatibilities_id'), 'excipient_compatibilities', ['id'], unique=False)
    op.create_table('projects',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('drug_id', sa.Integer(), nullable=True),
    sa.Column('dosage_form', sa.String(), nullable=True),
    sa.Column('strength', sa.String(), nullable=True),
    sa.Column('excipients', sa.JSON(), nullable=True),
    sa.Column('packaging_materials', sa.JSON(), nullable=True),
    sa.Column('stability_data', sa.JSON(), nullable=True),
    sa.Column('storage_conditions', sa.JSON(), nullable=True),
    sa.Column('prediction_settings', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['drug_id'], ['drugs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_table('stability_data',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('project_id', sa.String(), nullable=False),
    sa.Column('drug_id', sa.String(), nullable=False),
    sa.Column('excipient_id', sa.String(), nullable=True),
    sa.Column('environment_id', sa.String(), nullable=True),
    sa.Column('time_point', sa.Float(), nullable=True),
    sa.Column('value', sa.Float(), nullable=True),
    sa.Column('item', sa.String(), nullable=True),
    sa.Column('unit', sa.String(), nullable=True),
    sa.Column('specification', sa.String(), nullable=True),
    sa.Column('method', sa.String(), nullable=True),
    sa.Column('batch', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['environment_id'], ['environments.id'], ),
    sa.ForeignKeyConstraint(['excipient_id'], ['excipients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stability_data_id'), 'stability_data', ['id'], unique=False)
    op.create_table('export_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('project_id', sa.String(), nullable=True),
    sa.Column('file_name', sa.String(), nullable=False),
    sa.Column('file_type', sa.String(), nullable=False),
    sa.Column('file_path', sa.String(), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('export_time', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_export_history_id'), 'export_history', ['id'], unique=False)
    op.add_column('api_keys', sa.Column('name', sa.String(), nullable=True))
    op.add_column('api_keys', sa.Column('last_used', sa.DateTime(), nullable=True))
    op.alter_column('api_keys', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.create_index(op.f('ix_api_keys_id'), 'api_keys', ['id'], unique=False)
    op.create_index(op.f('ix_api_keys_key'), 'api_keys', ['key'], unique=True)
    op.create_foreign_key(None, 'api_keys', 'users', ['user_id'], ['id'])
    op.add_column('operation_logs', sa.Column('ip_address', sa.String(), nullable=True))
    op.add_column('operation_logs', sa.Column('user_agent', sa.String(), nullable=True))
    op.alter_column('operation_logs', 'action',
               existing_type=sa.VARCHAR(length=64),
               nullable=False)
    op.alter_column('operation_logs', 'detail',
               existing_type=sa.VARCHAR(length=256),
               type_=sa.Text(),
               existing_nullable=True)
    op.create_index(op.f('ix_operation_logs_id'), 'operation_logs', ['id'], unique=False)
    op.create_foreign_key(None, 'operation_logs', 'users', ['user_id'], ['id'])
    op.add_column('users', sa.Column('email', sa.String(), nullable=False))
    op.add_column('users', sa.Column('hashed_password', sa.String(), nullable=False))
    op.add_column('users', sa.Column('status', sa.String(), nullable=True))
    op.add_column('users', sa.Column('last_login', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.drop_column('users', 'password')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('password', sa.VARCHAR(length=128), nullable=False))
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_column('users', 'created_at')
    op.drop_column('users', 'last_login')
    op.drop_column('users', 'status')
    op.drop_column('users', 'hashed_password')
    op.drop_column('users', 'email')
    op.drop_constraint(None, 'operation_logs', type_='foreignkey')
    op.drop_index(op.f('ix_operation_logs_id'), table_name='operation_logs')
    op.alter_column('operation_logs', 'detail',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=256),
               existing_nullable=True)
    op.alter_column('operation_logs', 'action',
               existing_type=sa.VARCHAR(length=64),
               nullable=True)
    op.drop_column('operation_logs', 'user_agent')
    op.drop_column('operation_logs', 'ip_address')
    op.drop_constraint(None, 'api_keys', type_='foreignkey')
    op.drop_index(op.f('ix_api_keys_key'), table_name='api_keys')
    op.drop_index(op.f('ix_api_keys_id'), table_name='api_keys')
    op.alter_column('api_keys', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_column('api_keys', 'last_used')
    op.drop_column('api_keys', 'name')
    op.drop_index(op.f('ix_export_history_id'), table_name='export_history')
    op.drop_table('export_history')
    op.drop_index(op.f('ix_stability_data_id'), table_name='stability_data')
    op.drop_table('stability_data')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.drop_table('projects')
    op.drop_index(op.f('ix_excipient_compatibilities_id'), table_name='excipient_compatibilities')
    op.drop_table('excipient_compatibilities')
    op.drop_index(op.f('ix_excipient_batches_id'), table_name='excipient_batches')
    op.drop_table('excipient_batches')
    op.drop_index(op.f('ix_degradation_products_id'), table_name='degradation_products')
    op.drop_table('degradation_products')
    op.drop_index(op.f('ix_stability_studies_id'), table_name='stability_studies')
    op.drop_table('stability_studies')
    op.drop_index(op.f('ix_stability_protocols_id'), table_name='stability_protocols')
    op.drop_table('stability_protocols')
    op.drop_index(op.f('ix_stability_predictions_id'), table_name='stability_predictions')
    op.drop_table('stability_predictions')
    op.drop_index(op.f('ix_stability_conditions_id'), table_name='stability_conditions')
    op.drop_table('stability_conditions')
    op.drop_index(op.f('ix_packaging_materials_id'), table_name='packaging_materials')
    op.drop_table('packaging_materials')
    op.drop_index(op.f('ix_excipients_name'), table_name='excipients')
    op.drop_index(op.f('ix_excipients_id'), table_name='excipients')
    op.drop_table('excipients')
    op.drop_index(op.f('ix_excipient_interactions_name'), table_name='excipient_interactions')
    op.drop_index(op.f('ix_excipient_interactions_id'), table_name='excipient_interactions')
    op.drop_table('excipient_interactions')
    op.drop_index(op.f('ix_environments_id'), table_name='environments')
    op.drop_table('environments')
    op.drop_index(op.f('ix_drugs_name'), table_name='drugs')
    op.drop_index(op.f('ix_drugs_id'), table_name='drugs')
    op.drop_index(op.f('ix_drugs_cas'), table_name='drugs')
    op.drop_table('drugs')
    op.drop_index(op.f('ix_analysis_id'), table_name='analysis')
    op.drop_table('analysis')
    # ### end Alembic commands ###
