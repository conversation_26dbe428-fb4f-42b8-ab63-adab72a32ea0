#!/usr/bin/env python3
"""
端到端数据流测试
测试项目创建 -> 数据保存 -> 数据加载的完整流程
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api"

def test_complete_data_flow():
    """测试完整的数据流"""
    print("🚀 开始端到端数据流测试")
    print("=" * 60)
    
    try:
        # 步骤1: 创建新项目
        print("\n📝 步骤1: 创建新项目")
        project_data = {
            "name": f"数据流测试项目_{int(time.time())}",
            "description": "用于测试数据流的项目",
            "status": "进行中"
        }
        
        response = requests.post(f"{BASE_URL}/projects", json=project_data, timeout=15)
        if response.status_code != 200:
            print(f"❌ 项目创建失败: {response.status_code} - {response.text}")
            return False
        
        project = response.json()
        project_id = project["id"]
        print(f"✅ 项目创建成功: ID={project_id}, Name={project['name']}")
        
        # 步骤2: 保存项目数据
        print("\n💾 步骤2: 保存项目数据")
        test_data = {
            "drug_name": "测试药物_阿司匹林",
            "cas_number": "50-78-2",
            "molecular_formula": "C9H8O4",
            "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
            "category": "解热镇痛药",
            "description": "用于测试的阿司匹林数据",
            "formulation": [
                {"name": "微晶纤维素", "amount": "100mg"},
                {"name": "硬脂酸镁", "amount": "2mg"},
                {"name": "交联聚维酮", "amount": "5mg"}
            ],
            "packaging_storage": {
                "packaging": "铝塑泡罩包装",
                "storage": "密闭，在干燥处保存"
            },
            "production_process": "湿法制粒压片",
            "notes": "这是一个完整的测试数据集"
        }
        
        response = requests.post(f"{BASE_URL}/projects/{project_id}/save-data", 
                               json=test_data, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ 数据保存失败: {response.status_code} - {response.text}")
            return False
        
        save_result = response.json()
        if not save_result.get("success"):
            print(f"❌ 数据保存失败: {save_result}")
            return False
        
        print(f"✅ 数据保存成功: {save_result.get('message')}")
        print(f"   保存的数据: {save_result.get('saved_data', {}).get('drug_name', 'Unknown')}")
        
        # 步骤3: 获取项目数据
        print("\n📥 步骤3: 获取项目数据")
        response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 数据获取失败: {response.status_code} - {response.text}")
            return False
        
        get_result = response.json()
        if not get_result.get("success"):
            print(f"❌ 数据获取失败: {get_result}")
            return False
        
        retrieved_data = get_result.get("data", {})
        print(f"✅ 数据获取成功: {retrieved_data.get('drug_name', 'Unknown')}")
        
        # 步骤4: 验证数据完整性
        print("\n🔍 步骤4: 验证数据完整性")
        verification_passed = True
        
        # 检查关键字段
        key_fields = ["drug_name", "cas_number", "molecular_formula", "smiles", "category"]
        for field in key_fields:
            original_value = test_data.get(field)
            retrieved_value = retrieved_data.get(field)
            
            if original_value != retrieved_value:
                print(f"❌ 字段 {field} 不匹配: 原始='{original_value}', 获取='{retrieved_value}'")
                verification_passed = False
            else:
                print(f"✅ 字段 {field} 匹配: '{original_value}'")
        
        # 检查辅料数据
        original_formulation = test_data.get("formulation", [])
        retrieved_formulation = retrieved_data.get("formulation", [])
        
        if len(original_formulation) != len(retrieved_formulation):
            print(f"❌ 辅料数量不匹配: 原始={len(original_formulation)}, 获取={len(retrieved_formulation)}")
            verification_passed = False
        else:
            print(f"✅ 辅料数量匹配: {len(original_formulation)} 种")
        
        # 步骤5: 测试项目列表
        print("\n📋 步骤5: 验证项目列表")
        response = requests.get(f"{BASE_URL}/projects", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取项目列表失败: {response.status_code}")
            return False
        
        projects = response.json()
        project_found = False
        for p in projects:
            if p["id"] == project_id:
                project_found = True
                print(f"✅ 项目在列表中找到: {p['name']}")
                break
        
        if not project_found:
            print(f"❌ 项目在列表中未找到: ID={project_id}")
            verification_passed = False
        
        # 最终结果
        print("\n" + "=" * 60)
        if verification_passed:
            print("🎉 端到端数据流测试完全通过！")
            print("✅ 项目创建 -> 数据保存 -> 数据加载 -> 数据验证 全部成功")
            return True
        else:
            print("⚠️ 端到端数据流测试部分失败")
            print("❌ 数据完整性验证未通过")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_complete_data_flow()
    
    if success:
        print("\n🎯 结论: 数据保存和加载功能正常工作")
        print("   用户可以正常保存和恢复项目数据")
    else:
        print("\n🚨 结论: 数据流存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
