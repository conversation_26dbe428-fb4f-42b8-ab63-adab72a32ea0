"""
AI增强的API端点
提供AI驱动的药物稳定性分析和建议
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging

logger = logging.getLogger(__name__)

# 安全导入AI客户端
try:
    from app.services.ai_client import default_client, openai_client, deepseek_client, grok_client
    AI_CLIENT_AVAILABLE = True
except ImportError as e:
    logger.warning(f"AI客户端导入失败: {e}")
    AI_CLIENT_AVAILABLE = False
    default_client = None

# 安全导入AI配置
try:
    from app.config.ai_config import AIProvider
    AI_CONFIG_AVAILABLE = True
except ImportError as e:
    logger.warning(f"AI配置导入失败: {e}")
    AI_CONFIG_AVAILABLE = False

router = APIRouter()

class AIAnalysisRequest(BaseModel):
    drug_name: str
    drug_structure: Optional[str] = None
    analysis_type: str  # "stability", "compatibility", "formulation", "regulatory"
    data: Dict[str, Any]
    ai_provider: Optional[str] = "deepseek"  # "openai", "deepseek", "grok"

class StabilityAnalysisRequest(BaseModel):
    drug_name: str
    drug_structure: Optional[str] = None
    stability_data: List[Dict[str, Any]]
    conditions: Dict[str, Any]
    ai_provider: Optional[str] = "deepseek"

class CompatibilityAnalysisRequest(BaseModel):
    drug_name: str
    drug_structure: str
    excipients: List[str]
    conditions: Dict[str, Any]
    ai_provider: Optional[str] = "deepseek"

class FormulationOptimizationRequest(BaseModel):
    drug_name: str
    dosage_form: str
    formulation: List[Dict[str, Any]]
    target_specs: Dict[str, Any]
    ai_provider: Optional[str] = "deepseek"

class RegulatoryGuidanceRequest(BaseModel):
    product_type: str
    development_stage: str
    target_market: str
    specific_question: str
    ai_provider: Optional[str] = "deepseek"

def get_ai_client(provider: str):
    """根据提供商名称获取AI客户端"""
    if not AI_CLIENT_AVAILABLE:
        return None
    
    clients = {
        "openai": openai_client if 'openai_client' in globals() else None,
        "deepseek": deepseek_client if 'deepseek_client' in globals() else None,
        "grok": grok_client if 'grok_client' in globals() else None
    }
    return clients.get(provider.lower(), default_client)

@router.post("/ai/stability/analyze")
async def ai_stability_analysis(request: StabilityAnalysisRequest):
    """
    AI增强的稳定性分析
    """
    try:
        # 检查AI客户端可用性
        if not AI_CLIENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI服务不可用")
        
        # 获取AI客户端
        ai_client = get_ai_client(request.ai_provider)
        if not ai_client:
            raise HTTPException(status_code=400, detail=f"不支持的AI提供商: {request.ai_provider}")
        
        # AI增强分析
        ai_result = await ai_client.analyze_stability(
            drug_name=request.drug_name,
            stability_data=request.stability_data,
            conditions=request.conditions
        )
        
        return {
            "drug_name": request.drug_name,
            "analysis_type": "stability",
            "engine_analysis": {},
            "ai_analysis": ai_result,
            "combined_recommendations": _combine_stability_recommendations(ai_result),
            "provider": request.ai_provider
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"稳定性分析失败: {str(e)}")

@router.post("/ai/compatibility/analyze")
async def ai_compatibility_analysis(request: CompatibilityAnalysisRequest):
    """
    AI增强的相容性分析
    """
    try:
        # 检查AI客户端可用性
        if not AI_CLIENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI服务不可用")
        
        # 获取AI客户端
        ai_client = get_ai_client(request.ai_provider)
        if not ai_client:
            raise HTTPException(status_code=400, detail=f"不支持的AI提供商: {request.ai_provider}")
        
        # AI增强分析
        ai_result = await ai_client.analyze_compatibility(
            drug_name=request.drug_name,
            drug_structure=request.drug_structure,
            excipients=request.excipients,
            conditions=request.conditions
        )
        
        return {
            "drug_name": request.drug_name,
            "analysis_type": "compatibility",
            "engine_analysis": {},
            "ai_analysis": ai_result,
            "combined_recommendations": _combine_compatibility_recommendations(ai_result),
            "provider": request.ai_provider
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"相容性分析失败: {str(e)}")

@router.post("/ai/formulation/optimize")
async def ai_formulation_optimization(request: FormulationOptimizationRequest):
    """
    AI驱动的处方优化
    """
    try:
        # 检查AI客户端可用性
        if not AI_CLIENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI服务不可用")
        
        # 获取AI客户端
        ai_client = get_ai_client(request.ai_provider)
        if not ai_client:
            raise HTTPException(status_code=400, detail=f"不支持的AI提供商: {request.ai_provider}")
        
        # AI处方优化分析
        ai_result = await ai_client.optimize_formulation(
            drug_name=request.drug_name,
            dosage_form=request.dosage_form,
            formulation=request.formulation,
            target_specs=request.target_specs
        )
        
        # 基础处方分析
        basic_analysis = _analyze_formulation_basics(request.formulation, request.target_specs)
        
        return {
            "drug_name": request.drug_name,
            "dosage_form": request.dosage_form,
            "analysis_type": "formulation_optimization",
            "basic_analysis": basic_analysis,
            "ai_analysis": ai_result,
            "optimization_score": _calculate_optimization_score(basic_analysis, ai_result),
            "provider": request.ai_provider
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处方优化失败: {str(e)}")

@router.post("/ai/regulatory/guidance")
async def ai_regulatory_guidance(request: RegulatoryGuidanceRequest):
    """
    AI法规指导
    """
    try:
        # 检查AI客户端可用性
        if not AI_CLIENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI服务不可用")
        
        # 获取AI客户端
        ai_client = get_ai_client(request.ai_provider)
        if not ai_client:
            raise HTTPException(status_code=400, detail=f"不支持的AI提供商: {request.ai_provider}")
        
        # AI法规指导
        ai_result = await ai_client.get_regulatory_guidance(
            product_type=request.product_type,
            development_stage=request.development_stage,
            target_market=request.target_market,
            specific_question=request.specific_question
        )
        
        # 基础法规信息
        basic_guidance = _get_basic_regulatory_info(
            request.product_type, 
            request.target_market
        )
        
        return {
            "product_type": request.product_type,
            "development_stage": request.development_stage,
            "target_market": request.target_market,
            "analysis_type": "regulatory_guidance",
            "basic_guidance": basic_guidance,
            "ai_guidance": ai_result,
            "action_items": _extract_action_items(ai_result),
            "provider": request.ai_provider
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"法规指导失败: {str(e)}")

@router.post("/ai/comprehensive/analyze")
async def ai_comprehensive_analysis(request: AIAnalysisRequest):
    """
    AI综合分析
    """
    try:
        # 检查AI客户端可用性
        if not AI_CLIENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI服务不可用")
        
        # 获取AI客户端
        ai_client = get_ai_client(request.ai_provider)
        if not ai_client:
            raise HTTPException(status_code=400, detail=f"不支持的AI提供商: {request.ai_provider}")
        
        results = {}
        
        # 根据分析类型调用相应的分析方法
        if request.analysis_type == "stability":
            if "stability_data" in request.data:
                results["stability"] = await ai_client.analyze_stability(
                    drug_name=request.drug_name,
                    stability_data=request.data["stability_data"],
                    conditions=request.data.get("conditions", {})
                )
        
        elif request.analysis_type == "compatibility":
            if "excipients" in request.data:
                results["compatibility"] = await ai_client.analyze_compatibility(
                    drug_name=request.drug_name,
                    drug_structure=request.drug_structure or "",
                    excipients=request.data["excipients"],
                    conditions=request.data.get("conditions", {})
                )
        
        elif request.analysis_type == "formulation":
            if "formulation" in request.data:
                results["formulation"] = await ai_client.optimize_formulation(
                    drug_name=request.drug_name,
                    dosage_form=request.data.get("dosage_form", "片剂"),
                    formulation=request.data["formulation"],
                    target_specs=request.data.get("target_specs", {})
                )
        
        # 生成综合建议
        comprehensive_recommendations = _generate_comprehensive_recommendations(results)
        
        return {
            "drug_name": request.drug_name,
            "analysis_type": request.analysis_type,
            "results": results,
            "comprehensive_recommendations": comprehensive_recommendations,
            "provider": request.ai_provider
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"综合分析失败: {str(e)}")

@router.get("/ai/providers")
async def get_ai_providers():
    """
    获取可用的AI提供商
    """
    if not AI_CONFIG_AVAILABLE:
        return {
            "providers": [],
            "default": "none",
            "error": "AI配置不可用"
        }
    
    return {
        "providers": [
            {
                "name": "OpenAI",
                "value": "openai",
                "model": "gpt-4o-mini",
                "description": "OpenAI GPT模型，适合复杂分析",
                "available": AI_CLIENT_AVAILABLE
            },
            {
                "name": "DeepSeek",
                "value": "deepseek",
                "model": "deepseek-chat",
                "description": "DeepSeek模型，专业知识丰富",
                "available": AI_CLIENT_AVAILABLE
            },
            {
                "name": "Grok",
                "value": "grok",
                "model": "grok-beta",
                "description": "Grok模型，实时信息处理",
                "available": AI_CLIENT_AVAILABLE
            }
        ],
        "default": "deepseek" if AI_CLIENT_AVAILABLE else "none",
        "ai_client_available": AI_CLIENT_AVAILABLE,
        "compatibility_engine_available": True,
        "stability_engine_available": True
    }

def _combine_stability_recommendations(ai_result: Dict) -> List[str]:
    """合并稳定性分析建议"""
    recommendations = []
    
    # 从AI结果提取建议
    if isinstance(ai_result, dict) and "recommendations" in ai_result:
        recommendations.extend(ai_result["recommendations"])
    
    # 去重并返回前10条
    unique_recommendations = list(dict.fromkeys(recommendations))
    return unique_recommendations[:10]

def _combine_compatibility_recommendations(ai_result: Dict) -> List[str]:
    """合并相容性分析建议"""
    recommendations = []
    
    # 从AI结果提取建议
    if isinstance(ai_result, dict) and "recommendations" in ai_result:
        recommendations.extend(ai_result["recommendations"])
    
    # 去重并返回前10条
    unique_recommendations = list(dict.fromkeys(recommendations))
    return unique_recommendations[:10]

def _analyze_formulation_basics(formulation: List[Dict], target_specs: Dict) -> Dict[str, Any]:
    """基础处方分析"""
    total_percentage = sum(item.get("percentage", 0) for item in formulation)
    
    # 分析辅料类别分布
    categories = {}
    for item in formulation:
        category = item.get("function", "未知")
        if category not in categories:
            categories[category] = []
        categories[category].append(item["name"])
    
    # 基础评估
    issues = []
    if abs(total_percentage - 100) > 1:
        issues.append(f"处方总百分比为{total_percentage}%，应接近100%")
    
    if "填充剂" not in categories:
        issues.append("缺少填充剂")
    
    if "崩解剂" not in categories:
        issues.append("缺少崩解剂")
    
    return {
        "total_percentage": total_percentage,
        "categories": categories,
        "issues": issues,
        "excipient_count": len(formulation)
    }

def _calculate_optimization_score(basic_analysis: Dict, ai_result: Dict) -> float:
    """计算优化评分"""
    score = 100.0
    
    # 基础扣分
    if basic_analysis.get("issues"):
        score -= len(basic_analysis["issues"]) * 10
    
    # 百分比偏差扣分
    percentage_deviation = abs(basic_analysis.get("total_percentage", 100) - 100)
    score -= percentage_deviation * 2
    
    # AI建议数量影响
    if isinstance(ai_result, dict) and "optimization_suggestions" in ai_result:
        suggestion_count = len(ai_result["optimization_suggestions"])
        if suggestion_count > 3:
            score -= (suggestion_count - 3) * 5
    
    return max(score, 0)

def _get_basic_regulatory_info(product_type: str, target_market: str) -> Dict[str, Any]:
    """获取基础法规信息"""
    regulatory_info = {
        "applicable_guidelines": [],
        "key_requirements": [],
        "typical_timeline": "未知"
    }
    
    # 根据目标市场确定适用指导原则
    if "中国" in target_market or "NMPA" in target_market:
        regulatory_info["applicable_guidelines"].extend([
            "《药品注册管理办法》",
            "《化学药品注册分类及申报资料要求》"
        ])
        regulatory_info["typical_timeline"] = "12-18个月"
    
    if "美国" in target_market or "FDA" in target_market:
        regulatory_info["applicable_guidelines"].extend([
            "FDA Guidance Documents",
            "ICH Guidelines"
        ])
        regulatory_info["typical_timeline"] = "10-12个月"
    
    if "欧盟" in target_market or "EMA" in target_market:
        regulatory_info["applicable_guidelines"].extend([
            "EMA Guidelines",
            "ICH Guidelines"
        ])
        regulatory_info["typical_timeline"] = "12-15个月"
    
    # 根据产品类型确定关键要求
    if "片剂" in product_type:
        regulatory_info["key_requirements"].extend([
            "稳定性研究",
            "溶出度研究",
            "生物等效性研究"
        ])
    
    return regulatory_info

def _extract_action_items(ai_result: Dict) -> List[Dict[str, str]]:
    """提取行动项目"""
    action_items = []
    
    if isinstance(ai_result, dict):
        if "regulatory_requirements" in ai_result:
            for req in ai_result["regulatory_requirements"]:
                action_items.append({
                    "action": req,
                    "priority": "高",
                    "category": "法规要求"
                })
        
        if "timeline" in ai_result:
            for phase, description in ai_result["timeline"].items():
                action_items.append({
                    "action": description,
                    "priority": "中",
                    "category": "时间节点"
                })
    
    return action_items

def _generate_comprehensive_recommendations(results: Dict[str, Any]) -> List[str]:
    """生成综合建议"""
    recommendations = []
    
    for analysis_type, result in results.items():
        if isinstance(result, dict) and "recommendations" in result:
            recommendations.extend(result["recommendations"])
    
    # 去重并添加综合性建议
    unique_recommendations = list(dict.fromkeys(recommendations))
    
    # 添加综合性建议
    if len(results) > 1:
        unique_recommendations.insert(0, "建议综合考虑稳定性、相容性和处方优化的结果")
        unique_recommendations.append("建议进行全面的风险评估和控制策略制定")
    
    return unique_recommendations[:15] 