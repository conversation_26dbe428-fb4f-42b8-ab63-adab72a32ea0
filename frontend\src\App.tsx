import React, { createContext, useState, useEffect, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import './App.css';
import LoginPage from './pages/LoginPage';
import { jwtDecode } from 'jwt-decode';
import UserCenterPage from './pages/UserCenterPage';
import { applyTheme } from './theme';
import Dashboard from './pages/Dashboard';
import HelpCenter from './components/HelpCenter';
import FeedbackModal from './components/FeedbackModal';
import { Layout, message } from 'antd';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import ProjectList from './pages/ProjectList';
import ProjectDetail from './pages/ProjectDetail';
import DataInput from './pages/DataInput';
import StabilityPrediction from './pages/StabilityPrediction';
import ExcipientAnalysis from './pages/ExcipientAnalysis';
import FormulationAnalysis from './pages/FormulationAnalysis';
import UserManagement from './pages/UserManagement';
import SystemSettings from './pages/SystemSettings';
import NotFound from './pages/NotFound';
import PubChemSearch from './pages/PubChemSearch';
import AIAnalysis from './pages/AIAnalysis';
import ProjectManagementPage from './pages/ProjectManagementPage';
import DebugPanel from './components/DebugPanel';
import ErrorBoundary from './components/ErrorBoundary';
import { listProjects, createProject, updateProject, deleteProject as deleteProjectApi, getProjectData } from './api';
import type { Project, ProjectCreatePayload } from './api';

const { Content } = Layout;

interface ProjectContextType {
  projects: Project[];
  currentProject: Project | null;
  inputData?: any;
  setInputData?: (data: any) => void;
  analysisResult?: any;
  setAnalysisResult?: (result: any) => void;
  loading: boolean;
  fetchProjects: () => void;
  loadProjectData: (projectId: string) => Promise<void>;
  addProject: (p: ProjectCreatePayload) => Promise<any>;
  editProject: (p: Project) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (p: Project | null) => void;
}

export const ProjectContext = createContext<ProjectContextType>({
  projects: [],
  currentProject: null,
  inputData: undefined,
  setInputData: undefined,
  analysisResult: undefined,
  setAnalysisResult: undefined,
  loading: false,
  fetchProjects: () => {},
  loadProjectData: async () => {},
  addProject: async () => {},
  editProject: async () => {},
  deleteProject: async () => {},
  setCurrentProject: () => {},
});

export const RoleContext = createContext<'admin' | 'user' | ''>('');

function RequireAuth({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const token = localStorage.getItem('token');
  if (!token) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  return <>{children}</>;
}

function App() {
  const [role, setRole] = useState<'admin' | 'user' | ''>('');
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [inputData, setInputData] = useState<any>({});
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  const fetchProjects = useCallback(async () => {
    setLoading(true);
    try {
      const response = await listProjects();
      setProjects?.(response.data);
    } catch (error) {
      message.error("获取项目列表失败");
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, []);

  const addProject = async (p: ProjectCreatePayload) => {
    try {
      const response = await createProject(p);
      await fetchProjects();
      return response.data;
    } catch (error) {
      message.error("创建项目失败");
      console.error(error);
      throw error;
    }
  };

  const editProject = async (p: Project) => {
    try {
      await updateProject(p.id, p);
      await fetchProjects();
    } catch (error) {
      message.error("更新项目失败");
      console.error(error);
      throw error;
    }
  };

  const deleteProject = async (id: string) => {
    try {
      await deleteProjectApi(id);
      await fetchProjects();
      if (currentProject && currentProject.id === id) {
        setCurrentProject(null);
      }
    } catch (error) {
      message.error("删除项目失败");
      console.error(error);
      throw error;
    }
  };

  const loadProjectData = async (projectId: string) => {
    try {
      console.log('开始加载项目数据，项目ID:', projectId);
      const response = await getProjectData(projectId);
      console.log('API响应:', response.data);

      if (response.data && (response.data as any).success) {
        const projectData = (response.data as any).data;
        console.log('项目原始数据:', projectData);

        // 转换后端数据格式为前端期望的格式
        const formattedData = {
          drug_name: projectData?.drug_name,
          drug_cas: projectData?.cas_number,
          drug_formula: projectData?.molecular_formula,
          drug_smiles: projectData?.smiles,
          drug_category: projectData?.category,
          drug_description: projectData?.description,
          excipients: projectData?.formulation || [],
          packaging_storage: projectData?.packaging_storage,
          production_process: projectData?.production_process,
          notes: projectData?.notes
        };

        console.log('转换后的数据:', formattedData);
        setInputData(formattedData);
      } else {
        // 如果API响应不成功，设置空数据
        setInputData({
          drug_name: '',
          drug_cas: '',
          drug_formula: '',
          drug_smiles: '',
          drug_category: '',
          drug_description: '',
          excipients: [],
          packaging_storage: null,
          production_process: '',
          notes: ''
        });
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      // 设置空数据，确保加载状态能够结束
      setInputData({
        drug_name: '',
        drug_cas: '',
        drug_formula: '',
        drug_smiles: '',
        drug_category: '',
        drug_description: '',
        excipients: [],
        packaging_storage: null,
        production_process: '',
        notes: ''
      });
    }
  };

  useEffect(() => {
    applyTheme();
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload: any = jwtDecode(token);
        setRole(payload.role || 'user');
        fetchProjects();
      } catch {}
    }
  }, [fetchProjects]);
  
  // Load current project from local storage
  useEffect(() => {
    const c = localStorage.getItem('currentProject');
    if (c) {
        try {
            setCurrentProject(JSON.parse(c));
        } catch(e) {
            console.error("Failed to parse currentProject from localStorage", e)
        }
    }
  }, []);
  
  // Save current project to local storage
  useEffect(() => {
    if(currentProject) {
        localStorage.setItem('currentProject', JSON.stringify(currentProject));
    } else {
        localStorage.removeItem('currentProject');
    }
  }, [currentProject]);


  return (
    <ErrorBoundary>
      <Router>
        <RoleContext.Provider value={role}>
          <ProjectContext.Provider value={{
            projects,
            currentProject,
            inputData,
            setInputData,
            analysisResult,
            setAnalysisResult,
            loading,
            fetchProjects,
            loadProjectData,
            addProject,
            editProject,
            deleteProject,
            setCurrentProject
          }}>
            <Layout style={{ minHeight: '100vh' }}>
              <Routes>
                <Route path="/login" element={<LoginPage setRole={setRole} />} />
                <Route path="/*" element={
                  <RequireAuth>
                    <Layout>
                      <Sidebar />
                      <Layout>
                        <Header />
                        <Content style={{ margin: '24px 16px', padding: 24, background: '#fff' }}>
                          <ErrorBoundary>
                            <Routes>
                              <Route path="/" element={<Dashboard />} />
                              <Route path="/projects" element={<ProjectManagementPage />} />
                              <Route path="/project-management" element={<ProjectManagementPage />} />
                              <Route path="/projects/:id" element={<ProjectDetail />} />
                              <Route path="/data-input" element={<DataInput />} />
                              <Route path="/stability-prediction" element={<StabilityPrediction />} />
                              <Route path="/excipient-analysis" element={<ExcipientAnalysis />} />
                              <Route path="/formulation-analysis" element={<FormulationAnalysis />} />
                              <Route path="/user-management" element={<UserManagement />} />
                              <Route path="/system-settings" element={<SystemSettings />} />
                              <Route path="/user-center" element={<UserCenterPage />} />
                              <Route path="/help" element={<HelpCenter />} />
                              <Route path="/pubchem-search" element={<PubChemSearch />} />
                              <Route path="/ai-analysis" element={<AIAnalysis />} />
                              <Route path="*" element={<NotFound />} />
                            </Routes>
                          </ErrorBoundary>
                          <FeedbackModal />
                        </Content>
                      </Layout>
                    </Layout>
                  </RequireAuth>
                }/>
              </Routes>
            </Layout>
          </ProjectContext.Provider>
        </RoleContext.Provider>
      </Router>
      {/* 调试面板 - 仅在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && <DebugPanel />}
    </ErrorBoundary>
  );
}

export default App;
