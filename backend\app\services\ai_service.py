"""
AI服务模块
提供AI建议和分析功能
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.export import ExportHistory
from app.models.analysis import AISuggestion
from app.services.ai_client import AIClient
import asyncio
import json

# 实例化AI客户端
ai_client = AIClient()

async def generate_ai_suggestions_real(data: Dict[str, Any], user_id: Optional[int] = None) -> Dict[str, Any]:
    """
    使用真实的AI客户端生成AI建议
    """
    analysis_type = data.get("analysis_type", "general")
    analysis_results = {}
    suggestions = []
    risk_level = "低"

    try:
        if analysis_type == "stability" and "stability_data" in data:
            # 调用AI客户端进行稳定性分析
            drug_name = data.get("drug_name", "未知药物")
            conditions = data.get("conditions", {})
            stability_data = data.get("stability_data", [])
            ai_response = await ai_client.analyze_stability(drug_name, stability_data, conditions)
            analysis_results = ai_response
            if ai_response.get("recommendations"):
                for rec in ai_response["recommendations"]:
                    suggestions.append({"type": "stability", "content": rec, "priority": "高"})
            risk_level = ai_response.get("risk_assessment", {}).get("level", "低")

        elif analysis_type == "compatibility" and "compatibility_data" in data:
            # 调用AI客户端进行相容性分析
            drug_name = data.get("drug_name", "未知药物")
            drug_structure = data.get("drug_structure", "")
            excipients = [item.get("name") for item in data.get("excipients", [])]
            conditions = data.get("conditions", {})
            ai_response = await ai_client.analyze_compatibility(drug_name, drug_structure, excipients, conditions)
            analysis_results = ai_response
            if ai_response.get("recommendations"):
                 for rec in ai_response["recommendations"]:
                    suggestions.append({"type": "compatibility", "content": rec, "priority": "高"})
            # 简单的风险转换
            if any("高风险" in item.get("analysis", "") for item in ai_response.get("mechanisms", [])):
                risk_level = "高"
            elif any("中风险" in item.get("analysis", "") for item in ai_response.get("mechanisms", [])):
                risk_level = "中"

        else:
            # 通用分析
            # 对于通用情况，我们可以构造一个简单的prompt
            prompt_data = json.dumps(data, indent=2, ensure_ascii=False)
            messages = [
                {"role": "system", "content": "你是一名资深的药物制剂专家。请根据以下数据，提供专业的AI建议。"},
                {"role": "user", "content": f"分析数据: \n{prompt_data}"}
            ]
            ai_response = await ai_client.chat_completion(messages)
            analysis_results = ai_response
            suggestions.append({"type": "general", "content": ai_response.get("content", "请进行常规检查。"), "priority": "中"})

    except Exception as e:
        # 如果AI调用失败，返回错误信息或备用建议
        return {
            "error": f"AI分析失败: {str(e)}",
            "suggestions": [{"type": "error", "content": "AI服务暂时不可用，请稍后重试或联系管理员。", "priority": "高"}],
            "risk_level": "未知",
            "timestamp": datetime.now().isoformat(),
        }

    return {
        "suggestions": suggestions,
        "risk_level": risk_level,
        "analysis_details": analysis_results, # 包含来自AI的完整分析
        "timestamp": datetime.now().isoformat(),
        "analysis_id": f"ai-real-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    }


def generate_ai_suggestions(data: Dict[str, Any], user_id: Optional[int] = None) -> Dict[str, Any]:
    """
    生成AI建议 (此函数现在是新异步函数的同步包装器)
    
    参数:
        data: 输入数据
        user_id: 用户ID
        
    返回:
        包含AI建议的字典
    """
    # 这是一个同步包装器，用于调用异步函数。
    # 在支持异步的框架（如FastAPI）中，应直接调用 generate_ai_suggestions_real
    try:
        # 检查当前是否有正在运行的事件循环
        loop = asyncio.get_running_loop()
        # 如果有，并且它正在运行，我们不能使用 run_until_complete，需要创建新任务
        if loop.is_running():
            # 这是在Jupyter/Databricks等环境中常见的情况
            # 使用nest_asyncio库可以解决此问题，但这里我们尝试一种不引入新依赖的方法
            # 注意：这可能不是在所有情况下都有效
            task = loop.create_task(generate_ai_suggestions_real(data, user_id))
            # 这是一个简化的处理方式，实际应用中可能需要更复杂的同步上下文管理
            # 这里我们无法直接获得结果，所以这种方式不适合直接返回。
            # 为了简单起见，我们将启动一个新的事件循环。
            # 这在生产环境中效率不高，但能确保在不同步/异步环境中都能执行。
            return asyncio.run(generate_ai_suggestions_real(data, user_id))
        else: # pragma: no cover
            return asyncio.run(generate_ai_suggestions_real(data, user_id))
    except RuntimeError:
        # 没有正在运行的事件循环，安全地创建一个新的
        return asyncio.run(generate_ai_suggestions_real(data, user_id))


def add_export_history_service(db: Session, user_id: int, export_type: str, 
                              file_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    添加导出历史记录
    
    参数:
        db: 数据库会话
        user_id: 用户ID
        export_type: 导出类型
        file_name: 文件名
        params: 导出参数
        
    返回:
        导出历史记录
    """
    try:
        export_history = ExportHistory(
            user_id=user_id,
            export_type=export_type,
            file_name=file_name,
            params=str(params)
        )
        db.add(export_history)
        db.commit()
        db.refresh(export_history)
        
        return {
            "id": export_history.id,
            "user_id": export_history.user_id,
            "export_type": export_history.export_type,
            "file_name": export_history.file_name,
            "created_at": export_history.created_at.isoformat() if export_history.created_at else None
        }
    except Exception as e:
        db.rollback()
        return {"error": str(e)}

def list_export_history_service(db: Session, user_id: Optional[int] = None, 
                               limit: int = 50) -> List[Dict[str, Any]]:
    """
    获取导出历史记录列表
    
    参数:
        db: 数据库会话
        user_id: 用户ID，如果为None则获取所有记录
        limit: 限制返回记录数量
        
    返回:
        导出历史记录列表
    """
    try:
        query = db.query(ExportHistory)
        if user_id is not None:
            query = query.filter(ExportHistory.user_id == user_id)
        
        records = query.order_by(ExportHistory.created_at.desc()).limit(limit).all()
        
        return [{
            "id": record.id,
            "user_id": record.user_id,
            "export_type": record.export_type,
            "file_name": record.file_name,
            "created_at": record.created_at.isoformat() if record.created_at else None
        } for record in records]
    except Exception:
        # 出错时返回空列表
        return [] 