import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Space, Tooltip, Typography, message, Dropdown, Menu } from 'antd';
import { SearchOutlined, DownloadOutlined, ReloadOutlined, SettingOutlined, EllipsisOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { TableProps } from 'antd/lib/table';
import * as XLSX from 'xlsx';

const { Text } = Typography;

interface DataTableProps<T extends Record<string, any>> extends Omit<TableProps<T>, 'title'> {
  title?: string;
  data: T[];
  columns: any[];
  loading?: boolean;
  searchable?: boolean;
  exportable?: boolean;
  refreshable?: boolean;
  onRefresh?: () => void;
  extraActions?: React.ReactNode;
  filename?: string;
  sheetName?: string;
  rowKey?: string;
  pagination?: any;
  scroll?: any;
  summaryRow?: React.ReactNode;
  emptyText?: string;
  contextMenu?: {
    label: string;
    key: string;
    onClick: (record: T) => void;
    icon?: React.ReactNode;
    disabled?: (record: T) => boolean;
  }[];
}

/**
 * 增强的数据表格组件
 * 支持搜索、导出、刷新等功能
 */
const DataTable = <T extends Record<string, any>>({
  title,
  data = [],
  columns = [],
  loading = false,
  searchable = true,
  exportable = true,
  refreshable = true,
  onRefresh,
  extraActions,
  filename = 'export',
  sheetName = 'Sheet1',
  rowKey = 'id',
  pagination = { pageSize: 10 },
  scroll,
  summaryRow,
  emptyText = '暂无数据',
  contextMenu,
  ...rest
}: DataTableProps<T>) => {
  const [searchText, setSearchText] = useState<string>('');
  const [searchedColumn, setSearchedColumn] = useState<string>('');
  const [filteredData, setFilteredData] = useState<T[]>(data);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 初始化列配置
  useEffect(() => {
    if (columns.length > 0) {
      const newColumns = columns.map(col => {
        // 如果列需要搜索功能
        if (col.searchable) {
          return {
            ...col,
            filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
              <div style={{ padding: 8 }}>
                <Input
                  placeholder={`搜索 ${col.title}`}
                  value={selectedKeys[0]}
                  onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                  onPressEnter={() => handleSearch(selectedKeys, confirm, col.dataIndex)}
                  style={{ width: 188, marginBottom: 8, display: 'block' }}
                />
                <Space>
                  <Button
                    type="primary"
                    onClick={() => handleSearch(selectedKeys, confirm, col.dataIndex)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90 }}
                  >
                    搜索
                  </Button>
                  <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    重置
                  </Button>
                </Space>
              </div>
            ),
            filterIcon: (filtered: boolean) => (
              <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
            ),
            onFilter: (value: string, record: T) => {
              const dataValue = record[col.dataIndex as keyof T];
              if (dataValue === null || dataValue === undefined) return false;
              return dataValue.toString().toLowerCase().includes(value.toLowerCase());
            },
            render: (text: string) =>
              searchedColumn === col.dataIndex ? (
                <Highlighter
                  highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                  searchWords={[searchText]}
                  autoEscape
                  textToHighlight={text ? text.toString() : ''}
                />
              ) : (
                text
              ),
          };
        }
        
        return col;
      });

      // 如果有上下文菜单，添加操作列
      if (contextMenu && contextMenu.length > 0) {
        newColumns.push({
          title: '操作',
          key: 'operation',
          width: 70,
          render: (text: string, record: T) => (
            <Dropdown
              menu={{
                items: contextMenu.map(item => ({
                  key: item.key,
                  label: item.label,
                  icon: item.icon,
                  disabled: item.disabled ? item.disabled(record) : false,
                  onClick: () => item.onClick(record)
                }))
              }}
              trigger={['click']}
            >
              <Button type="text" icon={<EllipsisOutlined />} />
            </Dropdown>
          ),
        });
      }

      setTableColumns(newColumns);
    }
  }, [columns, searchText, searchedColumn, contextMenu]);

  // 更新过滤后的数据
  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  const handleSearch = (selectedKeys: string[], confirm: Function, dataIndex: string) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: Function) => {
    clearFilters();
    setSearchText('');
  };

  const handleExport = () => {
    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();
      
      // 提取导出数据
      const exportData = data.map(item => {
        const row: any = {};
        columns.forEach(col => {
          // 跳过操作列和不需要导出的列
          if (col.key !== 'operation' && col.dataIndex && !col.noExport) {
            if (col.render) {
              // 如果有自定义渲染函数，尝试获取原始值
              row[col.title] = item[col.dataIndex as keyof T];
            } else {
              row[col.title] = item[col.dataIndex as keyof T];
            }
          }
        });
        return row;
      });
      
      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);
      
      // 将工作表添加到工作簿
      XLSX.utils.book_append_sheet(wb, ws, sheetName);
      
      // 导出为Excel文件
      XLSX.writeFile(wb, `${filename}-${new Date().toISOString().split('T')[0]}.xlsx`);
      
      message.success('导出成功');
    } catch (error) {
      console.error('导出错误:', error);
      message.error('导出失败');
    }
  };

  return (
    <div className="data-table">
      {(title || searchable || exportable || refreshable || extraActions) && (
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          {title && <Text strong style={{ fontSize: 16 }}>{title}</Text>}
          
          <Space>
            {searchable && (
              <Input.Search
                placeholder="全局搜索"
                onSearch={value => {
                  const filtered = data.filter(item => {
                    return Object.keys(item).some(key => {
                      const val = item[key];
                      return val !== null && val !== undefined && val.toString().toLowerCase().includes(value.toLowerCase());
                    });
                  });
                  setFilteredData(filtered);
                }}
                style={{ width: 200 }}
                allowClear
              />
            )}
            
            {refreshable && (
              <Tooltip title="刷新">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    if (onRefresh) {
                      onRefresh();
                    }
                  }}
                />
              </Tooltip>
            )}
            
            {exportable && (
              <Tooltip title="导出">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  disabled={filteredData.length === 0}
                />
              </Tooltip>
            )}
            
            {extraActions}
          </Space>
        </div>
      )}
      
      <Table
        rowKey={rowKey}
        dataSource={filteredData}
        columns={tableColumns}
        loading={loading}
        pagination={pagination}
        scroll={scroll}
        summary={() => summaryRow}
        locale={{ emptyText }}
        {...rest}
      />
    </div>
  );
};

export default DataTable; 