"""
EPA CompTox数据集成服务
集成EPA CompTox Chemicals Dashboard的毒理学和环境数据
"""
import logging
import requests
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
import aiohttp
import asyncio
from datetime import datetime, timedelta
import sqlite3

logger = logging.getLogger(__name__)

# CompTox API配置
COMPTOX_BASE_URL = "https://comptox.epa.gov/dashboard-api"
COMPTOX_ENDPOINTS = {
    "search": "/ccdapp1/search/chemical/start-with/{query}",
    "details": "/ccdapp1/chemical-detail/{dtxsid}",
    "properties": "/ccdapp1/chemical-properties/{dtxsid}",
    "hazard": "/ccdapp1/hazard/{dtxsid}",
    "bioactivity": "/ccdapp1/bioactivity/{dtxsid}"
}

# 缓存配置
CACHE_DIR = Path("data/comptox_cache")
CACHE_DIR.mkdir(parents=True, exist_ok=True)
CACHE_DURATION = timedelta(days=30)  # 缓存30天


class CompToxService:
    """EPA CompTox数据服务类"""
    
    def __init__(self):
        self.base_url = COMPTOX_BASE_URL
        self.session = None
        self.cache_db = CACHE_DIR / "comptox_cache.db"
        self._init_cache_db()
    
    def _init_cache_db(self):
        """初始化缓存数据库"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS cache (
                key TEXT PRIMARY KEY,
                value TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS toxicity_data (
                cas_number TEXT PRIMARY KEY,
                dtxsid TEXT,
                chemical_name TEXT,
                
                -- 急性毒性
                oral_ld50 TEXT,
                dermal_ld50 TEXT,
                inhalation_lc50 TEXT,
                
                -- 慢性毒性
                noael TEXT,
                loael TEXT,
                
                -- 环境毒性
                fish_lc50 TEXT,
                daphnia_ec50 TEXT,
                algae_ec50 TEXT,
                
                -- 特殊毒性
                carcinogenicity TEXT,
                mutagenicity TEXT,
                reproductive_toxicity TEXT,
                endocrine_disruption TEXT,
                
                -- 其他信息
                ghs_classification TEXT,
                environmental_fate TEXT,
                bioaccumulation TEXT,
                
                source TEXT DEFAULT 'EPA CompTox',
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
    
    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT value, timestamp FROM cache 
            WHERE key = ? AND datetime(timestamp, '+30 days') > datetime('now')
        """, (key,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return json.loads(result[0])
        return None
    
    def _save_to_cache(self, key: str, value: Dict[str, Any]):
        """保存到缓存"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO cache (key, value, timestamp)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """, (key, json.dumps(value)))
        
        conn.commit()
        conn.close()
    
    async def search_chemical(self, query: str) -> List[Dict[str, Any]]:
        """搜索化学品"""
        cache_key = f"search_{query}"
        cached_result = self._get_from_cache(cache_key)
        
        if cached_result:
            return cached_result
        
        try:
            url = f"{self.base_url}{COMPTOX_ENDPOINTS['search'].format(query=query)}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = data.get('results', [])
                        self._save_to_cache(cache_key, results)
                        return results
                    else:
                        logger.error(f"CompTox API错误: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"搜索CompTox化学品时出错: {e}")
            return []
    
    async def get_chemical_details(self, dtxsid: str) -> Optional[Dict[str, Any]]:
        """获取化学品详细信息"""
        cache_key = f"details_{dtxsid}"
        cached_result = self._get_from_cache(cache_key)
        
        if cached_result:
            return cached_result
        
        try:
            details = {}
            
            # 获取基本信息
            url = f"{self.base_url}{COMPTOX_ENDPOINTS['details'].format(dtxsid=dtxsid)}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        details['basic'] = await response.json()
            
            # 获取性质信息
            url = f"{self.base_url}{COMPTOX_ENDPOINTS['properties'].format(dtxsid=dtxsid)}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        details['properties'] = await response.json()
            
            # 获取危害信息
            url = f"{self.base_url}{COMPTOX_ENDPOINTS['hazard'].format(dtxsid=dtxsid)}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        details['hazard'] = await response.json()
            
            self._save_to_cache(cache_key, details)
            return details
            
        except Exception as e:
            logger.error(f"获取CompTox详细信息时出错: {e}")
            return None
    
    def extract_toxicity_data(self, comptox_data: Dict[str, Any]) -> Dict[str, Any]:
        """从CompTox数据中提取毒理学信息"""
        toxicity = {
            "acute_toxicity": {},
            "chronic_toxicity": {},
            "environmental_toxicity": {},
            "special_endpoints": {}
        }
        
        if not comptox_data:
            return toxicity
        
        # 提取急性毒性数据
        hazard_data = comptox_data.get('hazard', {})
        if hazard_data:
            # 这里需要根据实际的API响应结构调整
            toxicity["acute_toxicity"] = {
                "oral_ld50": self._extract_value(hazard_data, "oral_ld50"),
                "dermal_ld50": self._extract_value(hazard_data, "dermal_ld50"),
                "inhalation_lc50": self._extract_value(hazard_data, "inhalation_lc50")
            }
        
        # 提取环境毒性数据
        env_data = comptox_data.get('properties', {}).get('environmental', {})
        if env_data:
            toxicity["environmental_toxicity"] = {
                "fish_lc50": self._extract_value(env_data, "fish_96h_lc50"),
                "daphnia_ec50": self._extract_value(env_data, "daphnia_48h_ec50"),
                "algae_ec50": self._extract_value(env_data, "algae_72h_ec50")
            }
        
        # 提取特殊终点
        toxicity["special_endpoints"] = {
            "carcinogenicity": self._extract_classification(hazard_data, "carcinogenicity"),
            "mutagenicity": self._extract_classification(hazard_data, "mutagenicity"),
            "reproductive_toxicity": self._extract_classification(hazard_data, "reproductive_toxicity"),
            "endocrine_disruption": self._extract_classification(hazard_data, "endocrine_disruption")
        }
        
        return toxicity
    
    def _extract_value(self, data: Dict, key: str) -> Optional[str]:
        """提取数值和单位"""
        if key in data and data[key]:
            value = data[key]
            if isinstance(value, dict):
                return f"{value.get('value', '')} {value.get('unit', '')}"
            return str(value)
        return None
    
    def _extract_classification(self, data: Dict, key: str) -> Optional[str]:
        """提取分类信息"""
        if key in data:
            return data[key].get('classification', 'Unknown')
        return None
    
    async def get_toxicity_by_cas(self, cas_number: str) -> Dict[str, Any]:
        """通过CAS号获取毒理学数据"""
        # 先查询本地数据库
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM toxicity_data WHERE cas_number = ?", (cas_number,))
        row = cursor.fetchone()
        
        if row:
            columns = [description[0] for description in cursor.description]
            return dict(zip(columns, row))
        
        # 如果本地没有，从API获取
        results = await self.search_chemical(cas_number)
        if results:
            # 获取第一个匹配结果的DTXSID
            dtxsid = results[0].get('dtxsid')
            if dtxsid:
                details = await self.get_chemical_details(dtxsid)
                if details:
                    toxicity = self.extract_toxicity_data(details)
                    
                    # 保存到本地数据库
                    self._save_toxicity_data(cas_number, dtxsid, results[0].get('name'), toxicity)
                    
                    return toxicity
        
        return {}
    
    def _save_toxicity_data(self, cas_number: str, dtxsid: str, 
                           chemical_name: str, toxicity_data: Dict[str, Any]):
        """保存毒理学数据到数据库"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        acute = toxicity_data.get("acute_toxicity", {})
        env = toxicity_data.get("environmental_toxicity", {})
        special = toxicity_data.get("special_endpoints", {})
        
        cursor.execute("""
            INSERT OR REPLACE INTO toxicity_data (
                cas_number, dtxsid, chemical_name,
                oral_ld50, dermal_ld50, inhalation_lc50,
                fish_lc50, daphnia_ec50, algae_ec50,
                carcinogenicity, mutagenicity, reproductive_toxicity, endocrine_disruption
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            cas_number, dtxsid, chemical_name,
            acute.get("oral_ld50"), acute.get("dermal_ld50"), acute.get("inhalation_lc50"),
            env.get("fish_lc50"), env.get("daphnia_ec50"), env.get("algae_ec50"),
            special.get("carcinogenicity"), special.get("mutagenicity"),
            special.get("reproductive_toxicity"), special.get("endocrine_disruption")
        ))
        
        conn.commit()
        conn.close()
    
    def get_hazard_summary(self, cas_number: str) -> Dict[str, Any]:
        """获取危害性总结"""
        toxicity_data = asyncio.run(self.get_toxicity_by_cas(cas_number))
        
        if not toxicity_data:
            return {"status": "No data available"}
        
        summary = {
            "cas_number": cas_number,
            "hazard_level": self._calculate_hazard_level(toxicity_data),
            "key_concerns": [],
            "ghs_classification": toxicity_data.get("ghs_classification"),
            "recommendations": []
        }
        
        # 分析关键关注点
        if toxicity_data.get("carcinogenicity") in ["Known", "Probable"]:
            summary["key_concerns"].append("潜在致癌物")
            summary["recommendations"].append("需要特殊防护措施")
        
        if toxicity_data.get("reproductive_toxicity") == "Yes":
            summary["key_concerns"].append("生殖毒性")
            summary["recommendations"].append("孕期避免接触")
        
        # 检查急性毒性
        oral_ld50 = toxicity_data.get("oral_ld50")
        if oral_ld50 and self._parse_ld50_value(oral_ld50) < 300:
            summary["key_concerns"].append("高急性毒性")
            summary["recommendations"].append("严格控制暴露")
        
        return summary
    
    def _calculate_hazard_level(self, toxicity_data: Dict[str, Any]) -> str:
        """计算总体危害等级"""
        score = 0
        
        # 根据不同毒性终点评分
        if toxicity_data.get("carcinogenicity") in ["Known", "Probable"]:
            score += 3
        
        if toxicity_data.get("mutagenicity") == "Positive":
            score += 2
        
        if toxicity_data.get("reproductive_toxicity") == "Yes":
            score += 2
        
        # 根据急性毒性评分
        oral_ld50 = toxicity_data.get("oral_ld50")
        if oral_ld50:
            ld50_value = self._parse_ld50_value(oral_ld50)
            if ld50_value < 50:
                score += 3
            elif ld50_value < 300:
                score += 2
            elif ld50_value < 2000:
                score += 1
        
        # 返回危害等级
        if score >= 5:
            return "高"
        elif score >= 3:
            return "中"
        else:
            return "低"
    
    def _parse_ld50_value(self, ld50_str: str) -> float:
        """解析LD50字符串，返回数值"""
        try:
            # 简单解析，实际需要更复杂的逻辑
            import re
            match = re.search(r'(\d+\.?\d*)', ld50_str)
            if match:
                return float(match.group(1))
        except:
            pass
        return 999999  # 返回大值表示低毒性


# 创建全局实例
comptox_service = CompToxService() 