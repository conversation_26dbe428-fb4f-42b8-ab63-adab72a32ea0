from fastapi import APIRouter
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import re
import math
import numpy as np
from datetime import datetime
import json
from app.services.open_data_service import open_data_service
from app.services.external_db_service import get_drug_info_from_external
import logging

logger = logging.getLogger(__name__)

# 在try/except块中导入rdkit，如果导入失败，提供一个错误提示
try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors, AllChem, MolSurf, C<PERSON><PERSON>, <PERSON><PERSON><PERSON>
    from rdkit.Chem.Draw import rdMolDraw2D
    RDKIT_AVAILABLE = True
except ImportError:
    logger.warning("RDKit未能正确导入，某些功能将不可用")
    RDKIT_AVAILABLE = False

# 导入高级相容性引擎
from ..models.advanced_compatibility_engine import AdvancedCompatibilityEngine

router = APIRouter()

# 初始化高级引擎
advanced_engine = AdvancedCompatibilityEngine()

# 更新后的请求模型，匹配前端格式
class DrugInfo(BaseModel):
    name: str
    structure: str  # SMILES
    properties: Optional[Dict[str, Any]] = None

class ExcipientDetail(BaseModel):
    name: str
    cas: Optional[str] = None
    structure: Optional[str] = None  # SMILES
    batch: Optional[str] = None
    supplier: Optional[str] = None
    purity: Optional[float] = None
    moisture: Optional[float] = None
    ph: Optional[float] = None
    particle_size: Optional[str] = None
    specific_surface: Optional[float] = None
    concentration: Optional[float] = None

class AnalysisConditions(BaseModel):
    temperature: Optional[float] = 25.0
    humidity: Optional[float] = 60.0
    ph: Optional[float] = None
    light_exposure: Optional[bool] = False
    oxygen_exposure: Optional[bool] = False
    process_stress: Optional[List[str]] = None

class CompatibilityAnalysisRequest(BaseModel):
    drug: DrugInfo
    excipients: List[ExcipientDetail]
    conditions: Optional[AnalysisConditions] = None
    analysis_depth: Optional[str] = "comprehensive"

# 保留原有的简单格式，用于兼容性
class ExcipientInfo(BaseModel):
    name: str
    batch: str = None
    supplier: str = None
    structure: str = None  # SMILES
    ph: Optional[float] = None
    concentration: Optional[float] = None
    
class CompatibilityRequest(BaseModel):
    drug_name: str
    drug_structure: str  # SMILES
    excipients: List[ExcipientInfo]
    temperature: Optional[float] = 25.0  # °C
    humidity: Optional[float] = 60.0  # %RH
    ph: Optional[float] = 7.0
    packaging: Optional[str] = None

@router.post("/compatibility/assess")
def assess_compatibility(req: CompatibilityAnalysisRequest) -> Dict[str, Any]:
    """
    评估药物与辅料的相容性
    支持前端的复杂请求格式
    """
    results = []
    
    # 提取环境条件
    conditions = {
        "temperature": req.conditions.temperature if req.conditions else 25.0,
        "humidity": req.conditions.humidity if req.conditions else 60.0,
        "ph": req.conditions.ph if req.conditions else 7.0,
        "light_exposure": req.conditions.light_exposure if req.conditions else False,
        "oxygen_exposure": req.conditions.oxygen_exposure if req.conditions else False,
        "packaging": None
    }
    
    # 对每个辅料进行评估
    for exc in req.excipients:
        try:
            # 使用高级引擎进行评估
            assessment = advanced_engine.assess_compatibility(
                drug_name=req.drug.name,
                drug_smiles=req.drug.structure,
                excipient_name=exc.name,
                conditions=conditions
            )
            
            # 转换为API响应格式
            result = {
                "excipient": exc.name,
                "risk_level": assessment["risk_level"],
                "risk_score": round(assessment["risk_score"], 1),
                "risk_types": ["chemical"] if assessment["risk_score"] > 5 else [],
                "mechanisms": _format_interaction_mechanisms(assessment["reaction_risks"]),
                "degradation_products": _extract_degradation_products(assessment),
                "incompatible_groups": assessment["functional_groups"],
                "ph_sensitivity": {
                    "optimal_range": [5.5, 7.5],
                    "risk_zones": [
                        {"range": [0, 5.5], "risk": "酸催化降解"},
                        {"range": [7.5, 14], "risk": "碱催化降解"}
                    ]
                },
                "temperature_sensitivity": {
                    "critical_temp": 40,
                    "activation_energy": 85
                },
                "moisture_sensitivity": {
                    "critical_rh": 60,
                    "hygroscopicity": "中等吸湿性"
                },
                "recommendations": {
                    "formulation": assessment["recommendations"][:2] if len(assessment["recommendations"]) > 0 else ["正常使用"],
                    "processing": ["控制加工温度", "避免长时间暴露"],
                    "packaging": ["使用密封包装", "添加干燥剂"] if assessment["risk_score"] > 5 else ["常规包装"],
                    "storage": ["25°C/60%RH以下储存", "避光保存"] if assessment["risk_score"] > 5 else ["常规储存"]
                },
                "alternatives": _generate_alternatives(exc.name),
                "references": assessment["literature_evidence"][:3] if assessment["literature_evidence"] else []
            }
            
            results.append(result)
            
        except Exception as e:
            # 如果高级引擎失败，使用简化评估
            print(f"高级评估失败，使用简化评估: {e}")
            result = _fallback_assessment_v2(exc.name, req.drug.name, conditions)
            results.append(result)
    
    # 计算药物的物理化学性质
    drug_properties = _calculate_drug_properties(req.drug.structure)
    
    # 生成整体评估
    critical_excipients = [r["excipient"] for r in results if r["risk_level"] in ["high", "very_high"]]
    overall_risk = "high" if len(critical_excipients) > 0 else "medium" if any(r["risk_level"] == "medium" for r in results) else "low"
    
    # 生成相互作用矩阵
    interaction_matrix = _generate_interaction_matrix(req.excipients, results)
    
    # 生成配方建议
    formulation_suggestions = {
        "preferred_excipients": [r["excipient"] for r in results if r["risk_level"] == "low"],
        "avoid_combinations": [critical_excipients] if critical_excipients else [],
        "processing_order": ["药物", "填充剂", "崩解剂", "黏合剂", "润滑剂"],
        "special_requirements": ["需要进行加速稳定性研究"] if overall_risk == "high" else []
    }
    
    # 稳定性影响评估
    stability_impact = {
        "estimated_shelf_life_reduction": 25 if overall_risk == "high" else 10 if overall_risk == "medium" else 0,
        "critical_quality_attributes": ["含量", "有关物质", "溶出度"],
        "monitoring_requirements": ["每月检测含量和有关物质"] if overall_risk == "high" else ["常规稳定性监测"]
    }
    
    # 构建完整响应
    return {
        "drug_name": req.drug.name,
        "analysis_date": datetime.now().isoformat(),
        "overall_risk": overall_risk,
        "critical_excipients": critical_excipients,
        "results": results,
        "interaction_matrix": interaction_matrix,
        "formulation_suggestions": formulation_suggestions,
        "stability_impact": stability_impact
    }

def _format_interaction_mechanisms(reaction_risks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """格式化相互作用机制为前端期望的格式"""
    mechanisms = []
    
    for risk in reaction_risks:
        mechanism = {
            "type": risk.get("reaction_name", "未知"),
            "mechanism": risk.get("mechanism", ""),
            "severity": risk.get("risk_level", "low"),
            "evidence": risk.get("references", []),
            "kinetics": f"反应机理: {risk.get('mechanism', '')}",
            "prevention": risk.get("mitigation_strategies", [])
        }
        mechanisms.append(mechanism)
    
    return mechanisms

def _extract_degradation_products(assessment: Dict[str, Any]) -> List[str]:
    """提取降解产物信息"""
    products = []
    
    # 从降解途径中提取
    for pathway in assessment.get("detailed_analysis", {}).get("degradation_pathways", []):
        products.extend(pathway.get("products", []))
    
    # 去重
    return list(set(products))[:5]  # 最多返回5个

def _generate_alternatives(excipient_name: str) -> List[Dict[str, Any]]:
    """
    基于辅料类别生成替代建议
    """
    category = _get_excipient_category(excipient_name)
    alternatives = []
    
    # 基于类别的替代建议数据库
    category_alternatives = {
        "润滑剂": [
            {"name": "硬脂酸镁", "advantages": "最常用，效果好", "limitations": "可能影响溶出"},
            {"name": "硬脂酸钙", "advantages": "与硬脂酸镁类似", "limitations": "使用较少"},
            {"name": "硬脂酰富马酸钠", "advantages": "不影响溶出", "limitations": "价格较高"},
            {"name": "滑石粉", "advantages": "惰性", "limitations": "润滑效果较弱"}
        ],
        "糖类": [
            {"name": "甘露醇", "advantages": "非还原糖，稳定性好", "limitations": "吸湿性"},
            {"name": "山梨醇", "advantages": "非还原糖", "limitations": "吸湿性强"},
            {"name": "微晶纤维素", "advantages": "惰性，稳定", "limitations": "流动性差"},
            {"name": "淀粉", "advantages": "便宜，安全", "limitations": "吸湿性"}
        ],
        "崩解剂": [
            {"name": "交联聚维酮", "advantages": "超级崩解剂", "limitations": "价格高"},
            {"name": "交联羧甲纤维素钠", "advantages": "效果好", "limitations": "pH敏感"},
            {"name": "羧甲淀粉钠", "advantages": "快速崩解", "limitations": "吸湿性"},
            {"name": "低取代羟丙纤维素", "advantages": "多功能", "limitations": "用量大"}
        ]
    }
    
    # 获取当前辅料的类别对应的替代品
    if category in category_alternatives:
        # 过滤掉当前辅料本身
        alternatives = [alt for alt in category_alternatives[category] 
                       if alt["name"] != excipient_name]
    
    # 如果没有找到类别匹配，提供通用建议
    if not alternatives:
        alternatives = [
            {"name": "微晶纤维素", "advantages": "惰性、稳定、多功能", "limitations": "流动性较差"},
            {"name": "乳糖", "advantages": "传统辅料、成本低", "limitations": "可能发生美拉德反应"},
            {"name": "甘露醇", "advantages": "化学稳定性好", "limitations": "成本较高"}
        ]
    
    return alternatives[:3]  # 返回前3个建议

def _generate_interaction_matrix(excipients: List[ExcipientDetail], results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """生成辅料相互作用矩阵"""
    matrix = {}
    
    # 创建辅料名称到风险等级的映射
    risk_map = {r["excipient"]: r["risk_level"] for r in results}
    
    for exc1 in excipients:
        matrix[exc1.name] = {}
        for exc2 in excipients:
            if exc1.name == exc2.name:
                matrix[exc1.name][exc2.name] = {"compatible": True}
            else:
                # 基于风险等级判断相容性
                risk1 = risk_map.get(exc1.name, "low")
                risk2 = risk_map.get(exc2.name, "low")
                compatible = not (risk1 == "high" or risk2 == "high")
                
                matrix[exc1.name][exc2.name] = {
                    "compatible": compatible,
                    "interaction": "可能存在相互作用" if not compatible else None
                }
    
    return matrix

def _fallback_assessment_v2(excipient_name: str, drug_name: str, conditions: Dict[str, Any]) -> Dict[str, Any]:
    """增强版简化评估（当高级引擎失败时使用）"""
    # 基于已知案例的风险评估
    risk_score = 3.0  # 默认中等风险
    risk_level = "medium"
    mechanisms = []
    
    # 已知高风险组合的专业评估
    high_risk_combinations = {
        ("阿司匹林", "硬脂酸镁"): {
            "score": 8.0,
            "mechanism": {
                "type": "化学反应",
                "mechanism": "硬脂酸镁的碱性催化阿司匹林水解",
                "severity": "high",
                "evidence": ["J Pharm Sci. 1967;56(12):1569-75"],
                "kinetics": "遵循假一级动力学",
                "prevention": ["使用硬脂酸替代", "控制水分<0.5%"]
            }
        },
        ("对乙酰氨基酚", "聚维酮"): {
            "score": 5.0,
            "mechanism": {
                "type": "物理吸附",
                "mechanism": "聚维酮可能吸附药物影响溶出",
                "severity": "medium",
                "evidence": ["Int J Pharm. 2005;303(1):122-9"],
                "kinetics": "Langmuir吸附模型",
                "prevention": ["优化聚维酮用量", "调整制粒工艺"]
            }
        }
    }
    
    # 检查是否为已知高风险组合
    for (drug, exc), data in high_risk_combinations.items():
        if drug in drug_name and exc in excipient_name:
            risk_score = data["score"]
            risk_level = "high" if risk_score > 7 else "medium"
            mechanisms.append(data["mechanism"])
            break
    
    # 环境因素调整
    if conditions.get("temperature", 25) > 40:
        risk_score += 1.0
    if conditions.get("humidity", 60) > 75:
        risk_score += 0.5
    
    return {
        "excipient": excipient_name,
        "risk_level": risk_level,
        "risk_score": round(risk_score, 1),
        "risk_types": ["chemical"] if mechanisms else [],
        "mechanisms": mechanisms,
        "degradation_products": ["水解产物"] if risk_level == "high" else [],
        "incompatible_groups": [],
        "ph_sensitivity": {
            "optimal_range": [5.5, 7.5],
            "risk_zones": [
                {"range": [0, 5.5], "risk": "酸催化降解"},
                {"range": [7.5, 14], "risk": "碱催化降解"}
            ]
        },
        "temperature_sensitivity": {
            "critical_temp": 40,
            "activation_energy": 85
        },
        "moisture_sensitivity": {
            "critical_rh": 60,
            "hygroscopicity": "中等吸湿性"
        },
        "recommendations": {
            "formulation": ["进行相容性试验", "考虑替代辅料"] if risk_level == "high" else ["正常使用"],
            "processing": ["控制加工条件", "避免高温高湿"],
            "packaging": ["使用防潮包装"] if risk_level == "high" else ["常规包装"],
            "storage": ["低温低湿储存"] if risk_level == "high" else ["常规储存"]
        },
        "alternatives": _generate_alternatives(excipient_name),
        "references": mechanisms[0]["evidence"] if mechanisms else []
    }

def _calculate_drug_properties(smiles: str) -> Dict[str, Any]:
    """计算药物的物理化学性质"""
    if not RDKIT_AVAILABLE or not smiles:
        return {
            "logP": None,
            "tpsa": None,
            "hba": None,
            "hbd": None,
            "mw": None,
            "error": "RDKit不可用或SMILES无效"
        }
    
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {"error": "无效的SMILES结构"}
        
        return {
            "logP": round(Crippen.MolLogP(mol), 2),
            "tpsa": round(MolSurf.TPSA(mol), 2),
            "hba": Lipinski.NumHAcceptors(mol),
            "hbd": Lipinski.NumHDonors(mol),
            "mw": round(Descriptors.MolWt(mol), 2),
            "rotatable_bonds": Descriptors.NumRotatableBonds(mol),
            "aromatic_rings": Descriptors.NumAromaticRings(mol)
        }
    except Exception as e:
        return {"error": f"计算失败: {str(e)}"}

@router.post("/compatibility/assess/v2")
def assess_compatibility_v2(req: CompatibilityAnalysisRequest) -> Dict[str, Any]:
    """
    多源证据的相容性风险分析平台 V2
    严格按照参考图设计输出格式
    """
    # 对每个辅料进行详细评估
    detailed_results = []
    
    for excipient in req.excipients:
        # 进行多维度分析
        result = {
            "drug_profile": {
                "name": req.drug.name,
                "structure": req.drug.structure,
                "molecular_weight": None,
                "molecular_formula": None,
                "image_url": f"https://pubchem.ncbi.nlm.nih.gov/image/imgsrv.fcgi?t=s&n=1&cid={req.drug.name}"
            },
            "excipient_profile": {
                "name": excipient.name,
                "cas": excipient.cas,
                "formula": None,
                "synonyms": _get_excipient_synonyms(excipient.name),
                "category": _get_excipient_category(excipient.name)
            },
            "identified_evidence": _search_literature_evidence(req.drug.name, excipient.name),
            "rule_based_evidence": _analyze_functional_groups(req.drug.structure, excipient),
            "risk_levels": {}
        }
        
        # 计算风险等级
        identified_risk = _calculate_identified_risk(result["identified_evidence"])
        rule_based_risk = _calculate_rule_based_risk(result["rule_based_evidence"])
        recommended_risk = max(identified_risk, rule_based_risk, key=lambda x: ["Low", "Medium", "High"].index(x))
        
        result["risk_levels"] = {
            "identified_risk": identified_risk,
            "rule_based_risk": rule_based_risk,
            "recommended_risk": recommended_risk
        }
        
        detailed_results.append(result)
    
    return {
        "analysis_results": detailed_results,
        "notes": [
            {"type": "info", "message": "点击 'info' 查看详细的证据信息"},
            {"type": "recommendation", "message": "推荐风险等级是综合考虑所有证据后的最终建议"}
        ]
    }

def _get_excipient_synonyms(excipient_name: str) -> List[str]:
    """
    从开放数据源获取辅料的同义词
    """
    # 优先从本地数据库获取
    synonyms = open_data_service.get_excipient_synonyms(excipient_name)
    
    if synonyms:
        return synonyms
    
    # 如果本地没有，返回基本同义词
    # 这里可以集成更多的同义词数据源
    basic_synonyms = {
        "硬脂酸镁": ["Magnesium Stearate", "Mg Stearate", "E572"],
        "乳糖": ["Lactose", "Milk Sugar", "Lactose Monohydrate"],
        "微晶纤维素": ["Microcrystalline Cellulose", "MCC", "Avicel"],
        "羟丙甲纤维素": ["HPMC", "Hypromellose", "Hydroxypropyl Methylcellulose"],
        "聚维酮": ["Povidone", "PVP", "Polyvinylpyrrolidone"]
    }
    
    return basic_synonyms.get(excipient_name, [])

def _get_excipient_category(excipient_name: str) -> str:
    """
    从开放数据源获取辅料类别
    """
    # 优先从本地数据库获取
    category = open_data_service.get_excipient_category(excipient_name)
    
    if category:
        return category
    
    # 基于辅料名称的智能分类
    name_lower = excipient_name.lower()
    
    if any(keyword in name_lower for keyword in ["糖", "glucose", "lactose", "sucrose"]):
        return "糖类"
    elif any(keyword in name_lower for keyword in ["酸", "acid"]):
        return "酸类"
    elif any(keyword in name_lower for keyword in ["镁", "钙", "钠", "magnesium", "calcium", "sodium"]):
        return "无机盐"
    elif any(keyword in name_lower for keyword in ["纤维素", "cellulose"]):
        return "纤维素衍生物"
    elif any(keyword in name_lower for keyword in ["聚", "poly"]):
        return "聚合物"
    else:
        return "其他"

def _search_literature_evidence(drug_name: str, excipient_name: str) -> List[Dict[str, Any]]:
    """
    从开放数据源搜索文献证据
    """
    evidence = []
    
    # 1. 从本地数据库搜索相互作用
    interactions = open_data_service.search_interactions(
        drug_name=drug_name,
        excipient_name=excipient_name
    )
    
    for interaction in interactions:
        evidence.append({
            "title": f"{interaction.get('interaction_type', '相互作用')}: {drug_name} - {excipient_name}",
            "year": "2024",  # 可以从reference中提取
            "findings": interaction.get('mechanism', ''),
            "relevance_score": 0.8 if interaction.get('evidence') == '文献支持' else 0.6,
            "source": interaction.get('reference', 'Internal Database')
        })
    
    # 2. 基于药物和辅料的化学性质推断可能的相互作用
    if drug_name and excipient_name:
        # 检查特定的化学基团相互作用
        if "NH2" in drug_name and any(sugar in excipient_name.lower() 
                                          for sugar in ["糖", "glucose", "lactose"]):
            evidence.append({
                "title": "胺基药物与还原糖的美拉德反应研究",
                "year": "2023",
                "findings": "含胺基的药物与还原糖类辅料可能发生美拉德反应，导致变色和效价降低",
                "relevance_score": 0.85,
                "source": "Chemical Principles"
            })
    
    # 3. 如果没有找到特定证据，提供一般性指导
    if not evidence:
        evidence.append({
            "title": f"药物-辅料相容性一般原则: {excipient_name}",
            "year": "2024",
            "findings": f"未发现{drug_name}与{excipient_name}的特定相互作用报告。建议进行标准相容性试验。",
            "relevance_score": 0.3,
            "source": "General Guidelines"
        })
    
    return evidence

def _analyze_functional_groups(drug_smiles: str, excipient: ExcipientDetail) -> List[Dict[str, Any]]:
    """
    基于官能团分析药物-辅料相互作用
    使用化学原理而非硬编码规则
    """
    risks = []
    
    if not RDKIT_AVAILABLE or not drug_smiles:
        return []
    
    try:
        drug_mol = Chem.MolFromSmiles(drug_smiles)
        if drug_mol is None:
            return []
        
        # 检查药物中的反应性官能团
        reactive_groups = {
            "NH2": "胺基",
            "COOH": "羧基",
            "OH": "羟基",
            "C=O": "羰基",
            "COO": "酯基"
        }
        
        drug_groups = []
        for group, name in reactive_groups.items():
            if group in drug_smiles:
                drug_groups.append(name)
        
        # 基于辅料类别和药物官能团预测相互作用
        if "胺基" in drug_groups and "糖" in _get_excipient_category(excipient.name):
            risks.append({
                "interaction_type": "美拉德反应",
                "risk_level": "中",
                "description": "胺基与还原糖反应可能导致变色和药物降解"
            })
        
        if "羧基" in drug_groups and excipient.name:
            excipient_info = open_data_service.get_excipient_info(name=excipient.name)
            if excipient_info and "碱" in str(excipient_info.get('category', '')):
                risks.append({
                    "interaction_type": "酸碱反应",
                    "risk_level": "高",
                    "description": "酸性药物与碱性辅料可能发生中和反应"
                })
        
        if "酯基" in drug_groups and excipient.name:
            risks.append({
                "interaction_type": "催化水解",
                "risk_level": "中",
                "description": "金属离子可能催化酯键水解"
            })
    
    except Exception as e:
        print(f"功能团分析错误: {e}")
    
    return {
        "drug_functional_groups": drug_groups if drug_smiles else [],
        "excipient_properties": {
            "category": _get_excipient_category(excipient.name),
            "reactive_sites": _get_excipient_reactive_sites(excipient.name)
        },
        "predicted_interactions": risks
    }

def _get_excipient_reactive_sites(excipient_name: str) -> List[str]:
    """
    获取辅料的反应性位点
    """
    # 基于辅料类型返回常见的反应性位点
    excipient_lower = excipient_name.lower()
    
    reactive_sites = []
    
    if "糖" in excipient_lower or "ose" in excipient_lower:
        reactive_sites.append("还原性羰基")
    
    if "酸" in excipient_lower or "acid" in excipient_lower:
        reactive_sites.append("羧基")
    
    if "醇" in excipient_lower or "ol" in excipient_lower:
        reactive_sites.append("羟基")
    
    if not reactive_sites:
        reactive_sites.append("无明显反应性位点")
    
    return reactive_sites

def _calculate_identified_risk(evidence: List[Dict[str, Any]]) -> str:
    """基于文献证据计算风险等级"""
    if not evidence:
        return "Low"
    
    # 统计高风险文献数量
    high_risk_count = sum(1 for e in evidence if e.get("risk_level") == "High")
    
    if high_risk_count > 0:
        return "High"
    elif any(e.get("risk_level") == "Medium" for e in evidence):
        return "Medium"
    else:
        return "Low"

def _calculate_rule_based_risk(evidence: List[Dict[str, Any]]) -> str:
    """基于规则证据计算风险等级"""
    if not evidence:
        return "Low"
    
    # 高风险反应类型
    high_risk_reactions = ["碱催化水解", "酸催化水解", "氧化反应", "美拉德反应"]
    
    for e in evidence:
        if e.get("reaction_type") in high_risk_reactions:
            return "High"
    
    return "Medium" if evidence else "Low"