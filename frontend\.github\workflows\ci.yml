name: Frontend CI

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
        working-directory: ./frontend
      - name: Lint
        run: npm run lint
        working-directory: ./frontend
      - name: Run tests
        run: npm test -- --watchAll=false
        working-directory: ./frontend
      - name: Build
        run: npm run build
        working-directory: ./frontend 