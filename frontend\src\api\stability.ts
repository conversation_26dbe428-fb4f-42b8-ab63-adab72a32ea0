import axios from 'axios';
import { message } from 'antd';
import { API_BASE_URL } from '../config';

/**
 * 高级稳定性预测API接口
 * 基于药物制剂科学原理的专业稳定性预测
 */

// 定义标准的稳定性数据点接口
export interface StabilityDataPoint {
  time_point: number;    // 时间点（月）
  temperature: number;   // 温度（°C）
  humidity: number;      // 相对湿度（%RH）
  value: number;        // 测量值（%）
  item: string;         // 检测项目（含量/杂质A/水分等）
  batch?: string;       // 批号
  package?: string;     // 包装类型
}

// 稳定性预测请求接口
export interface StabilityPredictionRequest {
  drug_name: string;
  drug_structure?: string;  // SMILES结构
  excipients: string[];     // 辅料列表
  process?: string;         // 生产工艺
  packaging?: string;       // 包装材料
  environment?: string;     // 环境条件
  history_data: StabilityDataPoint[];  // 历史稳定性数据
  model_selection?: 'auto' | 'zero-order' | 'first-order' | 'second-order' | 'ml-linear' | 'ml-rf' | 'ml-nn';
  confidence_level?: number;  // 置信水平(0-1)
  prediction_months?: number; // 预测时长（月）
}

// ICH条件下的预测结果
export interface ICHPrediction {
  t90: number;              // T90（月）
  ci: [number, number];     // 置信区间
  shelf_life: number;       // 建议货架期（月）
  degradation_rate: number; // 降解速率常数
}

// 降解途径分析
export interface DegradationPathway {
  pathway: string;          // 降解途径名称
  probability: number;      // 发生概率
  mechanism: string;        // 反应机理
  prevention: string[];     // 预防措施
  impact_factors: string[]; // 影响因素
}

// 稳定性预测响应接口
export interface StabilityPredictionResponse {
  drug_name: string;
  prediction: {
    long_term: ICHPrediction;      // 长期条件 (25°C/60%RH)
    intermediate: ICHPrediction;   // 中间条件 (30°C/65%RH)
    accelerated: ICHPrediction;    // 加速条件 (40°C/75%RH)
  };
  model_info: {
    type: string;                  // 使用的模型类型
    r_squared: number;             // 拟合优度
    activation_energy: number;     // 活化能 (kJ/mol)
    plot?: string;                 // Base64编码的拟合图
  };
  predicted_values: Array<{
    time: number;
    value: number;
    lower_ci: number;
    upper_ci: number;
  }>;
  degradation_pathways?: DegradationPathway[];
  sensitivity: Array<{
    factor: string;
    impact: number;
  }>;
  regulatory_check: string;
  recommendations?: string[];
  explainability_data?: {
    prediction_explanation?: {
      feature_importance: Array<{
        feature: string;
        value: number;
        shap_value: number;
        impact: 'positive' | 'negative';
      }>;
      interpretation?: string;
    };
    global_importance?: {
      global_feature_importance: Array<{
        rank: number;
        feature: string;
        importance: number;
      }>;
      sample_size?: number;
      insights?: string[];
    };
    waterfall_plot?: string;
    summary_plot?: string;
    error?: string;
  };
}

// API实例配置
const stabilityAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60000, // 稳定性预测可能需要较长时间
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
stabilityAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
stabilityAPI.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const errorMessage = error.response?.data?.detail || error.message || '稳定性预测失败';
    console.error('稳定性预测API错误:', error);
    message.error(errorMessage);
    return Promise.reject(error);
  }
);

/**
 * 执行稳定性预测
 * @param request 预测请求参数
 * @returns 预测结果
 */
export async function predictStability(
  request: StabilityPredictionRequest
): Promise<StabilityPredictionResponse> {
  try {
    console.log('发送稳定性预测请求:', request);
    // 使用新的端点路径
    const response = await stabilityAPI.post<StabilityPredictionResponse>(
      '/stability/predict',  // 使用正确的端点
      request
    );
    console.log('稳定性预测响应:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('稳定性预测失败:', error);
    
    throw error;
  }
}

/**
 * 获取稳定性模型列表
 */
export async function getStabilityModels(): Promise<{ models: Array<{ value: string; name: string; description: string }> }> {
  try {
    const response = await stabilityAPI.get<{ models: Array<{ value: string; name: string; description: string }> }>('/stability/models');
    return response.data;
  } catch (error) {
    console.error('获取模型列表失败:', error);
    // 返回默认模型列表
    return {
      models: [
        { value: 'auto', name: '自动选择', description: '系统自动选择最佳模型' },
        { value: 'zero-order', name: '零级动力学', description: '适用于固体制剂' },
        { value: 'first-order', name: '一级动力学', description: '最常见的降解模型' },
        { value: 'second-order', name: '二级动力学', description: '适用于双分子反应' },
      ]
    };
  }
}

/**
 * 计算平均降解速率
 */
function calculateDegradationRate(data: StabilityDataPoint[]): number {
  if (data.length < 2) return 0.1; // 默认值
  
  // 只分析含量数据
  const assayData = data.filter(d => d.item === '含量').sort((a, b) => a.time_point - b.time_point);
  if (assayData.length < 2) return 0.1;
  
  // 计算平均降解速率
  const firstPoint = assayData[0];
  const lastPoint = assayData[assayData.length - 1];
  const degradation = firstPoint.value - lastPoint.value;
  const timeDiff = lastPoint.time_point - firstPoint.time_point;
  
  return timeDiff > 0 ? degradation / timeDiff : 0.1;
}

/**
 * 验证稳定性数据
 */
export function validateStabilityData(data: StabilityDataPoint[]): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!data || data.length === 0) {
    errors.push('没有提供稳定性数据');
  }
  
  // 检查数据完整性
  data.forEach((point, index) => {
    if (point.time_point < 0) {
      errors.push(`第${index + 1}个数据点的时间值无效`);
    }
    if (point.value < 0 || point.value > 120) {
      errors.push(`第${index + 1}个数据点的测量值超出合理范围`);
    }
    if (point.temperature < -50 || point.temperature > 100) {
      errors.push(`第${index + 1}个数据点的温度值不合理`);
    }
  });
  
  // 检查是否有足够的时间点
  const uniqueTimePoints = new Set(data.map(d => d.time_point));
  if (uniqueTimePoints.size < 3) {
    errors.push('至少需要3个不同的时间点进行可靠预测');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
} 