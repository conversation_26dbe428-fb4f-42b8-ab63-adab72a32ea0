"""
安全配置和中间件
包含CORS、限流、认证等安全措施
"""

import os
import secrets
import hashlib
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from functools import wraps

logger = logging.getLogger(__name__)

class SecurityConfig:
    """安全配置类"""
    
    def __init__(self):
        # JWT配置
        self.jwt_secret_key = os.getenv("JWT_SECRET_KEY", self._generate_secret_key())
        self.jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        self.jwt_expire_minutes = int(os.getenv("JWT_EXPIRE_MINUTES", "1440"))  # 24小时
        
        # CORS配置
        self.cors_origins = self._parse_cors_origins()
        self.cors_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.cors_headers = ["*"]
        
        # 限流配置
        self.rate_limit_per_minute = int(os.getenv("RATE_LIMIT_PER_MINUTE", "100"))
        self.rate_limit_burst = int(os.getenv("RATE_LIMIT_BURST", "20"))
        
        # 文件上传限制
        self.max_upload_size = int(os.getenv("MAX_UPLOAD_SIZE", "50")) * 1024 * 1024  # MB转字节
        self.allowed_extensions = {'.json', '.csv', '.xlsx', '.txt', '.pdf'}
        
        # 安全头配置
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        # 密码策略
        self.password_min_length = 8
        self.password_require_uppercase = True
        self.password_require_lowercase = True
        self.password_require_numbers = True
        self.password_require_special = True
        
        # 会话配置
        self.session_timeout = int(os.getenv("SESSION_TIMEOUT", "3600"))  # 1小时
        self.max_login_attempts = int(os.getenv("MAX_LOGIN_ATTEMPTS", "5"))
        self.lockout_duration = int(os.getenv("LOCKOUT_DURATION", "900"))  # 15分钟
    
    def _generate_secret_key(self) -> str:
        """生成安全的密钥"""
        return secrets.token_urlsafe(32)
    
    def _parse_cors_origins(self) -> List[str]:
        """解析CORS允许的源"""
        origins_str = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000")
        return [origin.strip() for origin in origins_str.split(",") if origin.strip()]
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """验证密码强度"""
        errors = []
        
        if len(password) < self.password_min_length:
            errors.append(f"密码长度至少{self.password_min_length}位")
        
        if self.password_require_uppercase and not any(c.isupper() for c in password):
            errors.append("密码必须包含大写字母")
        
        if self.password_require_lowercase and not any(c.islower() for c in password):
            errors.append("密码必须包含小写字母")
        
        if self.password_require_numbers and not any(c.isdigit() for c in password):
            errors.append("密码必须包含数字")
        
        if self.password_require_special and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("密码必须包含特殊字符")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "strength": self._calculate_password_strength(password)
        }
    
    def _calculate_password_strength(self, password: str) -> str:
        """计算密码强度"""
        score = 0
        
        # 长度评分
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1
        if len(password) >= 16:
            score += 1
        
        # 字符类型评分
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        
        if score <= 3:
            return "弱"
        elif score <= 5:
            return "中等"
        else:
            return "强"
    
    def hash_password(self, password: str) -> str:
        """哈希密码"""
        import bcrypt
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        import bcrypt
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def generate_api_key(self) -> str:
        """生成API密钥"""
        return f"ds_{secrets.token_urlsafe(32)}"
    
    def validate_file_upload(self, filename: str, file_size: int) -> Dict[str, Any]:
        """验证文件上传"""
        errors = []
        
        # 检查文件扩展名
        if not any(filename.lower().endswith(ext) for ext in self.allowed_extensions):
            errors.append(f"不支持的文件类型，允许的类型：{', '.join(self.allowed_extensions)}")
        
        # 检查文件大小
        if file_size > self.max_upload_size:
            max_size_mb = self.max_upload_size / (1024 * 1024)
            errors.append(f"文件大小超过限制（最大{max_size_mb}MB）")
        
        # 检查文件名安全性
        if any(char in filename for char in ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*']):
            errors.append("文件名包含非法字符")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }

# 全局安全配置实例
security_config = SecurityConfig()

# 限流装饰器
class RateLimiter:
    """简单的内存限流器"""
    
    def __init__(self):
        self.requests = {}
        self.blocked_ips = {}
    
    def is_allowed(self, client_ip: str, limit_per_minute: int = None) -> bool:
        """检查是否允许请求"""
        limit = limit_per_minute or security_config.rate_limit_per_minute
        now = datetime.now()
        
        # 检查是否被封禁
        if client_ip in self.blocked_ips:
            if now < self.blocked_ips[client_ip]:
                return False
            else:
                del self.blocked_ips[client_ip]
        
        # 清理过期记录
        minute_ago = now - timedelta(minutes=1)
        if client_ip in self.requests:
            self.requests[client_ip] = [
                req_time for req_time in self.requests[client_ip] 
                if req_time > minute_ago
            ]
        
        # 检查请求频率
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        if len(self.requests[client_ip]) >= limit:
            # 封禁IP
            self.blocked_ips[client_ip] = now + timedelta(minutes=15)
            logger.warning(f"IP {client_ip} blocked due to rate limiting")
            return False
        
        self.requests[client_ip].append(now)
        return True

# 全局限流器实例
rate_limiter = RateLimiter()

def require_rate_limit(limit_per_minute: int = None):
    """限流装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 这里需要从请求中获取客户端IP
            # 在实际使用中需要结合FastAPI的Request对象
            client_ip = "127.0.0.1"  # 占位符
            
            if not rate_limiter.is_allowed(client_ip, limit_per_minute):
                from fastapi import HTTPException
                raise HTTPException(status_code=429, detail="请求过于频繁，请稍后再试")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# 输入验证函数
def sanitize_input(input_str: str) -> str:
    """清理输入字符串"""
    if not isinstance(input_str, str):
        return str(input_str)
    
    # 移除潜在的恶意字符
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
    for char in dangerous_chars:
        input_str = input_str.replace(char, '')
    
    return input_str.strip()

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def generate_csrf_token() -> str:
    """生成CSRF令牌"""
    return secrets.token_urlsafe(32)

def verify_csrf_token(token: str, expected_token: str) -> bool:
    """验证CSRF令牌"""
    return secrets.compare_digest(token, expected_token)

# 安全日志记录
class SecurityLogger:
    """安全事件日志记录器"""
    
    @staticmethod
    def log_login_attempt(username: str, success: bool, ip_address: str):
        """记录登录尝试"""
        status = "成功" if success else "失败"
        logger.info(f"登录尝试 - 用户: {username}, 状态: {status}, IP: {ip_address}")
    
    @staticmethod
    def log_api_access(endpoint: str, method: str, ip_address: str, user_id: str = None):
        """记录API访问"""
        logger.info(f"API访问 - 端点: {endpoint}, 方法: {method}, IP: {ip_address}, 用户: {user_id}")
    
    @staticmethod
    def log_security_event(event_type: str, details: str, ip_address: str = None):
        """记录安全事件"""
        logger.warning(f"安全事件 - 类型: {event_type}, 详情: {details}, IP: {ip_address}")

# 全局安全日志记录器
security_logger = SecurityLogger()
