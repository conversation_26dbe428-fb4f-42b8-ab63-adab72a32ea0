import React, { useState, useEffect, useContext } from 'react';
import { Card, Form, Input, Select, Button, Divider, Spin, Table, Row, Col, InputNumber, Alert, Typography, Radio, Tabs, Tag, Switch, message, Modal, Space, AutoComplete, Tooltip, Progress, Statistic, Descriptions, Badge } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, ReferenceLine, Area, ComposedChart, Scatter } from 'recharts';
import { ProjectContext } from '../App';
import { DEFAULT_VALUES } from '../config';
import type { ColumnType } from 'antd/es/table';
import { 
  predictStability, 
  StabilityDataPoint, 
  StabilityPredictionRequest, 
  StabilityPredictionResponse,
  validateStabilityData,
  getStabilityModels 
} from '../api/stability';
import { PlusOutlined, DeleteOutlined, ExperimentOutlined, InfoCircleOutlined, WarningOutlined, CheckCircleOutlined, DownloadOutlined, FileTextOutlined, CloudUploadOutlined } from '@ant-design/icons';
import ReportGenerator from '../components/Reports/ReportGenerator';
import { PredictionExplainer } from '../components/PredictionExplainer';
import ProjectSelector from '../components/ProjectSelector';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 定义模型类型
interface StabilityModel {
  value: string;
  name: string;
  description: string;
}

// 标准ICH试验条件定义
const STANDARD_CONDITIONS = {
  LONG_TERM: { 
    name: '长期试验', 
    temperature: 25, 
    humidity: 60, 
    description: 'ICH长期：25°C ± 2°C/60% ± 5% RH',
    color: '#1890ff'
  },
  INTERMEDIATE: { 
    name: '中间试验', 
    temperature: 30, 
    humidity: 65, 
    description: 'ICH中间：30°C ± 2°C/65% ± 5% RH',
    color: '#faad14'
  },
  ACCELERATED: { 
    name: '加速试验', 
    temperature: 40, 
    humidity: 75, 
    description: 'ICH加速：40°C ± 2°C/75% ± 5% RH',
    color: '#f5222d'
  },
  ZONE_IV: { 
    name: 'Zone IV', 
    temperature: 30, 
    humidity: 65, 
    description: 'Zone IV：30°C/65% RH (热带气候)',
    color: '#fa8c16'
  },
  ZONE_IVB: { 
    name: 'Zone IVb', 
    temperature: 30, 
    humidity: 75, 
    description: 'Zone IVb：30°C/75% RH (高湿热带)',
    color: '#eb2f96'
  },
  REFRIGERATED: { 
    name: '冷藏条件', 
    temperature: 5, 
    humidity: 0, 
    description: '冷藏：5°C ± 3°C',
    color: '#13c2c2'
  },
  FREEZER: { 
    name: '冷冻条件', 
    temperature: -20, 
    humidity: 0, 
    description: '冷冻：-20°C ± 5°C',
    color: '#2f54eb'
  }
};

const StabilityPrediction: React.FC = () => {
  const [form] = Form.useForm();
  const { currentProject } = useContext(ProjectContext);
  const [loading, setLoading] = useState(false);
  const [predictionResult, setPredictionResult] = useState<StabilityPredictionResponse | null>(null);
  const [historyData, setHistoryData] = useState<StabilityDataPoint[]>([]);
  const [inputData, setInputData] = useState<any>({});
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('data-input');
  const [customTestItems, setCustomTestItems] = useState<string[]>([]);
  const [impurityModalVisible, setImpurityModalVisible] = useState(false);
  const [newImpurityName, setNewImpurityName] = useState('');
  const [availableModels, setAvailableModels] = useState<StabilityModel[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 初始化数据
  useEffect(() => {
    if (inputData.stability_data) {
      setHistoryData(inputData.stability_data);
    } else {
      setHistoryData([]); // 初始化为空数组，而不是示例数据
    }
    
    // 加载可用模型
    loadAvailableModels();
  }, [inputData]);

  const loadAvailableModels = async () => {
    try {
      const data = await getStabilityModels();
      setAvailableModels(data.models || []);
    } catch (error) {
      console.error('加载模型失败:', error);
      message.error('加载可用预测模型失败，请检查后端服务');
      setAvailableModels([]); // API失败时设置为空数组
    }
  };

  // 执行稳定性预测
  const handleSubmit = async (values: any) => {
    setLoading(true);
    setError(null);
    setPredictionResult(null);
    
    try {
      // 验证数据
      const validation = validateStabilityData(historyData);
      if (!validation.valid) {
        setValidationErrors(validation.errors);
        message.error('数据验证失败，请检查输入数据');
        setLoading(false);
        return;
      }
      
      // 准备请求数据
      const request: StabilityPredictionRequest = {
        drug_name: currentProject?.name || inputData.drug_name || "未知药物",
        drug_structure: inputData.drug_structure,
        excipients: (inputData.excipients || []).map((e: any) => 
          typeof e === 'string' ? e : e.name
        ),
        process: inputData.process || "标准工艺",
        packaging: inputData.packaging || values.packaging || "铝塑泡罩",
        environment: values.target_condition,
        history_data: historyData,
        model_selection: values.model_selection || 'auto',
        confidence_level: (values.confidence_level || 95) / 100,
        prediction_months: values.prediction_months || 24
      };
      
      // 调用API
      const response = await predictStability(request);
      
      // 设置结果
      setPredictionResult(response);
      setActiveTab('results'); // 自动切换到结果标签
      
      // 更新本地的输入数据状态
      setInputData({
        ...inputData,
        stability_prediction: response,
        stability_data: historyData
      });
      
      message.success('稳定性预测完成！');
      
    } catch (err: any) {
      console.error('预测失败:', err);
      const errorMessage = err?.message || '预测失败，请稍后重试';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 添加数据点
  const handleAddDataPoint = () => {
    const newPoint: StabilityDataPoint = {
      time_point: historyData.length > 0 ? 
        Math.max(...historyData.map(d => d.time_point)) + 3 : 0,
      temperature: 25,
      humidity: 60,
        value: 100,
        item: '含量'
    };
    setHistoryData([...historyData, newPoint]);
  };

  // 修改数据点
  const handleDataChange = (index: number, field: keyof StabilityDataPoint, value: any) => {
    const newData = [...historyData];
    newData[index] = { ...newData[index], [field]: value };
    setHistoryData(newData);
  };

  // 删除数据点
  const handleRemoveDataPoint = (index: number) => {
    const newData = historyData.filter((_, i) => i !== index);
    setHistoryData(newData);
  };

  // 应用标准条件
  const handleApplyStandardCondition = (condition: typeof STANDARD_CONDITIONS.LONG_TERM) => {
    const selectedRows = form.getFieldValue('selectedRows') || [];
    if (selectedRows.length === 0) {
      message.warning('请先选择要应用条件的数据行');
      return;
    }
    
      const newData = [...historyData];
    selectedRows.forEach((index: number) => {
      newData[index] = {
        ...newData[index],
        temperature: condition.temperature,
        humidity: condition.humidity
      };
    });
      
      setHistoryData(newData);
    message.success(`已应用${condition.name}条件到选中的数据点`);
  };

  // 批量导入数据
  const handleBatchImport = () => {
    Modal.info({
      title: '批量导入稳定性数据',
      content: (
        <div>
          <p>请按照以下格式准备CSV文件：</p>
          <pre style={{ background: '#f5f5f5', padding: 10 }}>
            时间点(月),温度(°C),湿度(%RH),含量(%),检测项目
            0,25,60,100.0,含量
            3,25,60,98.5,含量
            6,25,60,97.2,含量
          </pre>
          <p>支持的检测项目：含量、有关物质、溶出度、水分、总杂质等</p>
        </div>
      ),
      width: 600
    });
  };

  // 数据表格列定义
  const columns: ColumnType<StabilityDataPoint>[] = [
    {
      title: '时间点(月)',
      dataIndex: 'time_point',
      key: 'time_point',
      width: 120,
      render: (_, record, index) => (
        <InputNumber
          value={record.time_point}
          onChange={(value) => handleDataChange(index, 'time_point', value)}
          min={0}
          max={60}
          precision={0}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '温度(°C)',
      dataIndex: 'temperature',
      key: 'temperature',
      width: 120,
      render: (_, record, index) => (
        <InputNumber
          value={record.temperature}
          onChange={(value) => handleDataChange(index, 'temperature', value)}
          min={-50}
          max={100}
          precision={1}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '湿度(%RH)',
      dataIndex: 'humidity',
      key: 'humidity',
      width: 120,
      render: (_, record, index) => (
        <InputNumber
          value={record.humidity}
          onChange={(value) => handleDataChange(index, 'humidity', value)}
          min={0}
          max={100}
          precision={0}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '检测值(%)',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (_, record, index) => (
        <InputNumber
          value={record.value}
          onChange={(value) => handleDataChange(index, 'value', value)}
          min={0}
          max={120}
          precision={2}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '检测项目',
      dataIndex: 'item',
      key: 'item',
      width: 150,
      render: (_, record, index) => (
            <Select
          value={record.item}
              onChange={(value) => handleDataChange(index, 'item', value)}
              style={{ width: '100%' }}
            >
          <Option value="含量">含量</Option>
          <Option value="有关物质">有关物质</Option>
          <Option value="溶出度">溶出度</Option>
          <Option value="水分">水分</Option>
          <Option value="总杂质">总杂质</Option>
          {customTestItems.map(item => (
                <Option key={item} value={item}>{item}</Option>
              ))}
            </Select>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record, index) => (
        <Space>
          <Tooltip title="删除">
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveDataPoint(index)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 渲染预测结果
  const renderPredictionResults = () => {
    if (!predictionResult) return null;

    return (
      <div>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title="预测概览" bordered={false}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="长期储存条件 (25°C/60%RH)"
                    value={predictionResult.prediction.long_term.t90}
                    suffix="个月"
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  />
                  <Text type="secondary">
                    建议货架期：{predictionResult.prediction.long_term.shelf_life}个月
                  </Text>
                </Col>
                <Col span={8}>
                  <Statistic
                    title="中间条件 (30°C/65%RH)"
                    value={predictionResult.prediction.intermediate.t90}
                    suffix="个月"
                    prefix={<WarningOutlined style={{ color: '#faad14' }} />}
                  />
                  <Text type="secondary">
                    降解速率：{predictionResult.prediction.intermediate.degradation_rate.toFixed(3)}/月
                  </Text>
                </Col>
                <Col span={8}>
                  <Statistic
                    title="加速条件 (40°C/75%RH)"
                    value={predictionResult.prediction.accelerated.t90}
                    suffix="个月"
                    prefix={<ExperimentOutlined style={{ color: '#f5222d' }} />}
                  />
                  <Text type="secondary">
                    活化能：{predictionResult.model_info.activation_energy} kJ/mol
                  </Text>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={16}>
            <Card title="稳定性趋势预测" bordered={false}>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={predictionResult.predicted_values}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="time" 
                    label={{ value: '时间 (月)', position: 'insideBottom', offset: -5 }} 
                  />
                  <YAxis 
                    label={{ value: '含量 (%)', angle: -90, position: 'insideLeft' }} 
                    domain={[80, 105]}
                  />
                  <RechartsTooltip 
                    formatter={(value: number) => `${value.toFixed(2)}%`}
                    labelFormatter={(label) => `${label} 个月`}
                  />
                  <Legend />
                  
                  {/* 置信区间 */}
                  <Area
                    type="monotone"
                    dataKey="upper_ci"
                    stackId="1"
                    stroke="none"
                    fill="#1890ff"
                    fillOpacity={0.2}
                    name="置信上限"
                  />
                  <Area
                    type="monotone"
                    dataKey="lower_ci"
                    stackId="2"
                    stroke="none"
                    fill="#1890ff"
                    fillOpacity={0.2}
                    name="置信下限"
                  />
                  
                  {/* 预测值 */}
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#1890ff" 
                    strokeWidth={2}
                    name="预测值"
                    dot={{ r: 4 }}
                  />
                  
                  {/* 90%参考线 */}
                  <ReferenceLine 
                    y={90} 
                    stroke="#ff4d4f" 
                    strokeDasharray="5 5"
                    label={{ value: "90%限度", position: "left" }}
                  />
                  
                  {/* 历史数据点 */}
                  <Scatter
                    name="历史数据"
                    data={historyData.filter(d => d.item === '含量')}
                    fill="#52c41a"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card title="模型信息" bordered={false}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="拟合模型">
                  <Tag color="blue">{predictionResult.model_info.type}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="拟合优度 (R²)">
                  <Progress 
                    percent={predictionResult.model_info.r_squared * 100} 
                    size="small"
                    format={(percent) => `${percent?.toFixed(2)}%`}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="活化能">
                  {predictionResult.model_info.activation_energy} kJ/mol
                </Descriptions.Item>
                <Descriptions.Item label="法规符合性">
                  <Badge 
                    status="success" 
                    text={predictionResult.regulatory_check}
                  />
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Title level={5}>敏感性分析</Title>
              {predictionResult.sensitivity.map((item) => (
                <div key={item.factor} style={{ marginBottom: 8 }}>
                  <Text>{item.factor}</Text>
                  <Progress 
                    percent={item.impact * 100} 
                    size="small"
                    strokeColor={item.impact > 0.5 ? '#ff4d4f' : '#1890ff'}
                  />
                </div>
              ))}
            </Card>
          </Col>
        </Row>

        {predictionResult.degradation_pathways && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="降解途径分析" bordered={false}>
                <Table
                  dataSource={predictionResult.degradation_pathways}
                  rowKey="pathway"
                  pagination={false}
                  columns={[
                    {
                      title: '降解途径',
                      dataIndex: 'pathway',
                      key: 'pathway',
                      render: (text) => <Tag color="purple">{text}</Tag>
                    },
                    {
                      title: '发生概率',
                      dataIndex: 'probability',
                      key: 'probability',
                      render: (val) => (
                        <Progress 
                          percent={val * 100} 
                          size="small" 
                          format={(percent) => `${percent?.toFixed(0)}%`}
                        />
                      )
                    },
                    {
                      title: '反应机理',
                      dataIndex: 'mechanism',
                      key: 'mechanism',
                    },
                    {
                      title: '预防措施',
                      dataIndex: 'prevention',
                      key: 'prevention',
                      render: (prevention: string[]) => (
                        <Space direction="vertical" size="small">
                          {prevention.map((p, i) => (
                            <Tag key={i} color="green">{p}</Tag>
                          ))}
                        </Space>
                      )
                    },
                    {
                      title: '影响因素',
                      dataIndex: 'impact_factors',
                      key: 'impact_factors',
                      render: (factors: string[]) => (
                        <>
                          {factors.map((f, i) => (
                            <Tag key={i}>{f}</Tag>
                          ))}
                        </>
                      )
                    }
                  ]}
                />
              </Card>
            </Col>
          </Row>
        )}

        {predictionResult.recommendations && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card 
                title="专家建议" 
                bordered={false}
              >
                <Alert
                  message="基于预测结果的专业建议"
                  description={
                    <ul style={{ paddingLeft: 20 }}>
                      {predictionResult.recommendations.map((rec, index) => (
                        <li key={index} style={{ marginBottom: 8 }}>{rec}</li>
                      ))}
                    </ul>
                  }
                  type="info"
                  showIcon
                />
                
                <Divider />
                
                {/* 集成报告生成组件 */}
                <ReportGenerator 
                  analysisData={{
                    drug_name: inputData.drug_name || '测试药物',
                    drug_smiles: inputData.drug_structure || '',
                    analysis_type: 'stability',
                    prediction: predictionResult.prediction,
                    model_info: predictionResult.model_info,
                    sensitivity: predictionResult.sensitivity,
                    regulatory_check: predictionResult.regulatory_check,
                    degradation_pathways: predictionResult.degradation_pathways,
                    recommendations: predictionResult.recommendations.map((rec, index) => ({
                      type: 'info',
                      category: '稳定性建议',
                      content: rec
                    })),
                    predicted_values: predictionResult.predicted_values,
                    history_data: historyData
                  }}
                  onReportGenerated={(report) => {
                    message.success('稳定性预测报告生成成功！');
                  }}
                />
              </Card>
            </Col>
          </Row>
        )}
        
        {/* 集成SHAP可解释性分析 */}
        {predictionResult && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <PredictionExplainer 
                explainabilityData={predictionResult.explainability_data}
                predictionResult={predictionResult}
                loading={loading}
              />
            </Col>
          </Row>
        )}
      </div>
    );
  };

  return (
    <div>
      {/* 项目选择器 */}
      <div style={{ marginBottom: '16px' }}>
        <ProjectSelector showCreateButton={true} />
      </div>

      <Title level={2}>
        <ExperimentOutlined /> 稳定性预测
      </Title>
      
      {validationErrors.length > 0 && (
        <Alert
          message="数据验证错误"
          description={
            <ul>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          }
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'data-input',
            label: '数据输入',
            children: (
              <div>
      <Card title="历史稳定性数据" style={{ marginBottom: 24 }}>
            <Card type="inner" title="快速应用标准条件" style={{ marginBottom: 16 }}>
          <Row gutter={[8, 8]}>
                {Object.entries(STANDARD_CONDITIONS).map(([key, condition]) => (
                  <Col key={key} span={6}>
                <Tag 
                      color={condition.color}
                      style={{ 
                        cursor: 'pointer', 
                        padding: '8px 12px', 
                        width: '100%',
                        textAlign: 'center'
                      }}
                      onClick={() => handleApplyStandardCondition(condition)}
                >
                      <div>{condition.name}</div>
                      <div style={{ fontSize: '12px', marginTop: '4px' }}>
                        {condition.temperature}°C / {condition.humidity}%RH
                      </div>
                </Tag>
            </Col>
                ))}
          </Row>
        </Card>
        
        <Table
          dataSource={historyData}
          columns={columns}
              rowKey={(_, index) => index?.toString() || '0'}
          pagination={false}
          size="middle"
              scroll={{ x: 800 }}
              rowSelection={{
                type: 'checkbox',
                onChange: (selectedRowKeys) => {
                  form.setFieldsValue({ selectedRows: selectedRowKeys });
              }
              }}
          footer={() => (
                <Space>
                  <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddDataPoint}>
              添加数据点
            </Button>
                  <Button onClick={handleBatchImport}>
                    批量导入
                  </Button>
                </Space>
          )}
        />
      </Card>
      
          <Card title="预测参数设置">
            <Form
              form={form}
              onFinish={handleSubmit}
              layout="vertical"
              initialValues={{
                model_selection: 'auto',
                confidence_level: 95,
                prediction_months: 24,
                target_condition: 'long_term',
                packaging: inputData.packaging || '铝塑泡罩'
              }}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="model_selection"
                    label="动力学模型"
                    tooltip="选择合适的降解动力学模型"
                  >
                    <Select>
                      {availableModels.map(model => (
                        <Option key={model.value} value={model.value}>
                          {model.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="confidence_level"
                    label="置信水平(%)"
                    rules={[{ required: true, message: '请输入置信水平' }]}
                  >
                    <InputNumber min={80} max={99} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="prediction_months"
                    label="预测时长(月)"
                    rules={[{ required: true, message: '请输入预测时长' }]}
                  >
                    <InputNumber min={12} max={60} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="target_condition"
                    label="目标储存条件"
                    tooltip="选择要预测的目标储存条件"
                  >
                    <Select>
                      <Option value="long_term">长期条件(25°C/60%RH)</Option>
                      <Option value="intermediate">中间条件(30°C/65%RH)</Option>
                      <Option value="accelerated">加速条件(40°C/75%RH)</Option>
                      <Option value="zone_iv">Zone IV(30°C/65%RH)</Option>
                      <Option value="zone_ivb">Zone IVb(30°C/75%RH)</Option>
                      <Option value="refrigerated">冷藏条件(5°C)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="packaging"
                    label="包装材料"
                    tooltip="选择产品的包装材料类型"
                  >
                    <Select>
                      <Option value="铝塑泡罩">铝塑泡罩</Option>
                      <Option value="PVC/PVDC泡罩">PVC/PVDC泡罩</Option>
                      <Option value="高密度聚乙烯瓶">高密度聚乙烯瓶</Option>
                      <Option value="玻璃瓶">玻璃瓶</Option>
                      <Option value="铝管">铝管</Option>
                      <Option value="复合膜袋">复合膜袋</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<ExperimentOutlined />}
                  size="large"
                >
                  开始预测
                </Button>
              </Form.Item>
            </Form>
          </Card>
              </div>
            )
          },
          {
            key: 'results',
            label: '预测结果',
            disabled: !predictionResult,
            children: (
              loading ? (
                <div style={{ textAlign: 'center', padding: '50px' }}>
                  <Spin size="large" tip="正在进行稳定性预测，请稍候..." />
                </div>
              ) : (
                renderPredictionResults()
              )
            )
          }
        ]}
      />
    </div>
  );
};

export default StabilityPrediction; 