"""
分子结构相关API端点
提供SMILES验证、性质计算、可视化等功能
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from app.services.structure_service import structure_service
from app.services.open_data_service import open_data_service
from app.services.comptox_service import comptox_service
from app.services.excipient_data_service import ExcipientDataService
from app.config.database import get_db
from sqlalchemy.orm import Session

router = APIRouter()


class SMILESValidationRequest(BaseModel):
    """SMILES验证请求"""
    smiles: str = Field(..., description="要验证的SMILES字符串")


class SMILESValidationResponse(BaseModel):
    """SMILES验证响应"""
    valid: bool
    canonical_smiles: Optional[str]
    errors: List[str]
    warnings: List[str]


class MolecularPropertiesRequest(BaseModel):
    """分子性质计算请求"""
    smiles: str


class MolecularPropertiesResponse(BaseModel):
    """分子性质响应"""
    molecular_weight: Optional[float]
    logp: Optional[float]
    tpsa: Optional[float]
    num_hbd: Optional[int]
    num_hba: Optional[int]
    num_rotatable_bonds: Optional[int]
    num_aromatic_rings: Optional[int]
    num_heavy_atoms: Optional[int]
    molecular_formula: Optional[str]
    inchi: Optional[str]
    inchi_key: Optional[str]


class StructureVisualizationRequest(BaseModel):
    """结构可视化请求"""
    smiles: str
    width: int = 300
    height: int = 300
    highlight_groups: Optional[List[str]] = None


class SimilaritySearchRequest(BaseModel):
    """相似性搜索请求"""
    query_smiles: str
    threshold: float = Field(0.7, ge=0, le=1)
    limit: int = Field(10, ge=1, le=100)


class BatchValidationRequest(BaseModel):
    """批量验证请求"""
    smiles_list: List[str]


@router.post("/validate", response_model=SMILESValidationResponse)
async def validate_smiles(
    request: SMILESValidationRequest
):
    """验证SMILES字符串的有效性"""
    try:
        result = structure_service.validate_smiles(request.smiles)
        return SMILESValidationResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/properties", response_model=MolecularPropertiesResponse)
async def calculate_properties(
    request: MolecularPropertiesRequest
):
    """计算分子的物理化学性质"""
    try:
        # 先验证SMILES
        validation = structure_service.validate_smiles(request.smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 计算性质
        properties = structure_service.calculate_properties(request.smiles)
        return MolecularPropertiesResponse(**properties)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/visualize")
async def visualize_structure(
    request: StructureVisualizationRequest
):
    """生成分子的2D可视化图像"""
    try:
        # 验证SMILES
        validation = structure_service.validate_smiles(request.smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 生成图像
        if request.highlight_groups:
            image_data = structure_service.highlight_functional_groups(
                request.smiles, request.highlight_groups
            )
        else:
            image_data = structure_service.generate_2d_image(
                request.smiles, (request.width, request.height)
            )

        if not image_data:
            raise HTTPException(
                status_code=500,
                detail="无法生成分子图像，请确保已安装RDKit"
            )

        return {"image": image_data}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/functional-groups")
async def identify_functional_groups(
    request: MolecularPropertiesRequest
):
    """识别分子中的官能团"""
    try:
        # 验证SMILES
        validation = structure_service.validate_smiles(request.smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 识别官能团
        groups = structure_service.identify_functional_groups_detailed(request.smiles)

        return {
            "smiles": request.smiles,
            "functional_groups": groups,
            "total_groups": len(groups)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/drug-likeness")
async def check_drug_likeness(
    request: MolecularPropertiesRequest
):
    """检查分子的类药性（Lipinski五规则）"""
    try:
        # 验证SMILES
        validation = structure_service.validate_smiles(request.smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 检查类药性
        result = structure_service.check_drug_likeness(request.smiles)

        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/similarity")
async def calculate_similarity(
    smiles1: str,
    smiles2: str,
    method: str = "tanimoto"
):
    """计算两个分子的相似度"""
    try:
        # 验证两个SMILES
        for smiles in [smiles1, smiles2]:
            validation = structure_service.validate_smiles(smiles)
            if not validation["valid"]:
                raise HTTPException(
                    status_code=400,
                    detail=f"无效的SMILES: {smiles}"
                )

        # 计算相似度
        similarity = structure_service.calculate_similarity(smiles1, smiles2, method)

        if similarity is None:
            raise HTTPException(
                status_code=500,
                detail="无法计算相似度，请确保已安装RDKit"
            )

        return {
            "smiles1": smiles1,
            "smiles2": smiles2,
            "method": method,
            "similarity": similarity
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/similarity-search")
async def similarity_search(
    request: SimilaritySearchRequest,
    db: Session = Depends(get_db)
):
    """在数据库中搜索相似结构"""
    try:
        # 验证查询SMILES
        validation = structure_service.validate_smiles(request.query_smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 用实例方法获取辅料SMILES
        excipient_service = ExcipientDataService(db)
        excipient_smiles = {}
        all_excipients = excipient_service.get_all_excipients()
        for excipient in all_excipients:
            info = excipient_service.get_excipient_info(excipient.name)
            if info and hasattr(info, "smiles") and info.smiles:
                excipient_smiles[excipient.name] = info.smiles

        # 如果数据库中没有SMILES数据，尝试从开放数据源获取
        if not excipient_smiles:
            # 使用一些常见辅料的已知SMILES作为备用
            common_excipients = {
                "乳糖一水合物": "64044-51-5",
                "微晶纤维素": "9004-34-6",
                "硬脂酸镁": "557-04-0",
                "甘露醇": "69-65-8",
                "淀粉": "9005-25-8"
            }

            for name, cas in common_excipients.items():
                compound_info = open_data_service.search_compound(cas_number=cas)
                if compound_info and compound_info.get("smiles"):
                    excipient_smiles[name] = compound_info["smiles"]

        # 查找相似结构
        smiles_list = list(excipient_smiles.values())
        similar_structures = structure_service.find_similar_structures(
            request.query_smiles,
            smiles_list,
            request.threshold
        )

        # 添加辅料名称
        for result in similar_structures:
            for name, smiles in excipient_smiles.items():
                if smiles == result["smiles"]:
                    result["name"] = name
                    break

        # 限制返回数量
        similar_structures = similar_structures[:request.limit]

        return {
            "query_smiles": request.query_smiles,
            "threshold": request.threshold,
            "results": similar_structures,
            "total_found": len(similar_structures)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-validate")
async def batch_validate(
    request: BatchValidationRequest
):
    """批量验证SMILES字符串"""
    try:
        results = []
        for smiles in request.smiles_list:
            validation = structure_service.validate_smiles(smiles)
            results.append({
                "smiles": smiles,
                "validation": validation
            })

        # 统计信息
        valid_count = sum(1 for r in results if r["validation"]["valid"])

        return {
            "total": len(request.smiles_list),
            "valid": valid_count,
            "invalid": len(request.smiles_list) - valid_count,
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/toxicity-assessment")
async def assess_toxicity(
    smiles: str,
    cas_number: Optional[str] = None
):
    """评估化合物的毒理学风险"""
    try:
        # 验证SMILES
        validation = structure_service.validate_smiles(smiles)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"无效的SMILES: {', '.join(validation['errors'])}"
            )

        # 计算分子性质
        properties = structure_service.calculate_properties(smiles)

        # 如果提供了CAS号，获取CompTox数据
        toxicity_data = {}
        if cas_number:
            toxicity_data = await comptox_service.get_toxicity_by_cas(cas_number)
            hazard_summary = comptox_service.get_hazard_summary(cas_number)
        else:
            hazard_summary = {"status": "需要CAS号以获取毒理学数据"}

        # 综合评估
        assessment = {
            "smiles": smiles,
            "cas_number": cas_number,
            "molecular_properties": properties,
            "toxicity_data": toxicity_data,
            "hazard_summary": hazard_summary,
            "recommendations": _generate_safety_recommendations(properties, toxicity_data)
        }

        return assessment
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def _generate_safety_recommendations(properties: Dict, toxicity_data: Dict) -> List[str]:
    """基于性质和毒性数据生成安全建议"""
    recommendations = []
    
    # 基于分子性质的建议
    if properties.get("molecular_weight", 0) < 100:
        recommendations.append("小分子化合物，注意挥发性")
    
    if properties.get("logp", 0) > 3:
        recommendations.append("脂溶性较高，可能有生物累积性")
    
    # 基于毒性数据的建议
    if toxicity_data.get("carcinogenicity") in ["Known", "Probable"]:
        recommendations.append("潜在致癌物，需要严格的防护措施")
    
    if toxicity_data.get("acute_toxicity", {}).get("oral_ld50"):
        recommendations.append("已知急性毒性数据，处理时需谨慎")
    
    if not recommendations:
        recommendations.append("建议进行完整的安全性评估")
    
    return recommendations 