declare module 'react-highlight-words' {
  import React from 'react';

  export interface HighlighterProps {
    searchWords: string[];
    textToHighlight: string;
    autoEscape?: boolean;
    highlightStyle?: React.CSSProperties;
    highlightClassName?: string;
    activeIndex?: number;
    activeClassName?: string;
    activeStyle?: React.CSSProperties;
    unhighlightClassName?: string;
    unhighlightStyle?: React.CSSProperties;
    caseSensitive?: boolean;
    findChunks?: (options: {
      searchWords: string[];
      textToHighlight: string;
      autoEscape?: boolean;
      caseSensitive?: boolean;
    }) => {
      start: number;
      end: number;
    }[];
    sanitize?: (text: string) => string;
  }

  export default class Highlighter extends React.Component<HighlighterProps> {}
} 