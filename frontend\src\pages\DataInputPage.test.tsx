import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DataInputPage from './DataInputPage';

describe('DataInputPage', () => {
  it('批量导入历史与错误修正入口渲染', async () => {
    render(<DataInputPage />);
    expect(await screen.findByText('批量导入历史')).toBeInTheDocument();
    expect(await screen.findByText('导入错误记录')).toBeInTheDocument();
    expect(screen.getAllByText('修正').length).toBeGreaterThan(0);
  });

  it('联网识别功能交互', async () => {
    render(<DataInputPage />);
    const identifyBtn = screen.getByText('联网识别');
    fireEvent.click(identifyBtn);
    expect(await screen.findByText('请至少填写一项信息')).toBeInTheDocument();
  });

  it('国际化切换下主要功能可用', async () => {
    render(<DataInputPage />);
    // 假设有语言切换按钮
    // fireEvent.click(screen.getByLabelText('切换到英文'));
    // expect(await screen.findByText('Batch Import History')).toBeInTheDocument();
  });

  // 补充API异常、边界条件等测试，确保批量导入、联网识别等功能与真实后端联调。
}); 