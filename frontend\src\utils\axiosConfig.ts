import axios from 'axios';
import { message } from 'antd';

// 配置基础URL - 统一使用8000端口
axios.defaults.baseURL = 'http://127.0.0.1:8000';

// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      if (error.response.status === 401) {
        message.error('登录已过期，请重新登录');
        localStorage.removeItem('token');
        window.location.href = '/login';
      } else if (error.response.data && error.response.data.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('请求失败');
      }
    } else {
      message.error('网络异常，请检查连接');
    }
    return Promise.reject(error);
  }
);

export default axios; 