"""
数据库配置和连接池优化
"""

import os
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import sqlite3
import logging

logger = logging.getLogger(__name__)

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./app.db")
        self.pool_size = int(os.getenv("DATABASE_POOL_SIZE", "10"))
        self.max_overflow = int(os.getenv("DATABASE_MAX_OVERFLOW", "20"))
        self.pool_timeout = int(os.getenv("DATABASE_POOL_TIMEOUT", "30"))
        self.pool_recycle = int(os.getenv("DATABASE_POOL_RECYCLE", "3600"))
        self.echo = os.getenv("DATABASE_ECHO", "false").lower() == "true"
        
    def create_engine_with_pool(self):
        """创建带连接池的数据库引擎"""
        
        if self.database_url.startswith("sqlite"):
            # SQLite配置
            engine = create_engine(
                self.database_url,
                echo=self.echo,
                poolclass=QueuePool,
                pool_size=5,  # SQLite建议较小的连接池
                max_overflow=10,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20,
                    "isolation_level": None,  # 启用autocommit模式
                }
            )
            
            # SQLite优化设置
            @event.listens_for(engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                if isinstance(dbapi_connection, sqlite3.Connection):
                    cursor = dbapi_connection.cursor()
                    # 启用外键约束
                    cursor.execute("PRAGMA foreign_keys=ON")
                    # 设置WAL模式提高并发性能
                    cursor.execute("PRAGMA journal_mode=WAL")
                    # 设置同步模式
                    cursor.execute("PRAGMA synchronous=NORMAL")
                    # 设置缓存大小(KB)
                    cursor.execute("PRAGMA cache_size=10000")
                    # 设置临时存储
                    cursor.execute("PRAGMA temp_store=MEMORY")
                    # 启用查询优化器
                    cursor.execute("PRAGMA optimize")
                    cursor.close()
                    
        elif self.database_url.startswith("postgresql"):
            # PostgreSQL配置
            engine = create_engine(
                self.database_url,
                echo=self.echo,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                connect_args={
                    "options": "-c timezone=utc",
                    "connect_timeout": 10,
                }
            )
            
        elif self.database_url.startswith("mysql"):
            # MySQL配置
            engine = create_engine(
                self.database_url,
                echo=self.echo,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                connect_args={
                    "charset": "utf8mb4",
                    "connect_timeout": 10,
                }
            )
        else:
            # 默认配置
            engine = create_engine(
                self.database_url,
                echo=self.echo,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
            )
        
        return engine
    
    def create_session_factory(self, engine):
        """创建会话工厂"""
        return sessionmaker(
            bind=engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )

# 全局数据库配置实例
db_config = DatabaseConfig()
engine = db_config.create_engine_with_pool()
SessionLocal = db_config.create_session_factory(engine)

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_sync():
    """获取同步数据库会话"""
    return SessionLocal()

class DatabaseHealthCheck:
    """数据库健康检查"""
    
    @staticmethod
    def check_connection():
        """检查数据库连接"""
        try:
            db = get_db_sync()
            db.execute("SELECT 1")
            db.close()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    @staticmethod
    def get_pool_status():
        """获取连接池状态"""
        try:
            pool = engine.pool
            return {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
        except Exception as e:
            logger.error(f"Failed to get pool status: {e}")
            return None

# 数据库初始化函数
def init_database():
    """初始化数据库"""
    try:
        from app.models.base import Base
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False

# 数据库迁移函数
def migrate_database():
    """数据库迁移"""
    try:
        # 这里可以添加数据库迁移逻辑
        # 例如使用Alembic进行版本管理
        logger.info("Database migration completed")
        return True
    except Exception as e:
        logger.error(f"Database migration failed: {e}")
        return False

# 数据库备份函数
def backup_database():
    """数据库备份"""
    try:
        if db_config.database_url.startswith("sqlite"):
            import shutil
            import datetime
            
            db_path = db_config.database_url.replace("sqlite:///", "")
            backup_path = f"{db_path}.backup.{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(db_path, backup_path)
            logger.info(f"Database backed up to: {backup_path}")
            return backup_path
        else:
            logger.warning("Backup not implemented for this database type")
            return None
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        return None
