from fastapi import APIRouter, HTTPException, Depends, Query, status
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, timedelta
import jwt as pyjwt
from fastapi.responses import JSONResponse
from app.schemas import LoginRequest, TokenResponse
from app.services.auth import authenticate_user
from app.services import list_api_keys, create_api_key, disable_api_key, list_operation_logs, get_db, db_list_api_keys, db_create_api_key, db_list_operation_logs
from sqlalchemy.orm import Session # type: ignore
from app.models import UserORM
from fastapi.security import OA<PERSON>2<PERSON><PERSON>word<PERSON>earer, OAuth2PasswordRequestForm

router = APIRouter()

SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

fake_users_db = {
    "admin": {"username": "admin", "password": "admin123", "role": "admin"},
    "user": {"username": "user", "password": "user123", "role": "user"},
}

# API密钥存储
api_keys = {
    "admin": "demo-api-key-123456",
    "user": "demo-api-key-789012"
}

api_key_store = [
    {'key': 'demo-api-key-123456', 'enabled': True, 'created_at': '2024-06-01 10:00'},
    {'key': 'demo-api-key-789012', 'enabled': True, 'created_at': '2024-06-01 10:05'},
]

class LoginRequest(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

# 认证相关模型
class Token(BaseModel):
    access_token: str
    token_type: str
    role: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

# 获取当前用户
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的身份验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 这里应该解析JWT令牌
        # 简化版本，实际应用中应该验证签名
        payload = {"sub": "admin", "role": "admin"}
        username = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        return {"username": username, "role": payload.get("role", "user")}
    except:
        raise credentials_exception

# 简化的用户验证函数
def simple_authenticate_user(username: str, password: str) -> Optional[dict]:
    """简化的用户验证"""
    user = fake_users_db.get(username)
    if user and user["password"] == password:
        # 创建访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": username, "role": user["role"]},
            expires_delta=access_token_expires
        )
        return {
            "access_token": access_token,
            "role": user["role"]
        }
    return None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = pyjwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 登录接口 - 修改为接受JSON格式
@router.post("/login", response_model=Token)
async def login_for_access_token(login_data: LoginRequest):
    auth_result = simple_authenticate_user(login_data.username, login_data.password)

    if not auth_result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return {
        "access_token": auth_result["access_token"],
        "token_type": "bearer",
        "role": auth_result.get("role", "user")
    }

# 用户信息接口
@router.get("/me")
async def read_users_me(current_user: dict = Depends(get_current_user)):
    return current_user

@router.get('/user/api-key')
def get_api_key(user=Depends(get_current_user)):
    """
    获取当前用户的API Key。
    - **return**: apiKey
    """
    username = user['username'] if isinstance(user, dict) and 'username' in user else 'demo-user'
    k = api_keys.get(username, 'demo-api-key-123456')
    # 禁用Key不可用
    for key in api_key_store:
        if key['key'] == k and not key['enabled']:
            raise HTTPException(status_code=403, detail='API Key已禁用')
    return {'apiKey': k}

@router.post('/user/api-key/reset')
def reset_api_key(user=Depends(get_current_user)):
    """
    重置当前用户的API Key。
    - **return**: apiKey
    """
    import secrets
    username = user['username'] if isinstance(user, dict) and 'username' in user else 'demo-user'
    new_key = 'api-' + secrets.token_hex(12)
    api_keys[username] = new_key
    return {'apiKey': new_key}

@router.get('/user/operation-logs')
def api_list_operation_logs(skip: int = Query(0), limit: int = Query(20), db: Session = Depends(get_db)):
    """
    获取操作日志，支持分页。
    - **skip**: 跳过条数
    - **limit**: 返回条数
    - **return**: 操作日志列表
    """
    return db_list_operation_logs(db, skip, limit)

@router.get('/user/api-keys')
def api_list_api_keys(db: Session = Depends(get_db)):
    """
    获取API Key列表（数据库版，预留）。
    - **return**: API Key列表
    """
    return db_list_api_keys(db)

@router.post('/user/api-keys')
def api_create_api_key(user=Depends(get_current_user), db: Session = Depends(get_db)):
    """
    新建API Key（数据库版，预留）。
    - **return**: 新API Key
    """
    # 假设用户名唯一，查找用户ID
    username = user.get('sub', None) or user.get('username', None)
    user_id = 1
    if username:
        db_user = db.query(UserORM).filter_by(username=username).first()
        if db_user:
            user_id = db_user.id
    return db_create_api_key(db, user_id)

@router.post('/user/api-keys/disable')
def api_disable_api_key(key: str):
    """
    禁用API Key。
    - **key**: API Key字符串
    - **return**: ok
    """
    ok = disable_api_key(key)
    if not ok:
        raise HTTPException(status_code=404, detail="API Key未找到")
    return {"ok": True}

@router.get('/operation-logs/import-export')
def get_import_export_logs(user=Depends(get_current_user)):
    """
    获取导入/导出操作日志（模拟）。
    - **return**: 导入/导出日志列表
    """
    # 模拟导入/导出操作日志
    return [
        {'id': 1, 'time': '2024-06-01 10:00', 'action': '导入', 'detail': '导入药物信息Excel'},
        {'id': 2, 'time': '2024-06-01 10:05', 'action': '导出', 'detail': '导出项目1报告'},
    ]

@router.get('/users', response_model=List[dict])
def get_users():
    """
    获取用户列表
    """
    # 通过数据库ORM查询真实用户列表
    from app.models.user import UserORM
    users = UserORM.query.all()
    return [
        {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "status": user.status,
            "last_login": user.last_login,
            "created_at": user.created_at
        }
        for user in users
    ]

@router.get('/user/permissions')
def get_user_permissions(user=Depends(get_current_user)):
    """
    获取当前用户的权限信息
    """
    # 从用户信息中获取角色
    role = user.get('role', 'user')
    
    # 定义角色权限映射
    role_permissions = {
        'admin': [
            'project.create', 'project.read', 'project.update', 'project.delete',
            'data.import', 'data.export', 'analysis.all', 'settings.manage',
            'user.manage', 'system.configure'
        ],
        'user': [
            'project.create', 'project.read', 'project.update',
            'data.import', 'data.export', 'analysis.all'
        ],
        'guest': [
            'project.read', 'analysis.view'
        ]
    }
    
    permissions = role_permissions.get(role, [])
    
    return {
        'role': role,
        'permissions': permissions
    } 