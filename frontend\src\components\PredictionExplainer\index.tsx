import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Row,
  Col,
  Progress,
  Typography,
  List,
  Tag,
  Alert,
  Space,
  Tooltip,
  Divider,
  Statistic
} from 'antd';
import {
  InfoCircleOutlined,
  RiseOutlined,
  FallOutlined,
  ExperimentOutlined,
  BulbOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

interface PredictionExplainerProps {
  explainabilityData?: any;
  predictionResult?: any;
  loading?: boolean;
}

export const PredictionExplainer: React.FC<PredictionExplainerProps> = ({
  explainabilityData,
  predictionResult,
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  if (!explainabilityData || explainabilityData.error) {
    return (
      <Card title="模型可解释性分析">
        <Alert
          message="暂无可解释性数据"
          description={explainabilityData?.error || "请先运行稳定性预测以获取解释"}
          type="info"
          showIcon
        />
      </Card>
    );
  }

  const renderFeatureImportance = () => {
    const features = explainabilityData?.prediction_explanation?.feature_importance || [];
    
    return (
      <div>
        <Title level={5}>
          特征重要性分析
          <Tooltip title="显示对当前预测影响最大的因素">
            <InfoCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
          </Tooltip>
        </Title>
        
        <List
          dataSource={features.slice(0, 10)}
          renderItem={(item: any) => (
            <List.Item>
              <Row style={{ width: '100%' }} align="middle">
                <Col span={8}>
                  <Text strong>{item.feature}</Text>
                </Col>
                <Col span={4}>
                  <Text type="secondary">值: {item.value.toFixed(2)}</Text>
                </Col>
                <Col span={8}>
                  <Progress
                    percent={Math.abs(item.shap_value) * 10}
                    strokeColor={item.impact === 'positive' ? '#52c41a' : '#f5222d'}
                    showInfo={false}
                  />
                </Col>
                <Col span={4} style={{ textAlign: 'right' }}>
                  {item.impact === 'positive' ? (
                    <Tag icon={<RiseOutlined />} color="success">
                      +{item.shap_value.toFixed(3)}
                    </Tag>
                  ) : (
                    <Tag icon={<FallOutlined />} color="error">
                      {item.shap_value.toFixed(3)}
                    </Tag>
                  )}
                </Col>
              </Row>
            </List.Item>
          )}
        />
        
        {explainabilityData?.prediction_explanation?.interpretation && (
          <Alert
            message="解释"
            description={explainabilityData.prediction_explanation.interpretation}
            type="info"
            icon={<BulbOutlined />}
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  const renderGlobalImportance = () => {
    const globalData = explainabilityData?.global_importance || {};
    const features = globalData.global_feature_importance || [];
    
    return (
      <div>
        <Title level={5}>
          全局特征重要性
          <Tooltip title="基于所有历史预测的平均特征重要性">
            <InfoCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
          </Tooltip>
        </Title>
        
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Statistic
              title="分析样本数"
              value={globalData.sample_size || 0}
              prefix={<ExperimentOutlined />}
            />
          </Col>
        </Row>
        
        <List
          dataSource={features}
          renderItem={(item: any, index: number) => (
            <List.Item>
              <Row style={{ width: '100%' }} align="middle">
                <Col span={2}>
                  <Tag color={index < 3 ? 'red' : index < 5 ? 'orange' : 'default'}>
                    #{item.rank}
                  </Tag>
                </Col>
                <Col span={14}>
                  <Text strong>{item.feature}</Text>
                </Col>
                <Col span={8}>
                  <Progress
                    percent={item.importance * 100}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    format={(percent) => `${item.importance.toFixed(3)}`}
                  />
                </Col>
              </Row>
            </List.Item>
          )}
        />
        
        {globalData.insights && globalData.insights.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Divider orientation="left">关键洞察</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              {globalData.insights.map((insight: string, index: number) => (
                <Alert
                  key={index}
                  message={insight}
                  type="success"
                  showIcon
                />
              ))}
            </Space>
          </div>
        )}
      </div>
    );
  };

  const renderVisualization = () => {
    return (
      <div>
        <Title level={5}>可视化分析</Title>
        
        <Row gutter={[16, 16]}>
          {explainabilityData?.waterfall_plot && (
            <Col span={24}>
              <Card
                title="瀑布图"
                extra={
                  <Tooltip title="显示每个特征对预测值的贡献">
                    <InfoCircleOutlined />
                  </Tooltip>
                }
              >
                <img
                  src={explainabilityData.waterfall_plot}
                  alt="SHAP Waterfall Plot"
                  style={{ maxWidth: '100%', height: 'auto' }}
                />
              </Card>
            </Col>
          )}
          
          {explainabilityData?.summary_plot && (
            <Col span={24}>
              <Card
                title="特征重要性汇总图"
                extra={
                  <Tooltip title="所有预测的特征重要性分布">
                    <InfoCircleOutlined />
                  </Tooltip>
                }
              >
                <img
                  src={explainabilityData.summary_plot}
                  alt="SHAP Summary Plot"
                  style={{ maxWidth: '100%', height: 'auto' }}
                />
              </Card>
            </Col>
          )}
        </Row>
      </div>
    );
  };

  const renderPredictionSummary = () => {
    if (!predictionResult) return null;
    
    const prediction = predictionResult.prediction || {};
    
    return (
      <div>
        <Title level={5}>预测摘要</Title>
        
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card>
              <Statistic
                title="预测货架期 (T90)"
                value={prediction.long_term?.t90 || 'N/A'}
                suffix="个月"
                valueStyle={{ color: '#3f8600' }}
              />
              {prediction.long_term?.ci && (
                <Text type="secondary">
                  置信区间: {prediction.long_term.ci[0]} - {prediction.long_term.ci[1]} 个月
                </Text>
              )}
            </Card>
          </Col>
          
          <Col span={12}>
            <Card>
              <Statistic
                title="模型置信度"
                value={prediction.confidence || 0}
                suffix="%"
                valueStyle={{ color: prediction.confidence > 80 ? '#3f8600' : '#cf1322' }}
              />
            </Card>
          </Col>
          
          <Col span={24}>
            {prediction.risk_factors && prediction.risk_factors.length > 0 && (
              <Alert
                message="主要风险因素"
                description={
                  <Space wrap>
                    {prediction.risk_factors.map((risk: string, index: number) => (
                      <Tag key={index} color="warning">{risk}</Tag>
                    ))}
                  </Space>
                }
                type="warning"
                showIcon
              />
            )}
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <Card 
      title="稳定性预测可解释性分析"
      loading={loading}
      extra={
        <Text type="secondary">
          基于SHAP (SHapley Additive exPlanations) 技术
        </Text>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="预测摘要" key="summary">
          {renderPredictionSummary()}
        </TabPane>
        
        <TabPane tab="特征重要性" key="features">
          {renderFeatureImportance()}
        </TabPane>
        
        <TabPane tab="全局分析" key="global">
          {renderGlobalImportance()}
        </TabPane>
        
        <TabPane tab="可视化" key="visualization">
          {renderVisualization()}
        </TabPane>
      </Tabs>
    </Card>
  );
}; 