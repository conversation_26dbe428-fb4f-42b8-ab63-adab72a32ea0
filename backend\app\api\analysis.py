# pyright: reportMissingImports=false, reportGeneralTypeIssues=false, reportOptionalMemberAccess=false, reportArgumentType=false, reportCallIssue=false
# type: ignore
"""
分析相关API路由
"""
# 标准库
import os
import tempfile
from datetime import datetime
import logging

# 第三方
from fastapi import APIRouter, HTTPException, Depends, Body, UploadFile, File
from fastapi.responses import FileResponse, StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
try:
    from reportlab.pdfgen import canvas
except ImportError:
    canvas = None
    print('警告: 未安装reportlab，PDF导出功能不可用')
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import openpyxl
try:
    import openpyxl
except ImportError:
    openpyxl = None
    print('警告: 未安装openpyxl，Excel导入功能不可用')
import io
try:
    import pandas as pd
except ImportError:
    pd = None
    print('警告: 未安装pandas，部分数据处理功能不可用')
import uuid

# 项目内
from .auth import get_current_user
from ..models import AISuggestion, ProjectORM
from ..schemas import (
    AISuggestionFeedback, AnalysisRequest, AnalysisResult, ReportExportRequest, ExportHistoryOut, ProjectCreateRequest
)
from ..services import generate_ai_suggestions, add_export_history_service, list_export_history_service, get_db
import re
from ..models.stability import StabilityDataORM
from ..models.drug import DrugORM
from ..models.excipient import ExcipientORM
from ..models.environment import EnvironmentORM
from app.models.feedback import AISuggestionFeedbackORM

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局变量用于存储导入历史和错误
import_history = []
import_errors = []

class ImportErrorFixRequest(BaseModel):
    row: int
    col: str
    new_value: str


class StabilityData(BaseModel):
    id: str
    project_id: str
    drug_id: str
    excipient_id: Optional[str] = None
    environment_id: Optional[str] = None
    time_point: Optional[str] = None
    value: Optional[float] = None
    item: Optional[str] = None


class StabilityDataCreateRequest(BaseModel):
    project_id: str
    drug_id: str
    excipient_id: Optional[str] = None
    environment_id: Optional[str] = None
    time_point: Optional[str] = None
    value: Optional[float] = None
    item: Optional[str] = None


@router.post("/analysis", response_model=AnalysisResult)
def start_analysis(req: AnalysisRequest, user=Depends(get_current_user)):
    """发起分析任务（模拟）"""
    return AnalysisResult(status="done", progress=100, result="分析已完成，结果见报告")


@router.post("/report/export")
def export_report(req: ReportExportRequest, user=Depends(get_current_user)):
    """
    导出报告，支持PDF/Word格式，内容自定义，样式美观。
    - options: 导出内容选项
    - lang: 语言
    - format: pdf/word
    - return: 文件下载链接
    """
    username = user['username'] if isinstance(user, dict) and 'username' in user else '未知'
    user_id = user.get('id', 0) if isinstance(user, dict) else 0
    export_format = getattr(req, 'format', 'pdf')
    file_ext = 'pdf' if export_format == 'pdf' else 'docx'

    # 生成临时文件
    tmp_dir = tempfile.gettempdir()
    file_name = f"report_{req.project_id}_{int(tempfile.mkstemp()[1][-6:])}.{file_ext}"
    file_path = os.path.join(tmp_dir, file_name)
    
    try:
        if export_format == 'pdf':
            if canvas is None: raise HTTPException(status_code=500, detail='未安装reportlab，无法导出PDF')
            c = canvas.Canvas(file_path)
            c.setFont("Helvetica-Bold", 18)
            c.drawString(100, 800, f"项目ID: {req.project_id} 稳定性研究报告")
            c.setFont("Helvetica", 12)
            c.drawString(100, 780, f"导出人: {username}")
            c.drawString(100, 760, f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            c.drawString(100, 740, f"导出内容: {','.join(req.options)}")
            c.save()
        else:
            if DocxDocument:
                doc = DocxDocument()
                doc.add_heading(f"项目ID: {req.project_id} 稳定性研究报告", 0)
                doc.add_paragraph(f"导出人: {username}")
                doc.add_paragraph(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                doc.add_paragraph(f"导出内容: {','.join(req.options)}")
                doc.save(file_path)
            else:
                raise HTTPException(status_code=500, detail="未安装python-docx，无法导出Word格式报告")
        
        # 记录导出历史（修复参数传递）
        record = add_export_history_service(
            user_id=user_id,
            export_type=export_format,
            file_path=file_path,
            description=f"项目{req.project_id}报告导出"
        )
        
        # 返回文件下载链接（实际部署应用静态文件服务或OSS）
        url = f"/api/report/download/{file_name}"
        
        # 构建返回记录
        export_record = {
            "id": len(import_history) + 1,  # 临时ID
            "fileName": file_name,
            "importTime": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "importedBy": username,
            "status": "成功",
            "url": url
        }
        
        return export_record
        
    except Exception as e:
        logger.error(f"导出报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出报告失败: {str(e)}")


@router.get("/report/download/{file_name}")
def download_report(file_name: str):
    tmp_dir = tempfile.gettempdir()
    file_path = os.path.join(tmp_dir, file_name)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")
    return FileResponse(file_path, filename=file_name)


@router.get("/report/export/history", response_model=List[ExportHistoryOut])
def get_export_history(project_id: Optional[str] = None, user=Depends(get_current_user)):
    """获取导出历史，支持按项目筛选"""
    return list_export_history_service(project_id)


@router.get("/ai/suggestions", response_model=List[AISuggestion])
def get_ai_suggestions(
    project_id: Optional[str] = None, 
    lang: str = "zh", 
    user=Depends(get_current_user), 
    input_data: Optional[str] = None, 
    drug: str = '', 
    excipients: str = '', 
    env: str = '', 
    package: str = '', 
    stability: str = ''
):
    # 支持多参数，联动AI/知识库
    import json
    input_dict = None
    if input_data:
        try:
            input_dict = json.loads(input_data)
        except Exception:
            input_dict = None
    
    # 这里可接入AI/知识库/规则引擎
    # 示例：
    suggestions = [
        {
            "id": 1,
            "title": "优化辅料配比", 
            "desc": "建议调整辅料A比例以提升稳定性。", 
            "risk": "高", 
            "detail": "根据结构和辅料分析，辅料A在高温下易降解，建议将比例从20%降至10%。", 
            "type": "配比"
        },
        {
            "id": 2,
            "title": "更换包装材料", 
            "desc": "推荐使用铝塑包装以降低湿度影响。", 
            "risk": "中", 
            "detail": "铝塑包装可有效隔绝水汽，提升长期稳定性。", 
            "type": "包装"
        }
    ]
    
    # 根据输入参数动态生成建议
    if drug:
        suggestions.append({
            "id": 3,
            "title": f"针对{drug}的专项建议",
            "desc": f"基于{drug}的特性，建议进行专项稳定性测试。",
            "risk": "中",
            "detail": f"考虑到{drug}的分子结构特点，建议重点关注其在不同环境条件下的稳定性表现。",
            "type": "药物特异性"
        })
    
    if env:
        suggestions.append({
            "id": 4,
            "title": f"环境条件优化",
            "desc": f"针对{env}环境条件的优化建议。",
            "risk": "低",
            "detail": f"在{env}条件下，建议采用相应的保护措施以确保产品稳定性。",
            "type": "环境"
        })
    
    return suggestions


@router.post("/ai/suggestions/feedback")
def submit_ai_suggestion_feedback(feedbacks: List[AISuggestionFeedback] = Body(...), user=Depends(get_current_user), db: Session = Depends(get_db)):
    # 存储团队反馈统计，可接入数据库
    feedback_obj = AISuggestionFeedbackORM(
        user_id=getattr(user, 'id', None),
        feedbacks=[f.dict() for f in feedbacks]
    )
    db.add(feedback_obj)
    db.commit()
    db.refresh(feedback_obj)
    return {"ok": True, "count": len(feedbacks), "feedback_id": feedback_obj.id}


@router.get("/import/history")
def get_import_history(user=Depends(get_current_user)):
    return import_history


@router.get("/import/errors")
def get_import_errors(user=Depends(get_current_user)):
    return import_errors


@router.post("/import/error/fix")
def fix_import_error(req: ImportErrorFixRequest, user=Depends(get_current_user)):
    """修正导入错误并落库（模拟）"""
    # 查找并修正错误
    for err in import_errors:
        if err["row"] == req.row and err["col"] == req.col:
            err["value"] = req.new_value
            err["error"] = "已修正"
            err["suggestion"] = ""
            return {"ok": True, "msg": "修正已落库（模拟）", "error": err}
    raise HTTPException(status_code=404, detail="未找到对应的导入错误")


@router.post("/import/upload")
def import_upload(file: UploadFile = File(...), type: str = Body(...), user=Depends(get_current_user), db: Session = Depends(get_db)):
    """批量导入Excel，type参数支持drug/excipient/environment/stability"""
    import_history_item = {
        "id": len(import_history) + 1,
        "fileName": file.filename,
        "importTime": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "importedBy": user.get('username', '未知') if isinstance(user, dict) else '未知',
        "status": "成功",
        "errorCount": 0,
        "dataCount": 0
    }
    errors = []
    success_count = 0
    try:
        if openpyxl is None: raise HTTPException(status_code=500, detail='未安装openpyxl，无法导入Excel')
        wb = openpyxl.load_workbook(file.file)
        ws = wb.active
        headers = [cell.value for cell in next(ws.iter_rows(min_row=1, max_row=1))]
        for idx, row in enumerate(ws.iter_rows(min_row=2), start=2):
            row_data = {headers[i]: (cell.value or '').strip() if isinstance(cell.value, str) else cell.value for i, cell in enumerate(row)}
            # 校验与写入
            try:
                if type == 'drug':
                    did = str(uuid.uuid4())
                    drug = DrugORM(id=did, name=row_data.get('名称'), formula=row_data.get('分子式'), cas=row_data.get('CAS号'), smiles=row_data.get('SMILES'), structure_image_url=row_data.get('结构图'))
                    db.add(drug)
                elif type == 'excipient':
                    eid = str(uuid.uuid4())
                    excipient = ExcipientORM(id=eid, name=row_data.get('名称'), function=row_data.get('功能'), amount=row_data.get('用量'), risk_level=row_data.get('风险等级'), remark=row_data.get('备注'))
                    db.add(excipient)
                elif type == 'environment':
                    eid = str(uuid.uuid4())
                    env = EnvironmentORM(id=eid, temperature=row_data.get('温度'), humidity=row_data.get('湿度'), packaging=row_data.get('包装'), batch=row_data.get('批次'), sample_code=row_data.get('样品编号'))
                    db.add(env)
                elif type == 'stability':
                    sid = str(uuid.uuid4())
                    sdata = StabilityDataORM(id=sid, project_id=row_data.get('项目ID'), drug_id=row_data.get('药物ID'), excipient_id=row_data.get('辅料ID'), environment_id=row_data.get('环境ID'), time_point=row_data.get('时间点'), value=row_data.get('数值'), item=row_data.get('项目'))
                    db.add(sdata)
                else:
                    errors.append({"row": idx, "col": "type", "error": "未知类型", "suggestion": "type参数应为drug/excipient/environment/stability", "value": type})
                    continue
                success_count += 1
            except Exception as e:
                errors.append({"row": idx, "col": "全部", "error": str(e), "suggestion": "请检查数据格式", "value": str(row_data)})
            import_history_item["dataCount"] += 1
        db.commit()
        if errors:
            import_history_item["status"] = "部分失败"
            import_history_item["errorCount"] = len(errors)
            import_errors.extend(errors)
        import_history.append(import_history_item)
        return {"ok": not errors, "errors": errors, "history": import_history_item, "success": success_count}
    except Exception as e:
        import_history_item["status"] = "失败"
        import_history_item["errorCount"] = 1
        import_errors.append({"row": 0, "col": "文件", "error": str(e), "suggestion": "请检查文件格式", "value": ""})
        import_history.append(import_history_item)
        return {"ok": False, "errors": [str(e)], "history": import_history_item}


@router.post('/excipient/analyze')
def excipient_analyze(data: dict = Body(...), user=Depends(get_current_user)):
    smiles = data.get('smiles')
    # 专家知识：药物辅料相容性分析
    # 这里可接入AI/知识库/规则引擎
    # 示例：
    if not smiles:
        return {"result": {"risk": "高", "explanation": "未提供结构信息，无法分析相容性。"}, "suggestions": []}
    # 假设规则：含有硫醇/胺基团高风险
    high_risk = any(x in smiles for x in ['S', 'N'])
    result = {
        "risk": "高" if high_risk else "低",
        "explanation": "结构中含有高反应性基团，易与辅料发生降解反应。" if high_risk else "结构稳定，常见辅料相容性良好。"
    }
    # AI建议（可接入AI/知识库）
    suggestions = [
        {"title": "避免使用含羧基辅料", "desc": "羧基易与胺/硫醇反应，建议选择惰性辅料。"},
        {"title": "优选微晶纤维素", "desc": "微晶纤维素化学稳定性高，适合多数药物。"}
    ] if high_risk else [
        {"title": "常规辅料均可相容", "desc": "结构稳定，辅料选择空间大。"}
    ]
    # 证据（可扩展）
    evidence = [
        {"type": "规则证据", "desc": "结构含S/N，易与辅料发生反应。" if high_risk else "结构无高反应性基团。"}
    ]
    return {"result": result, "suggestions": suggestions, "evidence": evidence}


@router.post('/excipient/export')
def excipient_export(data: dict = Body(...), user=Depends(get_current_user)):
    smiles = data.get('smiles')
    format = data.get('format', 'pdf')
    lang = data.get('lang', 'zh')
    # 生成分析结果
    high_risk = smiles and any(x in smiles for x in ['S', 'N'])
    risk = '高' if high_risk else '低'
    explanation = "结构中含有高反应性基团，易与辅料发生降解反应。" if risk == '高' else "结构稳定，常见辅料相容性良好。"
    suggestions = [
        {"title": "避免使用含羧基辅料", "desc": "羧基易与胺/硫醇反应，建议选择惰性辅料。"},
        {"title": "优选微晶纤维素", "desc": "微晶纤维素化学稳定性高，适合多数药物。"}
    ] if risk == '高' else [
        {"title": "常规辅料均可相容", "desc": "结构稳定，辅料选择空间大。"}
    ]
    # 导出Excel
    if format == 'excel':
        if pd is None: raise HTTPException(status_code=500, detail='未安装pandas，无法处理数据')
        df = pd.DataFrame([{"风险等级": risk, "风险解释": explanation}])
        for i, sug in enumerate(suggestions):
            df[f'建议{i+1}'] = sug['title'] + ':' + sug['desc']
        output = io.BytesIO()
        df.to_excel(output, index=False)
        output.seek(0)
        return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers={"Content-Disposition": "attachment; filename=excipient_analysis.xlsx"})
    else:
        output = io.BytesIO()
        if canvas is None: raise HTTPException(status_code=500, detail='未安装reportlab，无法导出PDF')
        c = canvas.Canvas(output)
        c.setFont("Helvetica-Bold", 16)
        c.drawString(100, 800, f"辅料相容性分析报告")
        c.setFont("Helvetica", 12)
        c.drawString(100, 780, f"风险等级: {risk}")
        c.drawString(100, 760, f"风险解释: {explanation}")
        y = 740
        for sug in suggestions:
            c.drawString(100, y, f"建议: {sug['title']} - {sug['desc']}")
            y -= 20
        c.save()
        output.seek(0)
        return StreamingResponse(output, media_type='application/pdf', headers={"Content-Disposition": "attachment; filename=excipient_analysis.pdf"}) 


@router.post('/stability/create', response_model=StabilityData)
def create_stability_data(data: StabilityDataCreateRequest, db: Session = Depends(get_db)):
    sid = str(uuid.uuid4())
    sdata = StabilityDataORM(id=sid, project_id=data.project_id, drug_id=data.drug_id, excipient_id=data.excipient_id, environment_id=data.environment_id, time_point=data.time_point, value=data.value, item=data.item)
    db.add(sdata)
    db.commit()
    db.refresh(sdata)
    return StabilityData(
        id=sdata.id,
        project_id=sdata.project_id,
        drug_id=sdata.drug_id,
        excipient_id=sdata.excipient_id,
        environment_id=sdata.environment_id,
        time_point=sdata.time_point,
        value=sdata.value,
        item=sdata.item
    )


@router.get('/stability/list', response_model=List[StabilityData])
def list_stability_data(db: Session = Depends(get_db)):
    sdata = db.query(StabilityDataORM).all()
    return [StabilityData(
        id=s.id,
        project_id=s.project_id,
        drug_id=s.drug_id,
        excipient_id=s.excipient_id,
        environment_id=s.environment_id,
        time_point=s.time_point,
        value=s.value,
        item=s.item
    ) for s in sdata]


@router.delete('/stability/delete/{stability_id}')
def delete_stability_data(stability_id: str, db: Session = Depends(get_db)):
    sdata = db.query(StabilityDataORM).filter(StabilityDataORM.id == stability_id).first()
    if not sdata:
        raise HTTPException(status_code=404, detail='稳定性数据不存在')
    db.delete(sdata)
    db.commit()
    return {'msg': 'deleted'}


@router.get('/drug/export')
def export_drug(db: Session = Depends(get_db)):
    drugs = db.query(DrugORM).all()
    df = pd.DataFrame([{k: getattr(d, k) for k in ['id', 'name', 'formula', 'cas', 'smiles', 'structure_image_url']} for d in drugs])
    output = io.BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers={"Content-Disposition": "attachment; filename=drugs.xlsx"})


@router.get('/excipient/export')
def export_excipient(db: Session = Depends(get_db)):
    excipients = db.query(ExcipientORM).all()
    df = pd.DataFrame([{k: getattr(e, k) for k in ['id', 'name', 'function', 'amount', 'risk_level', 'remark']} for e in excipients])
    output = io.BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers={"Content-Disposition": "attachment; filename=excipients.xlsx"})


@router.get('/environment/export')
def export_environment(db: Session = Depends(get_db)):
    envs = db.query(EnvironmentORM).all()
    df = pd.DataFrame([{k: getattr(e, k) for k in ['id', 'temperature', 'humidity', 'packaging', 'batch', 'sample_code']} for e in envs])
    output = io.BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers={"Content-Disposition": "attachment; filename=environments.xlsx"})


@router.get('/stability/export')
def export_stability(db: Session = Depends(get_db)):
    sdata = db.query(StabilityDataORM).all()
    df = pd.DataFrame([{k: getattr(s, k) for k in ['id', 'project_id', 'drug_id', 'excipient_id', 'environment_id', 'time_point', 'value', 'item']} for s in sdata])
    output = io.BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers={"Content-Disposition": "attachment; filename=stability_data.xlsx"})


@router.post('/ai/suggestions')
def ai_suggestions(data: dict = Body(...), user=Depends(get_current_user)):
    # 示例：根据输入生成AI建议卡片
    drug = data.get('drug', '未知药物')
    env = data.get('env', '25℃/60%RH')
    package = data.get('package', '瓶装')
    suggestions = [
        {
            'title': '优化辅料配比',
            'desc': f'{drug} 在 {env} 条件下建议调整辅料A比例以提升稳定性。',
            'risk': '高',
            'detail': '根据结构和辅料分析，辅料A在高温下易降解，建议将比例从20%降至10%。',
            'type': '配比',
            'mechanism': '辅料降解反应',
            'reference': '药物稳定性研究技术指导原则'
        },
        {
            'title': '更换包装材料',
            'desc': f'推荐为 {drug} 采用铝塑包装以降低湿度影响。',
            'risk': '中',
            'detail': '铝塑包装可有效隔绝水汽，提升长期稳定性。',
            'type': '包装',
            'mechanism': '防潮屏障',
            'reference': 'ICH Q1A (R2)'
        }
    ]
    analysis = {
        'summary': f'{drug} 在 {env} 条件下的主要风险为辅料降解和湿度影响。',
        'highlights': ['辅料A降解', '湿度敏感'],
        'detail': 'AI基于结构、辅料、环境等多维数据自动生成建议。'
    }
    return {'suggestions': suggestions, 'analysis': analysis}