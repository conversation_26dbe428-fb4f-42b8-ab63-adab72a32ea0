import { message } from 'antd';
import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // 如果是未授权错误且未进行过重试
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // 如果是token过期问题，可以在这里实现自动刷新token的逻辑
      // 暂时简单处理为重定向到登录页
      localStorage.removeItem('token');
      message.error('会话已过期，请重新登录');
      window.location.href = '/login';
      return Promise.reject(error);
    }
    
    // 处理其他错误
    message.error(error.response?.data?.detail || '请求失败，请稍后重试');
    return Promise.reject(error);
  }
);

export default api;

// 类型声明
export interface ProjectData { name: string; description?: string; status?: string; }
export interface DrugData { name: string; formula?: string; cas?: string; smiles?: string; structure_image_url?: string; }
export interface ExcipientData { name: string; function?: string; amount?: string; risk_level?: string; remark?: string; }
export interface EnvironmentData { temperature?: string; humidity?: string; packaging?: string; batch?: string; sample_code?: string; }
export interface StabilityData {
  id: string;
  project_id: string;
  drug_id: string;
  excipient_id?: string;
  environment_id?: string;
  time_point?: string;
  value?: number;
  item?: string;
}
export type StabilityDataCreatePayload = Omit<StabilityData, 'id'>;
export type StabilityDataUpdatePayload = Partial<StabilityDataCreatePayload>;

// 权限与用户
export interface LoginRequest { username: string; password: string; }
export interface TokenResponse { access_token: string; token_type: string; expires_in: number; }

// 角色与权限管理
export interface Role { id: number; name: string; description?: string; }
export interface Permission { id: number; name: string; description?: string; }
export interface UserRole { user_id: number; role_id: number; }
export interface RolePermission { role_id: number; permission_id: number; }

export async function apiFetch<T>(input: RequestInfo, init?: RequestInit): Promise<T | null> {
  const token = localStorage.getItem('token');
  const headers = {
    ...(init?.headers || {}),
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };
  try {
    const res = await fetch(input, { ...init, headers });
    if (res.status === 401) {
      message.error('请先登录');
      window.location.href = '/login';
      return null;
    }
    if (!res.ok) {
      let errMsg = '请求失败';
      try {
        const err = await res.json();
        errMsg = err.detail || errMsg;
      } catch {}
      message.error(errMsg);
      return null;
    }
    return await res.json();
  } catch (e) {
    message.error('网络异常，请检查连接');
    return null;
  }
}

// 类型声明补充
export interface AISuggestion {
  id: string;
  content: string;
  type?: string;
  score?: number;
  [key: string]: unknown;
}

export async function postFeedback(content: string, related: Record<string, unknown>) {
  return apiFetch('/api/feedback', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ content, related }),
  });
} 

// 项目
export interface Project {
  id: string;
  name: string;
  status: string;
  description?: string;
  created?: string;
  created_at: string;
  updated_at: string;
  drug_info?: {
    drug_name?: string;
    cas_number?: string;
  };
  stability_data?: ProjectDetailStabilityData[];
}

export type ProjectCreatePayload = Omit<Project, 'id' | 'created_at' | 'updated_at' | 'drug_info' | 'stability_data'>;
export type ProjectUpdatePayload = Partial<ProjectCreatePayload>;

// 药物
export interface Drug {
  id: string;
  name: string;
  formula?: string;
  cas?: string;
  smiles?: string;
  structure_image_url?: string;
}
export type DrugCreatePayload = Omit<Drug, 'id'>;
export type DrugUpdatePayload = Partial<DrugCreatePayload>;

// 辅料
export interface Excipient {
  id: string;
  name: string;
  function?: string;
  amount?: string;
  risk_level?: string;
  remark?: string;
}
export type ExcipientCreatePayload = Omit<Excipient, 'id'>;
export type ExcipientUpdatePayload = Partial<ExcipientCreatePayload>;

// 环境
export interface Environment {
  id: string;
  temperature?: string;
  humidity?: string;
  packaging?: string;
  batch?: string;
  sample_code?: string;
}
export type EnvironmentCreatePayload = Omit<Environment, 'id'>;
export type EnvironmentUpdatePayload = Partial<EnvironmentCreatePayload>;

// 项目管理
export const createProject = (data: ProjectCreatePayload) => api.post<Project>('/projects', data);
export const listProjects = () => api.get<Project[]>('/projects');
export const getProject = (id: string) => api.get<Project>(`/projects/${id}`);
export const getProjectData = (id: string) => api.get(`/projects/${id}/data`);
export const updateProject = (id: string, data: ProjectUpdatePayload) => api.put<Project>(`/projects/${id}`, data);
export const deleteProject = (id: string) => api.delete(`/projects/${id}`);

// 药物管理
export const createDrug = (data: DrugCreatePayload) => api.post<Drug>('/drugs', data);
export const listDrugs = () => api.get<Drug[]>('/drugs');
export const getDrug = (id: string) => api.get<Drug>(`/drugs/${id}`);
export const updateDrug = (id: string, data: DrugUpdatePayload) => api.put<Drug>(`/drugs/${id}`, data);
export const deleteDrug = (id:string) => api.delete(`/drugs/${id}`);
export const exportDrugs = () => api.get('/drugs/export', { responseType: 'blob' });

// 辅料管理
export const createExcipient = (data: ExcipientCreatePayload) => api.post<Excipient>('/excipients', data);
export const listExcipients = () => api.get<Excipient[]>('/excipients');
export const getExcipient = (id: string) => api.get<Excipient>(`/excipients/${id}`);
export const updateExcipient = (id: string, data: ExcipientUpdatePayload) => api.put<Excipient>(`/excipients/${id}`, data);
export const deleteExcipient = (id: string) => api.delete(`/excipients/${id}`);
export const exportExcipients = () => api.get('/excipients/export', { responseType: 'blob' });

// 环境参数管理
export const createEnvironment = (data: EnvironmentCreatePayload) => api.post<Environment>('/environments', data);
export const listEnvironments = () => api.get<Environment[]>('/environments');
export const getEnvironment = (id: string) => api.get<Environment>(`/environments/${id}`);
export const updateEnvironment = (id: string, data: EnvironmentUpdatePayload) => api.put<Environment>(`/environments/${id}`, data);
export const deleteEnvironment = (id: string) => api.delete(`/environments/${id}`);
export const exportEnvironments = () => api.get('/environments/export', { responseType: 'blob' });

// 稳定性数据管理
export const createStabilityData = (data: StabilityDataCreatePayload) => api.post<StabilityData>('/stability-data', data);
export const listStabilityData = (project_id?: string) => api.get<StabilityData[]>('/stability-data', { params: { project_id } });
export const getStabilityData = (id: string) => api.get<StabilityData>(`/stability-data/${id}`);
export const updateStabilityData = (id: string, data: StabilityDataUpdatePayload) => api.put<StabilityData>(`/stability-data/${id}`, data);
export const deleteStabilityData = (id: string) => api.delete(`/stability-data/${id}`);
export const exportStabilityData = (project_id?: string) => api.get('/stability-data/export', { params: { project_id }, responseType: 'blob' });

// 批量导入
export const batchImport = (formData: FormData, type: string) => api.post('/import/upload', formData, { params: { type }, headers: { 'Content-Type': 'multipart/form-data' } });

// 原辅料相容性分析
export const analyzeExcipient = (data: { smiles: string }) => api.post('/excipient/analyze', data);
export const exportExcipientAnalysis = (data: { smiles: string; format: string; lang: string }) => api.post('/excipient/export', data, { responseType: 'blob' });

// 稳定性预测
export const predictStability = (data: { batches: string[]; time_points: number[]; items: string[] }) => api.post('/stability/predict', data);

// AI建议
export const getAISuggestions = (data: { drug: string; env: string; package: string }) => api.post('/ai/suggestions', data);
export const getAISuggestionsForProject = (projectId: string) => api.get(`/ai/suggestions?project_id=${projectId}&lang=zh`);

// 操作日志
export const listOperationLogs = () => api.get('/auth/operation-logs');

// 导出历史
export const listExportHistory = (projectId?: string) => api.get('/report/export/history', { params: { project_id: projectId } });

// 用户反馈
export interface FeedbackRequest { content: string; related?: any; }
export const submitFeedback = (data: FeedbackRequest) => api.post('/feedback', data);

// 登录与用户
export const login = (data: LoginRequest) => api.post<TokenResponse>('/login', data);
export const getCurrentUser = () => api.get('/me');

// 角色与权限管理
export const listRoles = () => api.get<Role[]>('/roles');
export const listPermissions = () => api.get<Permission[]>('/permissions');
export const getUserRole = (userId: number) => api.get<UserRole>(`/user-role/${userId}`);
export const setUserRole = (userId: number, roleId: number) => api.post('/user-role', { user_id: userId, role_id: roleId });
export const getRolePermissions = (roleId: number) => api.get<Permission[]>(`/role-permissions/${roleId}`);
export const setRolePermissions = (roleId: number, permissionIds: number[]) => api.post('/role-permissions', { role_id: roleId, permission_ids: permissionIds });
export const getCurrentUserPermissions = () => api.get<{role: string; permissions: string[]}>('/auth/me/permissions');

// 操作日志（增强版）
export interface OperationLog {
  id: number;
  user: string;
  time: string;
  action: string;
  detail: string;
  object_type?: string;
  object_id?: string;
  before?: any;
  after?: any;
  ip?: string;
  device?: string;
}
export const listOperationLogsPaged = (params: { user?: string; action?: string; start?: string; end?: string; page?: number; page_size?: number }) => api.get<{logs: OperationLog[]; total: number}>('/auth/operation-logs', { params });
export const getOperationLogDetail = (id: number) => api.get<OperationLog>(`/auth/operation-log/${id}`);
export const exportOperationLogs = (params: any) => api.get('/auth/operation-logs/export', { params, responseType: 'blob' });

// 导出历史（增强版）
export interface ExportHistory {
  id: number;
  project_id?: string;
  export_time: string;
  file_name: string;
  status: string;
  summary?: string;
  download_url?: string;
  fail_reason?: string;
}
export const listExportHistoryDetail = (params?: { project_id?: string; page?: number; page_size?: number }) => api.get<{history: ExportHistory[]; total: number}>('/report/export/history', { params });
export const getExportHistoryDetail = (id: number) => api.get<ExportHistory>(`/report/export/history/${id}`);
export const downloadExportFile = (id: number) => api.get(`/report/export/download/${id}`, { responseType: 'blob' });
export const retryExport = (id: number) => api.post(`/report/export/retry/${id}`);

// 原辅料相容性风险评估
export interface ExcipientInfo { name: string; batch?: string; supplier?: string; structure?: string; }
export interface CompatibilityRequest { drug_name: string; drug_structure: string; excipients: ExcipientInfo[]; }
export interface CompatibilityResult { drug: string; results: { excipient: string; risk_level: string; risk_type: string; evidence: string[]; suggestion: string; }[]; }
export const assessCompatibility = (data: CompatibilityRequest) => api.post<CompatibilityResult>('/compatibility/assess', data);
export const aiCompatibilityPredict = (data: Record<string, unknown>) => api.post('/ai/compatibility/predict', data);

// 药物制剂稳定性风险预测
export interface StabilityInput {
  drug_name: string;
  excipients: string[];
  process: string;
  packaging: string;
  environment: string;
  history_data?: Record<string, unknown>[];
  prediction_timepoints?: number[];
  model_selection?: string;
  confidence_level?: number;
  prediction_months?: number;
}
export interface StabilityPredictionResult {
  drug: string;
  prediction: {
    long_term: { t90: number; ci: [number, number] };
    accelerated: { t90: number; ci: [number, number] };
  };
  sensitivity: { factor: string; impact: number }[];
  regulatory_check: string;
  explain?: string;
}
export const predictStabilityV2 = (data: StabilityInput) => api.post<StabilityPredictionResult>('/stability/predict', data);
export const aiStabilityPredict = (data: any) => api.post('/ai/stability/predict', data);

// 智能补全
export const suggestIdentify = (query: string, type: 'drug' | 'excipient' | 'packaging' = 'drug') => api.get<string[]>(`/identify/suggest`, { params: { query, type } });

// 系统设置
export const getCurrentSettings = () => api.get('/admin/settings');
export const updateSettings = (data: Record<string, unknown>) => api.post('/admin/settings', data);
export const testApiConnection = (data: Record<string, unknown>) => api.post('/admin/test-connection', data);
export const getSystemStatus = () => api.get('/admin/system-status');

// 报告导出
export const exportReport = (data: Record<string, unknown>) => api.post('/report/export', data);

// 添加请求重试机制
export const withRetry = async <T>(apiCall: () => Promise<T>, retries = 3, delay = 1000): Promise<T> => {
  try {
    return await apiCall();
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }
    await new Promise(resolve => setTimeout(resolve, delay));
    return withRetry(apiCall, retries - 1, delay * 2);
  }
}; 

// 综合分析API
export interface ComprehensiveAnalysisRequest {
  drug_name: string;
  drug_smiles: string;
  dosage_form: string;
  excipients: Array<{name: string; smiles?: string; concentration?: number}>;
  stability_data?: Array<{time_point: number; value: number; item: string; temperature: number; humidity: number}>;
  target_shelf_life?: number;
  storage_zone?: string;
  special_requirements?: any;
}

export interface ComprehensiveAnalysisResponse {
  drug_name: string;
  analysis_sections: {
    structure_analysis: any;
    compatibility_assessment: any;
    stability_prediction?: any;
    study_design: any;
    similar_cases: any[];
  };
  comprehensive_recommendations: any[];
  executive_summary: {
    overall_assessment: string;
    key_findings: string[];
    critical_actions: string[];
    timeline: string;
  };
}

export const performComprehensiveAnalysis = (data: ComprehensiveAnalysisRequest) => 
  api.post<ComprehensiveAnalysisResponse>('/comprehensive-analysis', data);

export const analyzeDrugStructure = (data: {drug_name: string; drug_smiles: string}) =>
  api.post('/drug-structure-analysis', data);

// 项目分析结果保存和获取
export interface AnalysisResultSaveRequest {
  analysis_type: string;
  analysis_date: string;
  analysis_data: any;
  summary?: string;
}

export const saveAnalysisResult = (projectId: string, data: AnalysisResultSaveRequest) =>
  api.post(`/projects/${projectId}/save-analysis`, data);

export const getAnalysisResults = (projectId: string, analysisType: string) =>
  api.get(`/projects/${projectId}/analysis/${analysisType}`);

export const predictStabilityAdvanced = (data: any) => 
  api.post('/stability-prediction-advanced', data);

export const assessCompatibilityAdvanced = (data: any) => 
  api.post('/compatibility-assessment-advanced', data);

export const designStabilityStudy = (data: any) => 
  api.post('/study-design-advanced', data);

export const queryKnowledgeGraph = (params: {
  query_type: string;
  drug_name?: string;
  mechanism?: string;
  issue?: string;
  region?: string;
}) => api.get('/knowledge-graph/query', { params });

export interface ProjectDetailStabilityData {
  id: number;
  time_point: number;
  temperature: number;
  humidity: number;
  value: number;
  item: string;
}