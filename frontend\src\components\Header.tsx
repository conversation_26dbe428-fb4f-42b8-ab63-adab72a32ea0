import React, { useState } from 'react';
import { Layout, Menu, Avatar, Badge, Dropdown, Space, Button, Typography } from 'antd';
import { 
  BellOutlined, 
  UserOutlined, 
  SettingOutlined,
  LogoutOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Array<{id: string, message: string, read: boolean}>>([
    { id: '1', message: '您的项目"阿司匹林稳定性研究"已完成分析', read: false },
    { id: '2', message: '系统更新：新增辅料相容性预测功能', read: true }
  ]);
  
  // 获取当前登录用户信息
  const getUserInfo = () => {
    const token = localStorage.getItem('token');
    if (!token) return { username: '未登录', role: '' };
    
    try {
      const decoded: any = jwtDecode(token);
      return {
        username: decoded.sub || '未知用户',
        role: decoded.role || 'user'
      };
    } catch (error) {
      console.error('Token解析失败:', error);
      return { username: '未知用户', role: 'user' };
    }
  };

  const userInfo = getUserInfo();
  
  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/login');
  };
  
  const markAsRead = (id: string) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, read: true } : n
    ));
  };
  
  const unreadCount = notifications.filter(n => !n.read).length;

  const userMenu = (
    <Menu items={[
      {
        key: '1',
        icon: <UserOutlined />,
        label: '个人中心',
        onClick: () => navigate('/profile')
      },
      {
        key: '2',
        icon: <SettingOutlined />,
        label: '账户设置',
        onClick: () => navigate('/settings')
      },
      {
        type: 'divider',
      },
      {
        key: '3',
        icon: <LogoutOutlined />,
        label: '退出登录',
        onClick: handleLogout
      },
    ]} />
  );
  
  const notificationMenu = (
    <Menu
      style={{ width: 300 }}
      items={[
        {
          key: 'title',
          label: <div style={{ fontWeight: 'bold', padding: '8px 0' }}>通知</div>,
          disabled: true,
        },
        ...notifications.map((notification, index) => ({
          key: notification.id,
          label: (
            <div 
              style={{ 
                padding: '8px 0',
                backgroundColor: notification.read ? 'transparent' : 'rgba(24, 144, 255, 0.1)'
              }}
              onClick={() => markAsRead(notification.id)}
            >
              <div>{notification.message}</div>
              <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                {notification.read ? '已读' : '未读'} · 刚刚
              </div>
            </div>
          )
        })),
        {
          key: 'all',
          label: <div style={{ textAlign: 'center', padding: '8px 0' }}>查看全部</div>,
        }
      ]}
    />
  );

  return (
    <AntHeader style={{
      background: 'var(--content-background)',
      padding: '0 24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderBottom: '1px solid var(--border-light)',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
      height: '64px'
    }}>
      <div style={{
        fontSize: '20px',
        fontWeight: '600',
        color: 'var(--text-color)'
      }}>
        药物稳定性研究助手
      </div>
      <Space size="large">
        <Button
          type="text"
          icon={<QuestionCircleOutlined />}
          style={{
            fontSize: '14px',
            height: '36px',
            padding: '0 12px'
          }}
        >
          帮助
        </Button>
        <Dropdown overlay={notificationMenu} placement="bottomRight" arrow>
          <Badge count={unreadCount} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{
                fontSize: '16px',
                height: '36px',
                width: '36px'
              }}
            />
          </Badge>
        </Dropdown>
        <Dropdown overlay={userMenu} placement="bottomRight" arrow>
          <Space style={{ cursor: 'pointer' }}>
            <Avatar
              icon={<UserOutlined />}
              style={{
                backgroundColor: 'var(--primary-color)'
              }}
            />
            <Text style={{
              fontSize: '14px',
              fontWeight: '500',
              color: 'var(--text-color)'
            }}>
              {userInfo.username}
            </Text>
          </Space>
        </Dropdown>
      </Space>
    </AntHeader>
  );
};

export default Header; 