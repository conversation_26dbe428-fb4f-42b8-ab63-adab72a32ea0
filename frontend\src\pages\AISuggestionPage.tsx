import React, { useState, useContext } from 'react';
import { Card, Tabs, Button, Modal, List } from 'antd';
import { ProjectContext } from '../App';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { getAISuggestions } from '../api';

const AISuggestionPage: React.FC = () => {
  const [tab, setTab] = useState('suggest');
  const [modal, setModal] = useState<{ open: boolean; detail: string }>({ open: false, detail: '' });
  const { currentProject, inputData, analysisResult } = useContext(ProjectContext);
  const { t, i18n } = useTranslation();
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [analysis, setAnalysis] = useState<any>(null);

  React.useEffect(() => {
    if (inputData && inputData.name) {
      getAISuggestions({ drug: inputData.name, env: inputData.env || '', package: inputData.package || '' })
        .then(({ data }) => {
          const result = data as { suggestions: any[]; analysis: any };
          setSuggestions(result.suggestions || []);
          setAnalysis(result.analysis || null);
        });
    }
  }, [inputData]);

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Card>
        <div style={{ marginBottom: 16, color: analysisResult?.error ? 'red' : '#388e3c' }}>
          AI建议：{analysisResult?.error ? analysisResult.error : (analysisResult?.suggestion || '无')}
        </div>
        <Tabs activeKey={tab} onChange={setTab} items={[
          { key: 'suggest', label: t('改进建议'), children: <List dataSource={suggestions} renderItem={item => (
            <List.Item actions={[<Button onClick={() => setModal({ open: true, detail: item.detail })}>{t('查看详情')}</Button>]}> <Card>{item.title}</Card> </List.Item>
          )} /> },
          { key: 'detail', label: t('分析详情'), children: <div>{analysis ? <pre>{JSON.stringify(analysis, null, 2)}</pre> : t('AI分析详情内容（开发中）')}</div> },
        ]} />
        <Button style={{ marginTop: 16 }} onClick={async () => {
          const res = await fetch('/api/report/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ project_id: currentProject?.id, options: ['AI建议'], lang: i18n.language, format: 'pdf' })
          });
          const data = await res.json();
          window.open(data.url, '_blank');
        }}>{t('下载报告')}</Button>
      </Card>
      <Modal open={modal.open} onCancel={() => setModal({ open: false, detail: '' })} footer={null} title="建议详情">
        <div>{modal.detail}</div>
      </Modal>
    </div>
  );
};

export default AISuggestionPage; 