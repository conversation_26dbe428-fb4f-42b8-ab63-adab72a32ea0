{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^6.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/echarts": "^4.9.22", "@types/jest": "^29.5.14", "@types/node": "^22.15.19", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/xlsx": "^0.0.35", "antd": "^5.25.2", "axios": "^1.9.0", "docx": "^9.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-highlight-words": "^0.21.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.6.0", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "typescript": "^5.8.3", "web-vitals": "^5.0.1", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "lint": "eslint ./src --ext .ts,.tsx --max-warnings=0", "format": "prettier --write ./src", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/axios": "^0.9.36", "@types/react-router-dom": "^5.3.3"}, "proxy": "http://127.0.0.1:8000"}