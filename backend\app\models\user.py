from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base
from passlib.hash import bcrypt

class UserORM(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(String, default="user")  # admin, user, guest
    status = Column(String, default="active")  # active, inactive, locked
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    
    # 关系
    api_keys = relationship("APIKeyORM", back_populates="user")
    exports = relationship("ExportHistory", back_populates="user")
    operation_logs = relationship("OperationLogORM", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    def set_password(self, password: str):
        self.hashed_password = bcrypt.hash(password)

    def check_password(self, password: str) -> bool:
        return bcrypt.verify(password, self.hashed_password) 