# Pylance忽略文件 - 完全版本
# PowerShell脚本和相关文件
*.ps1
*.psm1
*.psd1
*.bat
*.cmd
*.sh

# 文档文件
*.md
*.txt
*.rst
*.doc
*.docx

# 配置文件
*.json
*.yml
*.yaml
*.toml
*.ini
*.cfg
*.conf

# 前端相关
frontend/
temp-app/
node_modules/
dist/
build/

# 开发工具和IDE
.vscode/
.cursor/
.idea/
.vs/

# Python缓存和虚拟环境
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.venv/
venv/
ENV/
env/

# 版本控制
.git/
.github/
.gitignore
.gitattributes

# 数据库和日志
*.db
*.sqlite
*.sqlite3
*.log

# 其他
*.tmp
*.temp
*.bak
*.backup
