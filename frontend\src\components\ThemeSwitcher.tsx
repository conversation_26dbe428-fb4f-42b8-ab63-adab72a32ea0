import React from 'react';
import { Select } from 'antd';
import { Theme, setTheme, getTheme, themeColors } from '../theme';

const options = [
  { value: 'blue', label: '专业蓝' },
  { value: 'gray', label: '高级灰' },
  { value: 'dark', label: '暗色' },
];

const ThemeSwitcher: React.FC<{ onChange?: (theme: Theme) => void }> = ({ onChange }) => {
  const [value, setValue] = React.useState<Theme>(getTheme());

  const handleChange = (val: Theme) => {
    setValue(val);
    setTheme(val);
    if (onChange) onChange(val);
  };

  return (
    <div style={{ margin: '12px 0' }}>
      <span style={{ marginRight: 8 }}>主题色：</span>
      <Select
        value={value}
        onChange={handleChange}
        options={options}
        style={{ width: 120 }}
        dropdownStyle={{ fontFamily: 'SimSun' }}
      />
      <span style={{ display: 'inline-block', width: 16, height: 16, background: themeColors[value].primary, borderRadius: 4, marginLeft: 8, verticalAlign: 'middle' }} />
    </div>
  );
};

export default ThemeSwitcher; 