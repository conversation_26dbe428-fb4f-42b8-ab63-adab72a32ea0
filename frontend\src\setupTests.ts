// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// jest setup for browser API mocks
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: () => ({}),
});

if (!global.URL.createObjectURL) {
  global.URL.createObjectURL = jest.fn();
}
