<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端代理测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>前端代理连接测试</h1>
    
    <div class="info">
        <h3>📋 测试说明</h3>
        <p>此页面用于测试前端开发服务器的代理配置是否正常工作。</p>
        <p>当前访问地址：<strong id="current-url"></strong></p>
        <p>代理配置：前端请求 <code>/api/*</code> 会被代理到 <code>http://127.0.0.1:8001</code></p>
    </div>
    
    <div class="test-section">
        <h2>1. 健康检查（通过代理）</h2>
        <button onclick="testProxyHealth()">测试代理健康检查</button>
        <div id="proxy-health-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 登录API（通过代理）</h2>
        <button onclick="testProxyLogin()">测试代理登录</button>
        <div id="proxy-login-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 直接后端连接（对比测试）</h2>
        <button onclick="testDirectBackend()">直接连接后端</button>
        <div id="direct-result"></div>
    </div>

    <script>
        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href;
        
        async function testProxyHealth() {
            const resultDiv = document.getElementById('proxy-health-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 代理健康检查成功</h3>
                            <p>通过前端代理成功访问后端健康检查API</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 代理健康检查失败</h3>
                        <p>错误: ${error.message}</p>
                        <p>请检查：</p>
                        <ul>
                            <li>前端开发服务器是否运行在 localhost:3000</li>
                            <li>后端服务是否运行在 127.0.0.1:8001</li>
                            <li>package.json 中的 proxy 配置是否正确</li>
                        </ul>
                    </div>
                `;
            }
        }
        
        async function testProxyLogin() {
            const resultDiv = document.getElementById('proxy-login-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 代理登录测试成功</h3>
                            <p>通过前端代理成功访问后端登录API</p>
                            <p>获得访问令牌，长度：${data.access_token ? data.access_token.length : 0} 字符</p>
                            <pre>${JSON.stringify({...data, access_token: data.access_token ? data.access_token.substring(0, 50) + '...' : null}, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 代理登录测试失败</h3>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testDirectBackend() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('http://127.0.0.1:8001/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 直接后端连接成功</h3>
                            <p>直接访问后端API成功（绕过前端代理）</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 直接后端连接失败</h3>
                        <p>错误: ${error.message}</p>
                        <p>这可能是由于CORS策略或后端服务未运行</p>
                    </div>
                `;
            }
        }
        
        // 页面加载时自动运行基本检查
        window.onload = function() {
            console.log('前端代理测试页面加载完成');
            
            // 检查是否在正确的端口
            if (window.location.port !== '3000') {
                document.body.insertAdjacentHTML('afterbegin', `
                    <div class="error">
                        <h3>⚠️ 警告</h3>
                        <p>当前不在前端开发服务器环境中（端口不是3000）</p>
                        <p>请访问 <a href="http://localhost:3000/proxy-test.html">http://localhost:3000/proxy-test.html</a> 进行代理测试</p>
                    </div>
                `);
            }
        };
    </script>
</body>
</html>
