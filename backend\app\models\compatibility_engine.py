"""
药物-辅料相容性引擎
提供药物与辅料之间相容性评估的功能
"""

from typing import Dict, List, Any, Optional, Tuple
import math
import numpy as np

class AdvancedCompatibilityEngine:
    """高级相容性引擎
    
    基于化学结构、物理化学性质和已知不相容性规则，
    评估药物与辅料之间的相容性。
    """
    
    def __init__(self):
        """初始化相容性引擎"""
        # 初始化常见的药物-辅料不相容性数据库
        self.incompatibility_db = self._init_incompatibility_db()
        # 初始化Hansen溶解度参数数据库
        self.hansen_parameters = self._init_hansen_parameters()
        # 初始化固体状态风险数据库
        self.solid_state_risks = self._init_solid_state_risks()
    
    def _init_incompatibility_db(self) -> Dict[str, List[Dict[str, Any]]]:
        """初始化药物-辅料不相容性数据库"""
        return {
            "阿司匹林": [
                {"excipient": "碱性辅料", "risk_level": 3, "mechanism": "水解", "recommendation": "避免碱性环境"},
                {"excipient": "硬脂酸镁", "risk_level": 2, "mechanism": "酯水解", "recommendation": "减少用量或选择替代品"}
            ],
            "对乙酰氨基酚": [
                {"excipient": "强氧化剂", "risk_level": 3, "mechanism": "氧化", "recommendation": "避免使用强氧化剂"},
                {"excipient": "微晶纤维素", "risk_level": 1, "mechanism": "吸附", "recommendation": "调整配比"}
            ],
            "阿莫西林": [
                {"excipient": "乳糖", "risk_level": 2, "mechanism": "美拉德反应", "recommendation": "使用无水乳糖或替代品"},
                {"excipient": "水分", "risk_level": 3, "mechanism": "β-内酰胺环水解", "recommendation": "控制水分含量"}
            ]
        }
    
    def _init_hansen_parameters(self) -> Dict[str, Dict[str, float]]:
        """初始化Hansen溶解度参数数据库"""
        return {
            "阿司匹林": {"δd": 18.6, "δp": 7.1, "δh": 11.9},
            "对乙酰氨基酚": {"δd": 19.4, "δp": 8.6, "δh": 13.5},
            "阿莫西林": {"δd": 17.8, "δp": 9.4, "δh": 16.7},
            "乳糖": {"δd": 17.0, "δp": 9.5, "δh": 18.1},
            "微晶纤维素": {"δd": 18.7, "δp": 13.3, "δh": 16.3},
            "硬脂酸镁": {"δd": 16.5, "δp": 3.2, "δh": 4.5},
            "聚维酮": {"δd": 17.6, "δp": 8.7, "δh": 10.1},
            "淀粉": {"δd": 17.8, "δp": 12.5, "δh": 15.2}
        }
    
    def _init_solid_state_risks(self) -> Dict[str, Dict[str, Any]]:
        """初始化固体状态风险数据库"""
        return {
            "阿司匹林": {
                "hygroscopicity": "低",
                "polymorphic_forms": 2,
                "glass_transition": 78.5,
                "critical_rh": 65,
                "solid_state_stability": "中等"
            },
            "对乙酰氨基酚": {
                "hygroscopicity": "低",
                "polymorphic_forms": 3,
                "glass_transition": 85.2,
                "critical_rh": 75,
                "solid_state_stability": "良好"
            },
            "阿莫西林": {
                "hygroscopicity": "高",
                "polymorphic_forms": 1,
                "glass_transition": 92.4,
                "critical_rh": 55,
                "solid_state_stability": "较差"
            }
        }
    
    def calculate_hansen_distance(self, drug: str, excipient: str) -> Optional[float]:
        """计算药物与辅料之间的Hansen距离"""
        drug_params = self.hansen_parameters.get(drug)
        excipient_params = self.hansen_parameters.get(excipient)
        
        if not drug_params or not excipient_params:
            return None
        
        # 计算Hansen距离
        d_d = drug_params["δd"] - excipient_params["δd"]
        d_p = drug_params["δp"] - excipient_params["δp"]
        d_h = drug_params["δh"] - excipient_params["δh"]
        
        hansen_distance = math.sqrt(d_d**2 + d_p**2 + d_h**2)
        return hansen_distance
    
    def assess_compatibility(self, drug_name: str, excipient_name: str, 
                             temperature: float = 25.0, humidity: float = 60.0, 
                             ph: float = 7.0) -> Dict[str, Any]:
        """评估药物与辅料的相容性
        
        参数:
            drug_name: 药物名称
            excipient_name: 辅料名称
            temperature: 温度，默认25℃
            humidity: 湿度，默认60% RH
            ph: pH值，默认7.0
            
        返回:
            相容性评估结果的字典
        """
        # 基础相容性风险评分
        base_risk = 0
        risk_type = ""
        recommendation = ""
        
        # 检查已知不相容性数据库
        known_incompatibilities = self.incompatibility_db.get(drug_name, [])
        for item in known_incompatibilities:
            if excipient_name in item["excipient"] or item["excipient"] in excipient_name:
                base_risk = item["risk_level"]
                risk_type = item["mechanism"]
                recommendation = item["recommendation"]
                break
        
        # 计算Hansen距离
        hansen_distance = self.calculate_hansen_distance(drug_name, excipient_name)
        hansen_risk = 0
        if hansen_distance:
            # Hansen距离越大，相容性越差
            if hansen_distance > 15:
                hansen_risk = 3  # 高风险
            elif hansen_distance > 10:
                hansen_risk = 2  # 中风险
            elif hansen_distance > 5:
                hansen_risk = 1  # 低风险
        
        # 考虑温度因素 - 温度每升高10度，风险增加1分
        temp_factor = max(0, (temperature - 25) / 10)
        
        # 考虑湿度因素 - 湿度每升高20%，风险增加1分
        humidity_factor = max(0, (humidity - 60) / 20)
        
        # 考虑pH因素 - 偏离中性pH越远，风险越大
        ph_factor = abs(ph - 7) / 2
        
        # 综合风险评分
        total_risk = base_risk + hansen_risk + temp_factor + humidity_factor + ph_factor
        
        # 风险等级分类
        if total_risk >= 6:
            risk_level = "高"
        elif total_risk >= 3:
            risk_level = "中"
        elif total_risk > 0:
            risk_level = "低"
        else:
            risk_level = "无"
        
        return {
            "drug": drug_name,
            "excipient": excipient_name,
            "risk_level": risk_level,
            "risk_type": risk_type or "未知",
            "risk_score": round(total_risk, 2),
            "hansen_distance": round(hansen_distance, 2) if hansen_distance else None,
            "environmental_factors": {
                "temperature": temperature,
                "humidity": humidity,
                "ph": ph
            },
            "recommendation": recommendation or "无特殊建议"
        }
    
    def batch_assess_compatibility(self, drug_name: str, excipients: List[Dict[str, Any]], 
                                  temperature: float = 25.0, humidity: float = 60.0, 
                                  ph: float = 7.0) -> Dict[str, Any]:
        """批量评估药物与多种辅料的相容性"""
        results = []
        
        for excipient in excipients:
            excipient_name = excipient.get("name", "")
            if not excipient_name:
                continue
            
            # 单项评估
            assessment = self.assess_compatibility(
                drug_name=drug_name,
                excipient_name=excipient_name,
                temperature=temperature,
                humidity=humidity,
                ph=excipient.get("ph", ph)
            )
            
            results.append(assessment)
        
        # 整体风险评估
        overall_risk = "无"
        if any(r["risk_level"] == "高" for r in results):
            overall_risk = "高"
        elif any(r["risk_level"] == "中" for r in results):
            overall_risk = "中"
        elif any(r["risk_level"] == "低" for r in results):
            overall_risk = "低"
        
        return {
            "drug": drug_name,
            "results": results,
            "overall_risk": overall_risk,
            "environmental_factors": {
                "temperature": temperature,
                "humidity": humidity,
                "ph": ph
            }
        }

# 兼容旧接口：定义CompatibilityEngine为AdvancedCompatibilityEngine的别名
CompatibilityEngine = AdvancedCompatibilityEngine

