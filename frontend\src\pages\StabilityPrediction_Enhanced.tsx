import React, { useState, useEffect, useContext } from 'react';
import { Card, Form, Input, Select, Button, Divider, Spin, Table, Row, Col, InputNumber, Alert, Typography, Radio, Tabs, Tag, Switch, message, Modal, Space, AutoComplete, Tooltip as AntTooltip } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { ProjectContext } from '../App';
import { DEFAULT_VALUES } from '../config';
import type { ColumnType } from 'antd/es/table';
import { predictStabilityV2 } from '../api';
import axios from 'axios';
import { PlusOutlined, DeleteOutlined, CopyOutlined, InfoCircleOutlined, SwapOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// 定义标准试验条件
const STANDARD_CONDITIONS = {
  LONG_TERM: { name: '长期试验', temperature: 25, humidity: 60, description: 'ICH长期：25°C ± 2°C/60% ± 5% RH' },
  INTERMEDIATE: { name: '中间试验', temperature: 30, humidity: 65, description: 'ICH中间：30°C ± 2°C/65% ± 5% RH' },
  ACCELERATED: { name: '加速试验', temperature: 40, humidity: 75, description: 'ICH加速：40°C ± 2°C/75% ± 5% RH' },
  ZONE_IV: { name: 'Zone IV', temperature: 30, humidity: 65, description: 'Zone IV：30°C/65% RH' },
  ZONE_IVB: { name: 'Zone IVb', temperature: 30, humidity: 75, description: 'Zone IVb：30°C/75% RH' },
}; 