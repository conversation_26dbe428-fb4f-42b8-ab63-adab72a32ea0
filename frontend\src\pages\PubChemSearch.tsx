import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  Alert,
  Spin,
  Tabs,
  Select,
  Space,
  Tag,
  Descriptions,
  Table,
  List,
  Divider,
  message,
  Badge,
  Collapse,
  Image,
  Empty,
  Tooltip,
  InputNumber
} from 'antd';
import {
  SearchOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  MedicineBoxOutlined,
  SafetyCertificateOutlined,
  GlobalOutlined,
  InfoCircleOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

interface CompoundInfo {
  cid: number;
  name: string;
  molecular_formula: string;
  molecular_weight: number;
  smiles: string;
  inchi: string;
  synonyms: string[];
  properties?: any;
  bioactivity?: any[];
  patents?: any[];
  pharmacology?: any;
}

// 定义API响应类型
interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
  config: any;
}

interface SearchCompoundResponse {
  compounds: CompoundInfo[];
  total?: number;
  message?: string;
}

interface CompoundDetailResponse {
  compound: CompoundInfo;
}

interface BioactivityResponse {
  bioactivities: any[];
}

interface PatentsResponse {
  patents: any[];
}

interface PharmacologyResponse {
  pharmacology: any;
}

interface SimilarCompoundsResponse {
  similar_compounds: CompoundInfo[];
}

interface BatchSearchResponse {
  results: Array<{
    query: string;
    compounds: CompoundInfo[];
  }>;
}

const PubChemSearch: React.FC = () => {
  const [searchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<CompoundInfo[]>([]);
  const [selectedCompound, setSelectedCompound] = useState<CompoundInfo | null>(null);
  const [activeTab, setActiveTab] = useState('search');
  const [detailLoading, setDetailLoading] = useState(false);

  // 搜索化合物
  const handleSearch = async (values: any) => {
    setLoading(true);
    try {
      const response = await axios.post<SearchCompoundResponse>('/api/pubchem/search/compound', {
        query: values.query,
        search_type: values.search_type || 'name'
      });

      if (response.data.compounds && response.data.compounds.length > 0) {
        setSearchResults(response.data.compounds);
        message.success(`找到 ${response.data.compounds.length} 个化合物`);
      } else {
        setSearchResults([]);
        message.info('未找到匹配的化合物');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取化合物详细信息
  const fetchCompoundDetails = async (cid: number) => {
    setDetailLoading(true);
    try {
      // 获取基本信息
      const basicInfo = await axios.get<CompoundDetailResponse>(`/api/pubchem/compound/${cid}`);
      
      // 并行获取其他信息
      const [bioactivity, patents, pharmacology] = await Promise.all([
        axios.get<BioactivityResponse>(`/api/pubchem/compound/${cid}/bioactivity`).catch(() => ({ data: { bioactivities: [] } })),
        axios.get<PatentsResponse>(`/api/pubchem/compound/${cid}/patents`).catch(() => ({ data: { patents: [] } })),
        axios.get<PharmacologyResponse>(`/api/pubchem/compound/${cid}/pharmacology`).catch(() => ({ data: { pharmacology: {} } }))
      ]);

      const detailedCompound: CompoundInfo = {
        ...basicInfo.data.compound,
        bioactivity: bioactivity.data.bioactivities,
        patents: patents.data.patents,
        pharmacology: pharmacology.data.pharmacology
      };

      setSelectedCompound(detailedCompound);
      setActiveTab('details');
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取化合物详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // 相似性搜索
  const handleSimilaritySearch = async () => {
    if (!selectedCompound) {
      message.warning('请先选择一个化合物');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post<SimilarCompoundsResponse>('/api/pubchem/search/similar', {
        smiles: selectedCompound.smiles,
        threshold: 0.8
      });

      if (response.data.similar_compounds) {
        setSearchResults(response.data.similar_compounds);
        message.success(`找到 ${response.data.similar_compounds.length} 个相似化合物`);
      }
    } catch (error) {
      console.error('相似性搜索失败:', error);
      message.error('相似性搜索失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染搜索结果表格
  const searchColumns = [
    {
      title: 'CID',
      dataIndex: 'cid',
      key: 'cid',
      width: 100,
      render: (cid: number) => (
        <Button type="link" onClick={() => fetchCompoundDetails(cid)}>
          {cid}
        </Button>
      )
    },
    {
      title: '化合物名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '分子式',
      dataIndex: 'molecular_formula',
      key: 'molecular_formula',
      width: 150
    },
    {
      title: '分子量',
      dataIndex: 'molecular_weight',
      key: 'molecular_weight',
      width: 120,
      render: (weight: number) => weight?.toFixed(2)
    },
    {
      title: 'SMILES',
      dataIndex: 'smiles',
      key: 'smiles',
      ellipsis: true,
      render: (smiles: string) => (
        <Tooltip title={smiles}>
          <Text copyable={{ text: smiles }}>
            {smiles?.substring(0, 30)}...
          </Text>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: CompoundInfo) => (
        <Space>
          <Button
            size="small"
            type="primary"
            onClick={() => fetchCompoundDetails(record.cid)}
          >
            查看详情
          </Button>
          <Button
            size="small"
            onClick={() => {
              setSelectedCompound(record);
              handleSimilaritySearch();
            }}
          >
            相似搜索
          </Button>
        </Space>
      )
    }
  ];

  // 渲染化合物详情
  const renderCompoundDetails = () => {
    if (!selectedCompound) return <Empty description="请选择一个化合物" />;

    return (
      <div>
        <Card title="基本信息" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="CID">
                  {selectedCompound.cid}
                </Descriptions.Item>
                <Descriptions.Item label="名称">
                  {selectedCompound.name}
                </Descriptions.Item>
                <Descriptions.Item label="分子式">
                  {selectedCompound.molecular_formula}
                </Descriptions.Item>
                <Descriptions.Item label="分子量">
                  {selectedCompound.molecular_weight?.toFixed(2)} g/mol
                </Descriptions.Item>
                <Descriptions.Item label="SMILES">
                  <Text copyable>{selectedCompound.smiles}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              {selectedCompound.properties?.structure_image && (
                <Image
                  src={selectedCompound.properties.structure_image}
                  alt="分子结构"
                  style={{ maxHeight: 200 }}
                />
              )}
            </Col>
          </Row>
        </Card>

        {selectedCompound.synonyms && selectedCompound.synonyms.length > 0 && (
          <Card title="同义词" style={{ marginBottom: 16 }}>
            <Space wrap>
              {selectedCompound.synonyms.slice(0, 10).map((syn, index) => (
                <Tag key={index}>{syn}</Tag>
              ))}
              {selectedCompound.synonyms.length > 10 && (
                <Tag>+{selectedCompound.synonyms.length - 10} 更多</Tag>
              )}
            </Space>
          </Card>
        )}

        {selectedCompound.bioactivity && selectedCompound.bioactivity.length > 0 && (
          <Card title="生物活性数据" style={{ marginBottom: 16 }}>
            <Table
              dataSource={selectedCompound.bioactivity}
              rowKey="aid"
              size="small"
              columns={[
                {
                  title: 'AID',
                  dataIndex: 'aid',
                  key: 'aid',
                  width: 100
                },
                {
                  title: '检测名称',
                  dataIndex: 'assay_name',
                  key: 'assay_name',
                  ellipsis: true
                },
                {
                  title: '活性',
                  dataIndex: 'activity',
                  key: 'activity',
                  width: 100,
                  render: (activity: string) => (
                    <Badge
                      status={activity === 'Active' ? 'success' : 'default'}
                      text={activity}
                    />
                  )
                },
                {
                  title: '数值',
                  dataIndex: 'value',
                  key: 'value',
                  width: 150
                }
              ]}
              pagination={{ pageSize: 5 }}
            />
          </Card>
        )}

        {selectedCompound.patents && selectedCompound.patents.length > 0 && (
          <Card title="专利信息" style={{ marginBottom: 16 }}>
            <List
              dataSource={selectedCompound.patents.slice(0, 5)}
              renderItem={(patent: any) => (
                <List.Item>
                  <List.Item.Meta
                    title={patent.title}
                    description={
                      <Space direction="vertical" size="small">
                        <Text type="secondary">专利号: {patent.patent_number}</Text>
                        <Text type="secondary">申请日期: {patent.filing_date}</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        )}

        {selectedCompound.pharmacology && (
          <Card title="药理学信息" style={{ marginBottom: 16 }}>
            <Collapse ghost>
              {selectedCompound.pharmacology.mechanism_of_action && (
                <Panel header="作用机制" key="1">
                  <Paragraph>{selectedCompound.pharmacology.mechanism_of_action}</Paragraph>
                </Panel>
              )}
              {selectedCompound.pharmacology.pharmacodynamics && (
                <Panel header="药效学" key="2">
                  <Paragraph>{selectedCompound.pharmacology.pharmacodynamics}</Paragraph>
                </Panel>
              )}
              {selectedCompound.pharmacology.absorption && (
                <Panel header="吸收" key="3">
                  <Paragraph>{selectedCompound.pharmacology.absorption}</Paragraph>
                </Panel>
              )}
              {selectedCompound.pharmacology.toxicity && (
                <Panel header="毒性" key="4">
                  <Alert
                    message="毒性信息"
                    description={selectedCompound.pharmacology.toxicity}
                    type="warning"
                    showIcon
                  />
                </Panel>
              )}
            </Collapse>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div>
      <Title level={2}>
        <GlobalOutlined /> PubChem 数据库搜索
      </Title>

      <Alert
        message="功能说明"
        description="通过PubChem数据库搜索化合物信息，包括结构、性质、生物活性、专利和药理学数据。支持名称、CAS号、SMILES等多种搜索方式。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="化合物搜索" key="search">
          <Card>
            <Form
              form={searchForm}
              onFinish={handleSearch}
              layout="vertical"
            >
              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item
                    name="query"
                    label="搜索关键词"
                    rules={[{ required: true, message: '请输入搜索关键词' }]}
                  >
                    <Input
                      placeholder="输入化合物名称、CAS号、SMILES或InChI"
                      size="large"
                      prefix={<SearchOutlined />}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="search_type"
                    label="搜索类型"
                    initialValue="name"
                  >
                    <Select size="large">
                      <Option value="name">名称</Option>
                      <Option value="cas">CAS号</Option>
                      <Option value="smiles">SMILES</Option>
                      <Option value="inchi">InChI</Option>
                      <Option value="formula">分子式</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SearchOutlined />}
                    size="large"
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={() => searchForm.resetFields()}
                    size="large"
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          {searchResults.length > 0 && (
            <Card title="搜索结果" style={{ marginTop: 16 }}>
              <Table
                dataSource={searchResults}
                columns={searchColumns}
                rowKey="cid"
                loading={loading}
                pagination={{ pageSize: 10 }}
              />
            </Card>
          )}
        </TabPane>

        <TabPane tab="化合物详情" key="details" disabled={!selectedCompound}>
          {detailLoading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" tip="正在加载化合物详情..." />
            </div>
          ) : (
            renderCompoundDetails()
          )}
        </TabPane>

        <TabPane tab="批量搜索" key="batch">
          <Card>
            <Alert
              message="批量搜索功能"
              description="支持批量输入多个化合物进行搜索，每行一个关键词"
              type="info"
              style={{ marginBottom: 16 }}
            />
            <Form
              onFinish={async (values) => {
                const queries = values.batch_queries.split('\n').filter((q: string) => q.trim());
                if (queries.length === 0) {
                  message.warning('请输入至少一个搜索关键词');
                  return;
                }
                
                setLoading(true);
                try {
                  const response = await axios.post<BatchSearchResponse>('/api/pubchem/batch/search', {
                    queries: queries,
                    search_type: values.batch_search_type
                  });
                  
                  if (response.data.results) {
                    const allCompounds = response.data.results.flatMap((r: any) => r.compounds || []);
                    setSearchResults(allCompounds);
                    message.success(`批量搜索完成，共找到 ${allCompounds.length} 个化合物`);
                  }
                } catch (error) {
                  console.error('批量搜索失败:', error);
                  message.error('批量搜索失败');
                } finally {
                  setLoading(false);
                }
              }}
            >
              <Form.Item
                name="batch_queries"
                label="批量搜索列表"
                rules={[{ required: true, message: '请输入搜索关键词' }]}
              >
                <Input.TextArea
                  rows={6}
                  placeholder="每行输入一个关键词，例如：&#10;阿司匹林&#10;50-78-2&#10;CC(=O)Oc1ccccc1C(=O)O"
                />
              </Form.Item>
              <Form.Item
                name="batch_search_type"
                label="搜索类型"
                initialValue="name"
              >
                <Select>
                  <Option value="name">名称</Option>
                  <Option value="cas">CAS号</Option>
                  <Option value="smiles">SMILES</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SearchOutlined />}
                >
                  批量搜索
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PubChemSearch; 