药物稳定性研究助手启动说明
============================

1. 启动后端服务:
   - 方法1: 在文件资源管理器中双击 "start_backend.bat" 文件
   - 方法2: 在PowerShell中运行 ".\start_backend.bat"
   - 等待看到 "Uvicorn running on http://127.0.0.1:8000" 消息
   - 保持命令窗口打开

2. 启动前端服务:
   - 方法1: 在文件资源管理器中双击 "start_frontend.bat" 文件
   - 方法2: 在PowerShell中运行 ".\start_frontend.bat"
   - 等待浏览器自动打开应用
   - 保持命令窗口打开

3. 访问应用:
   - 前端应用: http://localhost:3000
   - 后端API文档: http://127.0.0.1:8000/docs

4. 停止服务:
   - 在命令窗口中按 Ctrl+C
   - 关闭命令窗口

如果遇到问题:
- 确保已安装Node.js和Python
- 确保已安装所有必要的依赖
- 尝试重启电脑后再次运行
- 如果看到"'react-scripts'不是内部或外部命令"，请尝试在frontend目录中运行"npm install react-scripts --save"
- 如果使用PowerShell，记得在执行批处理文件时添加".\"前缀 