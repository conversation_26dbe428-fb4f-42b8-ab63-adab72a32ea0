#!/usr/bin/env python3
"""
前后端集成测试脚本
测试药物稳定性研究助手的主要功能
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_drug_info_search():
    """测试药物信息搜索"""
    print("\n🔍 测试药物信息搜索...")
    test_cases = [
        {"name": "aspirin", "expected": True},
        {"name": "阿司匹林", "expected": True},
        {"cas": "50-78-2", "expected": True},
    ]
    
    for case in test_cases:
        try:
            params = {k: v for k, v in case.items() if k != "expected"}
            response = requests.get(f"{BASE_URL}/drug-info/search", params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ 药物搜索成功: {params} -> {data.get('data', {}).get('name', 'Unknown')}")
                else:
                    print(f"⚠️ 药物搜索无结果: {params}")
            else:
                print(f"❌ 药物搜索失败: {params} -> {response.status_code}")
        except Exception as e:
            print(f"❌ 药物搜索异常: {params} -> {e}")

def test_project_operations():
    """测试项目操作"""
    print("\n🔍 测试项目操作...")
    
    # 1. 创建项目
    print("创建测试项目...")
    project_data = {
        "name": "集成测试项目",
        "description": "用于测试前后端集成的项目",
        "status": "进行中"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/projects", json=project_data, timeout=15)
        if response.status_code == 200:
            project = response.json()
            project_id = project.get("id")
            print(f"✅ 项目创建成功: ID={project_id}, Name={project.get('name')}")
            
            # 2. 获取项目列表
            print("获取项目列表...")
            response = requests.get(f"{BASE_URL}/projects", timeout=10)
            if response.status_code == 200:
                projects = response.json()
                print(f"✅ 获取项目列表成功: 共{len(projects)}个项目")
                
                # 3. 保存项目数据
                print("保存项目数据...")
                project_data = {
                    "drug_name": "阿司匹林",
                    "cas_number": "50-78-2",
                    "molecular_formula": "C9H8O4",
                    "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O",
                    "category": "解热镇痛药",
                    "description": "测试用药物数据",
                    "formulation": [
                        {"name": "微晶纤维素", "amount": "100mg"},
                        {"name": "硬脂酸镁", "amount": "2mg"}
                    ]
                }
                
                response = requests.post(f"{BASE_URL}/projects/{project_id}/save-data", 
                                       json=project_data, timeout=15)
                if response.status_code == 200:
                    print("✅ 项目数据保存成功")
                    
                    # 4. 获取项目数据
                    print("获取项目数据...")
                    response = requests.get(f"{BASE_URL}/projects/{project_id}/data", timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ 项目数据获取成功: {data.get('data', {}).get('drug_name', 'Unknown')}")
                    else:
                        print(f"❌ 项目数据获取失败: {response.status_code}")
                else:
                    print(f"❌ 项目数据保存失败: {response.status_code}")
            else:
                print(f"❌ 获取项目列表失败: {response.status_code}")
        else:
            print(f"❌ 项目创建失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 项目操作异常: {e}")

def test_excipient_database():
    """测试辅料数据库"""
    print("\n🔍 测试辅料数据库...")
    try:
        response = requests.get(f"{BASE_URL}/excipients/database", timeout=10)
        if response.status_code == 200:
            data = response.json()
            categories = data.get("categories", {})
            print(f"✅ 辅料数据库获取成功: {len(categories)}个类别")
            for category, items in categories.items():
                print(f"  - {category}: {len(items)}种辅料")
        else:
            print(f"❌ 辅料数据库获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 辅料数据库异常: {e}")

def test_pubchem_integration():
    """测试PubChem集成"""
    print("\n🔍 测试PubChem集成...")
    try:
        # 测试化合物搜索
        response = requests.post(f"{BASE_URL}/pubchem/search", 
                               json={"name": "aspirin"}, timeout=20)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ PubChem搜索成功: {data.get('name', 'Unknown')}")
        else:
            print(f"⚠️ PubChem搜索失败: {response.status_code}")
    except Exception as e:
        print(f"❌ PubChem集成异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始前后端集成测试")
    print("=" * 50)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        test_health_check,
        test_drug_info_search,
        test_project_operations,
        test_excipient_database,
        test_pubchem_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_func.__name__} - {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！前后端集成正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
