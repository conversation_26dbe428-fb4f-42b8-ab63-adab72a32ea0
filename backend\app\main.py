from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time
import logging

# 导入配置
try:
    from .config.database_config import init_database, get_db, SessionLocal
except ImportError:
    from .config.database import SessionLocal
    init_database = None
    get_db = None

try:
    from .config.security_config import security_config, rate_limiter, security_logger
except ImportError:
    security_config = None
    rate_limiter = None
    security_logger = None

try:
    from .config.monitoring_config import performance_monitor, alert_manager
except ImportError:
    performance_monitor = None
    alert_manager = None

try:
    from .config.ai_config import AIConfig
except ImportError:
    AIConfig = None

# 导入数据库种子
from .db_seeding import seed_excipients, seed_drugs

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 先定义lifespan函数
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动
    await startup_event()
    yield
    # 关闭
    logger.info("应用正在关闭...")

# 创建FastAPI应用
app = FastAPI(
    title="Drug Stability Assistant API",
    description="药物稳定性研究助手API",
    version="1.0.0",
    lifespan=lifespan
)

async def startup_event():
    """应用启动事件"""
    logger.info("正在启动药物稳定性研究助手API...")

    # 初始化数据库
    if init_database:
        init_database()
        logger.info("数据库初始化完成")

    # 数据库种子数据
    db = SessionLocal()
    try:
        seed_excipients(db)
        seed_drugs(db)
        logger.info("数据库种子数据加载完成")
    except Exception as e:
        logger.error(f"数据库种子数据加载失败: {e}")
    finally:
        db.close()

    # 验证AI配置
    if AIConfig:
        available_providers = AIConfig.get_available_providers()
        if available_providers:
            logger.info(f"可用的AI提供商: {[p.value for p in available_providers]}")
        else:
            logger.warning("没有配置可用的AI提供商")

    # 打印所有API路由
    logger.info("当前注册的API路由:")
    for route in app.routes:
        if hasattr(route, 'methods'):
            logger.info(f"  {route.path} [{','.join(route.methods)}]")

    logger.info("应用启动完成")



# 添加安全中间件
if security_config:
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=security_config.cors_origins,
        allow_credentials=True,
        allow_methods=security_config.cors_methods,
        allow_headers=security_config.cors_headers,
    )

    # 信任主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
    )
else:
    # 默认CORS配置
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 添加性能监控中间件
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()

    # 限流检查
    if rate_limiter:
        client_ip = request.client.host if request.client else "unknown"
        if not rate_limiter.is_allowed(client_ip):
            return JSONResponse(
                status_code=429,
                content={"detail": "请求过于频繁，请稍后再试"}
            )

    # 安全头检查
    response = await call_next(request)

    # 添加安全头
    if security_config:
        for header, value in security_config.security_headers.items():
            response.headers[header] = value

    # 记录性能指标
    if performance_monitor:
        process_time = time.time() - start_time
        performance_monitor.record_request(
            endpoint=str(request.url.path),
            method=request.method,
            response_time=process_time,
            status_code=response.status_code
        )

    # 记录访问日志
    if security_logger:
        client_ip = request.client.host if request.client else "unknown"
        security_logger.log_api_access(
            endpoint=str(request.url.path),
            method=request.method,
            ip_address=client_ip
        )

    return response

# 尝试导入API路由
try:
    from .api import api_router
    app.include_router(api_router, prefix="/api")
    print("✓ API路由已加载")
except ImportError as e:
    print(f"警告: API路由加载失败 - {e}")
    
    # 创建基本的健康检查端点
    @app.get("/api/health")
    async def health_check():
        if performance_monitor:
            return performance_monitor.get_health_status()
        return {"status": "healthy", "message": "API服务正常运行"}

    # 添加监控端点
    @app.get("/api/metrics")
    async def get_metrics():
        if performance_monitor:
            return performance_monitor.get_metrics_summary()
        return {"message": "监控功能未启用"}

    # 添加AI配置状态端点
    @app.get("/api/ai/status")
    async def get_ai_status():
        if AIConfig:
            return {
                "available_providers": [p.value for p in AIConfig.get_available_providers()],
                "default_provider": AIConfig.DEFAULT_PROVIDER.value,
                "validation_results": {p.value: v for p, v in AIConfig.validate_api_keys().items()}
            }
        return {"message": "AI配置未加载"}

# 根路由
@app.get("/")
async def root():
    return {
        "message": "Welcome to Drug Stability Assistant API",
        "version": "1.0.0",
        "status": "online"
    } 