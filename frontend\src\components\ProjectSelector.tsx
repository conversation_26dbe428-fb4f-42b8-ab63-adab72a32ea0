import React, { useContext } from 'react';
import { Select, Space, Typography, Tag, Button, message } from 'antd';
import { ProjectOutlined, SwapOutlined } from '@ant-design/icons';
import { ProjectContext } from '../App';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;
const { Option } = Select;

interface ProjectSelectorProps {
  showCreateButton?: boolean;
  style?: React.CSSProperties;
  size?: 'small' | 'middle' | 'large';
  placeholder?: string;
}

/**
 * 项目选择器组件
 * 用于在分析模块中切换项目
 */
const ProjectSelector: React.FC<ProjectSelectorProps> = ({
  showCreateButton = false,
  style = {},
  size = 'middle',
  placeholder = '选择项目'
}) => {
  const { projects, currentProject, setCurrentProject } = useContext(ProjectContext);
  const navigate = useNavigate();

  const handleProjectChange = (projectId: string) => {
    const selectedProject = projects.find(p => p.id === projectId);
    if (selectedProject) {
      setCurrentProject(selectedProject);
      message.success(`已切换到项目: ${selectedProject.name}`);
    }
  };

  const handleCreateProject = () => {
    navigate('/project-management');
  };

  return (
    <Space style={style}>
      <ProjectOutlined style={{ color: '#1890ff' }} />
      <Text strong>当前项目:</Text>
      <Select
        value={currentProject?.id}
        onChange={handleProjectChange}
        placeholder={placeholder}
        style={{ minWidth: 200 }}
        size={size}
        suffixIcon={<SwapOutlined />}
        showSearch
        filterOption={(input, option) => {
          if (!option || !option.value) return false;
          const project = projects.find(p => p.id === option.value);
          if (!project) return false;
          return project.name.toLowerCase().includes(input.toLowerCase());
        }}
        optionLabelProp="label"
      >
        {projects.map(project => (
          <Option
            key={project.id}
            value={project.id}
            label={project.name}
          >
            <Space>
              <Text>{project.name}</Text>
              <Tag
                color={project.status === '进行中' ? 'green' : 'blue'}
                style={{ fontSize: '12px', padding: '0 4px' }}
              >
                {project.status}
              </Tag>
            </Space>
          </Option>
        ))}
      </Select>
      {showCreateButton && (
        <Button 
          type="dashed" 
          size={size}
          onClick={handleCreateProject}
        >
          新建项目
        </Button>
      )}
    </Space>
  );
};

export default ProjectSelector;
