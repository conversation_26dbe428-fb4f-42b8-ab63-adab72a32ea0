import logging
from sqlalchemy.orm import Session
from app.models.excipient import ExcipientORM
from app.config.database import SessionLocal
from app.models.drug import DrugORM

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

COMMON_EXCIPIENTS = [
    {"name": "微晶纤维素", "function": "填充剂", "risk_level": "low", "remark": "常用的片剂填充剂，具有良好的流动性和压缩性"},
    {"name": "乳糖", "function": "填充剂", "risk_level": "low", "remark": "常用的片剂填充剂，溶解性好"},
    {"name": "硬脂酸镁", "function": "润滑剂", "risk_level": "low", "remark": "常用的片剂润滑剂，减少摩擦"},
    {"name": "聚维酮K30", "function": "黏合剂", "risk_level": "low", "remark": "常用的片剂黏合剂，增强颗粒强度"},
    {"name": "羟丙甲纤维素", "function": "崩解剂", "risk_level": "low", "remark": "常用的片剂崩解剂，促进片剂崩解"},
    {"name": "交联聚维酮", "function": "崩解剂", "risk_level": "low", "remark": "超级崩解剂，促进片剂快速崩解"},
    {"name": "二氧化硅", "function": "流动性改善剂", "risk_level": "low", "remark": "改善粉末流动性"},
    {"name": "淀粉", "function": "填充剂/崩解剂", "risk_level": "low", "remark": "多功能辅料，可作为填充剂和崩解剂"},
    {"name": "羟丙纤维素", "function": "黏合剂", "risk_level": "low", "remark": "常用的片剂黏合剂，可用于湿法制粒"},
    {"name": "甘露醇", "function": "填充剂", "risk_level": "low", "remark": "可用于口腔崩解片，口感好"},
]

COMMON_DRUGS = [
    {"name": "阿司匹林", "cas": "50-78-2", "formula": "C9H8O4", "smiles": "CC(=O)OC1=CC=CC=C1C(=O)O"},
    {"name": "对乙酰氨基酚", "cas": "103-90-2", "formula": "C8H9NO2", "smiles": "CC(=O)NC1=CC=C(C=C1)O"},
    {"name": "布洛芬", "cas": "15687-27-1", "formula": "C13H18O2", "smiles": "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O"}
]

def seed_excipients(db: Session):
    logger.info("Checking and seeding excipients...")
    try:
        existing_excipients_count = db.query(ExcipientORM).count()
        if existing_excipients_count == 0:
            logger.info("No excipients found, seeding database...")
            for exc_data in COMMON_EXCIPIENTS:
                db_excipient = ExcipientORM(**exc_data)
                db.add(db_excipient)
            db.commit()
            logger.info(f"Successfully seeded {len(COMMON_EXCIPIENTS)} common excipients.")
        else:
            logger.info("Excipients table is not empty, skipping seed.")
    except Exception as e:
        logger.error(f"Error seeding excipients: {e}")
        db.rollback()

def seed_drugs(db: Session):
    logger.info("Checking and seeding drugs...")
    try:
        existing_drugs_count = db.query(DrugORM).count()
        if existing_drugs_count == 0:
            logger.info("No drugs found, seeding database...")
            for drug_data in COMMON_DRUGS:
                db_drug = DrugORM(**drug_data)
                db.add(db_drug)
            db.commit()
            logger.info(f"Successfully seeded {len(COMMON_DRUGS)} common drugs.")
        else:
            logger.info("Drugs table is not empty, skipping seed.")
    except Exception as e:
        logger.error(f"Error seeding drugs: {e}")
        db.rollback()

if __name__ == "__main__":
    logger.info("Manual seeding process started.")
    db = SessionLocal()
    try:
        seed_excipients(db)
        seed_drugs(db)
    finally:
        db.close()
    logger.info("Manual seeding process finished.") 