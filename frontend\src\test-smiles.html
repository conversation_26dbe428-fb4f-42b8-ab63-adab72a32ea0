<!DOCTYPE html>
<html>
<head>
    <title>SMILES测试</title>
</head>
<body>
    <h1>SMILES API测试</h1>
    <div>
        <input type="text" id="smiles" placeholder="输入SMILES" value="CCO" />
        <button onclick="testValidate()">验证</button>
        <button onclick="testVisualize()">可视化</button>
        <button onclick="testProperties()">计算性质</button>
    </div>
    <div id="result"></div>
    
    <script>
        async function testValidate() {
            const smiles = document.getElementById('smiles').value;
            try {
                const response = await fetch('/api/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    },
                    body: JSON.stringify({ smiles })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function testVisualize() {
            const smiles = document.getElementById('smiles').value;
            try {
                const response = await fetch('/api/visualize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    },
                    body: JSON.stringify({ smiles, width: 300, height: 300 })
                });
                const data = await response.json();
                if (data.image) {
                    document.getElementById('result').innerHTML = '<img src="' + data.image + '" />';
                } else {
                    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function testProperties() {
            const smiles = document.getElementById('smiles').value;
            try {
                const response = await fetch('/api/properties', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    },
                    body: JSON.stringify({ smiles })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
