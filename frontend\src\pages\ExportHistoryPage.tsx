import React, { useEffect, useState } from 'react';
import { Card, Table, Button, Modal, message } from 'antd';
import { listExportHistoryDetail, getExportHistoryDetail, downloadExportFile, retryExport, ExportHistory } from '../api';
import { useTranslation } from 'react-i18next';

const ExportHistoryPage: React.FC = () => {
  const { t } = useTranslation();
  const [history, setHistory] = useState<ExportHistory[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [detail, setDetail] = useState<ExportHistory | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchHistory = async () => {
    setLoading(true);
    const res = await listExportHistoryDetail({ page, page_size: pageSize });
    setHistory(res.data.history);
    setTotal(res.data.total);
    setLoading(false);
  };

  useEffect(() => { fetchHistory(); }, [page]);

  const handleDownload = async (id: number) => {
    const res = await downloadExportFile(id);
    const url = window.URL.createObjectURL(new Blob([res.data as BlobPart]));
    const a = document.createElement('a');
    a.href = url;
    a.download = 'exported_file';
    a.click();
  };

  const handleRetry = async (id: number) => {
    await retryExport(id);
    message.success(t('已重新导出，稍后请刷新查看'));
    fetchHistory();
  };

  return (
    <Card title={t('导出历史详情')} style={{ margin: 24 }}>
      <Table dataSource={history} rowKey="id" loading={loading} pagination={{ current: page, pageSize, total, onChange: setPage }} bordered
        columns={[
          { title: t('导出时间'), dataIndex: 'export_time' },
          { title: t('文件名'), dataIndex: 'file_name' },
          { title: t('状态'), dataIndex: 'status' },
          { title: t('摘要'), dataIndex: 'summary' },
          { title: t('操作'), render: (_, item) => <span>
            <Button onClick={() => getExportHistoryDetail(item.id).then(res => setDetail(res.data))}>{t('详情')}</Button>
            <Button onClick={() => handleDownload(item.id)} style={{ marginLeft: 8 }}>{t('下载')}</Button>
            <Button onClick={() => handleRetry(item.id)} style={{ marginLeft: 8 }}>{t('重新导出')}</Button>
          </span> },
        ]}
      />
      <Modal open={!!detail} onCancel={() => setDetail(null)} footer={null} title={t('导出详情')} width={600}>
        {detail && (
          <div>
            <div><b>{t('导出时间')}：</b>{detail.export_time}</div>
            <div><b>{t('文件名')}：</b>{detail.file_name}</div>
            <div><b>{t('状态')}：</b>{detail.status}</div>
            <div><b>{t('摘要')}：</b>{detail.summary}</div>
            <div><b>{t('下载链接')}：</b><a href={detail.download_url} target="_blank" rel="noopener noreferrer">{t('下载')}</a></div>
            {detail.fail_reason && <div style={{ color: 'red' }}><b>{t('失败原因')}：</b>{detail.fail_reason}</div>}
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default ExportHistoryPage; 