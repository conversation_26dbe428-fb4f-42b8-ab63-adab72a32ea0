"""
知识库服务
加载和管理药物-辅料相互作用知识库
"""
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False
    print("警告: RDKit未安装，某些化学结构分析功能将不可用")

logger = logging.getLogger(__name__)

class KnowledgeBaseService:
    """知识库服务类"""
    
    def __init__(self):
        self.knowledge_base = None
        self.knowledge_file = (Path(__file__).parent.parent.parent / "data" / "functional_group_interactions.json").resolve()
        self.pharmacopoeia_file = Path("backend/data/pharmacopoeia_knowledge.json")
        self._load_knowledge_base()
        self._load_pharmacopoeia_knowledge()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        try:
            if self.knowledge_file.exists():
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_base = json.load(f)
                logger.info("知识库加载成功")
            else:
                logger.warning(f"知识库文件不存在: {self.knowledge_file}")
                self.knowledge_base = self._create_default_knowledge_base()
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            self.knowledge_base = self._create_default_knowledge_base()
    
    def _load_pharmacopoeia_knowledge(self):
        """加载药典知识"""
        try:
            if self.pharmacopoeia_file.exists():
                with open(self.pharmacopoeia_file, 'r', encoding='utf-8') as f:
                    self.pharmacopoeia_knowledge = json.load(f)
                logger.info("药典知识加载成功")
            else:
                self.pharmacopoeia_knowledge = {}
        except Exception as e:
            logger.error(f"加载药典知识失败: {e}")
            self.pharmacopoeia_knowledge = {}
    
    def _create_default_knowledge_base(self) -> Dict[str, Any]:
        """创建默认知识库"""
        return {
            "metadata": {
                "description": "默认知识库",
                "version": "1.0"
            },
            "functional_groups": {},
            "interaction_rules": [],
            "excipient_compatibility_matrix": {}
        }
    
    def identify_functional_groups(self, smiles: str) -> List[Dict[str, Any]]:
        """识别分子中的官能团"""
        if not RDKIT_AVAILABLE or not smiles:
            return []
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            if not mol:
                return []
            
            found_groups = []
            drug_groups = self.knowledge_base.get("functional_groups", {}).get("drug_groups", {})
            
            for group_id, group_info in drug_groups.items():
                smarts = group_info.get("smarts")
                if smarts:
                    pattern = Chem.MolFromSmarts(smarts)
                    if pattern and mol.HasSubstructMatch(pattern):
                        found_groups.append({
                            "id": group_id,
                            "name": group_info.get("name"),
                            "reactivity": group_info.get("reactivity"),
                            "common_reactions": group_info.get("common_reactions", [])
                        })
            
            return found_groups
            
        except Exception as e:
            logger.error(f"识别官能团失败: {e}")
            return []
    
    def get_excipient_group(self, excipient_name: str) -> Optional[str]:
        """获取辅料所属的化学类别"""
        excipient_groups = self.knowledge_base.get("functional_groups", {}).get("excipient_groups", {})
        
        for group_id, group_info in excipient_groups.items():
            examples = group_info.get("examples", [])
            if any(excipient_name in example or example in excipient_name 
                   for example in examples):
                return group_id
        
        # 基于名称的智能分类
        name_lower = excipient_name.lower()
        if any(sugar in name_lower for sugar in ["糖", "ose", "glucose", "lactose"]):
            return "reducing_sugar"
        elif any(metal in name_lower for metal in ["镁", "钙", "钠", "magnesium", "calcium"]):
            return "metal_salt"
        elif any(acid in name_lower for acid in ["酸", "acid"]):
            return "organic_acid"
        
        return None
    
    def predict_interactions(self, drug_groups: List[str], 
                           excipient_group: str) -> List[Dict[str, Any]]:
        """基于官能团预测相互作用"""
        predictions = []
        interaction_rules = self.knowledge_base.get("interaction_rules", [])
        
        for rule in interaction_rules:
            if (rule.get("drug_group") in drug_groups and 
                rule.get("excipient_group") == excipient_group):
                predictions.append({
                    "interaction_type": rule.get("interaction_type"),
                    "mechanism": rule.get("mechanism"),
                    "severity": rule.get("severity"),
                    "conditions": rule.get("conditions", []),
                    "consequences": rule.get("consequences", []),
                    "prevention": rule.get("prevention", []),
                    "references": rule.get("references", [])
                })
        
        return predictions
    
    def get_excipient_alternatives(self, excipient_name: str, 
                                 drug_groups: List[str]) -> List[Dict[str, Any]]:
        """获取辅料的替代建议"""
        matrix = self.knowledge_base.get("excipient_compatibility_matrix", {})
        excipient_info = matrix.get(excipient_name, {})
        
        # 检查是否有不相容的官能团
        incompatible_groups = excipient_info.get("incompatible_groups", [])
        has_incompatibility = any(group in drug_groups for group in incompatible_groups)
        
        if has_incompatibility:
            # 返回建议的替代品
            alternatives = []
            for alt_name in excipient_info.get("alternatives", []):
                alt_info = matrix.get(alt_name, {})
                alternatives.append({
                    "name": alt_name,
                    "reason": f"避免{excipient_info.get('reason', '相互作用')}",
                    "advantages": self._get_excipient_advantages(alt_name)
                })
            return alternatives
        
        return []
    
    def _get_excipient_advantages(self, excipient_name: str) -> List[str]:
        """获取辅料的优势"""
        # 基于辅料名称返回常见优势
        advantages_map = {
            "甘露醇": ["非还原糖，化学稳定", "不吸湿", "可作甜味剂"],
            "微晶纤维素": ["化学惰性", "相容性好", "多功能（填充、崩解、粘合）"],
            "淀粉": ["天然来源", "成本低", "安全性高"],
            "硬脂酸": ["酸性润滑剂", "避免碱催化反应"],
            "硬脂酰富马酸钠": ["不影响溶出", "用量少", "效果好"],
            "滑石粉": ["惰性", "不参与化学反应", "良好的滑动性"]
        }
        
        return advantages_map.get(excipient_name, ["相容性好"])
    
    def get_detailed_excipient_info(self, excipient_name: str) -> Dict[str, Any]:
        """获取辅料的详细信息，包括药典要求"""
        info = {
            "name": excipient_name,
            "category": self.get_excipient_group(excipient_name),
            "pharmacopoeia_info": {},
            "physical_properties": {},
            "stability_info": {},
            "applications": {}
        }
        
        # 从药典知识获取信息
        if excipient_name in self.pharmacopoeia_knowledge.get("common_excipients", {}):
            pharm_info = self.pharmacopoeia_knowledge["common_excipients"][excipient_name]
            info["pharmacopoeia_info"] = pharm_info
            
            # 提取关键理化性质
            if "usp" in pharm_info and "specifications" in pharm_info["usp"]:
                specs = pharm_info["usp"]["specifications"]
                info["physical_properties"] = {
                    "water_content": specs.get("water_content"),
                    "ph": specs.get("ph"),
                    "melting_point": specs.get("melting_point"),
                    "loss_on_drying": specs.get("loss_on_drying")
                }
            
            # 提取稳定性信息
            if "usp" in pharm_info:
                info["stability_info"] = {
                    "storage": pharm_info["usp"].get("storage"),
                    "incompatibilities": pharm_info["usp"].get("incompatibilities"),
                    "stability": pharm_info["usp"].get("stability", "一般稳定")
                }
            
            # 提取应用信息
            if "general_info" in pharm_info:
                info["applications"] = {
                    "common_uses": pharm_info["general_info"].get("common_uses", []),
                    "types": pharm_info["general_info"].get("types", [])
                }
        
        return info
    
    def validate_formulation_against_pharmacopoeia(self, 
                                                 excipient_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证配方是否符合药典要求"""
        validation_results = {
            "overall_compliance": True,
            "excipient_validations": [],
            "recommendations": [],
            "critical_parameters": []
        }
        
        for excipient in excipient_list:
            excipient_name = excipient.get("name")
            concentration = excipient.get("concentration", 0)
            
            # 获取详细信息
            detailed_info = self.get_detailed_excipient_info(excipient_name)
            
            # 验证浓度是否在推荐范围内
            validation = {
                "excipient": excipient_name,
                "compliant": True,
                "issues": []
            }
            
            # 检查药典要求
            if detailed_info["pharmacopoeia_info"]:
                # 这里可以添加具体的验证逻辑
                pass
            
            validation_results["excipient_validations"].append(validation)
        
        # 生成建议
        validation_results["recommendations"] = [
            "确保所有辅料符合药典标准",
            "定期检查辅料的COA",
            "建立辅料供应商审计制度"
        ]
        
        return validation_results
    
    def analyze_formulation_compatibility(self, drug_smiles: str, 
                                        excipient_names: List[str]) -> Dict[str, Any]:
        """分析整个配方的相容性"""
        # 识别药物官能团
        drug_groups = []
        if drug_smiles:
            groups = self.identify_functional_groups(drug_smiles)
            drug_groups = [g["id"] for g in groups]
        
        # 分析每个辅料
        compatibility_results = []
        overall_risk = "低"
        
        for excipient_name in excipient_names:
            excipient_group = self.get_excipient_group(excipient_name)
            
            # 获取详细信息
            detailed_info = self.get_detailed_excipient_info(excipient_name)
            
            if excipient_group and drug_groups:
                interactions = self.predict_interactions(drug_groups, excipient_group)
                
                if interactions:
                    # 找出最高风险
                    severities = [i["severity"] for i in interactions]
                    if "高" in severities:
                        risk = "高"
                        overall_risk = "高"
                    elif "中" in severities:
                        risk = "中"
                        if overall_risk == "低":
                            overall_risk = "中"
                    else:
                        risk = "低"
                    
                    compatibility_results.append({
                        "excipient": excipient_name,
                        "risk_level": risk,
                        "interactions": interactions,
                        "alternatives": self.get_excipient_alternatives(
                            excipient_name, drug_groups
                        ),
                        "pharmacopoeia_info": detailed_info["pharmacopoeia_info"]
                    })
                else:
                    compatibility_results.append({
                        "excipient": excipient_name,
                        "risk_level": "低",
                        "interactions": [],
                        "alternatives": [],
                        "pharmacopoeia_info": detailed_info["pharmacopoeia_info"]
                    })
            else:
                compatibility_results.append({
                    "excipient": excipient_name,
                    "risk_level": "未知",
                    "interactions": [],
                    "alternatives": [],
                    "pharmacopoeia_info": detailed_info["pharmacopoeia_info"]
                })
        
        return {
            "drug_functional_groups": [g["name"] for g in self.identify_functional_groups(drug_smiles)],
            "overall_risk": overall_risk,
            "excipient_assessments": compatibility_results,
            "recommendations": self._generate_formulation_recommendations(
                overall_risk, compatibility_results
            )
        }
    
    def _generate_formulation_recommendations(self, overall_risk: str, 
                                            results: List[Dict[str, Any]]) -> List[str]:
        """生成配方建议"""
        recommendations = []
        
        if overall_risk == "高":
            recommendations.append("配方存在高风险相互作用，强烈建议重新设计")
            
            # 找出高风险辅料
            high_risk = [r["excipient"] for r in results if r["risk_level"] == "高"]
            for excipient in high_risk:
                recommendations.append(f"替换{excipient}为更稳定的辅料")
        
        elif overall_risk == "中":
            recommendations.append("配方存在中等风险，建议进行详细的相容性试验")
            recommendations.append("考虑添加稳定剂或调整储存条件")
        
        else:
            recommendations.append("配方相容性良好，按标准条件进行稳定性试验即可")
        
        # 通用建议
        recommendations.extend([
            "进行加速稳定性试验验证",
            "监测关键质量属性（含量、有关物质、溶出度）",
            "考虑包装材料的影响",
            "确保所有辅料符合药典标准"
        ])
        
        return recommendations

# 创建全局实例
knowledge_base_service = KnowledgeBaseService() 