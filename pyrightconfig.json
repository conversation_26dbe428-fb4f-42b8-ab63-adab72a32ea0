{"include": ["backend"], "exclude": ["**/node_modules", "**/__pycache__", "frontend", "temp-app", "*.ps1", "*.bat", "*.md", "*.txt", "*.json", "*.yml", "*.yaml"], "extraPaths": ["backend"], "pythonVersion": "3.11", "pythonPlatform": "All", "typeCheckingMode": "basic", "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportGeneralTypeIssues": "none", "reportOptionalMemberAccess": "none", "reportOptionalSubscript": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportInconsistentConstructor": "none", "reportOverlappingOverloads": "none", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportInvalidStringEscapeSequence": "warning", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStubStatement": "none", "reportIncompleteStub": "none", "reportUnsupportedDunderAll": "none", "reportUnusedCoroutine": "none", "reportTypedDictNotRequiredAccess": "none", "reportPrivateImportUsage": "none", "reportCallIssue": "none", "reportArgumentType": "none", "reportAssignmentType": "none", "reportAttributeAccessIssue": "none", "reportReturnType": "none"}