from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON, Float, ForeignKey
from sqlalchemy.orm import relationship
import datetime
# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base

class ProjectORM(Base):
    __tablename__ = 'projects'
    
    # 基本信息
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    status = Column(String, default='新建')
    
    # 关联药物信息
    drug_id = Column(Integer, ForeignKey('drugs.id'), nullable=True)
    drug = relationship("DrugORM", back_populates="projects")
    
    # 制剂信息
    dosage_form = Column(String, nullable=True)
    strength = Column(String, nullable=True)
    excipients = Column(JSON, nullable=True)  # 存储辅料列表
    packaging_materials = Column(JSON, nullable=True)  # 包装材料信息
    
    # 稳定性数据
    stability_data = Column(JSON, nullable=True)  # 稳定性试验数据
    storage_conditions = Column(JSON, nullable=True)  # 储存条件
    
    # 预测设置
    prediction_settings = Column(JSON, nullable=True)  # 预测模型设置

    # 通用数据字段
    data = Column(JSON, nullable=True)  # 存储项目的通用数据

    # 时间戳
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 关系
    exports = relationship("ExportHistory", back_populates="project") 