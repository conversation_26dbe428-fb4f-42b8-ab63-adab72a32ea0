"""
包装材料数据库和服务
"""
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class DosageForm(Enum):
    """剂型枚举"""
    ORAL_SOLID = "口服固体制剂"
    ORAL_LIQUID = "口服液体制剂"
    INJECTION = "注射剂"
    INHALATION = "吸入制剂"
    TRANSDERMAL = "透皮制剂"
    TOPICAL = "外用制剂"
    OPHTHALMIC = "眼用制剂"
    NASAL = "鼻用制剂"

@dataclass
class PackagingMaterial:
    """包装材料数据类"""
    name: str
    category: str
    dosage_forms: List[str]
    material_type: str
    barrier_properties: Dict[str, str]
    compatibility: List[str]
    regulatory_status: str
    description: str
    advantages: List[str]
    disadvantages: List[str]
    typical_applications: List[str]

class PackagingService:
    """包装材料服务"""
    
    def __init__(self):
        self.packaging_database = {
            # 口服固体制剂包装
            "PVC/PVDC泡罩": PackagingMaterial(
                name="PVC/PVDC泡罩",
                category="泡罩包装",
                dosage_forms=[DosageForm.ORAL_SOLID.value],
                material_type="复合材料",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "良好",
                    "光阻隔": "一般"
                },
                compatibility=["大多数固体制剂"],
                regulatory_status="FDA/EMA批准",
                description="聚氯乙烯/聚偏二氯乙烯复合泡罩包装",
                advantages=["优异的阻隔性能", "成本适中", "易于加工"],
                disadvantages=["环保性较差", "透明度一般"],
                typical_applications=["片剂", "胶囊", "颗粒剂"]
            ),
            
            "铝塑泡罩": PackagingMaterial(
                name="铝塑泡罩",
                category="泡罩包装",
                dosage_forms=[DosageForm.ORAL_SOLID.value],
                material_type="复合材料",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "优秀",
                    "光阻隔": "优秀"
                },
                compatibility=["光敏性药物", "湿敏性药物"],
                regulatory_status="FDA/EMA批准",
                description="铝箔与塑料薄膜复合的泡罩包装",
                advantages=["全面阻隔性能", "保护性强"],
                disadvantages=["成本较高", "不透明"],
                typical_applications=["光敏药物", "高价值药物"]
            ),
            
            "HDPE瓶": PackagingMaterial(
                name="HDPE瓶",
                category="瓶装包装",
                dosage_forms=[DosageForm.ORAL_SOLID.value, DosageForm.ORAL_LIQUID.value],
                material_type="塑料",
                barrier_properties={
                    "水蒸气阻隔": "良好",
                    "氧气阻隔": "一般",
                    "光阻隔": "一般"
                },
                compatibility=["大多数药物"],
                regulatory_status="FDA/EMA批准",
                description="高密度聚乙烯瓶",
                advantages=["化学稳定性好", "成本低", "可回收"],
                disadvantages=["阻隔性有限", "可能有渗透性"],
                typical_applications=["片剂", "胶囊", "糖浆"]
            ),
            
            # 注射剂包装
            "硼硅玻璃安瓿": PackagingMaterial(
                name="硼硅玻璃安瓿",
                category="安瓿包装",
                dosage_forms=[DosageForm.INJECTION.value],
                material_type="玻璃",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "优秀",
                    "光阻隔": "可选择"
                },
                compatibility=["所有注射剂"],
                regulatory_status="USP Type I",
                description="低硼硅酸盐玻璃安瓿",
                advantages=["化学惰性", "完全阻隔", "透明"],
                disadvantages=["易碎", "成本高"],
                typical_applications=["小容量注射剂", "疫苗"]
            ),
            
            "硼硅玻璃西林瓶": PackagingMaterial(
                name="硼硅玻璃西林瓶",
                category="西林瓶包装",
                dosage_forms=[DosageForm.INJECTION.value],
                material_type="玻璃",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "优秀",
                    "光阻隔": "可选择"
                },
                compatibility=["冻干制剂", "液体注射剂"],
                regulatory_status="USP Type I",
                description="硼硅玻璃西林瓶配丁基胶塞",
                advantages=["可重复使用", "密封性好", "适合冻干"],
                disadvantages=["成本高", "需要胶塞"],
                typical_applications=["冻干粉针", "大容量注射剂"]
            ),
            
            "COC预充式注射器": PackagingMaterial(
                name="COC预充式注射器",
                category="预充式包装",
                dosage_forms=[DosageForm.INJECTION.value],
                material_type="塑料",
                barrier_properties={
                    "水蒸气阻隔": "良好",
                    "氧气阻隔": "良好",
                    "光阻隔": "一般"
                },
                compatibility=["生物制品", "蛋白质药物"],
                regulatory_status="FDA/EMA批准",
                description="环烯烃共聚物预充式注射器",
                advantages=["不易破碎", "低蛋白吸附", "使用方便"],
                disadvantages=["成本高", "阻隔性有限"],
                typical_applications=["生物制品", "单剂量注射剂"]
            ),
            
            # 吸入制剂包装
            "铝制MDI罐": PackagingMaterial(
                name="铝制MDI罐",
                category="气雾剂包装",
                dosage_forms=[DosageForm.INHALATION.value],
                material_type="金属",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "优秀",
                    "光阻隔": "优秀"
                },
                compatibility=["气雾剂", "推进剂"],
                regulatory_status="FDA/EMA批准",
                description="铝制定量吸入器罐体",
                advantages=["耐压性好", "完全阻隔", "可回收"],
                disadvantages=["成本高", "需要特殊阀门"],
                typical_applications=["哮喘药物", "COPD药物"]
            ),
            
            "塑料DPI装置": PackagingMaterial(
                name="塑料DPI装置",
                category="干粉吸入器",
                dosage_forms=[DosageForm.INHALATION.value],
                material_type="塑料",
                barrier_properties={
                    "水蒸气阻隔": "良好",
                    "氧气阻隔": "一般",
                    "光阻隔": "一般"
                },
                compatibility=["干粉制剂"],
                regulatory_status="FDA/EMA批准",
                description="塑料干粉吸入器装置",
                advantages=["使用简便", "成本适中", "可设计性强"],
                disadvantages=["湿度敏感", "静电问题"],
                typical_applications=["干粉吸入剂", "胶囊型吸入剂"]
            ),
            
            # 透皮制剂包装
            "铝塑复合袋": PackagingMaterial(
                name="铝塑复合袋",
                category="袋装包装",
                dosage_forms=[DosageForm.TRANSDERMAL.value],
                material_type="复合材料",
                barrier_properties={
                    "水蒸气阻隔": "优秀",
                    "氧气阻隔": "优秀",
                    "光阻隔": "优秀"
                },
                compatibility=["贴剂", "凝胶剂"],
                regulatory_status="FDA/EMA批准",
                description="铝箔与塑料薄膜复合袋",
                advantages=["优异阻隔性", "密封性好", "成本适中"],
                disadvantages=["不可重复使用", "撕裂风险"],
                typical_applications=["透皮贴剂", "外用贴膏"]
            ),
            
            # 眼用制剂包装
            "LDPE滴眼瓶": PackagingMaterial(
                name="LDPE滴眼瓶",
                category="滴眼瓶",
                dosage_forms=[DosageForm.OPHTHALMIC.value],
                material_type="塑料",
                barrier_properties={
                    "水蒸气阻隔": "一般",
                    "氧气阻隔": "一般",
                    "光阻隔": "一般"
                },
                compatibility=["眼用溶液", "眼用混悬液"],
                regulatory_status="FDA/EMA批准",
                description="低密度聚乙烯滴眼瓶",
                advantages=["柔软易挤压", "成本低", "安全性好"],
                disadvantages=["阻隔性有限", "可能有渗透"],
                typical_applications=["滴眼液", "洗眼液"]
            )
        }
    
    def get_packaging_by_dosage_form(self, dosage_form: str) -> List[PackagingMaterial]:
        """根据剂型获取适用的包装材料"""
        suitable_packaging = []
        for material in self.packaging_database.values():
            if dosage_form in material.dosage_forms:
                suitable_packaging.append(material)
        return suitable_packaging
    
    def get_packaging_by_category(self, category: str) -> List[PackagingMaterial]:
        """根据包装类别获取包装材料"""
        return [material for material in self.packaging_database.values() 
                if material.category == category]
    
    def search_packaging(self, query: str) -> List[PackagingMaterial]:
        """搜索包装材料"""
        results = []
        query_lower = query.lower()
        
        for material in self.packaging_database.values():
            if (query_lower in material.name.lower() or 
                query_lower in material.description.lower() or
                any(query_lower in app.lower() for app in material.typical_applications)):
                results.append(material)
        
        return results
    
    def get_all_dosage_forms(self) -> List[str]:
        """获取所有剂型"""
        return [form.value for form in DosageForm]
    
    def get_all_categories(self) -> List[str]:
        """获取所有包装类别"""
        categories = set()
        for material in self.packaging_database.values():
            categories.add(material.category)
        return sorted(list(categories))
    
    def get_packaging_recommendations(self, 
                                    dosage_form: str, 
                                    drug_properties: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据剂型和药物性质推荐包装材料"""
        suitable_materials = self.get_packaging_by_dosage_form(dosage_form)
        recommendations = []
        
        for material in suitable_materials:
            score = 0
            reasons = []
            
            # 根据药物性质评分
            if drug_properties.get('light_sensitive', False):
                if material.barrier_properties.get('光阻隔') in ['优秀', '良好']:
                    score += 2
                    reasons.append("适合光敏性药物")
            
            if drug_properties.get('moisture_sensitive', False):
                if material.barrier_properties.get('水蒸气阻隔') in ['优秀', '良好']:
                    score += 2
                    reasons.append("适合湿敏性药物")
            
            if drug_properties.get('oxygen_sensitive', False):
                if material.barrier_properties.get('氧气阻隔') in ['优秀', '良好']:
                    score += 2
                    reasons.append("适合氧敏性药物")
            
            # 成本考虑
            if 'cost' in drug_properties:
                if drug_properties['cost'] == 'low' and '成本低' in material.advantages:
                    score += 1
                    reasons.append("成本经济")
                elif drug_properties['cost'] == 'high' and '保护性强' in material.advantages:
                    score += 1
                    reasons.append("高保护性能")
            
            recommendations.append({
                'material': material,
                'score': score,
                'reasons': reasons
            })
        
        # 按评分排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations
    
    def export_packaging_database(self) -> Dict[str, Any]:
        """导出包装材料数据库"""
        return {
            name: {
                'name': material.name,
                'category': material.category,
                'dosage_forms': material.dosage_forms,
                'material_type': material.material_type,
                'barrier_properties': material.barrier_properties,
                'compatibility': material.compatibility,
                'regulatory_status': material.regulatory_status,
                'description': material.description,
                'advantages': material.advantages,
                'disadvantages': material.disadvantages,
                'typical_applications': material.typical_applications
            }
            for name, material in self.packaging_database.items()
        }

# 全局服务实例
packaging_service = PackagingService() 