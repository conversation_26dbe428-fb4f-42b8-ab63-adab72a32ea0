from sqlalchemy import Column, String, DateTime, Float, <PERSON><PERSON>an, JSO<PERSON>, Text, Integer, ForeignKey, create_engine
from sqlalchemy.orm import relationship, sessionmaker
import datetime
from .base import Base

class ExcipientORM(Base):
    __tablename__ = 'excipients'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    function = Column(String, nullable=True)  # 功能，如"崩解剂"、"黏合剂"等
    amount = Column(Float, nullable=True)  # 用量
    risk_level = Column(String, nullable=True)  # 风险等级
    remark = Column(String, nullable=True)  # 备注
    
    # 基本信息
    chemical_name = Column(String, nullable=True)  # 化学名称
    cas = Column(String, nullable=True)  # CAS号
    smiles = Column(String, nullable=True)  # SMILES结构
    inchi = Column(String, nullable=True)  # InChI标识符
    structure_image_url = Column(String, nullable=True)  # 结构图URL
    
    # 物理化学性质
    molecular_weight = Column(Float, nullable=True)  # 分子量
    ph = Column(Float, nullable=True)  # pH值(1%水溶液)
    solubility = Column(String, nullable=True)  # 溶解性
    hygroscopicity = Column(String, nullable=True)  # 吸湿性
    melting_point = Column(Float, nullable=True)  # 熔点(°C)
    
    # 稳定性相关
    thermal_stability = Column(String, nullable=True)  # 热稳定性
    light_stability = Column(String, nullable=True)  # 光稳定性
    moisture_sensitivity = Column(Boolean, nullable=True)  # 湿敏感性
    oxygen_sensitivity = Column(Boolean, nullable=True)  # 氧敏感性
    
    # 杂质信息
    impurities = Column(JSON, nullable=True)  # 杂质信息
    
    # 相容性数据
    known_incompatibilities = Column(JSON, nullable=True)  # 已知不相容性
    compatibility_data = Column(JSON, nullable=True)  # 相容性数据
    
    # 功能团标记
    has_reducing_sugar = Column(Boolean, nullable=True)  # 含有还原糖
    has_aldehyde = Column(Boolean, nullable=True)  # 含有醛基
    has_peroxide = Column(Boolean, nullable=True)  # 含有过氧化物
    has_metal_ion = Column(Boolean, nullable=True)  # 含有金属离子
    has_acidic_group = Column(Boolean, nullable=True)  # 含有酸性基团
    has_basic_group = Column(Boolean, nullable=True)  # 含有碱性基团
    
    # 供应商和批次信息
    supplier = Column(String, nullable=True)  # 供应商
    grade = Column(String, nullable=True)  # 等级，如"药用级"、"食品级"
    specifications = Column(JSON, nullable=True)  # 规格
    
    # 监管信息
    regulatory_status = Column(JSON, nullable=True)  # 监管状态
    
    description = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class ExcipientBatchORM(Base):
    """辅料批次表"""
    __tablename__ = 'excipient_batches'
    id = Column(String, primary_key=True, index=True)
    excipient_id = Column(String, ForeignKey("excipients.id"), nullable=False)
    batch_number = Column(String, nullable=False)  # 批号
    supplier = Column(String, nullable=True)  # 供应商
    manufacturing_date = Column(DateTime, nullable=True)  # 生产日期
    expiry_date = Column(DateTime, nullable=True)  # 有效期
    
    # 批次特定属性
    ph = Column(Float, nullable=True)  # pH值
    moisture_content = Column(Float, nullable=True)  # 水分含量(%)
    particle_size = Column(JSON, nullable=True)  # 粒度分布
    specific_surface_area = Column(Float, nullable=True)  # 比表面积(m²/g)
    bulk_density = Column(Float, nullable=True)  # 堆密度(g/mL)
    
    # 杂质分析
    impurity_profile = Column(JSON, nullable=True)  # 杂质谱
    heavy_metals = Column(Float, nullable=True)  # 重金属含量(ppm)
    residual_solvents = Column(JSON, nullable=True)  # 残留溶剂
    
    # 稳定性数据
    stability_data = Column(JSON, nullable=True)  # 稳定性数据
    
    # 证书和文档
    coa_url = Column(String, nullable=True)  # 分析证书URL
    msds_url = Column(String, nullable=True)  # 安全数据表URL
    
    notes = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class ExcipientCompatibilityORM(Base):
    """辅料相容性表"""
    __tablename__ = 'excipient_compatibilities'
    id = Column(String, primary_key=True, index=True)
    excipient_id = Column(String, ForeignKey("excipients.id"), nullable=False)
    drug_id = Column(String, ForeignKey("drugs.id"), nullable=False)
    
    # 相容性评估
    compatibility_level = Column(String, nullable=False)  # 相容性等级：兼容/不兼容/条件兼容
    risk_level = Column(String, nullable=True)  # 风险等级：高/中/低
    risk_type = Column(String, nullable=True)  # 风险类型，如"水解"、"氧化"等
    
    # 实验条件
    temperature = Column(Float, nullable=True)  # 温度(°C)
    humidity = Column(Float, nullable=True)  # 湿度(%)
    duration = Column(Integer, nullable=True)  # 持续时间(天)
    ph = Column(Float, nullable=True)  # pH值
    
    # 实验结果
    observations = Column(Text, nullable=True)  # 观察结果
    analytical_results = Column(JSON, nullable=True)  # 分析结果
    degradation_products = Column(JSON, nullable=True)  # 降解产物
    
    # 证据和建议
    evidence = Column(JSON, nullable=True)  # 证据
    recommendations = Column(Text, nullable=True)  # 建议
    references = Column(JSON, nullable=True)  # 参考文献
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

class ExcipientInteraction(Base):
    __tablename__ = 'excipient_interactions'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    risk_with = Column(String, nullable=False)
    reaction = Column(String, nullable=False)
    risk_level = Column(String, nullable=False) # e.g., "low", "medium", "high"
    source = Column(String, nullable=True)

# Function to add the hardcoded data to the database
def populate_initial_interactions(session):
    interactions = [
        {"name": "乳糖", "risk_with": "含伯胺药物,含仲胺药物", "reaction": "Maillard反应", "risk_level": "medium"},
        {"name": "硬脂酸镁", "risk_with": "阿司匹林,萘普生", "reaction": "形成配合物，影响溶出", "risk_level": "low"},
        {"name": "聚维酮", "risk_with": "酚类药物", "reaction": "形成复合物", "risk_level": "low"},
        {"name": "交联聚维酮", "risk_with": "碱性药物", "reaction": "可能产生过氧化物", "risk_level": "medium"}
    ]
    for item in interactions:
        exists = session.query(ExcipientInteraction).filter_by(name=item['name'], reaction=item['reaction']).first()
        if not exists:
            interaction = ExcipientInteraction(**item)
            session.add(interaction)
    session.commit()

# Example of how to set up the database and populate it
if __name__ == '__main__':
    engine = create_engine('sqlite:///./test.db') # Use your actual database URL
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db_session = SessionLocal()
    populate_initial_interactions(db_session)
    db_session.close()

# 兼容旧接口：定义Excipient为ExcipientORM的别名
Excipient = ExcipientORM 