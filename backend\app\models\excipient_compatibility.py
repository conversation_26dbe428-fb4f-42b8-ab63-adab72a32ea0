"""
辅料兼容性模型
定义药物与辅料的兼容性评估相关的数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
from enum import Enum
import datetime

# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base


class ExcipientCompatibilityORM(Base):
    """辅料兼容性表"""
    __tablename__ = 'excipient_compatibility'
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    drug_id = Column(String, ForeignKey('drugs.id'), nullable=False)
    excipient_id = Column(String, ForeignKey('excipients.id'), nullable=False)
    compatibility_level = Column(Integer, default=0)  # 0-兼容, 1-轻度不兼容, 2-中度不兼容, 3-重度不兼容
    risk_type = Column(String, nullable=True)  # 风险类型 (水解、氧化、光降解等)
    mechanism = Column(Text, nullable=True)  # 降解机理描述
    recommendation = Column(Text, nullable=True)  # 建议措施
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 关系
    drug = relationship("DrugORM", back_populates="excipient_compatibilities")
    excipient = relationship("ExcipientORM", back_populates="drug_compatibilities")


# 为DrugORM和ExcipientORM添加关系
from app.models.drug import DrugORM
from app.models.excipient import ExcipientORM

# 添加关系定义(如果不存在)
if not hasattr(DrugORM, 'excipient_compatibilities'):
    DrugORM.excipient_compatibilities = relationship("ExcipientCompatibilityORM", back_populates="drug")

if not hasattr(ExcipientORM, 'drug_compatibilities'):
    ExcipientORM.drug_compatibilities = relationship("ExcipientCompatibilityORM", back_populates="excipient") 