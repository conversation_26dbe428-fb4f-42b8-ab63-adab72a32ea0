# Assistant for Drug Stability Study - 前端文档

## 项目简介
本项目为"药物制剂稳定性研究助手"前端，基于 React + TypeScript，支持多语言、无障碍、批量导入、AI建议、报告导出等核心功能，工程化与用户体验俱佳。

## 目录结构
```
frontend/
├── src/
│   ├── api.ts                # API请求封装
│   ├── components/           # 通用组件（批量导入、导出历史、主题切换等）
│   ├── pages/                # 主要页面（数据输入、项目管理、项目详情、用户中心等）
│   ├── utils/                # 工具函数（导出、国际化等）
│   ├── i18n.ts               # 国际化配置
│   ├── theme.ts              # 主题色配置
│   ├── App.tsx               # 路由与全局入口
│   └── ...
├── public/                   # 静态资源
├── package.json              # 依赖与脚本
└── ...
```

## 主要功能模块
- **数据输入页**：药物/辅料/环境/稳定性数据Tab，批量导入、联网识别、结构图预览。
- **项目管理页**：项目增删查改、批量导入、筛选、二次确认。
- **项目详情页**：分析、报告、AI建议Tab，ECharts趋势图，报告导出（PDF/Word）、内容自定义、导出历史、内容预览。
- **AI建议Tab**：AI建议卡片、溯源详情、用户反馈、历史追踪、国际化。
- **导出历史**：导出记录、重命名、内容预览、删除、国际化。
- **用户中心/个性化设置**：主题色切换、模拟修改密码。
- **国际化/无障碍**：react-i18next全量覆盖，aria属性、键盘导航、屏幕阅读器友好。

## 主要组件说明
- `BatchImportModal`：通用批量导入弹窗，支持模板下载、导入历史、错误报告导出。
- `ExportHistoryList`：导出历史列表，支持重命名、预览、删除。
- `ThemeSwitcher`：主题色切换。

## 主要API说明
- `/api/identify`：药物信息联网识别。
- `/api/projects`：项目列表、详情。
- `/api/report/export`：报告导出（PDF/Word）。
- `/api/report/exports`：导出历史列表。
- `/api/ai/suggestions`：AI建议。

## 开发/测试/构建指令
```bash
# 安装依赖
npm install
# 启动开发环境
npm start
# 运行单元测试
npm test
# 代码风格检查
npm run lint
# 构建生产包
npm run build
```

## 工程化与最佳实践
- TypeScript全量类型提示，JSDoc自动注释。
- ESLint/Prettier自动格式化，Conventional Commits。
- 单元测试覆盖主要业务模块（导出、AI建议、历史等）。
- 国际化与无障碍全流程覆盖。
- 代码结构清晰，模块化、可维护性强。
- 支持CI/CD自动化。

## 国际化与无障碍
- 所有界面、交互、导出内容均支持中英文切换。
- 主要交互元素均有aria-label、aria-modal、tabIndex等属性，支持屏幕阅读器和键盘导航。

## 贡献与维护
- 代码提交请遵循Conventional Commits规范。
- 重要变更请补充类型、注释、测试。
- 详细API与组件注释见源码JSDoc。

---
如需后端API文档、详细设计文档或有其他协作需求，请联系项目维护者。
