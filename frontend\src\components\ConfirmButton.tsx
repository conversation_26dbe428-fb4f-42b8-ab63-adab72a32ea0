import React from 'react';
import { Button, Modal } from 'antd';

interface ConfirmButtonProps {
  onConfirm: () => void;
  children?: React.ReactNode;
  confirmText?: string;
  okText?: string;
  cancelText?: string;
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  danger?: boolean;
  style?: React.CSSProperties;
  icon?: React.ReactNode;
  size?: 'large' | 'middle' | 'small';
}

const ConfirmButton: React.FC<ConfirmButtonProps> = ({
  onConfirm,
  children,
  confirmText = '确定要执行此操作吗？',
  okText = '确定',
  cancelText = '取消',
  type = 'default',
  danger = false,
  style,
  icon,
  size = 'middle'
}) => {
  const [open, setOpen] = React.useState(false);

  const handleOk = () => {
    setOpen(false);
    onConfirm();
  };

  return (
    <>
      <Button
        type={type}
        danger={danger}
        style={style}
        icon={icon}
        size={size}
        onClick={() => setOpen(true)}
      >
        {children}
      </Button>
      <Modal
        open={open}
        onOk={handleOk}
        onCancel={() => setOpen(false)}
        okText={okText}
        cancelText={cancelText}
        title="请确认"
      >
        {confirmText}
      </Modal>
    </>
  );
};

export default ConfirmButton; 