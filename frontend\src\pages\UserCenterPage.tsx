import React, { useState, useEffect, useContext } from 'react';
import { Card, Form, Input, Button, message, List, Modal } from 'antd';
import { jwtDecode } from 'jwt-decode';
import ThemeSwitcher from '../components/ThemeSwitcher';
import { apiFetch, login, getCurrentUser, listOperationLogs, listExportHistory, submitFeedback, LoginRequest } from '../api';
import { useTranslation } from 'react-i18next';
import { RoleContext } from '../App';

const UserCenterPage: React.FC = () => {
  const token = localStorage.getItem('token');
  const [username, setUsername] = useState('');
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [logs, setLogs] = useState<{id:number;time:string;action:string;detail:string}[]>([]);
  const [apiKeys, setApiKeys] = useState<{key:string;enabled:boolean;created_at:string}[]>([]);
  const [logPage, setLogPage] = useState(0);
  const LOG_PAGE_SIZE = 10;
  const { t } = useTranslation();
  const roleCtx = useContext(RoleContext);
  const [user, setUser] = useState<any>(null);
  const [exportHistory, setExportHistory] = useState<any[]>([]);
  const [loginModal, setLoginModal] = useState(false);
  const [loginForm, setLoginForm] = useState<LoginRequest>({ username: '', password: '' });
  const [feedback, setFeedback] = useState('');
  const [feedbackModal, setFeedbackModal] = useState(false);

  useEffect(() => {
    if (token) {
      try {
        const payload: any = jwtDecode(token);
        setUsername(payload.sub || '');
        setRole(payload.role || '');
      } catch {}
    }
  }, [token]);

  useEffect(() => {
    getCurrentUser().then(res => setUser(res.data as any));
    listOperationLogs().then(res => setLogs(res.data as any[]));
    listExportHistory().then(res => setExportHistory(res.data as any[]));
  }, []);

  const fetchLogs = async (page: number) => {
    const res = await apiFetch<{id:number;time:string;action:string;detail:string}[]>(`/api/user/operation-logs?skip=${page*LOG_PAGE_SIZE}&limit=${LOG_PAGE_SIZE}`);
    if(Array.isArray(res)) setLogs(res);
  };

  useEffect(() => {
    apiFetch<{apiKey:string}>('/api/user/api-key').then(res => { if(res && res.apiKey) setApiKey(res.apiKey); });
    fetchLogs(logPage);
    apiFetch<{key:string;enabled:boolean;created_at:string}[]>('/api/user/api-keys').then(res => { if(Array.isArray(res)) setApiKeys(res); });
    apiFetch<{role: string, permissions: string[]}>('/api/user/permissions').then(res => {
      if(res) setRole(res.role);
      // 可扩展：setPermissions(res.permissions)
    });
  }, [logPage]);

  const onFinish = async (values: { oldPassword: string; newPassword: string }) => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      message.success('密码修改成功（模拟）');
    }, 1200);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey);
    message.success('API Key已复制');
  };

  const handleReset = async () => {
    const res = await apiFetch<{apiKey:string}>('/api/user/api-key/reset', { method: 'POST' });
    if(res && res.apiKey) setApiKey(res.apiKey);
    message.success('API Key已重置');
  };

  const handleCreateKey = async () => {
    const res = await apiFetch<{key:string;enabled:boolean;created_at:string}>('/api/user/api-keys', { method: 'POST' });
    if(res && res.key) setApiKeys(keys => [...keys, res]);
  };

  const handleDisableKey = async (key: string) => {
    await apiFetch('/api/user/api-keys/disable', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key }) });
    setApiKeys(keys => keys.map(k => k.key === key ? { ...k, enabled: false } : k));
  };

  const handleLogin = async () => {
    try {
      const { data } = await login(loginForm);
      localStorage.setItem('token', data.access_token);
      message.success('登录成功');
      setLoginModal(false);
      getCurrentUser().then(res => setUser(res.data));
    } catch {
      message.error('登录失败');
    }
  };

  const handleFeedback = async () => {
    await submitFeedback({ content: feedback });
    message.success('反馈已提交');
    setFeedback('');
    setFeedbackModal(false);
  };

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Card title="用户中心">
        <ThemeSwitcher />
        <div style={{ marginBottom: 16 }}>
          {user ? <span>当前用户：{user.username}</span> : <Button onClick={() => setLoginModal(true)}>登录</Button>}
        </div>
        <Button onClick={() => setFeedbackModal(true)} style={{ marginBottom: 16 }}>提交反馈</Button>
        <Button onClick={() => window.location.href='/role-permission'} style={{ marginBottom: 16, marginLeft: 8 }}>权限管理</Button>
        <Button onClick={() => window.location.href='/operation-log'} style={{ marginBottom: 16, marginLeft: 8 }}>操作日志</Button>
        <Button onClick={() => window.location.href='/export-history'} style={{ marginBottom: 16, marginLeft: 8 }}>导出历史</Button>
        <div style={{ marginBottom: 16 }}>角色：{roleCtx || role}</div>
        <div style={{ marginBottom: 16, color: '#888', fontSize: 13 }}>
          权限说明：{(roleCtx || role) === 'admin' ? '可新建、编辑、删除项目，管理数据和用户。' : '可查看和编辑项目，部分操作受限。'}
        </div>
        <Form onFinish={onFinish} layout="vertical">
          <Form.Item name="oldPassword" label={t('原密码')} rules={[{ required: true, message: t('请输入原密码') }]}> <Input.Password aria-label={t('原密码')} /> </Form.Item>
          <Form.Item name="newPassword" label={t('新密码')} rules={[{ required: true, message: t('请输入新密码') }]}> <Input.Password aria-label={t('新密码')} /> </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block style={{ borderRadius: 6 }} aria-label={t('修改密码')}>{t('修改密码')}</Button>
          </Form.Item>
        </Form>
        <div style={{ marginTop: 32 }} aria-label={t('API Key管理')}>
          <h3>{t('API Key管理')}</h3>
          <div style={{ marginBottom: 8 }}>
            <Button size="small" onClick={handleCreateKey} aria-label={t('新建API Key')}>{t('新建API Key')}</Button>
          </div>
          <ul>
            {apiKeys.map(k => (
              <li key={k.key} style={{ color: k.enabled ? '#333' : '#aaa' }}>
                <span style={{ fontFamily: 'monospace', background: '#f6f8fa', padding: '4px 12px', borderRadius: 4 }}>{k.key}</span>
                <span style={{ marginLeft: 8 }}>{k.created_at}</span>
                <span style={{ marginLeft: 8, color: k.enabled ? '#388e3c' : '#aaa' }}>{k.enabled ? t('可用') : t('已禁用')}</span>
                <span style={{ marginLeft: 8, color: '#1976d2', fontWeight: 500 }}>{role === 'admin' ? t('可写') : t('只读')}</span>
                {!k.enabled && <span style={{ color: '#d32f2f', marginLeft: 8 }}>{t('已禁用')}</span>}
                {k.enabled && <Button size="small" onClick={() => handleDisableKey(k.key)} aria-label={t('禁用API Key') + ':' + k.key}>{t('禁用')}</Button>}
              </li>
            ))}
          </ul>
          <h3 style={{ marginTop: 24 }} aria-label={t('操作日志')}>{t('操作日志')}</h3>
          <div style={{ marginBottom: 8 }}>
            <Button size="small" onClick={() => setLogPage(p => Math.max(0, p-1))} disabled={logPage===0}>{t('上一页')}</Button>
            <Button size="small" onClick={() => setLogPage(p => p+1)}>{t('下一页')}</Button>
            <span style={{ marginLeft: 8 }}>{t('第')} {logPage+1} {t('页')}</span>
          </div>
          <List dataSource={logs} renderItem={item => <List.Item>{item.time} - {item.action} - {item.detail}</List.Item>} />
        </div>
        <h3 style={{ marginTop: 24 }}>{t('权限管理')}</h3>
        <div style={{ color: '#888', fontSize: 13 }}>{t('当前角色')}：{role}，{t('权限管理功能开发中，后续支持更细粒度角色、操作授权、日志审计等。')}</div>
        <h3>导出历史</h3>
        <List dataSource={exportHistory} renderItem={item => <List.Item>{item.export_time} - {item.file_name} - {item.status}</List.Item>} />
      </Card>
      <Modal open={loginModal} onCancel={() => setLoginModal(false)} onOk={handleLogin} okText="登录" cancelText="取消">
        <Input placeholder="用户名" value={loginForm.username} onChange={e => setLoginForm(f => ({ ...f, username: e.target.value }))} style={{ marginBottom: 8 }} />
        <Input.Password placeholder="密码" value={loginForm.password} onChange={e => setLoginForm(f => ({ ...f, password: e.target.value }))} />
      </Modal>
      <Modal open={feedbackModal} onCancel={() => setFeedbackModal(false)} onOk={handleFeedback} okText="提交" cancelText="取消">
        <Input.TextArea rows={4} value={feedback} onChange={e => setFeedback(e.target.value)} placeholder="请输入您的反馈..." />
      </Modal>
    </div>
  );
};

export default UserCenterPage; 