# 开发环境启动脚本
# 用于快速启动前后端服务

Write-Host "=== 药物稳定性研究助手 - 开发环境启动 ===" -ForegroundColor Green
Write-Host "启动时间: $(Get-Date)" -ForegroundColor Yellow

# 检查当前目录
$currentDir = Get-Location
Write-Host "当前目录: $currentDir" -ForegroundColor Cyan

# 检查必要的目录是否存在
if (-not (Test-Path "backend")) {
    Write-Host "错误: 未找到backend目录，请确保在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "frontend")) {
    Write-Host "错误: 未找到frontend目录，请确保在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 检查Python环境 ===" -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Python，请确保Python已安装并在PATH中" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 检查Node.js环境 ===" -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>&1
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
    $npmVersion = npm --version 2>&1
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Node.js，请确保Node.js已安装并在PATH中" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 启动后端服务 ===" -ForegroundColor Yellow
Write-Host "正在启动FastAPI服务..." -ForegroundColor Cyan

# 启动后端服务（在新窗口中）
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:currentDir
    Set-Location backend
    
    # 激活虚拟环境（如果存在）
    if (Test-Path "venv\Scripts\Activate.ps1") {
        & "venv\Scripts\Activate.ps1"
        Write-Host "虚拟环境已激活" -ForegroundColor Green
    }
    
    # 启动FastAPI服务
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
}

Write-Host "后端服务启动中... (Job ID: $($backendJob.Id))" -ForegroundColor Green

# 等待后端服务启动
Write-Host "等待后端服务启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# 检查后端服务是否启动成功
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8001/docs" -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ 后端服务启动成功: http://localhost:8001" -ForegroundColor Green
    Write-Host "✅ API文档地址: http://localhost:8001/docs" -ForegroundColor Green
} catch {
    Write-Host "⚠️  后端服务可能还在启动中，请稍后手动检查" -ForegroundColor Yellow
}

Write-Host "`n=== 启动前端服务 ===" -ForegroundColor Yellow
Write-Host "正在启动React开发服务器..." -ForegroundColor Cyan

# 启动前端服务（在新窗口中）
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:currentDir
    Set-Location frontend
    
    # 检查node_modules是否存在
    if (-not (Test-Path "node_modules")) {
        Write-Host "正在安装依赖..." -ForegroundColor Yellow
        npm install
    }
    
    # 启动React开发服务器
    npm start
}

Write-Host "前端服务启动中... (Job ID: $($frontendJob.Id))" -ForegroundColor Green

# 等待前端服务启动
Write-Host "等待前端服务启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# 检查前端服务是否启动成功
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ 前端服务启动成功: http://localhost:3000" -ForegroundColor Green
} catch {
    Write-Host "⚠️  前端服务可能还在启动中，请稍后手动检查" -ForegroundColor Yellow
}

Write-Host "`n=== 服务状态总结 ===" -ForegroundColor Green
Write-Host "后端服务: http://localhost:8001" -ForegroundColor Cyan
Write-Host "前端服务: http://localhost:3000" -ForegroundColor Cyan
Write-Host "API文档: http://localhost:8001/docs" -ForegroundColor Cyan

Write-Host "`n=== 使用说明 ===" -ForegroundColor Yellow
Write-Host "1. 前端应用: 在浏览器中访问 http://localhost:3000"
Write-Host "2. API文档: 在浏览器中访问 http://localhost:8001/docs"
Write-Host "3. 停止服务: 关闭PowerShell窗口或按Ctrl+C"
Write-Host "4. 查看日志: 检查各自的终端窗口"

Write-Host "`n=== 快速测试 ===" -ForegroundColor Yellow
Write-Host "建议测试流程:"
Write-Host "1. 访问项目管理页面: http://localhost:3000/projects"
Write-Host "2. 创建一个测试项目"
Write-Host "3. 选择项目并跳转到数据输入页面"
Write-Host "4. 测试删除功能"

Write-Host "`n=== 开发环境启动完成 ===" -ForegroundColor Green
Write-Host "所有服务已启动，可以开始开发工作！" -ForegroundColor Green

# 保持脚本运行，显示服务状态
Write-Host "`n按任意键退出监控..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 清理作业
Remove-Job $backendJob -Force -ErrorAction SilentlyContinue
Remove-Job $frontendJob -Force -ErrorAction SilentlyContinue
