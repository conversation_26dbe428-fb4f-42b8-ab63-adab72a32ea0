/* 侧边栏专用样式 */

.sidebar-container {
  height: 100vh;
  overflow: hidden;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-bottom: 1px solid var(--border-light);
  background: var(--content-background);
  overflow: hidden;
  box-sizing: border-box;
  min-width: 0;
}

.sidebar-header.collapsed {
  justify-content: center;
  padding: 0 8px;
}

.sidebar-header.expanded {
  justify-content: flex-start;
  padding: 0 12px;
}

.sidebar-logo {
  font-size: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
  transition: all 0.2s ease;
  width: 20px;
  text-align: center;
}

.sidebar-logo.collapsed {
  margin-right: 0;
  width: 20px;
}

.sidebar-logo.expanded {
  margin-right: 6px;
  width: 20px;
}

.sidebar-title {
  margin-bottom: 0 !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  line-height: 1.2 !important;
  transition: all 0.2s ease;
  max-width: calc(100% - 32px);
}

/* 菜单样式优化 */
.sidebar-menu {
  border-right: none !important;
  background: var(--content-background) !important;
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu .ant-menu-item {
  margin: 4px 8px !important;
  border-radius: var(--border-radius) !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-menu .ant-menu-item-selected {
  background: rgba(24, 144, 255, 0.1) !important;
  color: var(--primary-color) !important;
}

.sidebar-menu .ant-menu-item:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  color: var(--primary-color) !important;
}

.sidebar-menu .ant-menu-submenu-title {
  margin: 4px 8px !important;
  border-radius: var(--border-radius) !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-menu .ant-menu-submenu-title:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  color: var(--primary-color) !important;
}

.sidebar-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--primary-color) !important;
}

.sidebar-menu .ant-menu-item a {
  color: inherit;
  text-decoration: none;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-menu .ant-menu-item-icon {
  font-size: 16px !important;
  margin-right: 8px !important;
  min-width: 16px;
}

/* 折叠状态下的特殊处理 */
.sidebar-menu.collapsed .ant-menu-item,
.sidebar-menu.collapsed .ant-menu-submenu-title {
  padding: 0 calc(50% - 8px) !important;
  text-align: center;
}

.sidebar-menu.collapsed .ant-menu-item-icon {
  margin-right: 0 !important;
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-header {
    padding: 0 12px;
  }
  
  .sidebar-logo {
    font-size: 20px;
  }
  
  .sidebar-title {
    font-size: 14px !important;
  }
  
  .sidebar-menu .ant-menu-item,
  .sidebar-menu .ant-menu-submenu-title {
    height: 36px !important;
    line-height: 36px !important;
    font-size: 13px !important;
    margin: 2px 6px !important;
  }
  
  .sidebar-menu .ant-menu-item-icon {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .sidebar-header {
    height: 56px;
    padding: 0 8px;
  }
  
  .sidebar-logo {
    font-size: 18px;
    margin-right: 6px;
  }
  
  .sidebar-title {
    font-size: 13px !important;
  }
  
  .sidebar-menu {
    height: calc(100vh - 56px);
  }
  
  .sidebar-menu .ant-menu-item,
  .sidebar-menu .ant-menu-submenu-title {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 12px !important;
    margin: 2px 4px !important;
  }
}

/* 动画效果 */
.sidebar-header,
.sidebar-logo,
.sidebar-title,
.sidebar-menu .ant-menu-item,
.sidebar-menu .ant-menu-submenu-title {
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 深色主题适配 */
[data-theme="dark"] .sidebar-header {
  background: var(--content-background);
  border-bottom-color: var(--border-light);
}

[data-theme="dark"] .sidebar-menu {
  background: var(--content-background) !important;
}

[data-theme="dark"] .sidebar-menu .ant-menu-item:hover,
[data-theme="dark"] .sidebar-menu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.08) !important;
}

[data-theme="dark"] .sidebar-menu .ant-menu-item-selected {
  background: rgba(24, 144, 255, 0.2) !important;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .sidebar-header {
    border-bottom-width: 2px;
  }
  
  .sidebar-menu .ant-menu-item,
  .sidebar-menu .ant-menu-submenu-title {
    border: 1px solid transparent;
  }
  
  .sidebar-menu .ant-menu-item:hover,
  .sidebar-menu .ant-menu-submenu-title:hover {
    border-color: var(--primary-color);
  }
  
  .sidebar-menu .ant-menu-item-selected {
    border-color: var(--primary-color);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .sidebar-header,
  .sidebar-logo,
  .sidebar-title,
  .sidebar-menu .ant-menu-item,
  .sidebar-menu .ant-menu-submenu-title {
    transition: none;
  }
}
