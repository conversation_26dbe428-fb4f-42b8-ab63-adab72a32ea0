from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
import datetime
from .base import Base

class AISuggestionFeedbackORM(Base):
    __tablename__ = 'ai_suggestion_feedbacks'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, nullable=True)
    feedbacks = Column(JSON, nullable=False)  # 存储反馈内容列表
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    # 可扩展更多字段，如项目ID、AI建议ID等
