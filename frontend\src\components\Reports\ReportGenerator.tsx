import React, { useState } from 'react';
import {
  Card,
  Button,
  Select,
  Switch,
  Form,
  Space,
  Alert,
  Modal,
  Spin,
  Result,
  Tag,
  Row,
  Col,
  Divider,
  message
} from 'antd';
import {
  FileTextOutlined,
  DownloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;

interface ReportGeneratorProps {
  analysisData?: any;
  onReportGenerated?: (report: any) => void;
}

interface ReportPreview {
  title: string;
  drug_name: string;
  sections: Array<{
    title: string;
    content: string[];
  }>;
}

interface GeneratedReport {
  success: boolean;
  filename: string;
  pdf_data: string;
  size: number;
  generated_at: string;
  error?: string;
}

const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  analysisData,
  onReportGenerated
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState<ReportPreview | null>(null);
  const [generatedReport, setGeneratedReport] = useState<GeneratedReport | null>(null);

  // 获取报告模板
  const reportTemplates = [
    { id: 'stability_standard', name: '标准稳定性报告' },
    { id: 'stability_simple', name: '简化稳定性报告' },
    { id: 'compatibility_focus', name: '相容性专项报告' }
  ];

  // 预览报告
  const handlePreview = async () => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      
      const response = await axios.post<ReportPreview>('/api/report/preview', {
        analysis_data: analysisData,
        language: values.language,
        include_raw_data: values.includeRawData
      });

      setPreviewData(response.data);
      setPreviewVisible(true);
    } catch (error) {
      const axiosError = error as any;
      message.error('预览失败：' + (axiosError.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 生成报告
  const handleGenerate = async () => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      
      const response = await axios.post<GeneratedReport>('/api/report/generate', {
        analysis_data: analysisData,
        language: values.language,
        include_raw_data: values.includeRawData,
        report_type: values.template
      });

      if (response.data.success) {
        setGeneratedReport(response.data);
        message.success('报告生成成功！');
        
        if (onReportGenerated) {
          onReportGenerated(response.data);
        }
      } else {
        message.error('报告生成失败：' + response.data.error);
      }
    } catch (error) {
      const axiosError = error as any;
      message.error('生成失败：' + (axiosError.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 下载报告
  const handleDownload = () => {
    if (!generatedReport) return;

    try {
      // 将Base64转换为Blob
      const pdfData = atob(generatedReport.pdf_data);
      const pdfArray = new Uint8Array(pdfData.length);
      for (let i = 0; i < pdfData.length; i++) {
        pdfArray[i] = pdfData.charCodeAt(i);
      }
      const blob = new Blob([pdfArray], { type: 'application/pdf' });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = generatedReport.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('报告下载成功！');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      message.error('下载失败：' + errorMessage);
    }
  };

  // 渲染预览内容
  const renderPreview = () => {
    if (!previewData) return null;

    return (
      <div>
        <h2>{previewData.title}</h2>
        <p><strong>药物名称：</strong>{previewData.drug_name}</p>
        <Divider />
        {previewData.sections.map((section, index) => (
          <div key={index} style={{ marginBottom: 24 }}>
            <h3>{section.title}</h3>
            <ul>
              {section.content.map((item, idx) => (
                <li key={idx}>{item}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card 
      title={
        <Space>
          <FileTextOutlined />
          <span>报告生成</span>
        </Space>
      }
      extra={
        generatedReport && (
          <Tag icon={<CheckCircleOutlined />} color="success">
            已生成
          </Tag>
        )
      }
    >
      {!analysisData ? (
        <Alert
          message="无分析数据"
          description="请先完成药物稳定性分析，然后再生成报告。"
          type="warning"
          showIcon
        />
      ) : (
        <Spin spinning={loading}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              template: 'stability_standard',
              language: 'zh',
              includeRawData: false
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="报告模板"
                  name="template"
                  rules={[{ required: true, message: '请选择报告模板' }]}
                >
                  <Select>
                    {reportTemplates.map(template => (
                      <Option key={template.id} value={template.id}>
                        {template.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="报告语言"
                  name="language"
                  rules={[{ required: true, message: '请选择报告语言' }]}
                >
                  <Select>
                    <Option value="zh">中文</Option>
                    <Option value="en">English</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="includeRawData"
              valuePropName="checked"
            >
              <Switch /> 包含原始数据附录
            </Form.Item>

            <Divider />

            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                icon={<EyeOutlined />}
                onClick={handlePreview}
              >
                预览报告
              </Button>
              <Button
                type="primary"
                icon={<FileTextOutlined />}
                onClick={handleGenerate}
              >
                生成报告
              </Button>
              {generatedReport && (
                <Button
                  type="default"
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载PDF
                </Button>
              )}
            </Space>
          </Form>

          {generatedReport && (
            <Card
              style={{ marginTop: 24 }}
              size="small"
              title="生成结果"
            >
              <Row gutter={16}>
                <Col span={8}>
                  <div>
                    <small style={{ color: '#999' }}>文件名</small>
                    <p>{generatedReport.filename}</p>
                  </div>
                </Col>
                <Col span={8}>
                  <div>
                    <small style={{ color: '#999' }}>文件大小</small>
                    <p>{(generatedReport.size / 1024).toFixed(2)} KB</p>
                  </div>
                </Col>
                <Col span={8}>
                  <div>
                    <small style={{ color: '#999' }}>生成时间</small>
                    <p>{new Date(generatedReport.generated_at).toLocaleString()}</p>
                  </div>
                </Col>
              </Row>
            </Card>
          )}
        </Spin>
      )}

      {/* 预览模态框 */}
      <Modal
        title="报告预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button key="generate" type="primary" onClick={() => {
            setPreviewVisible(false);
            handleGenerate();
          }}>
            生成报告
          </Button>
        ]}
      >
        {renderPreview()}
      </Modal>
    </Card>
  );
};

export default ReportGenerator; 