from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class ExportHistory(Base):
    """导出历史记录模型"""
    __tablename__ = "export_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    project_id = Column(String, ForeignKey("projects.id"), nullable=True)
    file_name = Column(String, nullable=False)
    file_type = Column(String, nullable=False)  # csv, xlsx, pdf, etc.
    file_path = Column(String, nullable=True)
    file_size = Column(Integer, nullable=True)  # in bytes
    export_time = Column(DateTime, default=datetime.now)
    is_deleted = Column(Boolean, default=False)
    
    # 关系
    user = relationship("UserORM", back_populates="exports")
    project = relationship("ProjectORM", back_populates="exports")
    
    def __repr__(self):
        return f"<ExportHistory(id={self.id}, file_name='{self.file_name}', export_time={self.export_time})>" 