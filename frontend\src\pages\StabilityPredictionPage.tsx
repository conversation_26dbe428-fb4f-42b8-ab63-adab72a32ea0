import React, { useState, useContext } from 'react';
import { Card, Tabs, Table, Button, Row, Col, Tag, Modal, Radio, Input } from 'antd';
import { Line } from '@ant-design/charts';
import { ProjectContext } from '../App';
import { message } from 'antd';
import { predictStabilityV2, StabilityInput, StabilityPredictionResult, AISuggestion } from '../api';
import { useTranslation } from 'react-i18next';

const tabs = [
  { key: 'accelerated', label: '加速试验' },
  { key: 'intermediate', label: '中间条件' },
  { key: 'longterm', label: '长期试验' },
  { key: 'zone4', label: 'Zone IV' },
];
const chartData = [
  { time: '0月', value: 100 },
  { time: '3月', value: 98 },
  { time: '6月', value: 95 },
  { time: '12月', value: 90 },
];
const tableData = [
  { key: 1, time: '0月', value: 100, risk: '低' },
  { key: 2, time: '3月', value: 98, risk: '低' },
  { key: 3, time: '6月', value: 95, risk: '低' },
  { key: 4, time: '12月', value: 90, risk: '高' },
];
const params = { 温度: '40°C', 湿度: '75%', 包装: '铝箔', 批次: '202406' };

const StabilityPredictionPage: React.FC = () => {
  const { t } = useTranslation();
  const [tab, setTab] = useState('accelerated');
  const [modalOpen, setModalOpen] = useState(false);
  const [exportModal, setExportModal] = useState(false);
  const [exportFormat, setExportFormat] = useState('pdf');
  const { currentProject, inputData, analysisResult } = useContext(ProjectContext);
  const [aiSuggestions, setAiSuggestions] = useState<AISuggestion[]>([]);
  const [batches, setBatches] = useState<string[]>(['批次A', '批次B']);
  const [timePoints, setTimePoints] = useState<number[]>([0, 1, 3, 6, 12]);
  const [items, setItems] = useState<string[]>(['含量', '杂质']);
  const [result, setResult] = useState<StabilityPredictionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<StabilityInput>({ drug_name: '', excipients: [], process: '', packaging: '', environment: '', history_data: [] });

  React.useEffect(() => {
    if (currentProject?.id) {
      fetch(`/api/ai/suggestions?project_id=${currentProject.id}&lang=zh`).then(r => r.json()).then(data => setAiSuggestions(data || []));
    }
  }, [currentProject]);

  const handlePredict = async () => {
    if (!form.drug_name || !form.excipients.length || !form.process || !form.packaging || !form.environment) {
      message.warning('请完整填写所有信息');
      return;
    }
    setLoading(true);
    try {
      const res = await predictStabilityV2(form);
      setResult(res?.data || null);
    } catch (e) {
      message.error('预测失败: ' + (e instanceof Error ? e.message : '未知错误'));
    }
    setLoading(false);
  };

  return (
    <div style={{ padding: 24, fontFamily: 'SimSun, serif', background: '#f6f8fa', minHeight: '100vh' }}>
      <Card>
        <div style={{ marginBottom: 16, color: '#1976d2' }}>
          当前项目：{currentProject?.name || '无'} | 输入数据：{JSON.stringify(inputData)}
        </div>
        <div style={{ marginBottom: 16, color: analysisResult?.error ? 'red' : '#388e3c' }}>
          分析结果：{analysisResult?.error ? analysisResult.error : JSON.stringify(analysisResult)}
        </div>
        <Tabs activeKey={tab} onChange={setTab} items={tabs.map(t => ({ key: t.key, label: t.label, children: (
          <Row gutter={24}>
            <Col xs={24} md={16}>
              <Card title="预测结果图表" bordered style={{ marginBottom: 24 }}>
                <Line data={chartData} xField="time" yField="value" point={{ size: 5 }} />
              </Card>
              <Card title="预测结果表格" bordered>
                <Table columns={[
                  { title: '时间点', dataIndex: 'time', key: 'time' },
                  { title: '预测值', dataIndex: 'value', key: 'value' },
                  { title: '风险', dataIndex: 'risk', key: 'risk', render: (risk: string) => risk === '高' ? <Tag color="red">高</Tag> : <Tag color="green">低</Tag> },
                ]} dataSource={tableData} pagination={false} />
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <div style={{ background: '#f5f7fa', padding: 16, borderRadius: 8, marginBottom: 16 }}>
                <h3>分析参数摘要</h3>
                <ul>
                  <li>项目名：{currentProject?.name || '-'}</li>
                  <li>药物：{inputData?.drugName || inputData?.name || '-'}</li>
                  <li>环境：{inputData?.env || '-'}</li>
                  <li>批次：{inputData?.batch || '-'}</li>
                  <li>温度：{inputData?.temperature || params.温度}</li>
                  <li>湿度：{inputData?.humidity || params.湿度}</li>
                  <li>包装：{inputData?.package || params.包装}</li>
                </ul>
              </div>
              <Button onClick={() => setExportModal(true)} style={{ marginRight: 8 }}>导出报告</Button>
              <Button onClick={() => setModalOpen(true)} style={{ marginBottom: 16 }}>查看AI建议</Button>
            </Col>
          </Row>
        ) }))} />
        <div style={{ marginBottom: 16 }}>
          <Input
            value={batches.join(',')}
            onChange={e => setBatches(e.target.value.split(',').map(s => s.trim()))}
            placeholder="输入批次名，用逗号分隔"
            style={{ width: 300, marginRight: 8 }}
          />
          <Input
            value={timePoints.join(',')}
            onChange={e => setTimePoints(e.target.value.split(',').map(s => Number(s.trim())).filter(n => !isNaN(n)))}
            placeholder="输入时间点(月)，用逗号分隔"
            style={{ width: 200, marginRight: 8 }}
          />
          <Input
            value={items.join(',')}
            onChange={e => setItems(e.target.value.split(',').map(s => s.trim()))}
            placeholder="输入项目，用逗号分隔"
            style={{ width: 200, marginRight: 8 }}
          />
          <Button type="primary" onClick={handlePredict} loading={loading}>预测</Button>
        </div>
        {result && (
          <div style={{ marginTop: 32 }}>
            <h3>预测结果</h3>
            <div>长期（Long-term）t90: <span style={{color: result.prediction.long_term.t90 < 12 ? 'red' : '#388e3c', fontWeight: 600}}>{result.prediction.long_term.t90}</span>月 (置信区间: {result.prediction.long_term.ci[0]}~{result.prediction.long_term.ci[1]})</div>
            <div>加速（Accelerated）t90: {result.prediction.accelerated.t90}月 (置信区间: {result.prediction.accelerated.ci[0]}~{result.prediction.accelerated.ci[1]})</div>
            <div>法规比对: <span style={{color: result.regulatory_check.includes('不符合') ? 'red' : '#388e3c', fontWeight: 600}}>{result.regulatory_check}</span></div>
            <h4>敏感性分析（变量影响权重排序）</h4>
            <Table
              dataSource={result.sensitivity.sort((a, b) => b.impact - a.impact)}
              columns={[
                { title: '影响因子', dataIndex: 'factor' },
                { title: '影响程度', dataIndex: 'impact' },
              ]}
              rowKey={r => r.factor}
              pagination={false}
            />
            <h4>模型可解释性（SHAP值等）</h4>
            <div style={{fontFamily: 'monospace', color: '#1976d2'}}>{result.explain}</div>
          </div>
        )}
      </Card>
      <Modal open={exportModal} onCancel={() => setExportModal(false)} onOk={async () => {
        setExportModal(false);
        const res = await fetch('/api/report/export', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ project_id: currentProject?.id, options: ['全部'], lang: 'zh', format: exportFormat })
        });
        const data = await res.json();
        window.open(data.url, '_blank');
      }}>
        <Radio.Group value={exportFormat} onChange={e => setExportFormat(e.target.value)}>
          <Radio value="pdf">PDF</Radio>
          <Radio value="word">Word</Radio>
        </Radio.Group>
      </Modal>
      <Modal open={modalOpen} onCancel={() => setModalOpen(false)} footer={null}>
        <h3>AI建议</h3>
        <ul>
          {aiSuggestions.map((s, i) => <li key={i}>{String(s.title)}：{String(s.desc)}</li>)}
        </ul>
      </Modal>
    </div>
  );
};

export default StabilityPredictionPage;