import { exportReportPDF, exportReportWord } from './exportReport';

jest.mock('jspdf', () => {
  return function () {
    return {
      setFont: jest.fn(),
      setFontSize: jest.fn(),
      save: jest.fn(),
      autoTable: jest.fn(),
    };
  };
});

describe('exportReportPDF', () => {
  it('should export PDF with selected sections (mock)', () => {
    // 只测试不抛错，主流程调用
    expect(() => exportReportPDF({
      projectName: 'Test',
      exportOptions: ['raw', 'conclusion', 'chart', 'params', 'ai'],
      chartImage: undefined,
      params: { 项目名: 'Test', 状态: '进行中', 创建时间: '2024-06-01' },
      aiSuggestions: [{ title: 'AI1', desc: 'desc1', risk: '高' }],
      username: 'user',
      lang: 'zh',
    })).not.toThrow();
  });

  it('should support English export', () => {
    expect(() => exportReportPDF({
      projectName: 'Test',
      exportOptions: ['raw', 'conclusion'],
      lang: 'en',
    })).not.toThrow();
  });
});

describe('exportReportWord', () => {
  it('should export Word with selected sections (mock)', async () => {
    await expect(exportReportWord({
      projectName: 'Test',
      exportOptions: ['raw', 'conclusion', 'chart', 'params', 'ai'],
      chartImage: undefined,
      params: { 项目名: 'Test', 状态: '进行中', 创建时间: '2024-06-01' },
      aiSuggestions: [{ title: 'AI1', desc: 'desc1', risk: '高' }],
      username: 'user',
      lang: 'zh',
    })).resolves.not.toThrow();
  });

  it('should support English export', async () => {
    await expect(exportReportWord({
      projectName: 'Test',
      exportOptions: ['raw', 'conclusion'],
      lang: 'en',
    })).resolves.not.toThrow();
  });
}); 