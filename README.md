# 药物稳定性研究助手

一个用于药物稳定性研究的智能助手系统，提供数据管理、分析预测和报告生成功能。

## 功能特性

- 项目管理：创建和管理药物稳定性研究项目
- 数据录入：支持药物信息、辅料、环境条件等数据录入
- 稳定性预测：基于机器学习的稳定性预测模型
- 兼容性分析：药物-辅料兼容性评估
- 报告生成：自动生成研究报告和图表
- 用户管理：支持多用户和权限管理

## 技术栈

- 后端：FastAPI + SQLAlchemy + SQLite
- 前端：React + TypeScript + Ant Design
- 机器学习：scikit-learn + pandas + numpy

## 快速开始

### 后端启动
```bash
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 前端启动
```bash
cd frontend
npm install
npm start
```

## API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 项目结构

```
├── backend/          # 后端代码
│   ├── app/         # FastAPI应用
│   ├── models/      # 数据模型
│   └── api/         # API路由
├── frontend/        # 前端代码
│   ├── src/         # React源码
│   └── public/      # 静态资源
└── docs/           # 文档
```

## 开发说明

本项目采用前后端分离架构，后端提供RESTful API，前端通过HTTP请求与后端交互。

## 许可证

MIT License 

## 优化路线图与团队协作说明

本项目已制定系统性优化路线图，详见《优化任务规划与问题分析.md》。团队可参考该文档，分阶段推进核心功能真实化、AI能力深度集成、性能与安全提升、专业模块扩展、运维自动化、文档完善等多维度优化。每阶段均设有明确目标、任务清单和评估机制，鼓励团队成员协作、持续反馈和敏捷迭代，确保软件持续进步与高质量交付。

如需详细分阶段任务、专业建议和进展评估机制，请查阅《优化任务规划与问题分析.md》《OPTIMIZATION_REPORT.md》《软件功能优化计划.md》《实施总结与下一步计划.md》《软件修复总结报告.md》《software_issues_and_solutions.md》等文档。 