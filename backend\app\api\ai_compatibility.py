from fastapi import APIRouter, Body, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Session
from app.services import get_db

router = APIRouter()

class CompatibilityInput(BaseModel):
    drug_id: str
    drug_name: str
    drug_features: Optional[Dict[str, Any]] = None
    excipients: List[str]
    experimental_data: Optional[List[Dict[str, Any]]] = None

@router.post('/ai/compatibility/predict')
def predict_compatibility(req: CompatibilityInput, db: Session = Depends(get_db)):
    try:
        # 基于药物特性和辅料的规则引擎
        compatibility_results = []
        
        for excipient in req.excipients:
            compatibility = {
                'excipient': excipient,
                'compatibility_score': 0.8,  # 默认兼容性得分
                'potential_issues': [],
                'recommendations': []
            }
            
            # 基于药物特性的规则
            drug_features = req.drug_features or {}
            
            # 检查酸碱性相互作用
            if drug_features.get('has_carboxylic_acid', False) and excipient in ['碳酸氢钠', '碳酸钙', '氢氧化镁']:
                compatibility['compatibility_score'] = 0.3
                compatibility['potential_issues'].append('酸碱反应风险')
                compatibility['recommendations'].append('建议避免直接接触，使用涂层隔离')
            
            # 检查酯基水解风险
            if drug_features.get('has_ester', False) and excipient in ['硬脂酸镁', '泊洛沙姆', '山梨酸酯']:
                compatibility['compatibility_score'] = 0.6
                compatibility['potential_issues'].append('水解风险')
                compatibility['recommendations'].append('建议降低湿度，添加干燥剂')
            
            # 检查氧化还原反应
            if drug_features.get('has_phenol', False) and excipient in ['硝酸盐', '铁盐']:
                compatibility['compatibility_score'] = 0.4
                compatibility['potential_issues'].append('氧化风险')
                compatibility['recommendations'].append('建议添加抗氧化剂如BHT或抗坏血酸')
            
            compatibility_results.append(compatibility)
        
        return {
            'status': 'success',
            'drug_id': req.drug_id,
            'drug_name': req.drug_name,
            'compatibility_results': compatibility_results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
