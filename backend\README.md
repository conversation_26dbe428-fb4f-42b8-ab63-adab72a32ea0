# 开发环境配置与常见问题

## 1. VSCode/Pylance 无法解析 app.models.xxx 导入的解决方法

- 确保 VSCode 打开的是 backend 的上一级目录（即包含 backend 的目录）。
- 在 .env 文件或 VSCode 设置中添加：
  ```
  PYTHONPATH=./backend
  ```
- 或在 VSCode 的 settings.json 中添加：
  ```json
  "python.analysis.extraPaths": ["./backend"]
  ```
- 重启 VSCode 或 Python 语言服务器。

## 2. 必要依赖安装

如缺少依赖，请运行：
```bash
pip install fastapi sqlalchemy pydantic openpyxl pandas reportlab python-docx uvicorn
```

--- 