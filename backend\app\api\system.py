from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
import json
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# 配置文件路径
CONFIG_FILE = Path("config/system_config.json")

class APIConfig(BaseModel):
    pubchem_enabled: bool = True
    chemspider_enabled: bool = False
    chemspider_api_key: Optional[str] = None
    timeout: int = 30
    retry_count: int = 3

class AIConfig(BaseModel):
    enabled: bool = True
    provider: str = "deepseek"
    openai_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    grok_api_key: Optional[str] = None
    default_model: Optional[str] = "deepseek-chat"
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 4096

class DatabaseConfig(BaseModel):
    auto_backup: bool = True
    backup_interval: int = 24
    max_backups: int = 7

class PredictionConfig(BaseModel):
    default_confidence_level: int = 95
    default_prediction_months: int = 24
    enable_ml_models: bool = True
    cache_predictions: bool = True

class SecurityConfig(BaseModel):
    session_timeout: int = 60
    password_complexity: bool = True
    two_factor_auth: bool = False

class NotificationConfig(BaseModel):
    email_enabled: bool = False
    email_server: Optional[str] = None
    email_port: Optional[int] = None
    stability_alerts: bool = True

class SystemConfig(BaseModel):
    api: APIConfig
    ai: AIConfig
    database: DatabaseConfig
    prediction: PredictionConfig
    security: SecurityConfig
    notifications: NotificationConfig

def get_default_config() -> SystemConfig:
    """获取默认配置"""
    return SystemConfig(
        api=APIConfig(),
        ai=AIConfig(),
        database=DatabaseConfig(),
        prediction=PredictionConfig(),
        security=SecurityConfig(),
        notifications=NotificationConfig()
    )

def load_config() -> SystemConfig:
    """从文件加载配置"""
    try:
        if CONFIG_FILE.exists():
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return SystemConfig(**data)
        else:
            # 如果配置文件不存在，返回默认配置
            return get_default_config()
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return get_default_config()

def save_config(config: SystemConfig) -> bool:
    """保存配置到文件"""
    try:
        # 确保配置目录存在
        CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        # 将配置转换为字典并保存
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config.dict(), f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

@router.get("/config", response_model=SystemConfig)
async def get_config():
    """获取系统配置"""
    try:
        config = load_config()
        return config
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config")
async def update_config(config: SystemConfig):
    """更新系统配置"""
    try:
        # 保存配置
        success = save_config(config)
        
        if success:
            # 应用配置到系统（这里可以添加实际的配置应用逻辑）
            apply_config_to_system(config)
            
            return {
                "success": True,
                "message": "系统配置已更新"
            }
        else:
            raise HTTPException(status_code=500, detail="保存配置失败")
    
    except Exception as e:
        logger.error(f"更新系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def apply_config_to_system(config: SystemConfig):
    """
    将配置应用到系统（示例实现，可根据实际需求扩展）
    """
    # 示例：根据配置动态调整全局变量、服务参数等
    import os
    # 假设 config 有 api_timeout、ai_service_url、backup_plan 等字段
    if hasattr(config, 'api_timeout'):
        os.environ['API_TIMEOUT'] = str(config.api_timeout)
    if hasattr(config, 'ai_service_url'):
        os.environ['AI_SERVICE_URL'] = config.ai_service_url
    if hasattr(config, 'backup_plan'):
        os.environ['BACKUP_PLAN'] = json.dumps(config.backup_plan)
    # 可扩展更多配置项
    logger.info("系统配置已应用: %s", config)
    # 实际项目可在此处热更新服务参数、重载AI服务、调整数据库备份等
    pass

@router.get("/health")
async def system_health():
    """系统健康检查"""
    return {
        "status": "healthy",
        "services": {
            "api": "online",
            "database": "online",
            "ai": "online"
        },
        "version": "1.0.0"
    }