import React, { useEffect, useState } from 'react';
import { Card, Spin, Empty, Select, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

interface ArrheniusPlotProps {
  prediction: any;
  width?: number | string;
  height?: number | string;
}

/**
 * Arrhenius关系图组件
 * 用于显示药物稳定性的温度依赖性
 */
const ArrheniusPlot: React.FC<ArrheniusPlotProps> = ({
  prediction,
  width = '100%',
  height = 400
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<string>('含量');
  const [chartOptions, setChartOptions] = useState<any>(null);

  useEffect(() => {
    if (!prediction || !prediction.results || !prediction.results.kinetic_models) {
      return;
    }

    setLoading(true);

    try {
      const kineticModels = prediction.results.kinetic_models;
      const items = Object.keys(kineticModels);
      
      if (items.length > 0 && !items.includes(selectedItem)) {
        setSelectedItem(items[0]);
      }

      // 从动力学模型中提取数据
      const modelData = kineticModels[selectedItem] || kineticModels[items[0]];
      
      if (!modelData || !modelData.activation_energy) {
        setLoading(false);
        return;
      }
      
      // 准备Arrhenius图表数据
      const temperatures = [5, 15, 25, 30, 40]; // 摄氏度
      const invTemperatures = temperatures.map(t => (1 / (t + 273.15) * 1000)); // 1/T (K^-1) * 1000
      
      // 使用Arrhenius方程计算不同温度下的速率常数
      // k = A * exp(-Ea/RT)
      const A = modelData.params ? Math.exp(modelData.params[0]) : 1;
      const Ea = modelData.activation_energy;
      const R = 8.314 / 1000; // 气体常数, kJ/(mol·K)
      
      const lnK = invTemperatures.map(invT => 
        Math.log(A) - (Ea / R) * (invT / 1000)
      );
      
      // 计算线性回归
      const slope = calculateSlope(invTemperatures, lnK);
      const intercept = calculateIntercept(invTemperatures, lnK, slope);
      
      // 线性拟合线
      const fitLine = invTemperatures.map(invT => slope * invT + intercept);
      
      // 计算半衰期
      const halfLives = lnK.map(k => Math.log(2) / Math.exp(k));
      
      // 设置图表选项
      const options = {
        title: {
          text: 'Arrhenius关系图',
          subtext: `活化能: ${Ea.toFixed(2)} kJ/mol`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params: any) {
            const invT = params[0].value[0];
            const temp = (1000 / invT - 273.15).toFixed(1);
            const lnk = params[0].value[1].toFixed(4);
            const k = Math.exp(lnk).toExponential(4);
            const t90 = (0.1053 / Math.exp(lnk)).toFixed(2); // t90 ≈ 0.1053/k for 90% remaining
            
            return `温度: ${temp}°C<br/>1/T: ${invT}<br/>ln(k): ${lnk}<br/>k: ${k}<br/>t90: ${t90} 月`;
          }
        },
        legend: {
          data: ['ln(k) vs 1/T', '线性拟合'],
          right: 10,
          top: 30
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '1/T × 1000 (K⁻¹)',
          nameLocation: 'middle',
          nameGap: 30,
          min: 3.1,
          max: 3.7
        },
        yAxis: {
          type: 'value',
          name: 'ln(k)',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [
          {
            name: 'ln(k) vs 1/T',
            type: 'scatter',
            symbolSize: 10,
            data: invTemperatures.map((invT, index) => [invT, lnK[index]]),
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '线性拟合',
            type: 'line',
            showSymbol: false,
            data: invTemperatures.map((invT, index) => [invT, fitLine[index]]),
            lineStyle: {
              color: '#91cc75',
              width: 2
            },
            markPoint: {
              data: [
                { type: 'max', name: '最高点' },
                { type: 'min', name: '最低点' }
              ]
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ]
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 100
          }
        ]
      };
      
      setChartOptions(options);
    } catch (error) {
      console.error('生成Arrhenius图表错误:', error);
    } finally {
      setLoading(false);
    }
  }, [prediction, selectedItem]);

  // 计算斜率
  const calculateSlope = (x: number[], y: number[]) => {
    const n = x.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    
    for (let i = 0; i < n; i++) {
      sumX += x[i];
      sumY += y[i];
      sumXY += x[i] * y[i];
      sumXX += x[i] * x[i];
    }
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  };

  // 计算截距
  const calculateIntercept = (x: number[], y: number[], slope: number) => {
    const n = x.length;
    let sumX = 0, sumY = 0;
    
    for (let i = 0; i < n; i++) {
      sumX += x[i];
      sumY += y[i];
    }
    
    return (sumY - slope * sumX) / n;
  };

  const handleItemChange = (value: string) => {
    setSelectedItem(value);
  };

  if (!prediction || !prediction.results || !prediction.results.kinetic_models) {
    return <Empty description="无法生成Arrhenius图表，数据不足" />;
  }

  const kineticModels = prediction.results.kinetic_models;
  const items = Object.keys(kineticModels);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Arrhenius分析</span>
          <Tooltip title="Arrhenius方程展示了反应速率常数与温度的关系，用于预测不同温度下的稳定性">
            <InfoCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
          <div style={{ marginLeft: 'auto' }}>
            <span style={{ marginRight: 8 }}>测试项：</span>
            <Select 
              value={selectedItem} 
              onChange={handleItemChange}
              style={{ width: 120 }}
            >
              {items.map(item => (
                <Select.Option key={item} value={item}>{item}</Select.Option>
              ))}
            </Select>
          </div>
        </div>
      }
      bordered={false}
    >
      {loading ? (
        <div style={{ height, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="生成Arrhenius图表..." />
        </div>
      ) : !chartOptions ? (
        <Empty description="无法生成Arrhenius图表，数据不足" />
      ) : (
        <ReactECharts
          option={chartOptions}
          style={{ height, width }}
          opts={{ renderer: 'canvas' }}
        />
      )}
    </Card>
  );
};

export default ArrheniusPlot; 