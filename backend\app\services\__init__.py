"""
服务模块
"""
from sqlalchemy.orm import Session
from ..config.database import SessionLocal
import logging

logger = logging.getLogger(__name__)

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 新增的AI建议生成函数
def generate_ai_suggestions(data, context=None, model="default"):
    """
    生成AI分析建议
    
    参数:
        data: 需要分析的数据
        context: 额外的上下文信息
        model: 使用的AI模型名称
        
    返回:
        dict: 包含建议的字典
    """
    try:
        # 备用方案：返回预设的建议
        suggestions = {
            "summary": "基于提供的数据分析",
            "recommendations": [
                "建议优化配方以提高稳定性",
                "考虑使用更好的包装材料",
                "建议进行更多温度条件下的稳定性测试"
            ],
            "confidence_score": 0.85,
            "analysis_details": {
                "key_factors": ["温度", "湿度", "包装材料"],
                "critical_parameters": ["降解速率", "活化能"]
            }
        }
        return suggestions
    except Exception as e:
        logger.error(f"生成AI建议失败: {str(e)}")
        return {
            "error": "生成建议失败",
            "message": str(e)
        }

# 添加导出历史记录服务函数
def add_export_history_service(user_id, export_type, file_path, description=None):
    """
    添加导出历史记录
    
    参数:
        user_id: 用户ID
        export_type: 导出类型
        file_path: 文件路径
        description: 描述
        
    返回:
        dict: 操作结果
    """
    try:
        # 这里应该添加到数据库，暂时返回成功
        logger.info(f"添加导出历史记录: 用户{user_id}, 类型{export_type}, 路径{file_path}")
        return {
            "success": True,
            "message": "导出历史记录已添加"
        }
    except Exception as e:
        logger.error(f"添加导出历史记录失败: {str(e)}")
        return {
            "success": False,
            "message": str(e)
        }

# 添加获取导出历史记录列表函数
def list_export_history_service(project_id=None, limit=50):
    """
    获取导出历史记录列表
    
    参数:
        project_id: 项目ID，如果提供则只返回特定项目的历史记录
        limit: 返回结果数量限制
        
    返回:
        list: 导出历史记录列表
    """
    try:
        # 这里应该从数据库获取，暂时返回空列表
        logger.info(f"获取导出历史记录: 项目{project_id}, 限制{limit}条")
        return []
    except Exception as e:
        logger.error(f"获取导出历史记录失败: {str(e)}")
        return []

# 导入认证相关函数
from .auth import (
    # authenticate_user,  # 已废弃，使用app/services/auth.py中的新版本
    create_access_token,
    list_api_keys,
    create_api_key,
    disable_api_key,
    list_operation_logs,
    db_list_api_keys,
    db_create_api_key,
    db_list_operation_logs
)

# 移除所有具体Service的导入和导出，只保留通用函数和auth相关内容

__all__ = [
    'get_db',
    # authenticate_user,  # 已废弃，使用app/services/auth.py中的新版本
    'create_access_token',
    'list_api_keys',
    'create_api_key',
    'disable_api_key',
    'list_operation_logs',
    'db_list_api_keys',
    'db_create_api_key',
    'db_list_operation_logs',
    'generate_ai_suggestions',  # 添加新函数到导出列表
    'add_export_history_service',  # 添加导出历史服务函数
    'list_export_history_service'  # 添加获取导出历史列表函数
]
