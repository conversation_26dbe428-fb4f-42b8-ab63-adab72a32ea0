import React, { useState, useContext, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Select,
  Button,
  Space,
  Alert,
  Spin,
  Typography,
  Row,
  Col,
  Tag,
  Divider,
  List,
  Progress,
  Timeline,
  message,
  Tooltip,
  Radio,
  Checkbox,
  InputNumber
} from 'antd';
import {
  RobotOutlined,
  ExperimentOutlined,
  SafetyCertificateOutlined,
  FileTextOutlined,
  RocketOutlined,
  BulbOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { ProjectContext } from '../App';
import axios from 'axios';
import ProjectSelector from '../components/ProjectSelector';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface AIProvider {
  name: string;
  value: string;
  model: string;
  description: string;
  available: boolean;
}

interface AIProvidersResponse {
  providers: AIProvider[];
  default: string;
  ai_client_available: boolean;
  compatibility_engine_available: boolean;
  stability_engine_available: boolean;
}

const AIAnalysis: React.FC = () => {
  const [form] = Form.useForm();
  const { currentProject, inputData } = useContext(ProjectContext);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('stability');
  const [aiProviders, setAIProviders] = useState<AIProvider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState('deepseek');
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  // 加载AI提供商
  useEffect(() => {
    loadAIProviders();
  }, []);

  const loadAIProviders = async () => {
    try {
      const response = await axios.get<AIProvidersResponse>('/api/ai/providers');
      setAIProviders(response.data.providers || []);
      setSelectedProvider(response.data.default || 'deepseek');
    } catch (error) {
      console.error('加载AI提供商失败:', error);
      message.error('加载AI提供商失败');
    }
  };

  // 稳定性AI分析
  const handleStabilityAnalysis = async (values: any) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/ai/stability/analyze', {
        drug_name: values.drug_name || inputData?.drug_name || '未知药物',
        drug_structure: values.drug_structure || inputData?.drug_structure,
        stability_data: values.stability_data || inputData?.stability_data || [],
        conditions: {
          temperature: values.temperature || 25,
          humidity: values.humidity || 60,
          packaging: values.packaging || '铝塑泡罩'
        },
        ai_provider: selectedProvider
      });

      setAnalysisResult(response.data);
      message.success('AI稳定性分析完成');
    } catch (error: any) {
      console.error('AI稳定性分析失败:', error);
      message.error(error.response?.data?.detail || 'AI分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 相容性AI分析
  const handleCompatibilityAnalysis = async (values: any) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/ai/compatibility/analyze', {
        drug_name: values.drug_name || inputData?.drug_name || '未知药物',
        drug_structure: values.drug_structure || inputData?.drug_structure || '',
        excipients: values.excipients || inputData?.excipients?.map((e: any) => 
          typeof e === 'string' ? e : e.name
        ) || [],
        conditions: {
          ph: values.ph || 7,
          temperature: values.temperature || 25,
          humidity: values.humidity || 60
        },
        ai_provider: selectedProvider
      });

      setAnalysisResult(response.data);
      message.success('AI相容性分析完成');
    } catch (error: any) {
      console.error('AI相容性分析失败:', error);
      message.error(error.response?.data?.detail || 'AI分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 配方优化AI分析
  const handleFormulationOptimization = async (values: any) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/ai/formulation/optimize', {
        drug_name: values.drug_name || inputData?.drug_name || '未知药物',
        dosage_form: values.dosage_form || '片剂',
        formulation: values.formulation || [],
        target_specs: {
          dissolution: values.dissolution_target || 85,
          hardness: values.hardness_target || 10,
          disintegration: values.disintegration_target || 15
        },
        ai_provider: selectedProvider
      });

      setAnalysisResult(response.data);
      message.success('AI配方优化分析完成');
    } catch (error: any) {
      console.error('AI配方优化失败:', error);
      message.error(error.response?.data?.detail || 'AI分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 法规指导AI分析
  const handleRegulatoryGuidance = async (values: any) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/ai/regulatory/guidance', {
        product_type: values.product_type,
        development_stage: values.development_stage,
        target_market: values.target_market,
        specific_question: values.specific_question,
        ai_provider: selectedProvider
      });

      setAnalysisResult(response.data);
      message.success('AI法规指导分析完成');
    } catch (error: any) {
      console.error('AI法规指导失败:', error);
      message.error(error.response?.data?.detail || 'AI分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染分析结果
  const renderAnalysisResult = () => {
    if (!analysisResult) return null;

    return (
      <Card title="AI分析结果" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={24}>
            <Alert
              message={`使用 ${analysisResult.provider} 提供的AI模型进行分析`}
              type="info"
              icon={<RobotOutlined />}
              style={{ marginBottom: 16 }}
            />
          </Col>
        </Row>

        {/* 引擎分析结果 */}
        {analysisResult.engine_analysis && (
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>专业引擎分析</Title>
            {analysisResult.engine_analysis.error ? (
              <Alert
                message="专业引擎暂时不可用"
                description={analysisResult.engine_analysis.error}
                type="warning"
              />
            ) : (
              <Card size="small">
                <pre>{JSON.stringify(analysisResult.engine_analysis, null, 2)}</pre>
              </Card>
            )}
          </div>
        )}

        {/* AI分析结果 */}
        {analysisResult.ai_analysis && (
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>AI智能分析</Title>
            <Card size="small">
              {analysisResult.ai_analysis.recommendations && (
                <div>
                  <Text strong>建议：</Text>
                  <List
                    size="small"
                    dataSource={analysisResult.ai_analysis.recommendations}
                    renderItem={(item: string) => (
                      <List.Item>
                        <BulbOutlined style={{ marginRight: 8, color: '#faad14' }} />
                        {item}
                      </List.Item>
                    )}
                  />
                </div>
              )}
              {analysisResult.ai_analysis.risks && (
                <div style={{ marginTop: 16 }}>
                  <Text strong>风险因素：</Text>
                  <Space wrap>
                    {analysisResult.ai_analysis.risks.map((risk: string, index: number) => (
                      <Tag key={index} color="red">{risk}</Tag>
                    ))}
                  </Space>
                </div>
              )}
            </Card>
          </div>
        )}

        {/* 综合建议 */}
        {analysisResult.combined_recommendations && (
          <div>
            <Title level={5}>综合建议</Title>
            <Timeline>
              {analysisResult.combined_recommendations.map((rec: string, index: number) => (
                <Timeline.Item 
                  key={index}
                  color={index < 3 ? 'red' : 'blue'}
                  dot={index === 0 ? <ThunderboltOutlined /> : undefined}
                >
                  {rec}
                </Timeline.Item>
              ))}
            </Timeline>
          </div>
        )}

        {/* 行动项 */}
        {analysisResult.action_items && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>行动项</Title>
            <List
              dataSource={analysisResult.action_items}
              renderItem={(item: any) => (
                <List.Item>
                  <Checkbox>{item.action || item}</Checkbox>
                </List.Item>
              )}
            />
          </div>
        )}
      </Card>
    );
  };

  return (
    <div>
      {/* 项目选择器 */}
      <div style={{ marginBottom: '16px' }}>
        <ProjectSelector showCreateButton={true} />
      </div>

      <Title level={2}>
        <RobotOutlined /> AI增强分析
      </Title>

      <Alert
        message="AI智能分析功能"
        description="利用先进的AI技术，为您提供专业的药物稳定性、相容性、配方优化和法规指导建议。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* AI提供商选择 */}
      <Card style={{ marginBottom: 16 }}>
        <Form.Item label="选择AI模型">
          <Radio.Group
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value)}
          >
            {aiProviders.map(provider => (
              <Radio.Button 
                key={provider.value} 
                value={provider.value}
                disabled={!provider.available}
              >
                <Space>
                  <RobotOutlined />
                  {provider.name}
                  <Tag color={provider.available ? 'green' : 'default'}>
                    {provider.model}
                  </Tag>
                </Space>
              </Radio.Button>
            ))}
          </Radio.Group>
        </Form.Item>
        {selectedProvider && (
          <Alert
            message={aiProviders.find(p => p.value === selectedProvider)?.description}
            type="info"
          />
        )}
      </Card>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 稳定性分析 */}
        <TabPane tab={<span><ExperimentOutlined />稳定性分析</span>} key="stability">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleStabilityAnalysis}
            initialValues={{
              drug_name: inputData?.drug_name,
              temperature: 25,
              humidity: 60
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="drug_name"
                  label="药物名称"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="输入药物名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="drug_structure"
                  label="SMILES结构"
                >
                  <Input placeholder="可选：输入SMILES结构" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="temperature"
                  label="温度(°C)"
                >
                  <InputNumber min={-20} max={60} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="humidity"
                  label="湿度(%RH)"
                >
                  <InputNumber min={0} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="packaging"
                  label="包装材料"
                >
                  <Select>
                    <Option value="铝塑泡罩">铝塑泡罩</Option>
                    <Option value="HDPE瓶">HDPE瓶</Option>
                    <Option value="玻璃瓶">玻璃瓶</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始AI分析
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        {/* 相容性分析 */}
        <TabPane tab={<span><SafetyCertificateOutlined />相容性分析</span>} key="compatibility">
          <Form
            layout="vertical"
            onFinish={handleCompatibilityAnalysis}
            initialValues={{
              drug_name: inputData?.drug_name,
              ph: 7,
              temperature: 25,
              humidity: 60
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="drug_name"
                  label="药物名称"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="输入药物名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="drug_structure"
                  label="SMILES结构"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="输入SMILES结构" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="excipients"
              label="辅料列表"
              rules={[{ required: true }]}
            >
              <Select
                mode="tags"
                placeholder="输入或选择辅料"
                style={{ width: '100%' }}
              >
                <Option value="乳糖">乳糖</Option>
                <Option value="微晶纤维素">微晶纤维素</Option>
                <Option value="羟丙甲纤维素">羟丙甲纤维素</Option>
                <Option value="硬脂酸镁">硬脂酸镁</Option>
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="ph" label="pH值">
                  <InputNumber min={1} max={14} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="temperature" label="温度(°C)">
                  <InputNumber min={-20} max={60} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="humidity" label="湿度(%RH)">
                  <InputNumber min={0} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始AI分析
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        {/* 配方优化 */}
        <TabPane tab={<span><RocketOutlined />配方优化</span>} key="formulation">
          <Form
            layout="vertical"
            onFinish={handleFormulationOptimization}
            initialValues={{
              drug_name: inputData?.drug_name,
              dosage_form: '片剂',
              dissolution_target: 85,
              hardness_target: 10,
              disintegration_target: 15
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="drug_name"
                  label="药物名称"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="输入药物名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="dosage_form"
                  label="剂型"
                  rules={[{ required: true }]}
                >
                  <Select>
                    <Option value="片剂">片剂</Option>
                    <Option value="胶囊">胶囊</Option>
                    <Option value="颗粒剂">颗粒剂</Option>
                    <Option value="注射剂">注射剂</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Title level={5}>目标规格</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="dissolution_target"
                  label="溶出度目标(%)"
                >
                  <InputNumber min={0} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="hardness_target"
                  label="硬度目标(kg)"
                >
                  <InputNumber min={0} max={20} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="disintegration_target"
                  label="崩解时限(min)"
                >
                  <InputNumber min={0} max={60} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始AI优化
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        {/* 法规指导 */}
        <TabPane tab={<span><FileTextOutlined />法规指导</span>} key="regulatory">
          <Form
            layout="vertical"
            onFinish={handleRegulatoryGuidance}
            initialValues={{
              product_type: '化学药',
              development_stage: '临床前',
              target_market: '中国'
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="product_type"
                  label="产品类型"
                  rules={[{ required: true }]}
                >
                  <Select>
                    <Option value="化学药">化学药</Option>
                    <Option value="生物制品">生物制品</Option>
                    <Option value="中药">中药</Option>
                    <Option value="医疗器械">医疗器械</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="development_stage"
                  label="开发阶段"
                  rules={[{ required: true }]}
                >
                  <Select>
                    <Option value="临床前">临床前</Option>
                    <Option value="临床I期">临床I期</Option>
                    <Option value="临床II期">临床II期</Option>
                    <Option value="临床III期">临床III期</Option>
                    <Option value="上市申请">上市申请</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="target_market"
                  label="目标市场"
                  rules={[{ required: true }]}
                >
                  <Select>
                    <Option value="中国">中国</Option>
                    <Option value="美国">美国</Option>
                    <Option value="欧盟">欧盟</Option>
                    <Option value="日本">日本</Option>
                    <Option value="全球">全球</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="specific_question"
              label="具体问题"
              rules={[{ required: true }]}
            >
              <TextArea
                rows={4}
                placeholder="请描述您需要的法规指导，例如：稳定性试验设计要求、杂质控制策略、包装材料选择等"
              />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                获取AI指导
              </Button>
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>

      {/* 分析结果展示 */}
      {loading ? (
        <Card style={{ marginTop: 16, textAlign: 'center' }}>
          <Spin size="large" tip="AI正在分析中，请稍候..." />
        </Card>
      ) : (
        renderAnalysisResult()
      )}
    </div>
  );
};

export default AIAnalysis; 