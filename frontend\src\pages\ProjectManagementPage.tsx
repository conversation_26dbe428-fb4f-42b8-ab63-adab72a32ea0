import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  message,
  Button,
  Card,
  Table,
  Input,
  Space,
  Modal,
  Form,
  Select,
  Tag,
  Tooltip,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import '../styles/ProjectManagement.css';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  SearchOutlined,
  ImportOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { apiFetch } from '../api';
import BatchImportModal from '../components/BatchImportModal';
import { useTranslation } from 'react-i18next';
import ConfirmButton from '../components/ConfirmButton';
import { RoleContext, ProjectContext } from '../App';

const { Title } = Typography;
const { Option } = Select;

interface Project {
  id: number;
  name: string;
  status: string;
  createdAt: string;
}

const ProjectManagementPage: React.FC = () => {
  const [search, setSearch] = useState('');
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [newProject, setNewProject] = useState({ name: '', status: '进行中' });
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState('');
  const [deleting, setDeleting] = useState(false);
  const [editId, setEditId] = useState<number | null>(null);
  const [editProject, setEditProject] = useState({ name: '', status: '进行中' });
  const [editing, setEditing] = useState(false);
  const [editError, setEditError] = useState('');
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [importHistory, setImportHistory] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<any[]>([]);
  const [fixErrorModal, setFixErrorModal] = useState<{ open: boolean; error: any } | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const role = useContext(RoleContext);
  const {
    projects: contextProjects,
    currentProject,
    setCurrentProject,
    addProject: addProjectToContext,
    editProject: editProjectToContext,
    deleteProject: deleteProjectFromContext,
    fetchProjects: fetchProjectsFromContext
  } = useContext(ProjectContext);

  // 使用ProjectContext的fetchProjects
  const fetchProjects = () => {
    fetchProjectsFromContext();
  };

  useEffect(() => {
    // 初始化时获取项目列表
    fetchProjects();
    apiFetch<any[]>('/api/import/history').then(res => setImportHistory(res || []));
    apiFetch<any[]>('/api/import/errors').then(res => setImportErrors(res || []));
  }, []);

  // 转换contextProjects为本地格式并过滤
  const formattedProjects = contextProjects.map((p: any) => ({
    id: parseInt(p.id),
    name: p.name,
    status: p.status || '进行中',
    createdAt: p.created_at || p.createdAt || new Date().toISOString()
  }));

  const filtered = formattedProjects.filter(p => p.name.includes(search));

  const handleCreate = async () => {
    if (!newProject.name.trim()) {
      setError(t('项目名称不能为空'));
      message.warning(t('项目名称不能为空'));
      return;
    }
    setCreating(true);
    setError('');
    try {
      // 使用ProjectContext的addProject方法
      const createdProject = await addProjectToContext({
        name: newProject.name,
        status: newProject.status,
        description: ''
      });

      if (createdProject) {
        setShowModal(false);
        setNewProject({ name: '', status: '进行中' });

        // 设置为当前项目
        setCurrentProject(createdProject);
        message.success(t('项目创建成功'));

        // 跳转到数据输入页面
        setTimeout(() => {
          navigate('/data-input');
        }, 1000);
      } else {
        setError(t('创建失败'));
        message.error(t('创建失败'));
      }
    } catch (err: any) {
      const msg = err?.detail || err?.message || t('创建失败');
      setError(msg);
      message.error(msg);
    }
    setCreating(false);
  };

  const handleDelete = async (projectId: number) => {
    if (!projectId) return;

    try {
      // 使用ProjectContext的deleteProject方法
      await deleteProjectFromContext(projectId.toString());
      message.success('删除成功');
    } catch (error) {
      console.error('Delete error:', error);
      message.error('删除失败');
    }
  };

  const openEdit = (p: Project) => {
    setEditId(p.id);
    setEditProject({ name: p.name, status: p.status });
    setEditError('');
  };

  const handleEdit = async () => {
    if (editId == null) return;
    if (!editProject.name.trim()) {
      setEditError('项目名称不能为空');
      message.warning('项目名称不能为空');
      return;
    }
    setEditing(true);
    setEditError('');

    try {
      // 构造完整的项目对象
      const projectToUpdate = {
        id: editId.toString(),
        name: editProject.name,
        status: editProject.status,
        description: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // 使用ProjectContext的editProject方法
      await editProjectToContext(projectToUpdate);
      setEditId(null);
      message.success('编辑成功');
    } catch (error) {
      setEditError('编辑失败');
      message.error('编辑失败');
    }
    setEditing(false);
  };

  // 项目选择功能
  const handleSelectProject = (project: Project) => {
    // 转换为ProjectContext期望的格式
    const projectForContext = {
      id: project.id.toString(),
      name: project.name,
      status: project.status,
      description: '',
      created: project.createdAt ? new Date(project.createdAt).toISOString().slice(0, 10) : '',
      created_at: project.createdAt || new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setCurrentProject(projectForContext);
    message.success(`已选择项目: ${project.name}`);
    navigate('/data-input');
  };

  // 快速跳转到原辅料分析
  const handleQuickAnalysis = (project: Project) => {
    // 先设置为当前项目
    const projectForContext = {
      id: project.id.toString(),
      name: project.name,
      status: project.status,
      description: '',
      created: project.createdAt ? new Date(project.createdAt).toISOString().slice(0, 10) : '',
      created_at: project.createdAt || new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setCurrentProject(projectForContext);
    message.success(`已选择项目: ${project.name}，正在跳转到原辅料分析`);

    // 跳转到原辅料分析页面
    setTimeout(() => {
      navigate('/excipient-analysis');
    }, 500);
  };

  // 权限控制示例 - 临时允许所有用户删除项目用于测试
  const canDelete = (role: string) => true; // 临时修改：允许所有用户删除
  const canExport = (role: string) => ['admin', 'qa'].includes(role);

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成': return 'success';
      case '进行中': return 'processing';
      case '计划中': return 'default';
      case '风险': return 'error';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: (a: Project, b: Project) => a.id - b.id,
      render: (id: number) => (
        <span className="project-id-cell">
          #{id}
        </span>
      ),
    },
    {
      title: t('项目名称'),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text: string, record: Project) => (
        <div className="project-name-cell">
          <Tooltip title={`点击选择项目: ${text}`}>
            <Button
              type="link"
              onClick={() => handleSelectProject(record)}
              className={`project-name-link ${currentProject?.id === record.id.toString() ? 'current-project' : ''}`}
            >
              {text}
            </Button>
          </Tooltip>
          {currentProject?.id === record.id.toString() && (
            <span className="current-project-badge">
              (当前项目)
            </span>
          )}
        </div>
      ),
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag
          color={getStatusColor(status)}
          className="project-status-tag"
        >
          {t(status)}
        </Tag>
      ),
      filters: [
        { text: t('进行中'), value: '进行中' },
        { text: t('已完成'), value: '已完成' },
        { text: t('计划中'), value: '计划中' },
        { text: t('风险'), value: '风险' },
      ],
      onFilter: (value: any, record: Project) => record.status === value,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date: string) => (
        <span className="project-date-cell">
          {new Date(date).toLocaleString('zh-CN')}
        </span>
      ),
      sorter: (a: Project, b: Project) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: t('操作'),
      key: 'action',
      width: 280,
      render: (_: any, record: Project) => (
        <div className="project-actions-cell">
          <Tooltip title={t('选择项目')}>
            <Button
              type={currentProject?.id === record.id.toString() ? "primary" : "default"}
              size="small"
              onClick={() => handleSelectProject(record)}
              className="project-select-button"
            >
              {currentProject?.id === record.id.toString() ? t('已选择') : t('选择')}
            </Button>
          </Tooltip>
          <Tooltip title="原辅料分析">
            <Button
              type="text"
              icon={<ExperimentOutlined />}
              size="small"
              onClick={() => handleQuickAnalysis(record)}
              className="project-action-button"
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
          <Tooltip title={t('查看详情')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => navigate(`/projects/${record.id}`)}
              className="project-action-button"
            />
          </Tooltip>
          <Tooltip title={t('编辑')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => openEdit(record)}
              className="project-action-button"
            />
          </Tooltip>
          {canDelete(role) && (
            <Tooltip title={t('删除')}>
              <ConfirmButton
                danger
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                onConfirm={() => handleDelete(record.id)}
                confirmText={`确定要删除项目 "${record.name}" 吗？`}
              />
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="project-management-container">
      <Card className="project-management-card">
        <div className="project-header">
          <div className="project-header-left">
            <Title level={2}>
              {t('项目管理')}
            </Title>
            <div className="project-header-subtitle">
              管理您的药物稳定性研究项目
            </div>
            {currentProject && (
              <div className="current-project-indicator">
                当前项目: <span className="current-project-name">{currentProject.name}</span>
              </div>
            )}
          </div>
          <div className="project-actions">
            <Button
              className="project-action-btn"
              icon={<DownloadOutlined />}
              onClick={() => window.open('/templates/project_import_template.xlsx', '_blank')}
            >
              {t('下载模板')}
            </Button>
            <Button
              className="project-action-btn"
              icon={<ImportOutlined />}
              onClick={() => setImportModalOpen(true)}
            >
              {t('批量导入')}
            </Button>
            <Button
              className="project-action-btn"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShowModal(true)}
            >
              {t('新建项目')}
            </Button>
          </div>
        </div>

        <Row className="project-search-row">
          <Col span={12}>
            <Input
              className="project-search-input"
              placeholder={t('搜索项目名称')}
              prefix={<SearchOutlined />}
              value={search}
              onChange={e => setSearch(e.target.value)}
              allowClear
            />
          </Col>
        </Row>

        <div className="project-table-container">
          <Table
            className="project-table"
            columns={columns}
            dataSource={filtered}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 800 }}
            size="large"
          />
        </div>
      </Card>
      {/* 新建项目弹窗 */}
      <Modal
        title={t('新建项目')}
        open={showModal}
        onOk={handleCreate}
        onCancel={() => {
          setShowModal(false);
          setNewProject({ name: '', status: '进行中' });
          setError('');
        }}
        confirmLoading={creating}
        okText={t('创建')}
        cancelText={t('取消')}
        width={500}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('项目名称')}
            required
            validateStatus={error ? 'error' : ''}
            help={error}
          >
            <Input
              placeholder={t('请输入项目名称')}
              value={newProject.name}
              onChange={e => setNewProject({ ...newProject, name: e.target.value })}
              onPressEnter={handleCreate}
            />
          </Form.Item>
          <Form.Item label={t('项目状态')}>
            <Select
              value={newProject.status}
              onChange={value => setNewProject({ ...newProject, status: value })}
              style={{ width: '100%' }}
            >
              <Option value="计划中">{t('计划中')}</Option>
              <Option value="进行中">{t('进行中')}</Option>
              <Option value="已完成">{t('已完成')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      {/* 编辑项目弹窗 */}
      <Modal
        title={t('编辑项目')}
        open={editId !== null}
        onOk={handleEdit}
        onCancel={() => {
          setEditId(null);
          setEditError('');
        }}
        confirmLoading={editing}
        okText={t('保存')}
        cancelText={t('取消')}
        width={500}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('项目名称')}
            required
            validateStatus={editError ? 'error' : ''}
            help={editError}
          >
            <Input
              placeholder={t('请输入项目名称')}
              value={editProject.name}
              onChange={e => setEditProject({ ...editProject, name: e.target.value })}
              onPressEnter={handleEdit}
            />
          </Form.Item>
          <Form.Item label={t('项目状态')}>
            <Select
              value={editProject.status}
              onChange={value => setEditProject({ ...editProject, status: value })}
              style={{ width: '100%' }}
            >
              <Option value="计划中">{t('计划中')}</Option>
              <Option value="进行中">{t('进行中')}</Option>
              <Option value="已完成">{t('已完成')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      <BatchImportModal
        open={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        onSuccess={data => {
          message.success(t('批量导入成功'));
          setImportModalOpen(false);
          fetchProjects();
        }}
        templateUrl="/templates/project_import_template.xlsx"
        entityName={t('项目')}
        templateFields={['id','name','status','createdAt']}
      />
      {/* 批量导入历史 */}
      {(importHistory.length > 0 || importErrors.length > 0) && (
        <Card style={{ marginTop: 24 }} title={t('批量导入历史')}>
          {importHistory.length > 0 && (
            <>
              <Title level={4}>{t('导入记录')}</Title>
              <Table
                size="small"
                dataSource={importHistory}
                pagination={false}
                columns={[
                  { title: t('文件名'), dataIndex: 'file', key: 'file', render: (text: string, record: any) => text || record.fileName },
                  { title: t('导入时间'), dataIndex: 'time', key: 'time', render: (text: string, record: any) => text || record.importTime },
                  { title: t('状态'), dataIndex: 'result', key: 'result', render: (text: string, record: any) => (
                    <Tag color={text === '成功' ? 'success' : 'error'}>{t(text || record.status)}</Tag>
                  )},
                  { title: t('数据条数'), dataIndex: 'dataCount', key: 'dataCount' },
                  { title: t('错误条数'), dataIndex: 'errorCount', key: 'errorCount' },
                ]}
              />
            </>
          )}

          {importErrors.length > 0 && (
            <>
              <Divider />
              <Title level={4}>{t('导入错误记录')}</Title>
              <Table
                size="small"
                dataSource={importErrors}
                pagination={false}
                columns={[
                  { title: t('行号'), dataIndex: 'row', key: 'row', width: 80 },
                  { title: t('字段'), dataIndex: 'field', key: 'field', render: (text: string, record: any) => text || record.col },
                  { title: t('错误信息'), dataIndex: 'message', key: 'message', render: (text: string, record: any) => t(text || record.error) },
                  {
                    title: t('操作'),
                    key: 'action',
                    width: 100,
                    render: (_: any, record: any) => (
                      <Button
                        size="small"
                        type="link"
                        onClick={() => setFixErrorModal({ open: true, error: record })}
                      >
                        {t('修正')}
                      </Button>
                    )
                  },
                ]}
              />
            </>
          )}
        </Card>
      )}
      {/* 错误修正弹窗 */}
      <Modal
        title={t('修正导入错误')}
        open={fixErrorModal?.open || false}
        onCancel={() => setFixErrorModal(null)}
        onOk={async () => {
          const newValue = (document.getElementById('fixInput') as HTMLInputElement)?.value;
          await fetch('/api/import/error/fix', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              row: fixErrorModal?.error.row,
              col: fixErrorModal?.error.field || fixErrorModal?.error.col,
              new_value: newValue
            })
          });
          setFixErrorModal(null);
          message.success(t('修正已提交'));
          // 刷新错误列表
          apiFetch<any[]>('/api/import/errors').then(res => setImportErrors(res || []));
        }}
        okText={t('提交修正')}
        cancelText={t('取消')}
        width={500}
      >
        {fixErrorModal?.error && (
          <Form layout="vertical">
            <Form.Item label={t('字段')}>
              <Input value={fixErrorModal.error.field || fixErrorModal.error.col} disabled />
            </Form.Item>
            <Form.Item label={t('原始值')}>
              <Input value={fixErrorModal.error.value} disabled />
            </Form.Item>
            <Form.Item label={t('错误信息')}>
              <Input.TextArea
                value={t(fixErrorModal.error.message || fixErrorModal.error.error)}
                disabled
                rows={2}
              />
            </Form.Item>
            <Form.Item label={t('修正值')} required>
              <Input
                id="fixInput"
                defaultValue={fixErrorModal.error.value}
                placeholder={t('请输入修正后的值')}
              />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default ProjectManagementPage; 