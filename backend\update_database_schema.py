#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新数据库架构以支持增强的辅料信息
"""
import sqlite3
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.app.services.open_data_service import open_data_service

def update_schema():
    """更新数据库架构"""
    print("开始更新数据库架构...")
    
    # 确保数据库存在
    open_data_service._ensure_database()
    
    print("✅ 数据库架构更新完成！")
    print("\n新增的表和字段：")
    print("1. excipients 表 - 扩展了理化性质、稳定性、应用、药典等字段")
    print("2. excipient_physical_properties 表 - 物理性质详情")
    print("3. excipient_applications 表 - 应用信息")
    print("4. excipient_stability_data 表 - 稳定性数据")
    print("5. drug_excipient_interactions 表 - 增强了交互作用信息")
    
    # 显示表结构
    conn = sqlite3.connect(open_data_service.db_path)
    cursor = conn.cursor()
    
    print("\n当前数据库表：")
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    for table in tables:
        print(f"- {table[0]}")
    
    conn.close()

if __name__ == "__main__":
    try:
        update_schema()
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        sys.exit(1) 