name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
        python-version: [3.10]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci
      - name: Lint frontend
        run: |
          cd frontend
          npm run lint
      - name: Test frontend
        run: |
          cd frontend
          npm test -- --watchAll=false
      - name: Build frontend
        run: |
          cd frontend
          npm run build
      - name: Install backend dependencies
        run: |
          cd backend
          pip install -r requirements.txt
      - name: Lint backend
        run: |
          cd backend
          pip install flake8
          flake8 app
      - name: Test backend
        run: |
          cd backend
          pip install pytest
          pytest 