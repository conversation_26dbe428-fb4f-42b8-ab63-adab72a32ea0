# 快速参考指南

## 🚀 快速启动

### 一键启动（推荐）
```powershell
.\scripts\start_dev_environment.ps1
```

### 手动启动
```bash
# 后端服务
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

# 前端服务（新终端）
cd frontend
npm start
```

### 健康检查
```powershell
.\scripts\health_check.ps1
```

## 🌐 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:3000 | React应用主页 |
| 后端API | http://localhost:8001 | FastAPI服务 |
| API文档 | http://localhost:8001/docs | Swagger文档 |
| 项目管理 | http://localhost:3000/projects | 项目管理页面 |
| 数据输入 | http://localhost:3000/data-input | 数据输入页面 |

## 📁 关键文件位置

### 前端核心文件
```
frontend/src/
├── pages/
│   ├── ProjectManagementPage.tsx    # 项目管理主页面
│   ├── DataInput.tsx               # 数据输入页面
│   └── Dashboard.tsx               # 仪表盘
├── components/
│   ├── ConfirmButton.tsx           # 确认按钮组件
│   └── Sidebar.tsx                 # 侧边栏导航
├── App.tsx                         # 主应用和路由配置
└── api.ts                          # API接口定义
```

### 后端核心文件
```
backend/
├── app/
│   ├── main.py                     # FastAPI主应用
│   ├── api/
│   │   └── project.py              # 项目相关API
│   └── models/                     # 数据模型
├── app.db                          # SQLite数据库
└── requirements.txt                # Python依赖
```

### 配置和脚本
```
├── scripts/
│   ├── start_dev_environment.ps1   # 开发环境启动脚本
│   └── health_check.ps1            # 系统健康检查
├── config/
│   └── current_system_config.json  # 系统配置备份
└── docs/                           # 文档目录
```

## 🔧 常用命令

### 前端开发
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build

# 运行测试
npm test

# 代码检查
npm run lint
```

### 后端开发
```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

# 数据库迁移（如果需要）
alembic upgrade head
```

## 🎯 核心功能测试流程

### 1. 项目管理功能测试
1. 访问 http://localhost:3000/projects
2. 点击"新建项目"创建项目
3. 验证自动跳转到数据输入页面
4. 返回项目管理页面
5. 点击项目名称或"选择"按钮
6. 验证项目选择和跳转功能
7. 测试编辑项目功能
8. 测试删除项目功能（确认对话框）

### 2. 数据输入功能测试
1. 确保已选择项目
2. 验证当前项目信息显示
3. 输入药物信息
4. 测试数据保存功能
5. 验证数据与项目的关联

### 3. 用户工作流测试
1. 项目管理 → 创建项目 → 自动跳转数据输入
2. 数据输入 → 保存数据 → 跳转分析功能
3. 验证项目上下文在各页面间的传递

## 🐛 常见问题解决

### 前端问题

#### 编译错误
```bash
# 清理缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 端口占用
```bash
# 查找占用3000端口的进程
netstat -ano | findstr :3000
# 杀死进程
taskkill /PID <进程ID> /F
```

### 后端问题

#### 数据库连接错误
```bash
# 检查数据库文件
ls backend/app.db

# 重新初始化数据库（如果需要）
cd backend
python -c "from app.database import init_db; init_db()"
```

#### 端口占用
```bash
# 查找占用8001端口的进程
netstat -ano | findstr :8001
# 杀死进程
taskkill /PID <进程ID> /F
```

### 功能问题

#### 删除功能不工作
1. 检查ConfirmButton组件是否正确导入
2. 验证后端API响应格式
3. 检查浏览器控制台错误

#### 项目选择不生效
1. 检查ProjectContext是否正确集成
2. 验证currentProject状态更新
3. 检查页面间的状态传递

## 📊 系统监控

### 性能指标
- 前端页面加载时间 < 3秒
- API响应时间 < 1秒
- 内存使用 < 500MB
- CPU使用率 < 50%

### 健康检查指标
- 后端服务状态: ✅/❌
- 前端服务状态: ✅/❌
- 数据库连接: ✅/❌
- API功能: ✅/❌

## 🔄 版本控制

### Git工作流
```bash
# 查看当前状态
git status

# 添加更改
git add .

# 提交更改
git commit -m "描述更改内容"

# 推送到远程仓库
git push origin main
```

### 重要分支
- `main`: 主分支，稳定版本
- `develop`: 开发分支，新功能开发
- `feature/*`: 功能分支，特定功能开发

## 📝 日志和调试

### 前端调试
- 浏览器开发者工具 (F12)
- React Developer Tools
- Console日志查看

### 后端调试
- FastAPI自动重载日志
- 数据库查询日志
- API请求响应日志

### 日志文件位置
- 前端: 浏览器控制台
- 后端: 终端输出
- 数据库: SQLite日志（如果启用）

## 🚨 紧急情况处理

### 系统崩溃
1. 检查服务状态: `.\scripts\health_check.ps1`
2. 重启服务: `.\scripts\start_dev_environment.ps1`
3. 检查错误日志
4. 恢复最近备份（如果需要）

### 数据丢失
1. 检查数据库文件: `backend/app.db`
2. 恢复备份数据库
3. 重新初始化数据库（最后手段）

### 功能异常
1. 检查最近的代码更改
2. 回滚到上一个稳定版本
3. 逐步测试功能模块
4. 查看错误日志定位问题

## 📞 支持资源

### 文档资源
- 今日工作总结: `docs/daily_work_summary_2025-06-22.md`
- 系统配置: `config/current_system_config.json`
- 明日计划: `docs/tomorrow_work_plan_template.md`

### 在线资源
- React文档: https://reactjs.org/docs
- Ant Design文档: https://ant.design/docs
- FastAPI文档: https://fastapi.tiangolo.com

### 快速联系
- 技术问题: 查看错误日志和文档
- 功能问题: 参考测试流程
- 紧急情况: 按照紧急处理流程

---

**提示**: 这个快速参考指南包含了日常开发中最常用的信息。建议收藏此页面，遇到问题时首先查看这里！ 🔖
