import React, { useState, useContext } from 'react';
import { 
  Steps, 
  Card, 
  Button, 
  Space, 
  Alert, 
  Progress, 
  Tag, 
  Tooltip,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  ExperimentOutlined,
  SafetyCertificateOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { ProjectContext } from '../App';
import { useNavigate } from 'react-router-dom';

const { Step } = Steps;
const { Title, Text } = Typography;

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  status: 'wait' | 'process' | 'finish' | 'error';
  route: string;
  icon: React.ReactNode;
  estimatedTime: string;
  criticalPoints: string[];
  dependencies?: string[];
}

const FormulationWorkflow: React.FC = () => {
  const { currentProject, inputData, analysisResult } = useContext(ProjectContext);
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);

  // 定义制剂研发标准工作流程
  const workflowSteps: WorkflowStep[] = [
    {
      id: 'project-setup',
      title: '项目建立',
      description: '创建项目，定义药物基本信息和研发目标',
      status: currentProject ? 'finish' : 'process',
      route: '/projects',
      icon: <ExperimentOutlined />,
      estimatedTime: '1-2天',
      criticalPoints: [
        '明确药物化学结构和理化性质',
        '确定目标剂型和规格',
        '制定质量标准框架'
      ]
    },
    {
      id: 'data-collection',
      title: '数据收集',
      description: '收集药物稳定性数据、辅料信息等基础数据',
      status: inputData && Object.keys(inputData).length > 0 ? 'finish' : 
              currentProject ? 'process' : 'wait',
      route: '/data-input',
      icon: <BarChartOutlined />,
      estimatedTime: '3-5天',
      criticalPoints: [
        '建立完整的稳定性数据库',
        '收集辅料相容性信息',
        '确定关键工艺参数'
      ],
      dependencies: ['project-setup']
    },
    {
      id: 'excipient-compatibility',
      title: '辅料相容性评估',
      description: '评估主药与辅料的相容性，识别潜在风险',
      status: analysisResult?.compatibility ? 'finish' : 
              inputData ? 'process' : 'wait',
      route: '/excipient-analysis',
      icon: <SafetyCertificateOutlined />,
      estimatedTime: '2-3天',
      criticalPoints: [
        '识别高风险辅料组合',
        '评估化学相互作用',
        '制定风险控制策略'
      ],
      dependencies: ['data-collection']
    },
    {
      id: 'stability-prediction',
      title: '稳定性预测',
      description: '基于数据建立稳定性预测模型，预测货架期',
      status: analysisResult?.stability ? 'finish' : 
              analysisResult?.compatibility ? 'process' : 'wait',
      route: '/stability-prediction',
      icon: <ClockCircleOutlined />,
      estimatedTime: '2-4天',
      criticalPoints: [
        '建立Arrhenius动力学模型',
        '验证预测模型准确性',
        '确定储存条件要求'
      ],
      dependencies: ['excipient-compatibility']
    },
    {
      id: 'formulation-optimization',
      title: '配方优化',
      description: '基于分析结果优化配方组成和工艺参数',
      status: analysisResult?.formulation ? 'finish' : 
              analysisResult?.stability ? 'process' : 'wait',
      route: '/formulation-analysis',
      icon: <ExperimentOutlined />,
      estimatedTime: '5-7天',
      criticalPoints: [
        '优化辅料种类和用量',
        '确定最佳工艺条件',
        '验证配方稳定性'
      ],
      dependencies: ['stability-prediction']
    },
    {
      id: 'final-validation',
      title: '最终验证',
      description: '进行最终的稳定性验证和质量确认',
      status: analysisResult?.validation ? 'finish' : 
              analysisResult?.formulation ? 'process' : 'wait',
      route: '/ai-analysis',
      icon: <CheckCircleOutlined />,
      estimatedTime: '3-5天',
      criticalPoints: [
        '完成稳定性验证试验',
        '确认质量标准符合性',
        '准备注册申报资料'
      ],
      dependencies: ['formulation-optimization']
    }
  ];

  // 计算整体进度
  const completedSteps = workflowSteps.filter(step => step.status === 'finish').length;
  const totalProgress = Math.round((completedSteps / workflowSteps.length) * 100);

  // 获取当前活跃步骤
  const activeStep = workflowSteps.findIndex(step => step.status === 'process');

  const handleStepClick = (stepIndex: number, route: string) => {
    const step = workflowSteps[stepIndex];
    
    // 检查依赖关系
    if (step.dependencies) {
      const unmetDependencies = step.dependencies.filter(depId => {
        const depStep = workflowSteps.find(s => s.id === depId);
        return depStep?.status !== 'finish';
      });
      
      if (unmetDependencies.length > 0) {
        // 可以显示警告或阻止导航
        return;
      }
    }
    
    navigate(route);
  };

  const getStepStatus = (step: WorkflowStep) => {
    switch (step.status) {
      case 'finish':
        return { color: '#52c41a', text: '已完成' };
      case 'process':
        return { color: '#1890ff', text: '进行中' };
      case 'error':
        return { color: '#ff4d4f', text: '有问题' };
      default:
        return { color: '#d9d9d9', text: '待开始' };
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="项目进度"
                  value={totalProgress}
                  suffix="%"
                  valueStyle={{ color: totalProgress > 80 ? '#3f8600' : '#1890ff' }}
                />
                <Progress percent={totalProgress} strokeColor="#1890ff" />
              </Col>
              <Col span={8}>
                <Statistic
                  title="已完成步骤"
                  value={completedSteps}
                  suffix={`/ ${workflowSteps.length}`}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="当前阶段"
                  value={activeStep >= 0 ? workflowSteps[activeStep].title : '已完成'}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Card title="制剂研发工作流程" extra={
        <Space>
          <Tag color="blue">标准化流程</Tag>
          <Tag color="green">智能引导</Tag>
        </Space>
      }>
        <Steps current={activeStep} direction="vertical">
          {workflowSteps.map((step, index) => {
            const status = getStepStatus(step);
            return (
              <Step
                key={step.id}
                title={
                  <Space>
                    {step.icon}
                    <span>{step.title}</span>
                    <Tag color={status.color}>{status.text}</Tag>
                  </Space>
                }
                description={
                  <div>
                    <Text>{step.description}</Text>
                    <div style={{ marginTop: 8 }}>
                      <Text type="secondary">预计用时: {step.estimatedTime}</Text>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text strong>关键要点:</Text>
                      <ul style={{ marginTop: 4, marginBottom: 0 }}>
                        {step.criticalPoints.map((point, i) => (
                          <li key={i}><Text type="secondary">{point}</Text></li>
                        ))}
                      </ul>
                    </div>
                    <div style={{ marginTop: 12 }}>
                      <Button
                        type={step.status === 'process' ? 'primary' : 'default'}
                        size="small"
                        onClick={() => handleStepClick(index, step.route)}
                        disabled={step.dependencies && step.dependencies.some(depId => {
                          const depStep = workflowSteps.find(s => s.id === depId);
                          return depStep?.status !== 'finish';
                        })}
                      >
                        {step.status === 'finish' ? '查看结果' : 
                         step.status === 'process' ? '继续工作' : '开始'}
                      </Button>
                    </div>
                  </div>
                }
                status={step.status}
              />
            );
          })}
        </Steps>
      </Card>

      {activeStep >= 0 && (
        <Card 
          title="当前任务指导" 
          style={{ marginTop: 16 }}
          extra={<WarningOutlined style={{ color: '#faad14' }} />}
        >
          <Alert
            message={`正在进行: ${workflowSteps[activeStep].title}`}
            description={
              <div>
                <p>{workflowSteps[activeStep].description}</p>
                <p><strong>建议关注:</strong></p>
                <ul>
                  {workflowSteps[activeStep].criticalPoints.map((point, i) => (
                    <li key={i}>{point}</li>
                  ))}
                </ul>
              </div>
            }
            type="info"
            showIcon
            action={
              <Button 
                type="primary" 
                onClick={() => navigate(workflowSteps[activeStep].route)}
              >
                立即开始
              </Button>
            }
          />
        </Card>
      )}
    </div>
  );
};

export default FormulationWorkflow;
