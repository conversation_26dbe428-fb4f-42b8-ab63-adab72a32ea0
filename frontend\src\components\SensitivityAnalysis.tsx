import React from 'react';
import { Card, Empty, Spin, Row, Col, Divider, Tooltip, Tag } from 'antd';
import { InfoCircleOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

interface SensitivityAnalysisProps {
  prediction: any;
  detailed?: boolean;
  width?: string | number;
  height?: number;
}

interface SensitivityFactor {
  factor: string;
  impact: number;
  description: string;
}

/**
 * 稳定性敏感性分析组件
 * 展示影响药物稳定性的各种因素及其重要性
 */
const SensitivityAnalysis: React.FC<SensitivityAnalysisProps> = ({
  prediction,
  detailed = false,
  width = '100%',
  height = 400
}) => {
  // 没有数据时显示空状态
  if (!prediction || !prediction.results) {
    return <Empty description="无法生成敏感性分析，数据不足" />;
  }

  // 提取数据
  let sensitivityData: SensitivityFactor[] = [];
  
  // 如果有现成的敏感性数据就使用
  if (prediction.results.sensitivity) {
    sensitivityData = prediction.results.sensitivity;
  } 
  // 否则从预测结果中提取或生成示例数据
  else {
    // 生成示例数据用于展示
    sensitivityData = [
      { factor: '温度', impact: 0.85, description: '温度是影响稳定性的主要因素，每升高10°C，降解速率增加2-3倍。' },
      { factor: '湿度', impact: 0.65, description: '湿度增加可能加速水解反应，尤其对于酯类、酰胺类药物影响显著。' },
      { factor: '光照', impact: 0.45, description: '光照可能引起光氧化反应，导致染色和有效成分含量降低。' },
      { factor: '包装材料', impact: 0.55, description: '包装的水汽透过性和氧气渗透性直接影响产品的保护性。' },
      { factor: 'pH值', impact: 0.70, description: 'pH值影响水解反应速率，尤其对于酯类化合物和酰胺类化合物。' }
    ];
  }

  // 对影响因素进行排序
  sensitivityData.sort((a: SensitivityFactor, b: SensitivityFactor) => b.impact - a.impact);

  // 准备图表数据
  const chartData = sensitivityData.map((item: SensitivityFactor) => ({
    name: item.factor,
    value: item.impact * 100
  }));

  // 图表配置
  const barChartOption = {
    title: {
      text: '敏感性因素分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '影响度 (%)',
      max: 100
    },
    yAxis: {
      type: 'category',
      data: chartData.map((item: {name: string, value: number}) => item.name),
      axisTick: {
        alignWithLabel: true
      }
    },
    series: [
      {
        name: '影响度',
        type: 'bar',
        data: chartData.map((item: {name: string, value: number}) => item.value),
        itemStyle: {
          color: function(params: any) {
            const value = params.value;
            if (value > 70) return '#ff4d4f';
            if (value > 40) return '#faad14';
            return '#52c41a';
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  };

  // 雷达图配置
  const radarChartOption = {
    title: {
      text: '稳定性因素雷达图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: sensitivityData.map((item: SensitivityFactor) => ({
        name: item.factor,
        max: 100
      })),
      radius: '65%'
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: sensitivityData.map((item: SensitivityFactor) => item.impact * 100),
            name: '影响度',
            areaStyle: {
              color: 'rgba(255, 77, 79, 0.3)'
            },
            lineStyle: {
              width: 2
            }
          }
        ]
      }
    ]
  };

  return (
    <div className="sensitivity-analysis">
      {detailed ? (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>稳定性影响因素排序</span>
                    <Tooltip title="展示各因素对药物稳定性的影响程度，越高表示影响越大">
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </div>
                }
                bordered={false}
              >
                <ReactECharts
                  option={barChartOption}
                  style={{ height, width }}
                  opts={{ renderer: 'canvas' }}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>多维影响因素分析</span>
                    <Tooltip title="多维度展示各因素对药物稳定性的综合影响">
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </div>
                }
                bordered={false}
              >
                <ReactECharts
                  option={radarChartOption}
                  style={{ height, width }}
                  opts={{ renderer: 'canvas' }}
                />
              </Card>
            </Col>
          </Row>

          <Divider>详细分析</Divider>

          <Card bordered={false}>
            {sensitivityData.map((item: SensitivityFactor, index: number) => (
              <div key={index} style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Tag color={item.impact > 0.7 ? 'error' : item.impact > 0.4 ? 'warning' : 'success'}>
                    {item.factor}
                  </Tag>
                  <div style={{ flex: 1, marginLeft: 8 }}>
                    <div style={{ fontSize: 16, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                      影响度: {(item.impact * 100).toFixed(0)}%
                      {item.impact > 0.6 ? (
                        <ArrowUpOutlined style={{ color: '#ff4d4f', marginLeft: 4 }} />
                      ) : (
                        <ArrowDownOutlined style={{ color: '#52c41a', marginLeft: 4 }} />
                      )}
                    </div>
                    <div style={{ color: '#666' }}>{item.description}</div>
                  </div>
                </div>
                {index < sensitivityData.length - 1 && <Divider style={{ margin: '12px 0' }} />}
              </div>
            ))}
          </Card>
        </div>
      ) : (
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>稳定性敏感性分析</span>
              <Tooltip title="分析影响药物稳定性的关键因素及其重要性">
                <InfoCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </div>
          }
          bordered={false}
        >
          <ReactECharts
            option={barChartOption}
            style={{ height, width }}
            opts={{ renderer: 'canvas' }}
          />

          <Divider>关键影响因素</Divider>

          <div>
            {sensitivityData.slice(0, 3).map((item: SensitivityFactor, index: number) => (
              <div key={index} style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                <Tag color={item.impact > 0.7 ? 'error' : item.impact > 0.4 ? 'warning' : 'success'}>
                  {item.factor}
                </Tag>
                <div style={{ marginLeft: 8 }}>
                  <div style={{ fontWeight: 'bold' }}>
                    影响度: {(item.impact * 100).toFixed(0)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default SensitivityAnalysis; 