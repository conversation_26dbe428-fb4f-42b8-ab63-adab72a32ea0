from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.services.project_data_service import ProjectDataService
from app.config.database import get_db
from app.models.project import ProjectORM
from app.schemas import ProjectCreateRequest, ProjectUpdateRequest, ProjectResponse

router = APIRouter(
    prefix="/projects",
    tags=["Projects"]
)

# --- Dependency ---
def get_project_service(db: Session = Depends(get_db)) -> ProjectDataService:
    return ProjectDataService(db)

# --- API Routes ---
@router.post("/", response_model=ProjectResponse)
def create_project(
    data: ProjectCreateRequest,
    service: ProjectDataService = Depends(get_project_service)
):
    project_orm = service.create_project(project_data=data)
    return ProjectResponse.model_validate(project_orm)

@router.get("/", response_model=List[ProjectResponse])
def list_projects(
    service: ProjectDataService = Depends(get_project_service),
    skip: int = 0,
    limit: int = 100
):
    projects = service.list_projects(skip=skip, limit=limit)
    return [ProjectResponse.model_validate(p) for p in projects]

@router.get("/{project_id}", response_model=ProjectResponse)
def get_project(
    project_id: int,
    service: ProjectDataService = Depends(get_project_service)
):
    project = service.get_project(project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return ProjectResponse.model_validate(project)

@router.put("/{project_id}", response_model=ProjectResponse)
def update_project(
    project_id: int,
    data: ProjectUpdateRequest,
    service: ProjectDataService = Depends(get_project_service)
):
    project = service.update_project(project_id=project_id, project_data=data)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return ProjectResponse.model_validate(project)

@router.delete("/{project_id}")
def delete_project(
    project_id: int,
    service: ProjectDataService = Depends(get_project_service)
):
    success = service.delete_project(project_id=project_id)
    if not success:
        raise HTTPException(status_code=404, detail="项目不存在")
    return {"ok": True, "message": "项目删除成功"}

# 项目数据保存模型
class ProjectDataSaveRequest(BaseModel):
    drug_name: Optional[str] = None
    cas_number: Optional[str] = None
    molecular_formula: Optional[str] = None
    smiles: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    formulation: Optional[List[Dict[str, Any]]] = None
    packaging_storage: Optional[Dict[str, Any]] = None
    production_process: Optional[str] = None
    notes: Optional[str] = None

@router.post("/{project_id}/save-data")
def save_project_data(
    project_id: int,
    data: ProjectDataSaveRequest,
    service: ProjectDataService = Depends(get_project_service)
):
    """保存项目基本数据"""
    # 检查项目是否存在
    project = service.get_project(project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")

    # 保存数据到项目
    project_data = data.model_dump(exclude_unset=True)

    # 更新项目数据
    update_data = ProjectUpdateRequest(
        name=project.name,
        description=project.description,
        data=project_data  # 将数据保存到项目的data字段
    )

    updated_project = service.update_project(project_id=project_id, project_data=update_data)

    return {
        "success": True,
        "message": "项目数据保存成功",
        "project_id": project_id,
        "saved_data": project_data
    }

@router.get("/{project_id}/data")
def get_project_data(
    project_id: int,
    service: ProjectDataService = Depends(get_project_service)
):
    """获取项目基本数据"""
    try:
        project = service.get_project(project_id=project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 返回项目数据
        project_data = getattr(project, 'data', {}) or {}

        return {
            "success": True,
            "project_id": project_id,
            "project_name": project.name,
            "data": project_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 分析结果保存模型
class AnalysisResultSaveRequest(BaseModel):
    analysis_type: str  # 分析类型：compatibility, stability, etc.
    analysis_date: str
    analysis_data: Dict[str, Any]  # 分析结果数据
    summary: Optional[str] = None  # 分析摘要

@router.post("/{project_id}/save-analysis")
def save_analysis_result(
    project_id: int,
    data: AnalysisResultSaveRequest,
    service: ProjectDataService = Depends(get_project_service)
):
    """保存分析结果到项目"""
    # 检查项目是否存在
    project = service.get_project(project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")

    # 获取现有项目数据
    existing_data = getattr(project, 'data', {}) or {}

    # 初始化分析结果存储结构
    if 'analysis_results' not in existing_data:
        existing_data['analysis_results'] = {}

    if data.analysis_type not in existing_data['analysis_results']:
        existing_data['analysis_results'][data.analysis_type] = []

    # 添加新的分析结果
    analysis_result = {
        'date': data.analysis_date,
        'data': data.analysis_data,
        'summary': data.summary,
        'id': len(existing_data['analysis_results'][data.analysis_type]) + 1
    }

    existing_data['analysis_results'][data.analysis_type].append(analysis_result)

    # 更新项目数据
    update_data = ProjectUpdateRequest(
        name=project.name,
        description=project.description,
        data=existing_data
    )

    updated_project = service.update_project(project_id=project_id, project_data=update_data)

    return {
        "success": True,
        "message": f"{data.analysis_type}分析结果保存成功",
        "project_id": project_id,
        "analysis_id": analysis_result['id']
    }

@router.get("/{project_id}/analysis/{analysis_type}")
def get_analysis_results(
    project_id: int,
    analysis_type: str,
    service: ProjectDataService = Depends(get_project_service)
):
    """获取项目的分析结果"""
    try:
        project = service.get_project(project_id=project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 获取分析结果
        project_data = getattr(project, 'data', {}) or {}
        analysis_results = project_data.get('analysis_results', {}).get(analysis_type, [])

        return {
            "success": True,
            "project_id": project_id,
            "analysis_type": analysis_type,
            "results": analysis_results
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析结果失败: {str(e)}")