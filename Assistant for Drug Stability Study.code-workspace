{"folders": [{"path": "."}], "settings": {"python.defaultInterpreterPath": "./.venv/Scripts/python.exe", "python.analysis.typeCheckingMode": "off", "python.analysis.diagnosticMode": "openFilesOnly", "python.analysis.exclude": ["backend/**", "frontend/**", "node_modules/**", ".venv/**"], "python.analysis.ignore": ["backend/**", "frontend/**"], "python.linting.enabled": false, "pylance.insidersChannel": "off"}, "extensions": {"recommendations": ["ms-python.python", "ms-python.pylance"]}}