"""
批量数据导入API端点
"""
from fastapi import APIRouter, HTTPException, UploadFile, File, Query, Depends
from fastapi.responses import StreamingResponse
from typing import Optional, List, Dict, Any
import io
from datetime import datetime
import json

from app.api.auth import get_current_user
from app.services.data_import_service import data_import_service

router = APIRouter()

# 模拟导入历史数据存储（实际应该使用数据库）
import_history = []

@router.post("/import")
async def import_data(
    file: UploadFile = File(...),
    data_type: str = Query(..., description="数据类型: excipients/drugs/stability_data"),
    validate_only: bool = Query(False, description="仅验证不导入"),
    update_existing: bool = Query(False, description="更新已存在的记录"),
    current_user: dict = Depends(get_current_user)
):
    """
    批量导入数据
    
    支持的文件格式：Excel (.xlsx, .xls) 和 CSV (.csv)
    支持的数据类型：
    - excipients: 辅料信息
    - drugs: 药物信息  
    - stability_data: 稳定性数据
    """
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 调用导入服务
        result = await data_import_service.import_data(
            file_content=file_content,
            file_name=file.filename,
            data_type=data_type,
            validate_only=validate_only,
            update_existing=update_existing
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/template/{data_type}")
async def download_template(
    data_type: str,
    current_user: dict = Depends(get_current_user)
):
    """
    下载导入模板
    
    Args:
        data_type: 数据类型 (excipients/drugs/stability_data)
    
    Returns:
        Excel模板文件
    """
    try:
        # 生成模板
        template_content = data_import_service.generate_template(data_type)
        
        # 设置文件名
        filename = f"{data_type}_import_template.xlsx"
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(template_content),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates")
async def list_templates(
    current_user: dict = Depends(get_current_user)
):
    """获取所有可用的导入模板信息"""
    templates = data_import_service.import_templates
    
    return {
        "templates": [
            {
                "type": key,
                "name": value["name"],
                "required_columns": value["required_columns"],
                "optional_columns": value["optional_columns"],
                "download_url": f"/api/data-import/template/{key}"
            }
            for key, value in templates.items()
        ]
    }


@router.get("/validation-rules")
async def get_validation_rules(
    current_user: dict = Depends(get_current_user)
):
    """获取数据验证规则"""
    return {
        "rules": data_import_service.validation_rules,
        "supported_formats": data_import_service.supported_formats
    }


@router.get("/history")
async def get_import_history(
    project_id: Optional[str] = Query(None, description="项目ID"),
    limit: int = Query(100, description="返回记录数限制")
):
    """获取导入历史记录"""
    # 过滤项目相关的历史记录
    filtered_history = import_history
    if project_id:
        filtered_history = [h for h in import_history if h.get("project_id") == project_id]
    
    # 返回最新的记录
    return {
        "history": filtered_history[-limit:][::-1]  # 返回最新的记录，倒序排列
    }


@router.post("/history/add")
async def add_import_record(record: Dict[str, Any]):
    """添加导入历史记录（内部使用）"""
    record_with_id = {
        "id": f"import_{datetime.now().timestamp()}",
        "import_time": datetime.now().isoformat(),
        **record
    }
    import_history.append(record_with_id)
    return {"success": True, "record": record_with_id}


@router.delete("/history/clear")
async def clear_import_history():
    """清理导入历史记录"""
    import_history.clear()
    return {"success": True, "message": "导入历史已清理"} 