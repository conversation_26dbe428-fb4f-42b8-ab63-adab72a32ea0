from pydantic import BaseModel
from typing import Optional, List
from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
import datetime
from app.models.project import ProjectORM
from app.models.drug import DrugORM
from app.models.excipient import ExcipientORM
from app.models.environment import EnvironmentORM
from app.models.stability import StabilityDataORM

Base = declarative_base()

class User(BaseModel):
    username: str
    role: str

class Project(BaseModel):
    id: int
    name: str
    status: str
    created_at: str

class AISuggestion(BaseModel):
    id: int
    title: str
    desc: str
    risk: str
    detail: str
    type: Optional[str] = None

class ExportHistory(BaseModel):
    id: int
    file_name: str
    export_time: str
    exported_by: str
    options: List[str]
    url: str

class UserORM(Base):
    """
    用户表ORM模型。
    - id: 主键
    - username: 用户名
    - password: 密码（加密）
    - role: 角色
    """
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(64), unique=True, nullable=False)
    password = Column(String(128), nullable=False)
    role = Column(String(32), default='user')

class APIKeyORM(Base):
    """
    API Key表ORM模型。
    - id: 主键
    - key: API Key字符串
    - enabled: 是否可用
    - created_at: 创建时间
    - user_id: 关联用户ID
    """
    __tablename__ = 'api_keys'
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(64), unique=True, nullable=False)
    enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    user_id = Column(Integer)

class OperationLogORM(Base):
    """
    操作日志表ORM模型。
    - id: 主键
    - time: 操作时间
    - action: 操作类型
    - detail: 详情
    - user_id: 关联用户ID
    """
    __tablename__ = 'operation_logs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    time = Column(DateTime, default=datetime.datetime.utcnow)
    action = Column(String(64))
    detail = Column(String(256))
    user_id = Column(Integer) 