# ===========================================
# 依赖和包管理
# ===========================================
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ===========================================
# 环境变量和配置文件
# ===========================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config/local.json
config/production.json

# ===========================================
# 数据库文件
# ===========================================
*.db
*.sqlite
*.sqlite3
test.db
app.db

# ===========================================
# 日志文件
# ===========================================
*.log
logs/
log/

# ===========================================
# 缓存文件
# ===========================================
.cache/
cache/
.pytest_cache/
.coverage
htmlcov/

# ===========================================
# IDE和编辑器文件
# ===========================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ===========================================
# 临时文件和输出
# ===========================================
temp/
tmp/
*.tmp
*.temp
test_output/
output/

# ===========================================
# 安全敏感文件
# ===========================================
*.key
*.pem
*.crt
*.p12
secrets/
credentials/

# ===========================================
# 测试和调试文件
# ===========================================
test_*.py
debug_*.py
fix_*.py
check_*.py
verify_*.py
*_test.py
*_debug.py

# ===========================================
# 报告和文档临时文件
# ===========================================
*.pdf
*.docx
*.xlsx
reports/
exports/

# ===========================================
# 前端构建文件
# ===========================================
build/
dist/
.next/
out/

# ===========================================
# Python虚拟环境
# ===========================================
venv/
env/
ENV/
.venv/
