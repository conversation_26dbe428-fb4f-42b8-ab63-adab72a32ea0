"""
分子结构处理服务
提供SMILES验证、标准化、可视化和相似性计算功能
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
import base64
from io import BytesIO

# 导入RDKit
from rdkit import Chem
from rdkit.Chem import Draw, Descriptors, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from rdkit.Chem import AllChem, DataStructs
from rdkit.Chem import rdMolDescriptors

logger = logging.getLogger(__name__)


class StructureService:
    """分子结构处理服务类"""
    
    def validate_smiles(self, smiles: str) -> Dict[str, Any]:
        """验证SMILES字符串"""
        result = {
            "valid": False,
            "canonical_smiles": None,
            "errors": [],
            "warnings": []
        }
        
        if not smiles or not isinstance(smiles, str):
            result["errors"].append("SMILES字符串不能为空")
            return result
        
        try:
            # 尝试解析SMILES
            mol = Chem.MolFromSmiles(smiles)
            
            if mol is None:
                result["errors"].append("无效的SMILES字符串")
                return result
            
            # 生成标准化SMILES
            canonical_smiles = Chem.MolToSmiles(mol, canonical=True)
            result["valid"] = True
            result["canonical_smiles"] = canonical_smiles
            
            # 检查一些潜在问题
            if mol.GetNumAtoms() == 0:
                result["warnings"].append("分子不包含原子")
            
            if mol.GetNumBonds() == 0 and mol.GetNumAtoms() > 1:
                result["warnings"].append("分子不包含化学键")
            
            # 检查是否包含未定义的立体化学
            if '@' in smiles and '@' not in canonical_smiles:
                result["warnings"].append("立体化学信息可能丢失")
            
        except Exception as e:
            result["errors"].append(f"SMILES解析错误: {str(e)}")
            
        return result
    
    def calculate_properties(self, smiles: str) -> Dict[str, Any]:
        """计算分子性质"""
        properties = {
            "molecular_weight": None,
            "logp": None,
            "tpsa": None,
            "num_hbd": None,
            "num_hba": None,
            "num_rotatable_bonds": None,
            "num_aromatic_rings": None,
            "num_heavy_atoms": None,
            "molecular_formula": None,
            "inchi": None,
            "inchi_key": None
        }
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return properties
            
            # 基本性质
            properties["molecular_weight"] = round(Descriptors.MolWt(mol), 2)
            properties["molecular_formula"] = rdMolDescriptors.CalcMolFormula(mol)
            
            # 物理化学性质
            properties["logp"] = round(Crippen.MolLogP(mol), 2)
            properties["tpsa"] = round(Descriptors.TPSA(mol), 2)
            
            # 氢键供体和受体
            properties["num_hbd"] = Lipinski.NumHDonors(mol)
            properties["num_hba"] = Lipinski.NumHAcceptors(mol)
            
            # 结构特征
            properties["num_rotatable_bonds"] = Lipinski.NumRotatableBonds(mol)
            properties["num_aromatic_rings"] = rdMolDescriptors.CalcNumAromaticRings(mol)
            properties["num_heavy_atoms"] = mol.GetNumHeavyAtoms()
            
            # InChI
            try:
                properties["inchi"] = Chem.MolToInchi(mol)
                properties["inchi_key"] = Chem.MolToInchiKey(mol)
            except:
                pass
                
        except Exception as e:
            logger.error(f"计算分子性质时出错: {e}")
            
        return properties
    
    def generate_2d_image(self, smiles: str, size: Tuple[int, int] = (300, 300)) -> Optional[str]:
        """生成2D分子图像（返回base64编码）"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None
            
            # 生成2D坐标
            AllChem.Compute2DCoords(mol)
            
            # 绘制分子
            img = Draw.MolToImage(mol, size=size)
            
            # 转换为base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"生成分子图像时出错: {e}")
            return None
    
    def highlight_functional_groups(self, smiles: str, functional_groups: List[str]) -> Optional[str]:
        """高亮显示官能团"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None
            
            # 定义常见官能团的SMARTS模式
            group_patterns = {
                "amine": "[NX3;H2,H1;!$(NC=O)]",
                "carboxylic_acid": "[CX3](=O)[OX2H1]",
                "ester": "[#6][CX3](=O)[OX2H0][#6]",
                "amide": "[NX3][CX3](=[OX1])[#6]",
                "alcohol": "[OX2H]",
                "aldehyde": "[CX3H1](=O)[#6]",
                "ketone": "[#6][CX3](=O)[#6]",
                "ether": "[OD2]([#6])[#6]",
                "phenol": "[OX2H][cX3]:[c]",
                "thiol": "[SX2H]"
            }
            
            # 找出匹配的原子
            highlight_atoms = []
            for group in functional_groups:
                if group in group_patterns:
                    pattern = Chem.MolFromSmarts(group_patterns[group])
                    if pattern:
                        matches = mol.GetSubstructMatches(pattern)
                        for match in matches:
                            highlight_atoms.extend(match)
            
            # 生成高亮图像
            AllChem.Compute2DCoords(mol)
            img = Draw.MolToImage(mol, size=(400, 400), 
                                highlightAtoms=highlight_atoms)
            
            # 转换为base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"高亮官能团时出错: {e}")
            return None
    
    def calculate_similarity(self, smiles1: str, smiles2: str, 
                           method: str = "tanimoto") -> Optional[float]:
        """计算两个分子的相似度"""
        try:
            mol1 = Chem.MolFromSmiles(smiles1)
            mol2 = Chem.MolFromSmiles(smiles2)
            
            if mol1 is None or mol2 is None:
                return None
            
            # 生成Morgan指纹
            fp1 = AllChem.GetMorganFingerprintAsBitVect(mol1, 2, nBits=2048)
            fp2 = AllChem.GetMorganFingerprintAsBitVect(mol2, 2, nBits=2048)
            
            # 计算相似度
            if method == "tanimoto":
                similarity = DataStructs.TanimotoSimilarity(fp1, fp2)
            elif method == "dice":
                similarity = DataStructs.DiceSimilarity(fp1, fp2)
            else:
                similarity = DataStructs.TanimotoSimilarity(fp1, fp2)
            
            return round(similarity, 4)
            
        except Exception as e:
            logger.error(f"计算分子相似度时出错: {e}")
            return None
    
    def find_similar_structures(self, smiles: str, 
                              smiles_list: List[str], 
                              threshold: float = 0.7) -> List[Dict[str, Any]]:
        """在列表中查找相似结构"""
        similar_structures = []
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return []
            
            fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
            
            for idx, candidate_smiles in enumerate(smiles_list):
                try:
                    candidate_mol = Chem.MolFromSmiles(candidate_smiles)
                    if candidate_mol is None:
                        continue
                    
                    candidate_fp = AllChem.GetMorganFingerprintAsBitVect(
                        candidate_mol, 2, nBits=2048
                    )
                    
                    similarity = DataStructs.TanimotoSimilarity(fp, candidate_fp)
                    
                    if similarity >= threshold:
                        similar_structures.append({
                            "smiles": candidate_smiles,
                            "similarity": round(similarity, 3),
                            "index": idx
                        })
                except:
                    continue
            
            # 按相似度排序
            similar_structures.sort(key=lambda x: x["similarity"], reverse=True)
            
        except Exception as e:
            logger.error(f"查找相似结构时出错: {e}")
            
        return similar_structures
    
    def substructure_search(self, smiles: str, 
                          pattern_smarts: str) -> Dict[str, Any]:
        """子结构搜索"""
        result = {
            "has_match": False,
            "num_matches": 0,
            "match_atoms": []
        }
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            pattern = Chem.MolFromSmarts(pattern_smarts)
            
            if mol is None or pattern is None:
                return result
            
            matches = mol.GetSubstructMatches(pattern)
            
            if matches:
                result["has_match"] = True
                result["num_matches"] = len(matches)
                result["match_atoms"] = [list(match) for match in matches]
                
        except Exception as e:
            logger.error(f"子结构搜索时出错: {e}")
            
        return result
    
    def identify_functional_groups_detailed(self, smiles: str) -> List[Dict[str, Any]]:
        """详细识别分子中的官能团"""
        functional_groups = []
        
        # 扩展的官能团定义
        group_definitions = {
            "primary_amine": {
                "smarts": "[NX3;H2][CX4]",
                "name": "伯胺",
                "reactivity": "高",
                "reactions": ["酰化", "烷基化", "席夫碱形成"]
            },
            "secondary_amine": {
                "smarts": "[NX3;H1]([CX4])[CX4]",
                "name": "仲胺",
                "reactivity": "高",
                "reactions": ["酰化", "烷基化", "氧化"]
            },
            "tertiary_amine": {
                "smarts": "[NX3]([CX4])([CX4])[CX4]",
                "name": "叔胺",
                "reactivity": "中",
                "reactions": ["季铵化", "氧化"]
            },
            "carboxylic_acid": {
                "smarts": "[CX3](=O)[OX2H1]",
                "name": "羧酸",
                "reactivity": "高",
                "reactions": ["酯化", "酰胺化", "脱羧"]
            },
            "ester": {
                "smarts": "[#6][CX3](=O)[OX2H0][#6]",
                "name": "酯",
                "reactivity": "中",
                "reactions": ["水解", "氨解", "转酯化"]
            },
            "amide": {
                "smarts": "[NX3][CX3](=[OX1])[#6]",
                "name": "酰胺",
                "reactivity": "低",
                "reactions": ["水解", "霍夫曼降解"]
            },
            "alcohol": {
                "smarts": "[OX2H][CX4]",
                "name": "醇",
                "reactivity": "中",
                "reactions": ["氧化", "酯化", "醚化"]
            },
            "phenol": {
                "smarts": "[OX2H][cX3]:[c]",
                "name": "酚",
                "reactivity": "高",
                "reactions": ["酯化", "醚化", "氧化"]
            },
            "aldehyde": {
                "smarts": "[CX3H1](=O)[#6]",
                "name": "醛",
                "reactivity": "高",
                "reactions": ["氧化", "还原", "缩合"]
            },
            "ketone": {
                "smarts": "[#6][CX3](=O)[#6]",
                "name": "酮",
                "reactivity": "中",
                "reactions": ["还原", "缩合", "氧化"]
            }
        }
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return []
            
            for group_id, group_info in group_definitions.items():
                pattern = Chem.MolFromSmarts(group_info["smarts"])
                if pattern:
                    matches = mol.GetSubstructMatches(pattern)
                    if matches:
                        functional_groups.append({
                            "id": group_id,
                            "name": group_info["name"],
                            "count": len(matches),
                            "reactivity": group_info["reactivity"],
                            "possible_reactions": group_info["reactions"],
                            "atom_indices": [list(match) for match in matches]
                        })
                        
        except Exception as e:
            logger.error(f"识别官能团时出错: {e}")
            
        return functional_groups
    
    def check_drug_likeness(self, smiles: str) -> Dict[str, Any]:
        """检查类药性（Lipinski规则）"""
        result = {
            "passes_lipinski": False,
            "violations": [],
            "properties": {}
        }
        
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return result
            
            # 计算Lipinski性质
            mw = Descriptors.MolWt(mol)
            logp = Crippen.MolLogP(mol)
            hbd = Lipinski.NumHDonors(mol)
            hba = Lipinski.NumHAcceptors(mol)
            
            result["properties"] = {
                "molecular_weight": round(mw, 2),
                "logp": round(logp, 2),
                "hbd": hbd,
                "hba": hba
            }
            
            # 检查违规
            violations = 0
            if mw > 500:
                result["violations"].append("分子量 > 500")
                violations += 1
            if logp > 5:
                result["violations"].append("LogP > 5")
                violations += 1
            if hbd > 5:
                result["violations"].append("氢键供体 > 5")
                violations += 1
            if hba > 10:
                result["violations"].append("氢键受体 > 10")
                violations += 1
            
            # Lipinski规则：最多1个违规
            result["passes_lipinski"] = violations <= 1
            
        except Exception as e:
            logger.error(f"检查类药性时出错: {e}")
            
        return result


# 创建全局实例
structure_service = StructureService() 