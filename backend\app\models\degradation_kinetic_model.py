"""
高级药物降解动力学模型
基于ICH Q1E和最新药物稳定性研究理论
"""

import numpy as np
from scipy import optimize, stats
from scipy.integrate import odeint
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')


# 改名为DegradationKineticModel以匹配导入
class DegradationKineticModel:
    """高级降解动力学模型类"""
    
    def __init__(self):
        self.R = 8.314  # 气体常数, J/(mol·K)
        self.models = {
            "zero-order": self.zero_order_model,
            "first-order": self.first_order_model,
            "second-order": self.second_order_model,
            "weibull": self.weibull_model,
            "biphasic": self.biphasic_model,
            "avrami-erofeev": self.avrami_erofeev_model,
            "prout-tompkins": self.prout_tompkins_model,
            "jander": self.jander_model
        }
        
    # 基础动力学模型
    def zero_order_model(self, t, k, c0):
        """零级反应: C = C0 - kt"""
        return c0 - k * t
    
    def first_order_model(self, t, k, c0):
        """一级反应: C = C0 * exp(-kt)"""
        return c0 * np.exp(-k * t)
    
    def second_order_model(self, t, k, c0):
        """二级反应: 1/C = 1/C0 + kt"""
        return c0 / (1 + k * t * c0)
    
    # 高级动力学模型
    def weibull_model(self, t, k, c0, beta):
        """Weibull模型: C = C0 * exp(-(kt)^beta)
        适用于复杂降解机制"""
        return c0 * np.exp(-(k * t) ** beta)
    
    def biphasic_model(self, t, k1, k2, c0, alpha):
        """双相模型: C = C0 * [alpha * exp(-k1*t) + (1-alpha) * exp(-k2*t)]
        适用于多种降解途径并存"""
        return c0 * (alpha * np.exp(-k1 * t) + (1 - alpha) * np.exp(-k2 * t))
    
    # 固态反应动力学模型
    def avrami_erofeev_model(self, t, k, n):
        """Avrami-Erofeev模型: α = 1 - exp(-(kt)^n)
        适用于成核和生长控制的固态反应"""
        return 1 - np.exp(-(k * t) ** n)
    
    def prout_tompkins_model(self, t, k, alpha0=0.001):
        """Prout-Tompkins模型: dα/dt = k*α*(1-α)
        适用于自催化反应"""
        def deriv(alpha, t):
            return k * alpha * (1 - alpha)
        
        t_array = np.atleast_1d(t)
        alpha = odeint(deriv, alpha0, t_array)
        return alpha.flatten()[0] if np.isscalar(t) else alpha.flatten()
    
    def jander_model(self, t, k):
        """Jander模型: [1-(1-α)^(1/3)]^2 = kt
        适用于三维扩散控制的固态反应"""
        kt = k * t
        # 避免数值问题
        kt = np.minimum(kt, 0.99)
        return 1 - (1 - np.sqrt(kt)) ** 3
    
    # 温度依赖性模型
    def arrhenius_equation(self, T, A, Ea):
        """Arrhenius方程: k = A * exp(-Ea/RT)"""
        return A * np.exp(-Ea / (self.R * T))
    
    def modified_arrhenius(self, T, A, Ea, n):
        """修正Arrhenius方程: k = A * T^n * exp(-Ea/RT)"""
        return A * (T ** n) * np.exp(-Ea / (self.R * T))
    
    def eyring_equation(self, T, delta_H, delta_S):
        """Eyring方程: k = (kB*T/h) * exp(ΔS/R) * exp(-ΔH/RT)"""
        kB = 1.381e-23  # Boltzmann常数
        h = 6.626e-34   # Planck常数
        return (kB * T / h) * np.exp(delta_S / self.R) * np.exp(-delta_H / (self.R * T))
    
    # 湿度依赖性模型
    def humidity_modified_arrhenius(self, T, RH, A, Ea, B):
        """湿度修正的Arrhenius方程: k = A * exp(-Ea/RT) * exp(B*RH)"""
        return self.arrhenius_equation(T, A, Ea) * np.exp(B * RH / 100)
    
    def vtf_equation(self, T, RH, A, B, T0, RH0):
        """Vogel-Tammann-Fulcher方程
        考虑玻璃化转变温度的影响"""
        Tg = T0 - B * (RH - RH0)  # 玻璃化转变温度
        if T > Tg:
            return A * np.exp(-B / (T - Tg))
        else:
            return 0  # 低于Tg时反应极慢
    
    # 非等温动力学
    def ozawa_flynn_wall_method(self, T_data: List[np.ndarray], 
                                alpha_data: List[np.ndarray], 
                                heating_rates: List[float]) -> Dict[str, Any]:
        """Ozawa-Flynn-Wall法计算活化能
        适用于非等温条件下的动力学分析"""
        
        # 选择转化率
        alpha_values = np.arange(0.1, 0.91, 0.1)
        Ea_values = []
        
        for alpha in alpha_values:
            T_at_alpha = []
            for i, (T, alpha_curve) in enumerate(zip(T_data, alpha_data)):
                # 找到对应转化率的温度
                idx = np.argmin(np.abs(alpha_curve - alpha))
                T_at_alpha.append(T[idx])
            
            # ln(β) vs 1/T 线性回归
            x = 1 / np.array(T_at_alpha)
            y = np.log(heating_rates)
            
            slope, _, _, _, _ = stats.linregress(x, y)
            Ea = -slope * self.R / 1.052  # OFW修正因子
            Ea_values.append(Ea)
        
        return {
            "alpha": alpha_values,
            "Ea": np.array(Ea_values),
            "Ea_mean": np.mean(Ea_values),
            "Ea_std": np.std(Ea_values)
        }
    
    def kissinger_method(self, Tp_data: np.ndarray, 
                        heating_rates: np.ndarray) -> Dict[str, float]:
        """Kissinger法计算活化能
        基于峰值温度的分析"""
        
        # ln(β/Tp²) vs 1/Tp
        x = 1 / Tp_data
        y = np.log(heating_rates / (Tp_data ** 2))
        
        slope, intercept, r_value, _, _ = stats.linregress(x, y)
        
        Ea = -slope * self.R
        A = np.exp(intercept + Ea / (self.R * np.mean(Tp_data)))
        
        return {
            "Ea": Ea,
            "A": A,
            "r_squared": r_value ** 2
        }
    
    # 模型选择和拟合
    def fit_best_model(self, time_data: np.ndarray, 
                      conc_data: np.ndarray,
                      models_to_test: Optional[List[str]] = None) -> Dict[str, Any]:
        """自动选择最佳拟合模型"""
        
        if models_to_test is None:
            models_to_test = ["zero-order", "first-order", "second-order", 
                            "weibull", "biphasic"]
        
        results = {}
        best_aic = float('inf')
        best_model = None
        
        for model_name in models_to_test:
            try:
                if model_name == "zero-order":
                    p0 = [0.1, conc_data[0]]
                    bounds = ([0, 0], [np.inf, np.inf])
                elif model_name == "first-order":
                    p0 = [0.01, conc_data[0]]
                    bounds = ([0, 0], [np.inf, np.inf])
                elif model_name == "second-order":
                    p0 = [0.001, conc_data[0]]
                    bounds = ([0, 0], [np.inf, np.inf])
                elif model_name == "weibull":
                    p0 = [0.01, conc_data[0], 1.0]
                    bounds = ([0, 0, 0.1], [np.inf, np.inf, 10])
                elif model_name == "biphasic":
                    p0 = [0.1, 0.01, conc_data[0], 0.5]
                    bounds = ([0, 0, 0, 0], [np.inf, np.inf, np.inf, 1])
                else:
                    continue
                
                # 拟合模型
                popt, pcov = optimize.curve_fit(
                    self.models[model_name], 
                    time_data, 
                    conc_data, 
                    p0=p0,
                    bounds=bounds,
                    maxfev=10000
                )
                
                # 计算拟合优度
                y_pred = self.models[model_name](time_data, *popt)
                residuals = conc_data - y_pred
                ss_res = np.sum(residuals ** 2)
                ss_tot = np.sum((conc_data - np.mean(conc_data)) ** 2)
                r_squared = 1 - (ss_res / ss_tot)
                
                # 计算AIC和BIC
                n = len(time_data)
                k = len(popt)
                aic = n * np.log(ss_res / n) + 2 * k
                bic = n * np.log(ss_res / n) + k * np.log(n)
                
                # 计算RMSE
                rmse = np.sqrt(ss_res / n)
                
                # 参数标准误差
                perr = np.sqrt(np.diag(pcov))
                
                results[model_name] = {
                    "params": popt,
                    "params_err": perr,
                    "r_squared": r_squared,
                    "aic": aic,
                    "bic": bic,
                    "rmse": rmse
                }
                
                if aic < best_aic:
                    best_aic = aic
                    best_model = model_name
                    
            except Exception as e:
                print(f"模型 {model_name} 拟合失败: {str(e)}")
                continue
        
        if best_model:
            results["best_model"] = best_model
        
        return results
    
    # 货架期预测
    def predict_shelf_life(self, model_name: str, 
                          params: np.ndarray,
                          threshold: float = 90.0,
                          confidence_level: float = 0.95,
                          params_cov: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """预测货架期（t90或其他阈值）"""
        
        # 计算达到阈值的时间
        if model_name == "zero-order":
            k, c0 = params
            t_shelf = (c0 - threshold) / k
        elif model_name == "first-order":
            k, c0 = params
            t_shelf = -np.log(threshold / c0) / k
        elif model_name == "second-order":
            k, c0 = params
            t_shelf = (c0 / threshold - 1) / (k * c0)
        elif model_name == "weibull":
            k, c0, beta = params
            t_shelf = (-np.log(threshold / c0)) ** (1 / beta) / k
        else:
            # 数值求解
            def objective(t):
                return self.models[model_name](t, *params) - threshold
            
            t_shelf = optimize.brentq(objective, 0, 1000)
        
        # 计算置信区间
        if params_cov is not None:
            # Monte Carlo模拟
            n_simulations = 10000
            t_shelf_samples = []
            
            for _ in range(n_simulations):
                # 从多元正态分布采样参数
                params_sample = np.random.multivariate_normal(params, params_cov)
                
                try:
                    if model_name == "zero-order":
                        k, c0 = params_sample
                        t = (c0 - threshold) / k
                    elif model_name == "first-order":
                        k, c0 = params_sample
                        t = -np.log(threshold / c0) / k
                    # ... 其他模型类似
                    
                    if t > 0 and t < 1000:
                        t_shelf_samples.append(t)
                except:
                    continue
            
            if t_shelf_samples:
                lower_ci = np.percentile(t_shelf_samples, (1 - confidence_level) / 2 * 100)
                upper_ci = np.percentile(t_shelf_samples, (1 + confidence_level) / 2 * 100)
            else:
                lower_ci = t_shelf * 0.8
                upper_ci = t_shelf * 1.2
        else:
            # 简单估计
            lower_ci = t_shelf * 0.8
            upper_ci = t_shelf * 1.2
        
        return {
            "t_shelf": t_shelf,
            "ci_lower": lower_ci,
            "ci_upper": upper_ci,
            "confidence_level": confidence_level
        }
    
    # 加速试验设计
    def design_accelerated_study(self, 
                               target_shelf_life: float,
                               storage_temp: float = 25.0,
                               Ea_estimate: float = 80.0) -> Dict[str, Any]:
        """设计加速稳定性试验"""
        
        # 推荐的加速条件
        accelerated_conditions = [
            {"temp": 40, "RH": 75, "duration": 6},
            {"temp": 30, "RH": 65, "duration": 12},
            {"temp": 25, "RH": 60, "duration": target_shelf_life}
        ]
        
        # 计算加速因子
        factors = []
        for condition in accelerated_conditions:
            T_acc = condition["temp"] + 273.15
            T_storage = storage_temp + 273.15
            
            # Arrhenius加速因子
            factor = np.exp((Ea_estimate * 1000 / self.R) * (1/T_storage - 1/T_acc))
            factors.append(factor)
            
            # 等效储存时间
            condition["equivalent_time"] = condition["duration"] * factor
        
        return {
            "conditions": accelerated_conditions,
            "acceleration_factors": factors,
            "estimated_Ea": Ea_estimate
        }

# 添加高级降解动力学模型类，继承自DegradationKineticModel
class AdvancedDegradationKineticModel(DegradationKineticModel):
    """
    高级降解动力学模型类
    扩展了基础降解动力学模型，增加更多高级功能
    """
    
    def __init__(self):
        super().__init__()
        self.advanced_models = {
            "ml-linear": self.ml_linear_model,
            "ml-rf": self.ml_random_forest_model,
            "ml-nn": self.ml_neural_network_model,
            "monte-carlo": self.monte_carlo_simulation,
            "bayesian": self.bayesian_model
        }
        # 合并基础模型和高级模型
        self.models.update(self.advanced_models)
        
    # 机器学习模型预测
    def ml_linear_model(self, t, *params):
        """线性回归模型预测"""
        # 简化版实现，仅作为接口
        slope, intercept = params
        return intercept + slope * t
    
    def ml_random_forest_model(self, t, *params):
        """随机森林模型预测"""
        # 简化版实现，实际应用中需要使用scikit-learn
        # 这里仅作为接口占位
        return np.ones_like(t) * 95.0  # 返回固定值作为示例
    
    def ml_neural_network_model(self, t, *params):
        """神经网络模型预测"""
        # 简化版实现，实际应用中需要使用TensorFlow/PyTorch
        # 这里仅作为接口占位
        return 100.0 * np.exp(-0.01 * t)  # 返回简单的衰减函数作为示例
    
    def monte_carlo_simulation(self, time_points, model_name, params, n_simulations=1000):
        """
        蒙特卡洛模拟，计算预测不确定性
        """
        # 假设参数服从正态分布，实际使用中需要根据参数协方差矩阵生成
        results = np.zeros((n_simulations, len(time_points)))
        
        # 生成模拟参数
        sim_params = np.random.normal(
            loc=params, 
            scale=np.abs(params) * 0.05,  # 假设5%的相对误差
            size=(n_simulations, len(params))
        )
        
        # 对每组参数进行模拟
        for i in range(n_simulations):
            results[i, :] = self.models[model_name](time_points, *sim_params[i, :])
            
        # 计算每个时间点的平均值和置信区间
        mean_values = np.mean(results, axis=0)
        lower_ci = np.percentile(results, 2.5, axis=0)
        upper_ci = np.percentile(results, 97.5, axis=0)
        
        return {
            "mean": mean_values,
            "lower_ci": lower_ci,
            "upper_ci": upper_ci,
            "simulations": results
        }
    
    def bayesian_model(self, time_points, prior_params, data=None):
        """
        贝叶斯模型，考虑先验知识
        """
        # 简化版实现，实际应用中需要使用PyMC等贝叶斯框架
        # 这里仅作为接口占位
        return {
            "mean": 100.0 * np.exp(-0.01 * time_points),
            "lower_ci": 100.0 * np.exp(-0.015 * time_points),
            "upper_ci": 100.0 * np.exp(-0.005 * time_points)
        }
    
    def predict_with_packaging_transfer(self, model_name, params, 
                                       current_packaging, target_packaging,
                                       time_points, temperature=25.0, humidity=60.0):
        """
        预测不同包装材料下的稳定性
        通过透气性和透湿性差异进行校正
        """
        # 包装材料特性(示例)
        packaging_properties = {
            "hdpe_bottle": {"oxygen_perm": 0.3, "water_perm": 0.2},
            "glass_bottle": {"oxygen_perm": 0.05, "water_perm": 0.01},
            "alu_alu_blister": {"oxygen_perm": 0.01, "water_perm": 0.01},
            "pvc_blister": {"oxygen_perm": 0.8, "water_perm": 0.6},
            "ampoule": {"oxygen_perm": 0.001, "water_perm": 0.001}
        }
        
        # 获取包装特性
        if current_packaging not in packaging_properties or target_packaging not in packaging_properties:
            return self.models[model_name](time_points, *params)
        
        current_prop = packaging_properties[current_packaging]
        target_prop = packaging_properties[target_packaging]
        
        # 根据包装材料差异调整降解速率
        # 简化模型：假设降解速率与氧气和水透过性成正比
        oxygen_factor = target_prop["oxygen_perm"] / current_prop["oxygen_perm"]
        water_factor = target_prop["water_perm"] / current_prop["water_perm"]
        
        # 综合因子，考虑温度和湿度的影响
        if temperature > 30:  # 高温下氧化反应更显著
            combined_factor = 0.7 * oxygen_factor + 0.3 * water_factor
        elif humidity > 70:  # 高湿下水解反应更显著
            combined_factor = 0.3 * oxygen_factor + 0.7 * water_factor
        else:
            combined_factor = 0.5 * oxygen_factor + 0.5 * water_factor
            
        # 调整反应速率常数（通常是第一个参数）
        adjusted_params = list(params)
        adjusted_params[0] = params[0] * combined_factor
        
        # 使用调整后的参数进行预测
        return self.models[model_name](time_points, *adjusted_params)
    
    def comprehensive_stability_analysis(self, data, conditions, formulation_info=None):
        """
        综合稳定性分析
        整合多种条件、配方因素的影响
        """
        results = {
            "summary": {},
            "predictions": {},
            "recommendations": []
        }
        
        # 拟合最佳模型
        model_results = self.fit_best_model(data["time"], data["content"])
        best_model = model_results["best_model"]
        params = model_results[best_model]["params"]
        
        # 预测货架期
        shelf_life = self.predict_shelf_life(best_model, params)
        results["summary"]["shelf_life"] = shelf_life
        results["summary"]["best_model"] = best_model
        results["summary"]["model_quality"] = model_results[best_model]["r_squared"]
        
        # 生成预测曲线
        time_points = np.linspace(0, max(data["time"]) * 2, 100)
        predictions = self.models[best_model](time_points, *params)
        results["predictions"]["time"] = time_points.tolist()
        results["predictions"]["content"] = predictions.tolist()
        
        # 基于配方因素的建议
        if formulation_info:
            if "excipients" in formulation_info:
                for excipient in formulation_info["excipients"]:
                    if "antioxidant" in excipient.get("function", "").lower():
                        results["recommendations"].append(
                            "配方中包含抗氧化剂，有助于减缓氧化降解"
                        )
            
            if "packaging" in formulation_info:
                if "light_protection" in formulation_info["packaging"]:
                    results["recommendations"].append(
                        "使用遮光包装可以减缓光敏性降解"
                    )
                    
        # 基于拟合结果的建议
        if best_model == "first-order" and params[0] > 0.01:
            results["recommendations"].append(
                "降解符合一级动力学，速率较快，建议考虑优化配方或改进包装"
            )
        elif best_model == "biphasic":
            results["recommendations"].append(
                "存在双相降解，可能有多种降解途径，建议进行降解产物分析"
            )
            
        return results
