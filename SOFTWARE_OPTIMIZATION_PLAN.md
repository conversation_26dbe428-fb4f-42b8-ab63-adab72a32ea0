# 药物稳定性研究助手：深度优化任务规划

## 1. 概述 (Executive Summary)

本软件虽然具备了较为完整的界面和功能流程，但通过深入代码分析发现，其核心业务逻辑，特别是数据分析、AI建议和相容性评估等模块，严重依赖**模拟数据（Mock Data）和硬编码逻辑（Hardcoded Logic）**。现有代码中的"智能分析"和"数据库查询"多为预设的示例，而非真实、动态的计算和调用。

这导致软件目前处于"功能演示"阶段，无法应用于真实的科研和生产环境。本次优化旨在**彻底移除所有模拟实现**，替换为真实、可靠、可扩展的后端服务和算法，将软件从"原型"提升为"生产级"应用。

## 2. 优化指导原则

- **真实性优先**: 所有功能必须基于真实数据和真实算法。严禁任何形式的硬编码返回结果。
- **服务化与模块化**: 将核心计算（如稳定性预测、相容性分析）封装成独立的、可复用的服务。
- **数据驱动**: 建立统一、规范的数据模型，打通数据孤岛，确保数据在系统内顺畅流转。
- **测试驱动开发 (TDD)**: 为所有核心功能和API编写单元测试和集成测试，确保代码质量和重构安全。
- **文档完善**: 为关键代码、API接口和系统架构撰写清晰的文档。

## 3. 详细优化任务清单

### 3.1. 后端 (Backend) - 核心重构

#### 任务 3.1.1: 移除AI服务中的模拟逻辑
- **问题描述**: `ai_service.py` 中的 `generate_ai_suggestions` 函数完全是基于简单`if-else`规则的硬编码，并非真实AI。
- **目标文件**: `backend/app/services/ai_service.py`
- **执行方案**:
    1.  移除 `generate_ai_suggestions` 函数内的全部硬编码逻辑。
    2.  设计并实现一个真正的AI调用客户端 (`ai_client.py`)，能够连接到外部或本地的AI模型（如OpenAI API、本地部署的LLM等）。
    3.  重构 `generate_ai_suggestions`，使其通过AI客户端，将上下文数据（如药物、辅料、实验数据）发送给AI模型，并处理返回的真实建议。
    4.  为新的AI服务编写单元测试，模拟AI模型的返回并验证服务逻辑。

#### 任务 3.1.2: 移除辅料相容性评估中的模拟逻辑
- **问题描述**: `excipient_compatibility_service.py` 中的 `_name_based_assessment` 方法使用硬编码字典 `known_issues` 来模拟相容性评估。其调用的 `CompatibilityEngine` 和 `KnowledgeGraph` 也极有可能是模拟实现。
- **目标文件**:
    - `backend/app/services/excipient_compatibility_service.py`
    - `backend/app/models/compatibility_engine.py`
    - `backend/app/models/knowledge_graph.py`
- **执行方案**:
    1.  **废弃硬编码规则**: 彻底删除 `_name_based_assessment` 方法及其依赖的 `known_issues` 字典。
    2.  **实现真实知识库**:
        - 检查 `KnowledgeGraph` 模型，如果其为模拟，则需要连接到一个真实的数据库或知识图谱服务（如Neo4j）。
        - 设计一个真实的辅料相互作用数据库表，并填充可靠的公开数据。
    3.  **实现真实评估引擎**:
        - 审查 `CompatibilityEngine`。它的逻辑应基于真实的化学结构分析（如使用RDKit进行官能团匹配和反应预测），而非返回预设结果。
        - 对 `AdvancedCompatibilityEngine` 执行同样操作，确保其包含更复杂的评估逻辑（如考虑工艺条件、pKa影响等）。
    4.  **重构服务**: 重写 `assess_compatibility` 方法，确保其评估流程完全依赖于真实的知识库查询和化学结构分析。

#### 任务 3.1.3: 数据库模型与数据层完善
- **问题描述**: 根据 `优化任务规划与问题分析.md`，现有数据模型不完整，数据孤岛严重。
- **目标目录**: `backend/app/models/`
- **执行方案**:
    1.  全面审查 `drug.py`, `excipient.py`, `stability.py` 等模型文件。
    2.  根据行业标准（如ICH指南）和实际需求，补充缺失的关键字段（如药物CAS号、辅料规格、稳定性实验的详细条件等）。
    3.  建立清晰的外键关系，将项目、配方、稳定性数据、相容性分析结果等关联起来，消除数据冗余。
    4.  使用 `Alembic` 等工具创建数据库迁移脚本，以更新现有数据库结构。

#### 任务 3.1.4: 后端性能优化
- **问题描述**: 缺少必要的性能优化手段。
- **执行方案**:
    1.  **数据库连接池**: 在 `backend/app/config/database.py` 中配置并启用SQLAlchemy的连接池。
    2.  **引入缓存**: 对于不经常变动但查询频繁的数据（如辅料列表、药物信息），引入Redis缓存。
    3.  **异步任务**: 对于耗时操作（如生成复杂报告、批量数据分析），使用Celery或FastAPI的`BackgroundTasks`将其改造为异步任务，避免阻塞API响应。

### 3.2. 前端 (Frontend) - 数据对接与体验优化

#### 任务 3.2.1: 移除所有模拟数据和API调用
- **问题描述**: 前端组件和页面目前可能依赖于写死的JSON数据或模拟的API接口。
- **目标目录**: `frontend/src/`
- **执行方案**:
    1.  全局搜索并移除项目中的模拟数据文件（如 `mock.js`, `data.ts` 等）。
    2.  检查 `api.ts` 或类似文件，确保所有API端点都指向真实的后端服务器地址，并移除所有模拟实现。
    3.  审查各个页面组件（如 `StabilityPredictionPage.tsx`, `ExcipientCompatibilityPage.tsx`），确保它们通过API从后端获取真实数据，而不是使用本地状态或props中的假数据。

#### 任务 3.2.2: 统一状态管理
- **问题描述**: 复杂的应用需要一个统一的状态管理方案来处理跨组件的数据共享。
- **执行方案**:
    1.  评估并引入一个状态管理库，如 `Redux Toolkit` 或 `Zustand`。
    2.  将全局状态（如用户信息、当前项目信息）和复杂的UI状态纳入统一管理，避免混乱的prop drilling。

### 3.3. 测试与DevOps

#### 任务 3.3.1: 建立单元测试与集成测试体系
- **问题描述**: 缺乏自动化测试。
- **执行方案**:
    1.  **后端**: 使用 `pytest`，为所有API端点和服务层函数编写测试用例。重点测试业务逻辑、边界条件和异常处理。
    2.  **前端**: 使用 `Jest` 和 `React Testing Library`，为关键组件和hooks编写单元测试。

#### 任务 3.3.2: 实施CI/CD
- **问题描述**: 缺乏自动化部署流程。
- **执行方案**:
    1.  利用GitHub Actions或Jenkins，创建CI/CD流水线。
    2.  **CI (持续集成)**: 配置流水线在每次代码提交时自动运行代码风格检查（linting）、单元测试和集成测试。
    3.  **CD (持续部署)**: 配置流水线在主分支更新后，自动构建Docker镜像并将其部署到测试或生产环境。

## 4. 优化路线图 (Roadmap)

### 第一阶段：核心功能真实化 (预计2-3周)
- **目标**: 让软件的核心分析功能"活起来"。
- **任务**:
    - [x] 完成 **任务 3.1.1** (AI服务重构)。
    - [x] 完成 **任务 3.1.2** (相容性评估重构)。
    - [x] 完成 **任务 3.2.1** (前端数据对接)。
- **产出**: 一个可以执行真实分析、端到端数据流通的核心版本。

### 第二阶段：数据与性能强化 (预计2周)
- **目标**: 完善数据体系，提升系统响应能力。
- **任务**:
    - [x] 完成 **任务 3.1.3** (数据库模型完善)。
    - [x] 完成 **任务 3.1.4** (后端性能优化)。
    - [x] 完成 **任务 3.2.2** (前端状态管理)。
- **产出**: 一个数据结构更完善、性能更优越的稳定版本。

### 第三阶段：质量与自动化保障 (持续进行)
- **目标**: 建立长期维护和迭代的基石。
- **任务**:
    - [x] 完成 **任务 3.3.1** (建立测试体系)。
    - [x] 完成 **任务 3.3.2** (实施CI/CD)。
- **产出**: 一个拥有自动化质量保障和部署流程的、可信赖的系统。 