from typing import Optional
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.orm import relationship
import datetime
# 使用相对导入避免路径问题
try:
    from .base import Base
except ImportError:
    from app.models.base import Base

class IdentifyRequest(BaseModel):
    name: Optional[str] = None
    formula: Optional[str] = None
    cas: Optional[str] = None
    smiles: Optional[str] = None

class IdentifyResponse(BaseModel):
    name: str
    formula: str
    cas: str
    smiles: str
    structure_image_url: str

class IdentifyORM(Base):
    __tablename__ = 'identify'
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow) 