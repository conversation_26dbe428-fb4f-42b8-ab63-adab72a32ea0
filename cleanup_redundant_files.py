#!/usr/bin/env python3
"""
药物稳定性研究助手 - 冗余文件清理脚本
清理测试、调试、修复等临时文件，保持项目结构清洁
"""

import os
import shutil
import glob
from pathlib import Path
from typing import List, Set

class ProjectCleaner:
    """项目清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.deleted_files: List[str] = []
        self.deleted_dirs: List[str] = []
        self.preserved_files: List[str] = []
        
    def get_files_to_delete(self) -> Set[str]:
        """获取需要删除的文件列表"""
        patterns_to_delete = [
            # 测试文件
            "test_*.py",
            "*_test.py", 
            "debug_*.py",
            "*_debug.py",
            "fix_*.py",
            "*_fix.py",
            "check_*.py",
            "*_check.py",
            "verify_*.py",
            "*_verify.py",
            "final_*.py",
            "*_final.py",
            
            # 临时文件
            "temp_*.py",
            "*_temp.py",
            "tmp_*.py",
            "*_tmp.py",
            "quick_*.py",
            "*_quick.py",
            
            # 自动生成的文件
            "auto_*.py",
            "*_auto.py",
            "simple_*.py",
            "*_simple.py",
            
            # 综合测试文件
            "comprehensive_*.py",
            "*_comprehensive.py",
            
            # HTML测试文件
            "test_*.html",
            "*_test.html",
            "debug_*.html",
            "*_debug.html",
            
            # JavaScript测试文件
            "test_*.js",
            "*_test.js",
            
            # JSON测试数据
            "test_*.json",
            "*_test.json",
            
            # 批处理和脚本文件
            "restart_*.bat",
            "*_restart.bat",
            
            # PowerShell脚本
            "*.ps1",
            
            # 临时图片
            "test_*.png",
            "*_test.png",
            
            # 路由输出文件
            "route_output.txt",
            
            # 临时数据库
            "test.db",
        ]
        
        files_to_delete = set()
        for pattern in patterns_to_delete:
            files_to_delete.update(glob.glob(str(self.project_root / pattern)))
            # 也搜索子目录
            files_to_delete.update(glob.glob(str(self.project_root / "**" / pattern), recursive=True))
        
        return files_to_delete
    
    def get_dirs_to_delete(self) -> Set[str]:
        """获取需要删除的目录列表"""
        dirs_to_delete = set()
        
        # 查找临时目录
        temp_dirs = [
            "temp",
            "tmp", 
            "test_output",
            "debug_output",
            "__pycache__",
            ".pytest_cache",
            "cache/pubchem",  # 保留cache目录结构，但清理内容
        ]
        
        for dir_name in temp_dirs:
            for path in self.project_root.rglob(dir_name):
                if path.is_dir():
                    dirs_to_delete.add(str(path))
        
        return dirs_to_delete
    
    def get_reports_to_delete(self) -> Set[str]:
        """获取需要删除的报告文件"""
        report_patterns = [
            "*修复报告*.md",
            "*总结报告*.md", 
            "*实施报告*.md",
            "*进展报告*.md",
            "*状态报告*.md",
            "*完成报告*.md",
            "*警告*.md",
            "*错误*.md",
            "*API*.md",
            "*Pylance*.md",
            "*端口*.md",
            "*系统*.md",
            "🎉*.md",
            "*工作总结*.md",
            "*daily_work*.md",
            "*handover*.md",
            "*tomorrow_work*.md",
        ]
        
        reports_to_delete = set()
        for pattern in report_patterns:
            reports_to_delete.update(glob.glob(str(self.project_root / pattern)))
            reports_to_delete.update(glob.glob(str(self.project_root / "docs" / pattern)))
        
        return reports_to_delete
    
    def should_preserve_file(self, file_path: str) -> bool:
        """判断是否应该保留文件"""
        preserve_patterns = [
            # 保留核心测试文件
            "frontend/src/components/*test*",
            "frontend/src/pages/*test*", 
            "backend/app/tests/*",
            
            # 保留重要配置
            "jest.config.js",
            "tsconfig.json",
            "package.json",
            "requirements.txt",
            
            # 保留重要文档
            "README.md",
            "SOFTWARE_OPTIMIZATION_PLAN.md",
            "优化任务规划与问题分析.md",
            "software_issues_and_solutions.md",
            
            # 保留启动脚本
            "start_software.bat",
            "HOW_TO_START.txt",
        ]
        
        file_path_obj = Path(file_path)
        for pattern in preserve_patterns:
            if file_path_obj.match(pattern) or pattern in str(file_path):
                return True
        return False
    
    def clean_files(self, dry_run: bool = True) -> None:
        """清理文件"""
        print("🧹 开始清理冗余文件...")
        print(f"📁 项目根目录: {self.project_root.absolute()}")
        print(f"🔍 模式: {'预览模式' if dry_run else '执行模式'}")
        print("-" * 60)
        
        # 获取要删除的文件
        files_to_delete = self.get_files_to_delete()
        dirs_to_delete = self.get_dirs_to_delete()
        reports_to_delete = self.get_reports_to_delete()
        
        all_files_to_delete = files_to_delete | reports_to_delete
        
        # 过滤掉需要保留的文件
        filtered_files = []
        for file_path in all_files_to_delete:
            if self.should_preserve_file(file_path):
                self.preserved_files.append(file_path)
                print(f"🛡️  保留: {file_path}")
            else:
                filtered_files.append(file_path)
        
        # 删除文件
        print(f"\n📄 发现 {len(filtered_files)} 个文件需要删除:")
        for file_path in sorted(filtered_files):
            print(f"🗑️  删除文件: {file_path}")
            if not dry_run:
                try:
                    os.remove(file_path)
                    self.deleted_files.append(file_path)
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
        
        # 删除目录
        print(f"\n📁 发现 {len(dirs_to_delete)} 个目录需要删除:")
        for dir_path in sorted(dirs_to_delete):
            print(f"🗑️  删除目录: {dir_path}")
            if not dry_run:
                try:
                    shutil.rmtree(dir_path)
                    self.deleted_dirs.append(dir_path)
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
        
        # 统计信息
        print("\n" + "=" * 60)
        print("📊 清理统计:")
        print(f"🗑️  删除文件: {len(self.deleted_files) if not dry_run else len(filtered_files)}")
        print(f"🗑️  删除目录: {len(self.deleted_dirs) if not dry_run else len(dirs_to_delete)}")
        print(f"🛡️  保留文件: {len(self.preserved_files)}")
        
        if dry_run:
            print("\n⚠️  这是预览模式，没有实际删除文件")
            print("💡 要执行实际删除，请运行: python cleanup_redundant_files.py --execute")
        else:
            print("\n✅ 清理完成！")

def main():
    """主函数"""
    import sys
    
    # 检查命令行参数
    dry_run = "--execute" not in sys.argv
    
    if dry_run:
        print("⚠️  预览模式 - 不会实际删除文件")
        print("💡 要执行实际删除，请添加 --execute 参数")
        print()
    
    cleaner = ProjectCleaner()
    cleaner.clean_files(dry_run=dry_run)

if __name__ == "__main__":
    main()
