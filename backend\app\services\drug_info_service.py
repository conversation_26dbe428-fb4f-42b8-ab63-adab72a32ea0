"""
药物信息识别和查询服务
"""
import re
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from sqlalchemy.orm import Session
from redis import Redis
import logging

from ..models.drug import DrugORM
from app.schemas import DrugCreate # 使用Pydantic模型

logger = logging.getLogger(__name__)

@dataclass
class DrugInfo:
    """药物信息数据类"""
    name: str
    cas_number: Optional[str] = None
    molecular_formula: Optional[str] = None
    smiles: Optional[str] = None
    drug_class: Optional[str] = None
    description: Optional[str] = None
    molecular_weight: Optional[float] = None
    melting_point: Optional[str] = None
    solubility: Optional[str] = None

class DrugInfoService:
    """药物信息服务 - 已连接到真实数据库和缓存"""
    
    def __init__(self, db: Session, redis_client: Optional[Redis] = None):
        self.db = db
        self.redis = redis_client

    def _get_cache_key(self, drug_id: int) -> str:
        return f"drug:{drug_id}"
    
    def _get_drug_from_cache(self, drug_id: int) -> Optional[Dict]:
        if not self.redis:
            return None
        try:
            cached = self.redis.get(self._get_cache_key(drug_id))
            if cached:
                logger.info(f"Cache hit for drug ID: {drug_id}")
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Redis get failed for drug ID {drug_id}: {e}")
        return None

    def _set_drug_to_cache(self, drug: DrugORM):
        if not self.redis:
            return
        try:
            drug_dict = {c.name: getattr(drug, c.name) for c in drug.__table__.columns}
            self.redis.set(self._get_cache_key(drug.id), json.dumps(drug_dict, default=str), ex=3600)
            logger.info(f"Set cache for drug ID: {drug.id}")
        except Exception as e:
            logger.warning(f"Redis set failed for drug ID {drug.id}: {e}")

    def _invalidate_cache(self, drug_id: int):
        if not self.redis:
            return
        try:
            self.redis.delete(self._get_cache_key(drug_id))
            logger.info(f"Invalidated cache for drug ID: {drug_id}")
        except Exception as e:
            logger.warning(f"Redis delete failed for drug ID {drug_id}: {e}")

    def get_drug(self, drug_id: int) -> Optional[DrugORM]:
        """获取单个药物，带缓存"""
        cached_drug = self._get_drug_from_cache(drug_id)
        if cached_drug:
            # Pydantic的orm_mode需要ORM对象，所以我们只在db找不到时用缓存重建，或者直接返回db对象
            return DrugORM(**cached_drug)

        db_drug = self.db.query(DrugORM).filter(DrugORM.id == drug_id).first()
        if db_drug:
            self._set_drug_to_cache(db_drug)
        return db_drug

    def get_drug_by_name(self, name: str) -> Optional[DrugORM]:
        """根据名称获取药物"""
        return self.db.query(DrugORM).filter(DrugORM.name == name).first()

    def get_drugs(self, skip: int = 0, limit: int = 100) -> List[DrugORM]:
        """获取药物列表"""
        # 列表缓存较为复杂，暂时不实现
        return self.db.query(DrugORM).offset(skip).limit(limit).all()

    def add_drug(self, drug_data: DrugCreate) -> DrugORM:
        """添加新药物到数据库"""
        db_drug = DrugORM(**drug_data.dict())
        self.db.add(db_drug)
        self.db.commit()
        self.db.refresh(db_drug)
        self._set_drug_to_cache(db_drug)
        return db_drug

    def update_drug(self, drug_id: int, drug_data: DrugCreate) -> Optional[DrugORM]:
        """更新药物信息"""
        db_drug = self.get_drug(drug_id)
        if not db_drug:
            return None
        
        for key, value in drug_data.dict().items():
            setattr(db_drug, key, value)
            
        self.db.commit()
        self.db.refresh(db_drug)
        self._invalidate_cache(drug_id)
        self._set_drug_to_cache(db_drug)
        return db_drug

    def delete_drug(self, drug_id: int) -> bool:
        """删除药物"""
        db_drug = self.get_drug(drug_id)
        if not db_drug:
            return False
            
        self._invalidate_cache(drug_id)
        self.db.delete(db_drug)
        self.db.commit()
        return True

    def get_drug_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """获取药物名称建议"""
        if not partial_name:
            return []
        drugs = self.db.query(DrugORM.name).filter(DrugORM.name.ilike(f"{partial_name}%")).limit(limit).all()
        return [drug.name for drug in drugs]

    def search_drug_by_name(self, drug_name: str) -> Optional[Dict]:
        """根据药物名称搜索药物信息 (带缓存)"""
        if not drug_name:
            return None

        cache_key = self._get_cache_key(drug_name)
        
        # 1. 尝试从缓存中获取
        try:
            if self.redis:
                cached_drug = self.redis.get(cache_key)
                if cached_drug:
                    logger.info(f"Cache hit for drug: {drug_name}")
                    return json.loads(cached_drug)
        except Exception as e:
            logger.warning(f"Redis get failed for key {cache_key}: {e}")

        # 2. 如果缓存未命中，查询数据库
        logger.info(f"Cache miss for drug: {drug_name}. Querying database.")
        drug_orm = self.db.query(DrugORM).filter(DrugORM.name.ilike(f"%{drug_name}%")).first()

        if not drug_orm:
            # 也可以在这里考虑查询别名表
            return None

        # 将ORM对象转换为字典
        drug_dict = {c.name: getattr(drug_orm, c.name) for c in drug_orm.__table__.columns}
        
        # 3. 将结果存入缓存
        try:
            if self.redis:
                self.redis.set(cache_key, json.dumps(drug_dict, default=str), ex=3600) # 缓存1小时
        except Exception as e:
            logger.warning(f"Redis set failed for key {cache_key}: {e}")

        return drug_dict

    def search_drug_by_cas(self, cas_number: str) -> Optional[Dict]:
        """根据CAS号搜索药物信息"""
        if not cas_number:
            return None
        # This could also be cached
        drug_orm = self.db.query(DrugORM).filter(DrugORM.cas == cas_number).first()
        if not drug_orm:
            return None
        return {c.name: getattr(drug_orm, c.name) for c in drug_orm.__table__.columns}

    def get_all_drugs(self, skip: int = 0, limit: int = 100) -> List[Dict]:
        """获取所有药物列表 (可分页)"""
        # Caching for this list would be beneficial too
        drugs = self.db.query(DrugORM).offset(skip).limit(limit).all()
        return [{c.name: getattr(drug, c.name) for c in drug.__table__.columns} for drug in drugs]

    def search_drug_by_smiles(self, smiles: str) -> Optional[DrugInfo]:
        """根据SMILES结构搜索药物信息"""
        if not smiles:
            return None
        
        for info in self.drug_database.values():
            if info.smiles == smiles:
                return info
        
        return None
    
    def validate_cas_number(self, cas_number: str) -> bool:
        """验证CAS号格式"""
        if not cas_number:
            return False
        
        # CAS号格式：数字-数字-数字，最后一位是校验位
        pattern = r'^\d{2,7}-\d{2}-\d$'
        return bool(re.match(pattern, cas_number))
    
    def validate_smiles(self, smiles: str) -> bool:
        """验证SMILES结构格式（简单验证）"""
        if not smiles:
            return False
        
        # 简单的SMILES格式检查
        valid_chars = set('CNOSPFClBrI[]()=#+\\-@/0123456789')
        return all(c in valid_chars for c in smiles)
    
    def get_drug_classes(self) -> List[str]:
        """获取所有药物分类"""
        classes = set()
        for info in self.drug_database.values():
            if info.drug_class:
                classes.add(info.drug_class)
        return sorted(list(classes))
    
    def export_drug_database(self) -> Dict[str, Any]:
        """导出药物数据库"""
        return {
            name: {
                'name': info.name,
                'cas_number': info.cas_number,
                'molecular_formula': info.molecular_formula,
                'smiles': info.smiles,
                'drug_class': info.drug_class,
                'description': info.description,
                'molecular_weight': info.molecular_weight,
                'melting_point': info.melting_point,
                'solubility': info.solubility
            }
            for name, info in self.drug_database.items()
        }