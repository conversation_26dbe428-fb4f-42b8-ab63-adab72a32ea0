import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Row,
  Col,
  Collapse,
  Typography,
  Button,
  Space,
  Tag,
  Alert,
  InputNumber,
  Divider
} from 'antd';
import {
  DeleteOutlined,
  InfoCircleOutlined,
  ExperimentOutlined,
  SaveOutlined,
  SafetyOutlined,
  FileTextOutlined
} from '@ant-design/icons';

const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface PhysicalProperty {
  name: string;
  value: string;
  unit: string;
  conditions?: string;
}

interface ExcipientDetailedInfo {
  // 基本信息
  name: string;
  cas_number?: string;
  synonyms?: string[];
  category?: string;
  function?: string;
  
  // 理化性质
  appearance?: string;
  melting_point?: string;
  boiling_point?: string;
  density?: string;
  pka?: string;
  logp?: number;
  solubility_water?: string;
  solubility_organic?: string;
  hygroscopicity?: string;
  particle_size?: string;
  specific_surface_area?: string;
  angle_of_repose?: string;
  compressibility_index?: string;
  
  // 稳定性与贮藏
  stability_conditions?: string;
  storage_requirements?: string;
  incompatibilities?: string;
  light_sensitivity?: string;
  moisture_sensitivity?: string;
  temperature_sensitivity?: string;
  oxidation_sensitivity?: string;
  
  // 应用信息
  typical_concentration?: string;
  dosage_forms?: string[];
  
  // 安全性
  safety_profile?: string;
  regulatory_status?: string;
  
  // 药典信息
  usp_nf_info?: string;
  ph_eur_info?: string;
  jp_info?: string;
  chp_info?: string;
}

interface EnhancedExcipientInputProps {
  value: ExcipientDetailedInfo;
  onChange: (value: ExcipientDetailedInfo) => void;
  onDelete?: () => void;
  showPharmacopoeiaInfo?: boolean;
}

const EXCIPIENT_CATEGORIES = [
  '填充剂', '崩解剂', '粘合剂', '润滑剂', '包衣材料',
  '缓释材料', '抗氧化剂', '防腐剂', '矫味剂', '着色剂',
  '表面活性剂', '溶剂', '其他'
];

const DOSAGE_FORMS = [
  '片剂', '胶囊剂', '颗粒剂', '散剂', '注射剂',
  '口服液体制剂', '外用制剂', '吸入制剂', '栓剂', '贴剂'
];

const SENSITIVITY_OPTIONS = ['无', '低', '中', '高'];

export const EnhancedExcipientInput: React.FC<EnhancedExcipientInputProps> = ({
  value,
  onChange,
  onDelete,
  showPharmacopoeiaInfo = true
}) => {
  const [activeKey, setActiveKey] = useState<string | string[]>(['basic']);

  const handleChange = (field: keyof ExcipientDetailedInfo, newValue: any) => {
    onChange({
      ...value,
      [field]: newValue
    });
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>
            {value.name || '新辅料'}
          </Title>
          {onDelete && (
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              onClick={onDelete}
              size="small"
            >
              删除
            </Button>
          )}
        </div>
      }
      style={{ marginBottom: 16 }}
    >
      <Collapse activeKey={activeKey} onChange={setActiveKey}>
        {/* 基本信息 */}
        <Panel 
          header="基本信息" 
          key="basic"
          extra="名称、CAS号、类别等"
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item label="辅料名称" required>
                  <Input
                    value={value.name || ''}
                    onChange={(e) => handleChange('name', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="CAS号">
                  <Input
                    value={value.cas_number || ''}
                    onChange={(e) => handleChange('cas_number', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="辅料类别">
                  <Select
                    value={value.category || undefined}
                    onChange={(val) => handleChange('category', val)}
                    placeholder="请选择辅料类别"
                  >
                    {EXCIPIENT_CATEGORIES.map(cat => (
                      <Option key={cat} value={cat}>{cat}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="功能" help="例如：填充剂、润滑剂">
                  <Input
                    value={value.function || ''}
                    onChange={(e) => handleChange('function', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="同义词" help="多个同义词用逗号分隔">
                  <Input
                    value={value.synonyms?.join(', ') || ''}
                    onChange={(e) => handleChange('synonyms', e.target.value.split(',').map(s => s.trim()))}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>

        {/* 理化性质 */}
        <Panel 
          header={
            <Space>
              <ExperimentOutlined />
              <span>理化性质</span>
            </Space>
          }
          key="physical"
          extra="外观、熔点、溶解度、粒径等"
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col xs={24}>
                <Form.Item label="外观">
                  <TextArea
                    rows={2}
                    value={value.appearance || ''}
                    onChange={(e) => handleChange('appearance', e.target.value)}
                    placeholder="例如：白色或类白色结晶性粉末"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="熔点">
                  <Input
                    suffix="°C"
                    value={value.melting_point || ''}
                    onChange={(e) => handleChange('melting_point', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="密度">
                  <Input
                    suffix="g/cm³"
                    value={value.density || ''}
                    onChange={(e) => handleChange('density', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="pKa">
                  <Input
                    value={value.pka || ''}
                    onChange={(e) => handleChange('pka', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="水中溶解度">
                  <Input
                    value={value.solubility_water || ''}
                    onChange={(e) => handleChange('solubility_water', e.target.value)}
                    placeholder="例如：微溶（1:100）"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="有机溶剂中溶解度">
                  <Input
                    value={value.solubility_organic || ''}
                    onChange={(e) => handleChange('solubility_organic', e.target.value)}
                    placeholder="例如：易溶于乙醇"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="吸湿性">
                  <Input
                    value={value.hygroscopicity || ''}
                    onChange={(e) => handleChange('hygroscopicity', e.target.value)}
                    placeholder="例如：在RH 75%时增重2%"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="粒径分布">
                  <Input
                    value={value.particle_size || ''}
                    onChange={(e) => handleChange('particle_size', e.target.value)}
                    placeholder="例如：D50=100μm"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="休止角">
                  <Input
                    suffix="°"
                    value={value.angle_of_repose || ''}
                    onChange={(e) => handleChange('angle_of_repose', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="压缩指数">
                  <Input
                    suffix="%"
                    value={value.compressibility_index || ''}
                    onChange={(e) => handleChange('compressibility_index', e.target.value)}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>

        {/* 稳定性与贮藏 */}
        <Panel 
          header={
            <Space>
              <SaveOutlined />
              <span>稳定性与贮藏</span>
            </Space>
          }
          key="stability"
          extra="储存条件、配伍禁忌、敏感性等"
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col xs={24}>
                <Form.Item label="储存条件">
                  <TextArea
                    rows={2}
                    value={value.storage_requirements || ''}
                    onChange={(e) => handleChange('storage_requirements', e.target.value)}
                    placeholder="例如：密闭容器，室温储存，避光防潮"
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="配伍禁忌">
                  <TextArea
                    rows={2}
                    value={value.incompatibilities || ''}
                    onChange={(e) => handleChange('incompatibilities', e.target.value)}
                    placeholder="例如：与强酸、强氧化剂不相容"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="光敏感性">
                  <Select
                    value={value.light_sensitivity || '无'}
                    onChange={(val) => handleChange('light_sensitivity', val)}
                  >
                    {SENSITIVITY_OPTIONS.map(opt => (
                      <Option key={opt} value={opt}>{opt}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="湿度敏感性">
                  <Select
                    value={value.moisture_sensitivity || '无'}
                    onChange={(val) => handleChange('moisture_sensitivity', val)}
                  >
                    {SENSITIVITY_OPTIONS.map(opt => (
                      <Option key={opt} value={opt}>{opt}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="温度敏感性">
                  <Select
                    value={value.temperature_sensitivity || '无'}
                    onChange={(val) => handleChange('temperature_sensitivity', val)}
                  >
                    {SENSITIVITY_OPTIONS.map(opt => (
                      <Option key={opt} value={opt}>{opt}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="氧化敏感性">
                  <Select
                    value={value.oxidation_sensitivity || '无'}
                    onChange={(val) => handleChange('oxidation_sensitivity', val)}
                  >
                    {SENSITIVITY_OPTIONS.map(opt => (
                      <Option key={opt} value={opt}>{opt}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>

        {/* 应用信息 */}
        <Panel 
          header="应用信息" 
          key="application"
          extra="典型用量、适用剂型等"
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item label="典型用量范围">
                  <Input
                    value={value.typical_concentration || ''}
                    onChange={(e) => handleChange('typical_concentration', e.target.value)}
                    placeholder="例如：0.5-2.0%"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="适用剂型">
                  <Select
                    mode="multiple"
                    value={value.dosage_forms || []}
                    onChange={(val) => handleChange('dosage_forms', val)}
                    placeholder="请选择适用剂型"
                  >
                    {DOSAGE_FORMS.map(form => (
                      <Option key={form} value={form}>{form}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>

        {/* 药典信息 */}
        {showPharmacopoeiaInfo && (
          <Panel 
            header={
              <Space>
                <FileTextOutlined />
                <span>药典信息</span>
              </Space>
            }
            key="pharmacopoeia"
            extra="各国药典标准要求"
          >
            <Alert
              message="请输入该辅料在各国药典中的关键质量要求和特殊说明"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Form layout="vertical">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item label="USP-NF">
                    <TextArea
                      rows={3}
                      value={value.usp_nf_info || ''}
                      onChange={(e) => handleChange('usp_nf_info', e.target.value)}
                      placeholder="美国药典要求"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="Ph. Eur.">
                    <TextArea
                      rows={3}
                      value={value.ph_eur_info || ''}
                      onChange={(e) => handleChange('ph_eur_info', e.target.value)}
                      placeholder="欧洲药典要求"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="JP">
                    <TextArea
                      rows={3}
                      value={value.jp_info || ''}
                      onChange={(e) => handleChange('jp_info', e.target.value)}
                      placeholder="日本药典要求"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="ChP">
                    <TextArea
                      rows={3}
                      value={value.chp_info || ''}
                      onChange={(e) => handleChange('chp_info', e.target.value)}
                      placeholder="中国药典要求"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Panel>
        )}

        {/* 安全性信息 */}
        <Panel 
          header={
            <Space>
              <SafetyOutlined />
              <span>安全性信息</span>
            </Space>
          }
          key="safety"
          extra="安全概况、法规状态"
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col xs={24}>
                <Form.Item label="安全性概况">
                  <TextArea
                    rows={3}
                    value={value.safety_profile || ''}
                    onChange={(e) => handleChange('safety_profile', e.target.value)}
                    placeholder="例如：GRAS物质，无毒性报告"
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="法规状态">
                  <TextArea
                    rows={2}
                    value={value.regulatory_status || ''}
                    onChange={(e) => handleChange('regulatory_status', e.target.value)}
                    placeholder="例如：FDA批准用于口服制剂"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
    </Card>
  );
}; 