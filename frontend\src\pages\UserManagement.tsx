import React, { useState, useEffect, useContext } from 'react';
import { Card, Table, Button, Modal, Form, Input, Select, Tag, Space, Popconfirm, message, Typography, Row, Col, Alert } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons';
import { RoleContext } from '../App';
import { ENDPOINTS } from '../config';
import type { ColumnType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Option } = Select;

interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  status: 'active' | 'inactive' | 'locked';
  last_login?: string;
  created_at: string;
}

const UserManagement: React.FC = () => {
  const role = useContext(RoleContext);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [form] = Form.useForm();
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 检查是否有管理员权限
  const isAdmin = role === 'admin';

  // 加载用户列表
  useEffect(() => {
    if (!isAdmin) {
      setError('您没有权限访问此页面');
      return;
    }

    fetchUsers();
  }, [isAdmin]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(ENDPOINTS.USERS);
      if (!response.ok) {
        throw new Error('获取用户列表失败');
      }
      const data = await response.json();
      setUsers(data);
    } catch (err) {
      console.error('获取用户列表失败:', err);
      setError('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const showAddUserModal = () => {
    setModalTitle('添加用户');
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  const showEditUserModal = (user: User) => {
    setModalTitle('编辑用户');
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status
    });
    setModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields()
      .then(async (values) => {
        if (editingUser) {
          // 编辑现有用户
          await updateUser(editingUser.id, values);
        } else {
          // 添加新用户
          await addUser(values);
        }
        setModalVisible(false);
      })
      .catch((info) => {
        console.log('表单验证失败:', info);
      });
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  const addUser = async (userData: any) => {
    try {
      const response = await fetch(ENDPOINTS.USERS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      if (!response.ok) throw new Error('添加用户失败');
      const newUser = await response.json();
      setUsers([...users, newUser]);
      message.success('用户添加成功');
    } catch (err) {
      console.error('添加用户失败:', err);
      message.error('添加用户失败');
    }
  };

  const updateUser = async (userId: number, userData: any) => {
    try {
      const response = await fetch(ENDPOINTS.USER_DETAIL(userId), {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      if (!response.ok) throw new Error('更新用户失败');
      const updatedUser = await response.json();
      const updatedUsers = users.map(user => 
        user.id === userId ? updatedUser : user
      );
      setUsers(updatedUsers);
      message.success('用户更新成功');
    } catch (err) {
      console.error('更新用户失败:', err);
      message.error('更新用户失败');
    }
  };

  const deleteUser = async (userId: number) => {
    try {
      const response = await fetch(ENDPOINTS.USER_DETAIL(userId), {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('删除用户失败');
      const updatedUsers = users.filter(user => user.id !== userId);
      setUsers(updatedUsers);
      message.success('用户删除成功');
    } catch (err) {
      console.error('删除用户失败:', err);
      message.error('删除用户失败');
    }
  };

  const toggleUserStatus = async (user: User) => {
    const newStatus = user.status === 'locked' ? 'active' : 
                      user.status === 'active' ? 'locked' : 'active';
    
    try {
      const response = await fetch(ENDPOINTS.USER_DETAIL(user.id), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });
      if (!response.ok) throw new Error('更新用户状态失败');
      const updatedUser = await response.json();
      const updatedUsers = users.map(u => 
        u.id === user.id ? updatedUser : u
      );
      setUsers(updatedUsers);
      message.success(`用户已${newStatus === 'locked' ? '锁定' : '解锁'}`);
    } catch (err) {
      console.error('更新用户状态失败:', err);
      message.error('更新用户状态失败');
    }
  };

  const getRoleTag = (role: string) => {
    switch (role) {
      case 'admin':
        return <Tag color="red">管理员</Tag>;
      case 'user':
        return <Tag color="blue">普通用户</Tag>;
      case 'guest':
        return <Tag color="green">访客</Tag>;
      default:
        return <Tag color="default">{role}</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="success">正常</Tag>;
      case 'inactive':
        return <Tag color="default">未激活</Tag>;
      case 'locked':
        return <Tag color="error">已锁定</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const columns: ColumnType<User>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => getRoleTag(role),
      filters: [
        { text: '管理员', value: 'admin' },
        { text: '普通用户', value: 'user' },
        { text: '访客', value: 'guest' },
      ],
      onFilter: (value, record) => record.role === value,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
      filters: [
        { text: '正常', value: 'active' },
        { text: '未激活', value: 'inactive' },
        { text: '已锁定', value: 'locked' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (text: string) => text || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: User) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => showEditUserModal(record)}
          >
            编辑
          </Button>
          
          <Button
            type="link"
            icon={record.status === 'locked' ? <UnlockOutlined /> : <LockOutlined />}
            onClick={() => toggleUserStatus(record)}
          >
            {record.status === 'locked' ? '解锁' : '锁定'}
          </Button>
          
          <Popconfirm
            title="确定要删除此用户吗?"
            onConfirm={() => deleteUser(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (!isAdmin) {
    return (
      <Alert
        message="权限不足"
        description="您没有权限访问此页面，请联系管理员"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      <Title level={2}>用户管理</Title>
      
      {error && <Alert message={error} type="error" showIcon style={{ marginBottom: 16 }} />}
      
      <Card>
        <Row style={{ marginBottom: 16 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showAddUserModal}
            >
              添加用户
            </Button>
          </Col>
        </Row>
        
        <Table
          dataSource={users}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        destroyOnHidden={true}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>
          
          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}
          
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Option value="admin">管理员</Option>
              <Option value="user">普通用户</Option>
              <Option value="guest">访客</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
            initialValue="active"
          >
            <Select placeholder="请选择状态">
              <Option value="active">正常</Option>
              <Option value="inactive">未激活</Option>
              <Option value="locked">已锁定</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement; 