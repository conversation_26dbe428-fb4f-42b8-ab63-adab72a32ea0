@echo off
chcp 65001 > nul
title 药物稳定性研究助手 - 启动程序

:: 设置颜色
color 0A

echo ╔══════════════════════════════════════════════╗
echo ║      药物稳定性研究助手 - 启动程序          ║
echo ║      Drug Stability Research Assistant       ║
echo ╔══════════════════════════════════════════════╝
echo.

:: 检查Python
echo [检查] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python！请先安装Python 3.8或更高版本
    echo        下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查Node.js
echo [检查] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Node.js！请先安装Node.js 14或更高版本
    echo        下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查虚拟环境
echo [检查] 检查Python虚拟环境...
if not exist "backend\.venv\Scripts\python.exe" (
    echo [信息] 未找到虚拟环境，正在创建...
    cd backend
    python -m venv .venv
    echo [信息] 正在安装后端依赖，请稍候...
    .venv\Scripts\pip install -r requirements.txt
    cd ..
    echo [成功] 虚拟环境创建完成！
)

:: 检查前端依赖
echo [检查] 检查前端依赖...
if not exist "frontend\node_modules" (
    echo [信息] 未找到前端依赖，正在安装...
    cd frontend
    npm install
    cd ..
    echo [成功] 前端依赖安装完成！
)

:: 激活 Python 虚拟环境
cd backend
call .venv\Scripts\activate.bat

:: 启动后端（新窗口，确保虚拟环境生效）
start cmd /k ".venv\Scripts\activate.bat && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
cd ..

:: 启动前端（新窗口）
cd frontend
start cmd /k "npm start"
cd ..

:: 等待后端启动
echo [等待] 等待后端服务启动...
timeout /t 5 /nobreak > nul

:: 检查后端是否启动成功
curl -s http://localhost:8000/api/health >nul 2>&1
if errorlevel 1 (
    echo [警告] 后端服务可能尚未完全启动，请稍后手动检查
) else (
    echo [成功] 后端服务已启动！
)

:: 等待前端启动
echo [等待] 等待前端服务启动...
timeout /t 8 /nobreak > nul

:: 显示成功信息
cls
color 0A
echo ╔══════════════════════════════════════════════╗
echo ║           ✓ 启动成功！                      ║
echo ╠══════════════════════════════════════════════╣
echo ║                                              ║
echo ║  后端服务: http://localhost:8000             ║
echo ║  API文档:  http://localhost:8000/docs        ║
echo ║  前端应用: http://localhost:3000             ║
echo ║                                              ║
echo ╠══════════════════════════════════════════════╣
echo ║  提示:                                       ║
echo ║  - 首次启动可能需要较长时间                 ║
echo ║  - 如遇问题请查看各窗口的错误信息           ║
echo ║  - 使用 Ctrl+C 可以停止服务                 ║
echo ║                                              ║
echo ╚══════════════════════════════════════════════╝
echo.

:: 询问是否打开浏览器
echo 是否自动打开浏览器？(Y/N)
choice /C YN /N /M "请选择: "
if errorlevel 2 goto end
if errorlevel 1 goto openbrowser

:openbrowser
echo [信息] 正在打开浏览器...
start http://localhost:3000

:end
echo.
echo 按任意键退出此窗口（服务将继续运行）...
pause > nul 