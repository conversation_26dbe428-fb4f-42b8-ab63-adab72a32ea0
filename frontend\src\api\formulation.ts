import { ENDPOINTS, API_BASE_URL } from '../config';
import { message } from 'antd';
import axios from 'axios';

/**
 * 辅料信息接口
 */
export interface ExcipientInfo {
  id: string;
  name: string;
  category: string;
  smiles?: string;
  description?: string;
}

/**
 * 稳定性数据点接口
 */
export interface StabilityDataPoint {
  time_point: number;  // 月份
  temperature: number;  // °C
  humidity: number;  // %RH
  value: number;  // 测量值（如含量%）
  item: string;  // 测试项目（如"含量"、"杂质A"）
}

/**
 * 配方分析请求接口
 */
export interface FormulationAnalysisRequest {
  drug_name: string;
  drug_structure?: string;
  excipients: Array<{
    name: string;
    structure?: string;
    concentration?: number;
    ph?: number;
  }>;
  temperature: number;
  humidity: number;
  ph?: number;
  packaging?: string;
  history_data?: Array<{
    time_point: number;
    temperature: number;
    humidity: number;
    value: number;
    item: string;
  }>;
  prediction_timepoints: number[];
  model_selection: 'auto' | 'zero-order' | 'first-order' | 'second-order';
  confidence_level: number;
}

/**
 * 风险因素接口
 */
export interface RiskFactor {
  factor: string;
  risk_level: string;  // 高/中/低
  description: string;
  impact_score: number;  // 0-10
  mitigation: string;
}

/**
 * 建议接口
 */
export interface Recommendation {
  title: string;
  priority: string;
  description: string;
  rationale: string;
  implementation: string;
}

/**
 * AI建议接口
 */
export interface AISuggestion {
  title: string;
  risk: string;
  desc: string;
  detail: string;
  type: string;
  mechanism: string;
  reference: string;
}

/**
 * 相容性结果接口
 */
export interface CompatibilityResults {
  drug: string;
  results: Array<{
    excipient: string;
    risk_level: string;
    risk_type: string;
    risk_score: number;
    suggestion: string;
    evidence: string[];
    detailed_suggestions?: string[];
  }>;
}

/**
 * 稳定性预测接口
 */
export interface StabilityPrediction {
  prediction: {
    long_term: {
      t90: number;
      ci: [number, number];
    };
    intermediate: {
      t90: number;
      ci: [number, number];
    };
    accelerated: {
      t90: number;
      ci: [number, number];
    };
  };
  model_info: {
    type: string;
    r_squared: number;
    activation_energy: number;
    plot?: string;
  };
  sensitivity: Array<{
    factor: string;
    impact: number;
  }>;
  regulatory_check: string;
}

/**
 * 配方分析响应接口
 */
export interface FormulationAnalysisResponse {
  drug_name: string;
  analysis_date: string;
  overall_risk_level: string;
  risk_factors: RiskFactor[];
  recommendations: Recommendation[];
  compatibility_results: CompatibilityResults;
  stability_prediction: StabilityPrediction;
  ai_suggestions: AISuggestion[];
}

/**
 * 获取常用辅料列表
 * @returns 常用辅料列表
 */
export async function getCommonExcipients(): Promise<ExcipientInfo[]> {
  try {
    const response = await axios.get<ExcipientInfo[]>(`${API_BASE_URL}/excipient/excipients/common`);
    return response.data;
  } catch (error) {
    console.error('获取辅料列表失败:', error);
    message.error('获取辅料列表失败，请稍后重试');
    return [];
  }
}

/**
 * 搜索辅料
 * @param query 辅料名称或关键词
 * @returns 匹配的辅料列表
 */
export async function searchExcipients(query: string): Promise<ExcipientInfo[]> {
  try {
    const response = await axios.get<ExcipientInfo[]>(`${API_BASE_URL}/excipients/search`, {
      params: { query }
    });
    return response.data || [];
  } catch (error: any) {
    console.warn('搜索辅料失败，返回空数组:', error.response?.status || error.message);
    // 返回一些默认的搜索结果
    const defaultExcipients = [
      { id: '1', name: '微晶纤维素', category: '填充剂', description: '常用填充剂' },
      { id: '2', name: '乳糖', category: '填充剂', description: '常用填充剂' },
      { id: '3', name: '硬脂酸镁', category: '润滑剂', description: '常用润滑剂' },
      { id: '4', name: '羧甲基纤维素钠', category: '崩解剂', description: '常用崩解剂' },
      { id: '5', name: '聚维酮K30', category: '黏合剂', description: '常用黏合剂' }
    ];
    return defaultExcipients.filter(exc =>
      exc.name.toLowerCase().includes(query.toLowerCase())
    );
  }
}

/**
 * 辅料相容性分析结果接口
 */
export interface ExcipientCompatibilityResult {
  excipient: string;
  risk_level: 'high' | 'medium' | 'low';
  risk_type: string;
  risk_score: number;
  suggestion: string;
  evidence: string[];
  detailed_suggestions?: string[];
  reaction_mechanisms?: string[];
}

export interface CompatibilityAnalysisResult {
  drug: string;
  results: ExcipientCompatibilityResult[];
}

/**
 * 辅料相容性分析请求接口
 */
export interface CompatibilityAnalysisRequest {
  drug_name: string;
  drug_structure: string;
  excipients: Array<{
    name: string;
    structure?: string;
    batch?: string;
    supplier?: string;
    ph?: number;
    concentration?: number;
  }>;
}

/**
 * 执行辅料相容性分析
 * @param request 辅料相容性分析请求
 * @returns 辅料相容性分析结果
 */
export async function analyzeExcipientCompatibility(
  request: CompatibilityAnalysisRequest
): Promise<CompatibilityAnalysisResult> {
  try {
    const response = await axios.post<CompatibilityAnalysisResult>(`${API_BASE_URL}/compatibility/assess`, request);
    return response.data;
  } catch (error) {
    console.error('辅料相容性分析失败:', error);
    message.error('辅料相容性分析失败，请稍后重试');
    throw error;
  }
}

/**
 * 获取辅料详情
 * @param id 辅料ID
 * @returns 辅料详情
 */
export async function getExcipientDetails(id: string): Promise<ExcipientInfo> {
  try {
    const response = await axios.get<ExcipientInfo>(`${API_BASE_URL}/excipients/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取辅料详情失败:', error);
    message.error('获取辅料详情失败，请稍后重试');
    throw error;
  }
}

/**
 * 分析配方
 * @param data 配方分析请求数据
 * @returns 配方分析结果
 */
export const analyzeFormulation = async (
  data: FormulationAnalysisRequest
): Promise<FormulationAnalysisResponse> => {
  try {
    const response = await axios.post<FormulationAnalysisResponse>(`${API_BASE_URL}/formulation_analysis/analyze_comprehensive`, data);
    return response.data;
  } catch (error: any) {
    console.error('配方分析失败:', error);
    console.error('错误详情:', error.response?.data);
    
    // 提供更详细的错误信息
    if (error.response?.status === 404) {
      throw new Error('配方分析API端点未找到，请检查后端服务');
    } else if (error.response?.status === 422) {
      throw new Error(`数据验证失败: ${error.response.data.detail || '请检查输入数据格式'}`);
    } else if (error.response?.data?.detail) {
      throw new Error(error.response.data.detail);
    } else {
      throw error;
    }
  }
}; 