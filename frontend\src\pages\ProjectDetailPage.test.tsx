import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectDetailPage from './ProjectDetailPage';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

const renderPage = () =>
  render(
    <MemoryRouter initialEntries={[{ pathname: '/project/1' }]}> 
      <Routes>
        <Route path="/project/:id" element={<ProjectDetailPage />} />
      </Routes>
    </MemoryRouter>
  );

describe('ProjectDetailPage AI建议Tab', () => {
  it('renders AI建议Tab and cards', async () => {
    renderPage();
    await waitFor(() => {
      expect(screen.getByText('AI建议')).toBeInTheDocument();
      expect(screen.getByText('建议1')).toBeInTheDocument();
      expect(screen.getByText('建议2')).toBeInTheDocument();
    });
  });

  it('can open 溯源详情弹窗', async () => {
    renderPage();
    await waitFor(() => {
      const traceBtns = screen.getAllByText('溯源');
      expect(traceBtns.length).toBeGreaterThan(0);
      fireEvent.click(traceBtns[0]);
    });
    expect(screen.getByText('溯源详情1')).toBeInTheDocument();
  });

  it('can give feedback and see history', async () => {
    renderPage();
    await waitFor(() => {
      const helpfulBtn = screen.getAllByText('有帮助')[0];
      fireEvent.click(helpfulBtn);
    });
    expect(screen.getAllByText('感谢您的反馈！')[0]).toBeInTheDocument();
    expect(screen.getAllByText('反馈历史')[0].parentElement).toHaveTextContent('有帮助');
  });
});

describe('ProjectDetailPage 导出功能', () => {
  it('can open export preview modal', async () => {
    renderPage();
    await waitFor(() => {
      const previewBtn = screen.getByText('预览导出内容');
      fireEvent.click(previewBtn);
    });
    expect(screen.getByText('导出内容预览')).toBeInTheDocument();
    expect(screen.getByText('原始数据')).toBeInTheDocument();
  });

  it('export buttons are disabled/loading when exporting', async () => {
    renderPage();
    // 模拟点击导出PDF
    await waitFor(() => {
      const exportBtn = screen.getByText('导出PDF');
      fireEvent.click(exportBtn);
    });
    // 按钮应处于loading/disabled
    expect(screen.getByText('导出PDF').closest('button')).toHaveAttribute('disabled');
  });

  it('can toggle export content options', async () => {
    renderPage();
    await waitFor(() => {
      const exportBtn = screen.getByText('导出报告');
      fireEvent.click(exportBtn);
    });
    // 勾选/取消导出内容
    const option = screen.getByLabelText('原始数据');
    fireEvent.click(option);
    expect(option).toBeChecked();
  });
});

describe('ProjectDetailPage 报告导出与AI建议Tab', () => {
  it('导出报告按钮可点击，弹窗可正常显示', async () => {
    render(<ProjectDetailPage />);
    const exportBtn = await screen.findByText('导出报告');
    fireEvent.click(exportBtn);
    expect(await screen.findByText('自定义导出内容')).toBeInTheDocument();
  });

  it('AI建议Tab渲染卡片、溯源弹窗、反馈交互', async () => {
    render(<ProjectDetailPage />);
    const aiTab = await screen.findByText('AI建议');
    fireEvent.click(aiTab);
    expect(await screen.findByText('AI建议卡片')).toBeInTheDocument();
    const traceBtn = await screen.findByText('溯源');
    fireEvent.click(traceBtn);
    expect(await screen.findByText('建议溯源')).toBeInTheDocument();
    const helpfulBtn = await screen.findByText('有帮助');
    fireEvent.click(helpfulBtn);
    expect(await screen.findByText('感谢您的反馈！')).toBeInTheDocument();
  });

  it('批量导入历史与错误修正入口渲染', async () => {
    render(<ProjectDetailPage />);
    expect(await screen.findByText('导出历史')).toBeInTheDocument();
  });

  it('国际化切换下主要功能可用', async () => {
    render(<ProjectDetailPage />);
    // 假设有语言切换按钮
    // fireEvent.click(screen.getByLabelText('切换到英文'));
    // expect(await screen.findByText('Export Report')).toBeInTheDocument();
  });
});

// 新增测试：导出格式选择、AI建议反馈API、导出历史下载、无障碍aria、国际化切换
describe('ProjectDetailPage 新增测试', () => {
  it('导出格式选择', async () => {
    renderPage();
    await waitFor(() => {
      const exportBtn = screen.getByText('导出报告');
      fireEvent.click(exportBtn);
    });
    // 勾选/取消导出内容
    const option = screen.getByLabelText('原始数据');
    fireEvent.click(option);
    expect(option).toBeChecked();
  });

  it('AI建议反馈API', async () => {
    renderPage();
    await waitFor(() => {
      const helpfulBtn = screen.getAllByText('有帮助')[0];
      fireEvent.click(helpfulBtn);
    });
    expect(screen.getAllByText('感谢您的反馈！')[0]).toBeInTheDocument();
    expect(screen.getAllByText('反馈历史')[0].parentElement).toHaveTextContent('有帮助');
  });

  it('导出历史下载', async () => {
    renderPage();
    expect(await screen.findByText('导出历史')).toBeInTheDocument();
  });

  it('无障碍aria', async () => {
    renderPage();
    // 检查aria属性
    const exportBtn = screen.getByText('导出报告');
    expect(exportBtn).toHaveAttribute('aria-label', '导出报告');
  });

  it('国际化切换', async () => {
    renderPage();
    // 假设有语言切换按钮
    // fireEvent.click(screen.getByLabelText('切换到英文'));
    // expect(await screen.findByText('Export Report')).toBeInTheDocument();
  });
});

describe('ProjectDetailPage 关键业务流集成测试', () => {
  it('AI建议Tab应能正常渲染并显示建议', async () => {
    renderPage();
    expect(await screen.findByText('AI建议')).toBeInTheDocument();
    // 若无数据应有无数据提示
    // 若有数据应渲染建议卡片
  });

  it('导出功能应能正常弹窗并导出', async () => {
    renderPage();
    const exportBtn = await screen.findByText('导出报告');
    fireEvent.click(exportBtn);
    expect(await screen.findByText('自定义导出内容')).toBeInTheDocument();
    // 可进一步模拟导出点击
  });

  it('批量导入历史应能正常显示', async () => {
    renderPage();
    expect(await screen.findByText('导出历史')).toBeInTheDocument();
  });

  it('API异常时应有错误提示', async () => {
    // 可通过mock fetch/axios抛出异常，断言页面有错误提示
  });

  it('无权限或未登录时应跳转登录或提示', async () => {
    // 可通过mock用户状态，断言页面跳转或提示
  });
}); 