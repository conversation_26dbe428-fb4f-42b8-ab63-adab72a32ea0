#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
初始化开放数据源
下载并导入FDA、Guide to PHARMACOLOGY等开放数据
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.app.services.open_data_service import open_data_service

async def main():
    """主函数"""
    print("="*60)
    print("药物稳定性研究助手 - 开放数据源初始化")
    print("="*60)
    print()
    
    print("本脚本将从以下开放数据源下载数据：")
    print("1. FDA Inactive Ingredient Database")
    print("2. Guide to PHARMACOLOGY")
    print("3. 内置药物化学知识库")
    print()
    
    response = input("是否开始下载？(y/n): ")
    if response.lower() != 'y':
        print("取消下载")
        return
    
    print("\n开始初始化数据...")
    
    try:
        # 初始化所有数据源
        await open_data_service.initialize_data()
        
        print("\n✅ 数据初始化完成！")
        print("\n已下载的数据保存在: data/open_sources/")
        print("数据库文件: data/open_sources/excipients.db")
        
        # 显示一些统计信息
        import sqlite3
        conn = sqlite3.connect(open_data_service.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM excipients")
        excipient_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM drug_excipient_interactions")
        interaction_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"\n统计信息：")
        print(f"- 辅料信息: {excipient_count} 条")
        print(f"- 相互作用规则: {interaction_count} 条")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {e}")
        print("请检查网络连接并重试")

if __name__ == "__main__":
    asyncio.run(main()) 