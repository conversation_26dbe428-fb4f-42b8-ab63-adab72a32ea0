from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from app.services.excipient_data_service import ExcipientDataService
from app.config.database import get_db
from pydantic import BaseModel

router = APIRouter(
    prefix="/excipients",
    tags=["excipients"]
)

# Pydantic Models
class ExcipientBase(BaseModel):
    name: str
    function: Optional[str] = None
    risk_level: Optional[str] = None
    remark: Optional[str] = None

class ExcipientCreate(ExcipientBase):
    pass

class Excipient(ExcipientBase):
    id: int

    class Config:
        orm_mode = True

# Dependency to get the service
def get_excipient_service(db: Session = Depends(get_db)) -> ExcipientDataService:
    return ExcipientDataService(db)

@router.post("/", response_model=Excipient, status_code=201)
def create_excipient(
    excipient_data: ExcipientCreate,
    service: ExcipientDataService = Depends(get_excipient_service)
):
    """
    创建一个新的辅料。
    """
    db_excipient = service.get_excipient_by_name(excipient_data.name)
    if db_excipient:
        raise HTTPException(status_code=400, detail="同名辅料已存在")
    return service.create_excipient(excipient_data=excipient_data)

@router.get("/", response_model=List[Excipient])
def list_excipients(
    skip: int = 0,
    limit: int = 100,
    service: ExcipientDataService = Depends(get_excipient_service)
):
    """
    获取辅料列表。
    """
    return service.get_excipients(skip=skip, limit=limit)

@router.get("/{excipient_id}", response_model=Excipient)
def get_excipient(
    excipient_id: int,
    service: ExcipientDataService = Depends(get_excipient_service)
):
    """
    获取单个辅料的详细信息。
    """
    db_excipient = service.get_excipient(excipient_id=excipient_id)
    if db_excipient is None:
        raise HTTPException(status_code=404, detail="未找到该辅料")
    return db_excipient

@router.put("/{excipient_id}", response_model=Excipient)
def update_excipient(
    excipient_id: int,
    excipient_data: ExcipientCreate,
    service: ExcipientDataService = Depends(get_excipient_service)
):
    """
    更新现有辅料的信息。
    """
    db_excipient = service.update_excipient(excipient_id=excipient_id, excipient_data=excipient_data)
    if db_excipient is None:
        raise HTTPException(status_code=404, detail="未找到该辅料")
    return db_excipient

@router.delete("/{excipient_id}", status_code=204)
def delete_excipient(
    excipient_id: int,
    service: ExcipientDataService = Depends(get_excipient_service)
):
    """
    删除一个辅料。
    """
    success = service.delete_excipient(excipient_id=excipient_id)
    if not success:
        raise HTTPException(status_code=404, detail="未找到该辅料")
    return {"ok": True} 