from pydantic import BaseModel, field_validator
from typing import List, Optional
from datetime import datetime

class LoginRequest(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class AISuggestionFeedback(BaseModel):
    suggestion_id: int
    feedback: str  # 'helpful' | 'not_helpful'

class ExportReportRequest(BaseModel):
    project_id: int
    options: List[str]
    lang: str

class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: Optional[str] = '新建'

class ProjectCreateRequest(ProjectBase):
    pass

class ProjectUpdateRequest(ProjectBase):
    name: Optional[str] = None # Allow name to be optional on update
    data: Optional[dict] = None # Allow data to be updated

class ProjectResponse(ProjectBase):
    id: int
    created_at: str
    updated_at: str
    data: Optional[dict] = None

    @field_validator('created_at', 'updated_at', mode='before')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, datetime):
            return v.isoformat()
        return v

    class Config:
        from_attributes = True

class ProjectOut(ProjectBase):
    id: int
    created_at: str

class AnalysisRequest(BaseModel):
    project_id: int
    options: List[str]
    lang: str = "zh"
    format: str = "pdf"

class ReportExportRequest(BaseModel):
    project_id: int
    options: List[str]
    lang: str = "zh"
    format: str = "pdf"

class AnalysisResult(BaseModel):
    project_id: int
    result: dict  # 或根据你的实际结构调整
    created_at: str

class ExportHistoryOut(BaseModel):
    id: int
    fileName: str
    importTime: str
    importedBy: str
    status: str 

# 导出历史相关服务函数

_export_history = []

def add_export_history_service(history_item: dict):
    """添加导出历史记录"""
    _export_history.append(history_item)
    return True

def list_export_history_service():
    """获取所有导出历史记录"""
    return _export_history

# --- Drug Models (from api/drug_info.py) ---
class DrugBase(BaseModel):
    name: str
    formula: Optional[str] = None
    cas: Optional[str] = None
    smiles: Optional[str] = None
    structure_image_url: Optional[str] = None

class DrugCreate(DrugBase):
    pass

class Drug(DrugBase):
    id: int
    class Config:
        orm_mode = True

# --- Stability Prediction Models ---
class StabilityDataBase(BaseModel):
    project_id: int
    time_point: float
    temperature: float
    humidity: float
    value: float
    item: str

class StabilityDataCreate(StabilityDataBase):
    pass

class StabilityData(BaseModel):
    id: int
    project_id: int
    time_point: float
    temperature: float
    humidity: float
    value: float
    item: str
    class Config:
        orm_mode = True

class StabilityPredictRequest(BaseModel):
    project_id: int
    prediction_months: int = 36
    confidence_level: float = 0.95
    model_selection: str = "auto"

class StabilityPredictionResponse(BaseModel):
    fit_result: dict
    shelf_life_info: dict
    plot_base64: Optional[str]